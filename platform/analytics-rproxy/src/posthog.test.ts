import { HTTPException } from "hono/http-exception";
import { describe, expect, it } from "vitest";
import { generateSessionId, generateUserId, processEvents } from "./posthog";
import { SaltManager } from "./salt";
import { FakeKV } from "./test/fake-kv";

describe("processEvents", () => {
  it("should only mutate unidentified events and $identify events", async () => {
    const processedEvents = await processEvents(
      [
        {
          event: "identified_event",
          properties: {
            token: "phc_posthog_key",
            $raw_user_agent: "best-user-agent",
            $time: 1234567890,
            $timezone: "America/Los_Angeles",
            $configured_session_timeout_ms: 1800000,
            distinct_id: "existing_distinct_id",
            $device_id: "existing_device_id",
            $session_id: "existing_session_id",
            $is_identified: true,
          },
        },
        {
          event: "unidentified_event",
          properties: {
            token: "phc_posthog_key",
            $raw_user_agent: "best-user-agent",
            $time: 1234567890,
            $timezone: "America/Los_Angeles",
            $configured_session_timeout_ms: 1800000,
            distinct_id: "existing_distinct_id",
            $device_id: "existing_device_id",
            $session_id: "existing_session_id",
            $is_identified: false,
          },
        },
        {
          event: "$identify",
          properties: {
            token: "phc_posthog_key",
            $raw_user_agent: "best-user-agent",
            $time: 1234567890,
            $timezone: "America/Los_Angeles",
            $configured_session_timeout_ms: 1800000,
            distinct_id: "existing_distinct_id",
            $anon_distinct_id: "existing_anon_distinct_id",
            $device_id: "existing_device_id",
            $session_id: "existing_session_id",
            $is_identified: true,
          },
        },
      ],
      "phc_posthog_key",
      "*******",
      new SaltManager(new FakeKV(), () => "fake-salt")
    );

    expect(processedEvents).toEqual([
      {
        event: "identified_event",
        properties: {
          token: "phc_posthog_key",
          $raw_user_agent: "best-user-agent",
          $time: 1234567890,
          $timezone: "America/Los_Angeles",
          $configured_session_timeout_ms: 1800000,
          distinct_id: "existing_distinct_id",
          $device_id: "existing_device_id",
          $session_id: "existing_session_id",
          $is_identified: true,
        },
      },
      {
        event: "unidentified_event",
        properties: {
          token: "phc_posthog_key",
          $raw_user_agent: "best-user-agent",
          $time: 1234567890,
          $timezone: "America/Los_Angeles",
          $configured_session_timeout_ms: 1800000,
          distinct_id:
            "pcookieless_vGcZYXi1PBN4S3djRPozFvN5q7GgDUVa/m2OKXEivMc=",
          $device_id:
            "pcookieless_vGcZYXi1PBN4S3djRPozFvN5q7GgDUVa/m2OKXEivMc=",
          $session_id: "011f71f9-a4c0-7d05-b579-960bd655fd74",
          $is_identified: false,
        },
      },
      {
        event: "$identify",
        properties: {
          token: "phc_posthog_key",
          $raw_user_agent: "best-user-agent",
          $time: 1234567890,
          $timezone: "America/Los_Angeles",
          $configured_session_timeout_ms: 1800000,
          distinct_id: "existing_distinct_id",
          $anon_distinct_id:
            "pcookieless_vGcZYXi1PBN4S3djRPozFvN5q7GgDUVa/m2OKXEivMc=",
          $device_id: "existing_device_id",
          $session_id: "existing_session_id",
          $is_identified: true,
        },
      },
    ]);
  });
  it("should return events if token is correct", async () => {
    const events = [
      {
        event: "$pageview",
        properties: {
          token: "phc_posthog_key",
        },
      },
    ];
    const processedEvents = await processEvents(
      events,
      "phc_posthog_key",
      "*******",
      new SaltManager(new FakeKV(), () => "fake-salt")
    );
    expect(processedEvents).toEqual(events);
  });
  it("should throw 401 if token is wrong", async () => {
    const events = [
      {
        event: "$pageview",
        properties: {
          token: "phc_posthog_wrong_key",
        },
      },
    ];
    const promise = processEvents(
      events,
      "phc_posthog_key",
      "*******",
      new SaltManager(new FakeKV(), () => "fake-salt")
    );
    expect(promise).rejects.toThrowError(HTTPException);
    expect(promise).rejects.toThrowError("Invalid token");
  });
  it("should throw 401 if token is missing", async () => {
    const events = [{ event: "$pageview", properties: {} }];
    const promise = processEvents(
      events,
      "phc_posthog_key",
      "*******",
      new SaltManager(new FakeKV(), () => "fake-salt")
    );
    expect(promise).rejects.toThrowError(HTTPException);
    expect(promise).rejects.toThrowError("Invalid token");
  });
});

describe("generateUserHash", () => {
  it("should generate the same hash for the same input", async () => {
    const hash1 = await generateUserId("test_salt", "*******", "ua");
    const hash2 = await generateUserId("test_salt", "*******", "ua");
    expect(hash1).toEqual(hash2);
  });

  it("should generate different hashes for different user agents", async () => {
    const hash1 = await generateUserId("test_salt", "*******", "ua1");
    const hash2 = await generateUserId("test_salt", "*******", "ua2");
    expect(hash1).not.toEqual(hash2);
  });

  it("should generate different hashes for different IP addresses", async () => {
    const hash1 = await generateUserId("test_salt", "*******", "ua");
    const hash2 = await generateUserId("test_salt", "*******", "ua");
    expect(hash1).not.toEqual(hash2);
  });
});

describe("generateSessionId", () => {
  it("should generate the same ID within the same session window", async () => {
    const userId = "pcookieless_123";
    const sessionTimeoutMs = 1800000; // 30 minutes
    const t0 = await generateSessionId(userId, 0, sessionTimeoutMs);
    const t00m01s = await generateSessionId(userId, 1000, sessionTimeoutMs);
    const t00m02s = await generateSessionId(userId, 2000, sessionTimeoutMs);
    const t29m59s = await generateSessionId(userId, 1799000, sessionTimeoutMs);
    const t30m00s = await generateSessionId(userId, 1800000, sessionTimeoutMs);
    const t30m01s = await generateSessionId(userId, 1801000, sessionTimeoutMs);
    const t59m59s = await generateSessionId(userId, 3599000, sessionTimeoutMs);
    const t60m00s = await generateSessionId(userId, 3600000, sessionTimeoutMs);
    const t60m01s = await generateSessionId(userId, 3601000, sessionTimeoutMs);

    expect(t0).toEqual(t00m01s);
    expect(t0).toEqual(t00m02s);
    expect(t0).toEqual(t29m59s);
    expect(t0).not.toEqual(t30m00s);
    expect(t30m00s).toEqual(t30m01s);
    expect(t30m00s).toEqual(t59m59s);
    expect(t30m00s).not.toEqual(t60m00s);
    expect(t60m00s).toEqual(t60m01s);
  });

  it("should generate different IDs for different user hashes", async () => {
    const userId1 = "pcookieless_123";
    const userId2 = "pcookieless_456";

    const $configured_session_timeout_ms = 1;
    const sessionId1 = await generateSessionId(
      userId1,
      0,
      $configured_session_timeout_ms
    );
    const sessionId2 = await generateSessionId(
      userId2,
      0,
      $configured_session_timeout_ms
    );
    expect(sessionId1).not.toEqual(sessionId2);
  });

  it("should generate different IDs for different session timeouts", async () => {
    const userId = "pcookieless_123";
    const sessionId1 = await generateSessionId(userId, 0, 2);
    const sessionId2 = await generateSessionId(userId, 0, 3);
    expect(sessionId1).not.toEqual(sessionId2);

    const sessionId3 = await generateSessionId(userId, 5, 2);
    const sessionId4 = await generateSessionId(userId, 5, 3);
    expect(sessionId3).not.toEqual(sessionId4);
  });

  it("should generate valid UUID v7 format", async () => {
    const userId = "pcookieless_123";
    const sessionId = await generateSessionId(userId, 1234567890, 1800000);

    // UUID v7 format: 8-4-4-4-12 hexadecimal characters
    expect(sessionId).toMatch(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/
    );
  });
});
