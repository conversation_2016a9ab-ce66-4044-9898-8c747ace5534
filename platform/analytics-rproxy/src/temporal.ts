export interface PlainDate {
  year: number;
  month: number;
  day: number;
}

export function plainDate(year: number, month: number, day: number): PlainDate {
  return { year, month, day };
}

/** Format: "YYYY-MM-DD" */
export type PlainDateString = `${string}-${string}-${string}`;

export function plainDateToString(date: PlainDate): PlainDateString {
  return `${date.year}-${String(date.month).padStart(2, "0")}-${String(
    date.day
  ).padStart(2, "0")}`;
}

export function timestampToPlainDate(
  timestampMs: number,
  timezone: string
): PlainDate {
  const parts = new Intl.DateTimeFormat("en", {
    timeZone: timezone,
    year: "numeric",
    month: "numeric",
    day: "numeric",
  }).formatToParts(new Date(timestampMs));
  const year = getPart(parts, "year");
  const month = getPart(parts, "month");
  const day = getPart(parts, "day");
  return {
    year,
    month,
    day,
  };
}

export function startOfDayMs(date: PlainDate, timezone: string): number {
  const startOfDayUtcMs = Date.UTC(date.year, date.month - 1, date.day);

  const parts = new Intl.DateTimeFormat("en-US", {
    timeZone: timezone,
    day: "numeric",
    hourCycle: "h23",
    hour: "numeric",
    minute: "numeric",
    second: "numeric",
  }).formatToParts(new Date(startOfDayUtcMs));

  const day = getPart(parts, "day");
  const hour = getPart(parts, "hour");
  const minute = getPart(parts, "minute");
  const second = getPart(parts, "second");

  let offsetMs = (hour * 3600 + minute * 60 + second) * 1000;
  // adjust for negative offsets like America/Los_Angeles
  if (day !== date.day) {
    offsetMs = offsetMs - 86400000;
  }

  return startOfDayUtcMs - offsetMs;
}

function getPart(parts: Intl.DateTimeFormatPart[], type: string): number {
  return +parts.find((p) => p.type === type)!.value;
}
