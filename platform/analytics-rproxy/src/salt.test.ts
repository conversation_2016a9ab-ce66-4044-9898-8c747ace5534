import {
  afterEach,
  beforeEach,
  describe,
  expect,
  it,
  MockInstance,
  vi,
} from "vitest";
import { KV } from "./kv";
import { SaltManager } from "./salt";
import { timestampToPlainDate } from "./temporal";
import { FakeKV } from "./test/fake-kv";

describe("SaltManager", () => {
  let randomSaltCounter = 0;
  const randomSalt = vi.fn(() => {
    return `fake-salt-${++randomSaltCounter}`;
  });

  let kv: KV;
  let kvGetSpy: MockInstance<KV["get"]>;
  let kvPutSpy: MockInstance<KV["put"]>;
  let saltManager: SaltManager;

  beforeEach(() => {
    kv = new FakeKV();
    kvGetSpy = vi.spyOn(kv, "get");
    kvPutSpy = vi.spyOn(kv, "put");

    saltManager = new SaltManager(kv, randomSalt);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  /** Helper to call `getSalt` at the correct system time.  */
  function setTimeAndGetSalt(timestampMs: number, timezone: string) {
    vi.setSystemTime(timestampMs);
    return saltManager.getSalt(timestampMs, timezone);
  }

  /** Helper to call `prepareSalt` at the correct system time.  */
  function setTimeAndPrepareSalt(timestampMs: number) {
    vi.setSystemTime(timestampMs);
    return saltManager.prepareSalt(timestampMs);
  }

  it("handles different timestamps, same timezone", async () => {
    const timezone = "America/New_York";

    const day0Salt = await setTimeAndGetSalt(
      t("2024-12-30T23:59:59-05:00"),
      timezone
    );
    expect(randomSalt).toHaveBeenCalledTimes(1);
    expect(kvGetSpy).toHaveBeenCalledWith("2024-12-30");
    expect(kvGetSpy).toHaveBeenCalledTimes(1);
    expect(kvPutSpy).toHaveBeenCalledWith(
      "2024-12-30",
      expect.anything(),
      expect.anything()
    );
    expect(kvPutSpy).toHaveBeenCalledTimes(1);

    const day1Salt = expectEqual(
      await setTimeAndGetSalt(t("2024-12-31T00:00:00-05:00"), timezone),
      await setTimeAndGetSalt(t("2024-12-31T00:00:01-05:00"), timezone),
      await setTimeAndGetSalt(t("2024-12-31T12:00:00-05:00"), timezone),
      await setTimeAndGetSalt(t("2024-12-31T23:59:59-05:00"), timezone)
    );
    expect(day1Salt).not.toEqual(day0Salt);
    expect(randomSalt).toHaveBeenCalledTimes(2);
    expect(kvGetSpy).toHaveBeenCalledWith("2024-12-31");
    expect(kvGetSpy).toHaveBeenCalledTimes(2);
    expect(kvPutSpy).toHaveBeenCalledWith(
      "2024-12-31",
      expect.anything(),
      expect.anything()
    );
    expect(kvPutSpy).toHaveBeenCalledTimes(2);

    const day2Salt = await setTimeAndGetSalt(
      t("2025-01-01T00:00:00-05:00"),
      timezone
    );
    expect(day2Salt).not.toEqual(day0Salt);
    expect(day2Salt).not.toEqual(day1Salt);
    expect(randomSalt).toHaveBeenCalledTimes(3);
    expect(kvGetSpy).toHaveBeenCalledWith("2025-01-01");
    expect(kvGetSpy).toHaveBeenCalledTimes(3);
    expect(kvPutSpy).toHaveBeenCalledWith(
      "2025-01-01",
      expect.anything(),
      expect.anything()
    );
    expect(kvPutSpy).toHaveBeenCalledTimes(3);
  });

  it("handles same timestamp, different timezones", async () => {
    const timestampMs = t("2025-01-01T00:00:00Z");

    // Set system time once instead of using `setTimeAndGetSalt`
    vi.setSystemTime(timestampMs);

    const day0Salt = expectEqual(
      await saltManager.getSalt(timestampMs, "Pacific/Pago_Pago"), // -11
      await saltManager.getSalt(timestampMs, "Pacific/Honolulu"), // -10
      await saltManager.getSalt(timestampMs, "America/Los_Angeles"), // -8
      await saltManager.getSalt(timestampMs, "America/New_York"), // -5
      await saltManager.getSalt(timestampMs, "America/Sao_Paulo"), // -3
      await saltManager.getSalt(timestampMs, "Atlantic/Azores") // -1
    );
    expect(randomSalt).toHaveBeenCalledTimes(1);
    expect(kvGetSpy).toHaveBeenCalledWith("2024-12-31");
    expect(kvGetSpy).toHaveBeenCalledTimes(1);
    expect(kvPutSpy).toHaveBeenCalledWith(
      "2024-12-31",
      expect.anything(),
      expect.anything()
    );
    expect(kvPutSpy).toHaveBeenCalledTimes(1);

    const day1Salt = expectEqual(
      await saltManager.getSalt(timestampMs, "Etc/UTC"), // +0
      await saltManager.getSalt(timestampMs, "Africa/Abidjan"), // +0
      await saltManager.getSalt(timestampMs, "Europe/Warsaw"), // +1
      await saltManager.getSalt(timestampMs, "Asia/Karachi"), // +5
      await saltManager.getSalt(timestampMs, "Asia/Taipei"), // +8
      await saltManager.getSalt(timestampMs, "Asia/Tokyo"), // +9
      await saltManager.getSalt(timestampMs, "Pacific/Auckland"), // +12
      await saltManager.getSalt(timestampMs, "Pacific/Kanton"), // +13
      await saltManager.getSalt(timestampMs, "Pacific/Kiritimati") // +14
    );
    expect(day1Salt).not.toEqual(day0Salt);
    expect(randomSalt).toHaveBeenCalledTimes(2);
    expect(kvGetSpy).toHaveBeenCalledWith("2025-01-01");
    expect(kvGetSpy).toHaveBeenCalledTimes(2);
    expect(kvPutSpy).toHaveBeenCalledWith(
      "2025-01-01",
      expect.anything(),
      expect.anything()
    );
    expect(kvPutSpy).toHaveBeenCalledTimes(2);
  });

  it("prepares tomorrow's salt 30 minutes before the earliest timezone", async () => {
    expect(await setTimeAndPrepareSalt(t("2025-01-01T09:29:59Z"))).toEqual([
      "2025-01-01",
    ]); // 1ms early
    expect(await setTimeAndPrepareSalt(t("2025-01-01T09:30:00Z"))).toEqual([
      "2025-01-02",
    ]);
  });

  it("can prepare both today's and tomorrow's salt", async () => {
    expect(await setTimeAndPrepareSalt(t("2025-01-01T09:30:00Z"))).toEqual([
      "2025-01-01",
      "2025-01-02",
    ]);
  });

  it("is using the correct earliest timezone", async () => {
    // +14:00 is what we are using as the earliest timezone
    const timestampMs = t("2025-01-01T10:00:00Z");
    expect(timestampMs).toEqual(t("2025-01-02T00:00:00+14:00"));
    expect(await setTimeAndPrepareSalt(timestampMs)).toEqual(["2025-01-02"]);

    const yesterday = {
      year: 2024,
      month: 12,
      day: 31,
    };
    const today = {
      year: 2025,
      month: 1,
      day: 1,
    };
    const tomorrow = {
      year: 2025,
      month: 1,
      day: 2,
    };
    expect(timestampToPlainDate(timestampMs, "Pacific/Pago_Pago")).toEqual(
      yesterday
    ); // -11
    expect(timestampToPlainDate(timestampMs, "Pacific/Honolulu")).toEqual(
      today
    ); // -10
    expect(timestampToPlainDate(timestampMs, "Etc/UTC")).toEqual(today); // +0
    expect(timestampToPlainDate(timestampMs, "Pacific/Kanton")).toEqual(today); // +13
    expect(timestampToPlainDate(timestampMs, "Pacific/Kiritimati")).toEqual(
      tomorrow
    ); // +14
    expect(timestampToPlainDate(timestampMs, "Etc/GMT-14")).toEqual(tomorrow);
  });
});

function expectEqual(...salts: string[]): string {
  expect(salts.length).toBeGreaterThan(1);
  const firstSalt = salts[0];
  expect(salts.filter((salt) => salt !== firstSalt)).toEqual([]);
  return firstSalt;
}

function t(s: string): number {
  return new Date(s).getTime();
}
