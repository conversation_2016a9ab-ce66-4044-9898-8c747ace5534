import { decompressSync, gzipSync, strFromU8, strToU8 } from "fflate";
import { HonoRequest } from "hono";
import { HTTPException } from "hono/http-exception";

export async function handleBody<T>(
  req: HonoRequest,
  handler: (body: T) => Promise<T>
): Promise<RequestInit["body"]> {
  // Check if compression=gzip-js is in the query parameters
  if (req.query("compression") === "gzip-js") {
    const body = await decompressBody<T>(req);
    const newBody = await handler(body);
    return compress(newBody);
  }

  const contentType = req.header("content-type");
  if (contentType === "application/json") {
    let body: T;
    try {
      body = await req.json();
    } catch (err: unknown) {
      if (
        typeof err === "object" &&
        err &&
        "name" in err &&
        err.name === "SyntaxError"
      ) {
        throw new HTTPException(400);
      } else {
        throw err;
      }
    }
    const newBody = await handler(body);
    return JSON.stringify(newBody);
  }

  console.info(
    `Received request with content-type: "${contentType}" and body "${await req.text()}"`
  );
  throw new HTTPException(400);
}

export function compress(data: unknown): Uint8Array {
  return gzipSync(strToU8(JSON.stringify(data)));
}

export function decompress<T>(data: Uint8Array): T {
  return JSON.parse(strFromU8(decompressSync(data))) as T;
}

export async function decompressBody<T>(request: {
  arrayBuffer: () => Promise<ArrayBuffer>;
}): Promise<T> {
  const buffer = await request.arrayBuffer();
  return decompress<T>(new Uint8Array(buffer));
}
