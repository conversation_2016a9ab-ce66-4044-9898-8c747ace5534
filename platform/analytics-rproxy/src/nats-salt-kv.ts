import { connect } from "nats";
import { PROCESS_ENV } from "./env";
import { NatsKV } from "./nats-kv";

// 50 hours = 12 hours earliest timezone + 24 hours + 14 hours latest timezone
const SALT_TTL = 50 * 60 * 60 * 1000;

export async function createNatsSaltKV() {
  const conn = await connect({
    servers: PROCESS_ENV.NATS_SERVERS,
  });
  const saltKV = await conn.jetstream().views.kv("salt", {
    ttl: SALT_TTL,
  });
  const status = await saltKV.status();
  if (status.ttl !== SALT_TTL) {
    throw new Error(`KV TTL is ${status.ttl}, expected ${SALT_TTL}`);
  }
  return new NatsKV(conn, saltKV);
}
