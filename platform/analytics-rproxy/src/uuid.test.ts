import { describe, expect, it } from "vitest";
import { uuidToString, uuidV7 } from "./uuid";

describe("uuidV7", () => {
  // https://www.rfc-editor.org/rfc/rfc9562.html#name-example-of-a-uuidv7-value
  it("generates example UUIDv7", () => {
    // Timestamp: Tuesday, February 22, 2022 2:22:22.00 PM GMT-05:00
    // unix_ts_ms (48 bits): 0x017F22E279B0
    const timestampMs = 1645557742000;

    // rand_a (12 bits): 0xCC3
    const randA = new Uint8Array([0x0c, 0xc3]);

    // rand_b (62 bits): 0b01, 0x8C4DC0C0C07398F
    const randB = new Uint8Array([
      0x18, 0xc4, 0xdc, 0x0c, 0x0c, 0x07, 0x39, 0x8f,
    ]);

    const uuidBytes = uuidV7(timestampMs, randA, randB);
    const uuid = uuidToString(uuidBytes);

    expect(uuid).toBe("017f22e2-79b0-7cc3-98c4-dc0c0c07398f");
  });
});
