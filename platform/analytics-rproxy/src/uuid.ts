/**
 * Creates a UUID version 7 from a timestamp and random part(s).
 *
 * Some bits of random parts are ignored, see implementation for details.
 *
 * @param timestampMs - unix timestamp in milliseconds
 * @param randA - (80 bits) or (16 bits)
 * @param randB - (undefined0) or (64 bits)
 * @returns UUID v7 as Uint8Array
 */
export function uuidV7(
  timestampMs: number,
  randA: Uint8Array,
  randB?: Uint8Array
): Uint8Array {
  if (randB === undefined) {
    randB = randA.slice(2);
  }

  const bytes = new Uint8Array(16);

  // unix_ts_ms (48 bits)
  bytes[0] = timestampMs / 2 ** 40;
  bytes[1] = timestampMs / 2 ** 32;
  bytes[2] = timestampMs / 2 ** 24;
  bytes[3] = timestampMs / 2 ** 16;
  bytes[4] = timestampMs / 2 ** 8;
  bytes[5] = timestampMs;

  // version 7 -> 0x70 (4 bits) + randA (4 bits)
  bytes[6] = 0x70 | (randA[0] & 0x0f);
  bytes[7] = randA[1];

  // variant 0b10 -> 0x80 (2 bits) + randB (6 bits)
  bytes[8] = 0x80 | (randB[0] & 0x3f);
  bytes[9] = randB[1];
  bytes[10] = randB[2];
  bytes[11] = randB[3];
  bytes[12] = randB[4];
  bytes[13] = randB[5];
  bytes[14] = randB[6];
  bytes[15] = randB[7];

  return bytes;
}

export function uuidToString(uuid: Uint8Array): string {
  const hex = Array.from(uuid)
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
  return (
    hex.slice(0, 8) +
    "-" +
    hex.slice(8, 12) +
    "-" +
    hex.slice(12, 16) +
    "-" +
    hex.slice(16, 20) +
    "-" +
    hex.slice(20)
  );
}
