import { describe, expect, it } from "vitest";
import {
  plainDate,
  plainDateToString,
  startOfDayMs,
  timestampToPlainDate,
} from "./temporal";

describe("plainDateToYYYYMMDD", () => {
  it("should work", () => {
    expect(plainDateToString(plainDate(2024, 12, 31))).toBe("2024-12-31");
    expect(plainDateToString(plainDate(2025, 1, 1))).toBe("2025-01-01");
  });
});

describe("timestampToPlainDate", () => {
  it("should work across different timezones (winter)", () => {
    const beforeMidnightUtc = t("2024-12-31T23:59:59Z");
    expect(
      timestampToPlainDate(beforeMidnightUtc, "America/Los_Angeles")
    ).toEqual(plainDate(2024, 12, 31));
    expect(timestampToPlainDate(beforeMidnightUtc, "Etc/UTC")).toEqual(
      plainDate(2024, 12, 31)
    );
    expect(timestampToPlainDate(beforeMidnightUtc, "Asia/Tokyo")).toEqual(
      plainDate(2025, 1, 1)
    );

    const midnightUtc = t("2025-01-01T00:00:00Z");
    expect(timestampToPlainDate(midnightUtc, "America/Los_Angeles")).toEqual(
      plainDate(2024, 12, 31)
    );
    expect(timestampToPlainDate(midnightUtc, "Etc/UTC")).toEqual(
      plainDate(2025, 1, 1)
    );
    expect(timestampToPlainDate(midnightUtc, "Asia/Tokyo")).toEqual(
      plainDate(2025, 1, 1)
    );
  });
  it("should work across different timezones (summer)", () => {
    const beforeMidnightUtc = t("2025-06-30T23:59:59Z");
    expect(
      timestampToPlainDate(beforeMidnightUtc, "America/Los_Angeles")
    ).toEqual(plainDate(2025, 6, 30));
    expect(timestampToPlainDate(beforeMidnightUtc, "Etc/UTC")).toEqual(
      plainDate(2025, 6, 30)
    );
    expect(timestampToPlainDate(beforeMidnightUtc, "Asia/Tokyo")).toEqual(
      plainDate(2025, 7, 1)
    );

    const midnightUtc = t("2025-07-01T00:00:00Z");
    expect(timestampToPlainDate(midnightUtc, "America/Los_Angeles")).toEqual(
      plainDate(2025, 6, 30)
    );
    expect(timestampToPlainDate(midnightUtc, "Etc/UTC")).toEqual(
      plainDate(2025, 7, 1)
    );
    expect(timestampToPlainDate(midnightUtc, "Asia/Tokyo")).toEqual(
      plainDate(2025, 7, 1)
    );
  });
});

describe("startOfDayMs", () => {
  it("should return start of day timestamps for different timezones (winter)", () => {
    expect(startOfDayMs(plainDate(2025, 1, 1), "America/Los_Angeles")).toBe(
      t("2025-01-01T00:00:00-08:00")
    );
    expect(startOfDayMs(plainDate(2025, 1, 1), "Etc/UTC")).toBe(
      t("2025-01-01T00:00:00Z")
    );
    expect(startOfDayMs(plainDate(2025, 1, 1), "Asia/Tokyo")).toBe(
      t("2025-01-01T00:00:00+09:00")
    );
  });
  it("should return start of day timestamps for different timezones (summer)", () => {
    expect(startOfDayMs(plainDate(2025, 7, 1), "America/Los_Angeles")).toBe(
      t("2025-07-01T00:00:00-07:00")
    ); // DST
    expect(startOfDayMs(plainDate(2025, 7, 1), "Etc/UTC")).toBe(
      t("2025-07-01T00:00:00Z")
    );
    expect(startOfDayMs(plainDate(2025, 7, 1), "Asia/Tokyo")).toBe(
      t("2025-07-01T00:00:00+09:00")
    );
  });
});

function t(s: string): number {
  return new Date(s).getTime();
}
