import { Hono } from "hono";
import { Env, PROCESS_ENV, ProcessEnv } from "./env";
import { KV } from "./kv";
import { posthogHandler } from "./posthog";
import { SaltManager } from "./salt";

export async function createApp(deps: {
  processEnv?: Partial<ProcessEnv>;
  saltKV: KV;
}) {
  const app = new Hono<Env>();
  const saltManager = new SaltManager(deps?.saltKV);

  app.use("/*", async (ctx, next) => {
    ctx.set(
      "processEnv",
      deps?.processEnv ? { ...PROCESS_ENV, ...deps.processEnv } : PROCESS_ENV
    );
    ctx.set("saltManager", saltManager);
    await next();
  });

  app.use("/*", posthogHandler);

  return app;
}
