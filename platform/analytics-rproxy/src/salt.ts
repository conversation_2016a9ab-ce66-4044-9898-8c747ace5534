import crypto from "node:crypto";
import { KV } from "./kv";
import {
  PlainDateString,
  plainDateToString,
  timestampToPlainDate,
} from "./temporal";

const PREPARE_EARLIEST_TIMEZONE = "Etc/GMT-14";
const MAX_NEGATIVE_TIMEZONE_HOURS = 12;
const MAX_POSITIVE_TIMEZONE_HOURS = 14;
const BUFFER_HOURS = 0.5;
const MS_PER_HOUR = 60 * 60 * 1000;

export class SaltManager {
  private readonly localSaltCache: Map<string, string> = new Map();

  constructor(
    private readonly remoteSaltKV: KV,
    private readonly randomSalt = randomSaltImpl
  ) {}

  /**
   * Prepares today's salt and maybe tomorrow's salt if it's within
   * PREPARE_SALT_BUFFER_MS in the earliest timezone.
   */
  async prepareSalt(timestampMs: number): Promise<PlainDateString[]> {
    const prepared: PlainDateString[] = [];

    const today = timestampToPlainDate(timestampMs, PREPARE_EARLIEST_TIMEZONE);
    const todayKey = plainDateToString(today);
    if (!(await this.tryGetSalt(todayKey))) {
      await this.setSalt(todayKey);
      prepared.push(todayKey);
    }

    const maybeTomorrow = timestampToPlainDate(
      timestampMs + BUFFER_HOURS * MS_PER_HOUR,
      PREPARE_EARLIEST_TIMEZONE
    );
    if (maybeTomorrow.day !== today.day) {
      const tomorrowKey = plainDateToString(maybeTomorrow);
      if (!(await this.tryGetSalt(tomorrowKey))) {
        await this.setSalt(tomorrowKey);
        prepared.push(tomorrowKey);
      }
    }

    return prepared;
  }

  async getSalt(timestampMs: number, timezone: string): Promise<string> {
    const date = timestampToPlainDate(timestampMs, timezone);
    const key = plainDateToString(date);

    const salt = await this.tryGetSalt(key);
    if (salt) {
      return salt;
    } else {
      console.warn("missing salt for " + key);
      return this.setSalt(key);
    }
  }

  private async tryGetSalt(key: PlainDateString): Promise<string | null> {
    // Check local cache first
    const cachedSalt = this.localSaltCache.get(key);
    if (cachedSalt) {
      return cachedSalt;
    }

    // Check remote KV store
    return this.remoteSaltKV.get(key);
  }

  private async setSalt(key: PlainDateString): Promise<string> {
    console.info(`setting salt for ${key}`);

    const salt = this.randomSalt();

    const startOfDayUtcMs = new Date(`${key}T00:00:00Z`).getTime();
    const minMs =
      startOfDayUtcMs -
      (MAX_POSITIVE_TIMEZONE_HOURS + BUFFER_HOURS) * MS_PER_HOUR;
    const maxMs =
      startOfDayUtcMs +
      (MAX_NEGATIVE_TIMEZONE_HOURS + 24 + BUFFER_HOURS) * MS_PER_HOUR;

    const now = Date.now();
    if (now < minMs || now > maxMs) {
      console.error(
        `ERROR not setting salt for ${key}, because current time ${now} is not in valid range from ${minMs} to ${maxMs}`
      );
      return salt; // return salt anyway
    }

    try {
      await this.remoteSaltKV.put(key, salt, { expiresAtMs: maxMs });
      console.info(`SUCCESS setting salt for ${key}`);
      this.localSaltCache.set(key, salt);
      return salt;
    } catch (e) {
      console.error(`ERROR setting salt for ${key}`, e);
      return salt; // return salt anyway
    }
  }
}

function randomSaltImpl(): string {
  const saltBytes = new Uint8Array(32);
  crypto.getRandomValues(saltBytes);
  return btoa(String.fromCharCode(...saltBytes));
}
