{"method": "POST", "url": "http://localhost/e/?ip=1&_=1743989055088&ver=1.194.1", "headers": {"accept": "*/*", "accept-language": "en-US,en;q=0.9", "content-type": "application/json", "priority": "u=1, i", "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "sec-fetch-storage-access": "active", "referer": "https://www.plasmic.app/", "referrer-policy": "strict-origin-when-cross-origin", "x-forwarded-for": "*******, *******"}, "body": {"uuid": "01963363-2eaf-7ce9-a358-918c8ecb3048", "event": "$identify", "properties": {"$os": "Mac OS X", "$os_version": "10.15.7", "$browser": "Chrome", "$device_type": "Desktop", "$timezone": "America/Los_Angeles", "$current_url": "https://studio.plasmic.app/login", "$host": "studio.plasmic.app", "$pathname": "/login", "$raw_user_agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "$browser_version": 135, "$browser_language": "en-US", "$browser_language_prefix": "en", "$screen_height": 1440, "$screen_width": 2560, "$viewport_height": 670, "$viewport_width": 1579, "$lib": "web", "$lib_version": "1.234.9", "$insert_id": "c16bmeb9e7hyitsv", "$time": 1744618860.207, "distinct_id": "*************-4098-a0a9-a13c1bf12fd6", "$initial_person_info": {"r": "$direct", "u": "https://www.plasmic.app/"}, "$device_id": "0191d5ab-a338-70d4-bf4a-80c35e3b482d", "$last_posthog_reset": "2025-04-14T08:20:42.221Z", "$session_recording_network_payload_capture": {"capturePerformance": [null]}, "$session_recording_canvas_recording": {}, "$replay_sample_rate": null, "$replay_minimum_duration": null, "$configured_session_timeout_ms": 1800000, "$autocapture_disabled_server_side": false, "$web_vitals_enabled_server_side": false, "$web_vitals_allowed_metrics": null, "$exception_capture_enabled_server_side": false, "$dead_clicks_enabled_server_side": false, "$active_feature_flags": [], "$feature_flag_payloads": {}, "$feature_flag_request_id": "c2049068-7078-42e7-be8e-b33249c7948d", "$user_id": "*************-4098-a0a9-a13c1bf12fd6", "$referrer": "https://studio.plasmic.app/projects", "$referring_domain": "studio.plasmic.app", "$anon_distinct_id": "01963362-e86d-7590-898a-4b83ebdea185", "token": "phc_posthog_key", "$session_id": "01963362-e87d-7747-9da5-24f03d088500", "$window_id": "01963362-e87d-7747-9da5-24f1716060c9", "$session_entry_referrer": "https://studio.plasmic.app/projects", "$session_entry_referring_domain": "studio.plasmic.app", "$session_entry_url": "https://studio.plasmic.app/projects", "$session_entry_host": "studio.plasmic.app", "$session_entry_pathname": "/projects", "$recording_status": "disabled", "$sdk_debug_replay_internal_buffer_length": 0, "$sdk_debug_replay_internal_buffer_size": 0, "$sdk_debug_retry_queue_size": 0, "$lib_custom_api_host": "http://localhost:8787", "$pageview_id": "01963363-06c1-7191-822d-e28d61e23970", "$is_identified": true, "$process_person_profile": true, "$lib_rate_limit_remaining_tokens": 99}, "$set": {}, "$set_once": {"$initial_referrer": "https://studio.plasmic.app/projects", "$initial_referring_domain": "studio.plasmic.app", "$initial_current_url": "https://studio.plasmic.app/projects", "$initial_host": "studio.plasmic.app", "$initial_pathname": "/projects", "$initial_utm_source": null, "$initial_utm_medium": null, "$initial_utm_campaign": null, "$initial_utm_content": null, "$initial_utm_term": null, "$initial_gad_source": null, "$initial_mc_cid": null, "$initial_gclid": null, "$initial_gclsrc": null, "$initial_dclid": null, "$initial_gbraid": null, "$initial_wbraid": null, "$initial_fbclid": null, "$initial_msclkid": null, "$initial_twclid": null, "$initial_li_fat_id": null, "$initial_igshid": null, "$initial_ttclid": null, "$initial_rdt_cid": null, "$initial_irclid": null, "$initial__kx": null, "$referrer": "https://studio.plasmic.app/projects", "$referring_domain": "studio.plasmic.app", "$current_url": "https://studio.plasmic.app/projects", "$host": "studio.plasmic.app", "$pathname": "/projects", "utm_source": null, "utm_medium": null, "utm_campaign": null, "utm_content": null, "utm_term": null, "gad_source": null, "mc_cid": null, "gclid": null, "gclsrc": null, "dclid": null, "gbraid": null, "wbraid": null, "fbclid": null, "msclkid": null, "twclid": null, "li_fat_id": null, "igshid": null, "ttclid": null, "rdt_cid": null, "irclid": null, "_kx": null}, "timestamp": "2025-04-14T08:21:00.207Z"}}