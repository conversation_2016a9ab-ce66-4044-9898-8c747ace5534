FROM node:22-alpine AS base

FROM base AS build

RUN apk add --no-cache gcompat~=1

WORKDIR /platform/

COPY pnpm* ./
COPY analytics-rproxy/package.json analytics-rproxy/

RUN npm install -g pnpm@10.8.0 && \
    pnpm install

COPY analytics-rproxy/ analytics-rproxy/

WORKDIR /platform/analytics-rproxy/

RUN pnpm build && \
    pnpm --filter analytics-rproxy --prod deploy pruned/

FROM base AS production

RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 hono

COPY --from=build --chown=hono:nodejs /platform/analytics-rproxy/pruned/ /app/

EXPOSE 8787
CMD ["node", "/app/dist/entry-app.js"]
