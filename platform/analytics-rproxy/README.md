# analytics-rproxy

Reverse proxy to our analytics platform, PostHog.

Purpose:

- Get around ad blockers.
- Generate hashes for users based on IP, user agent, and salt.
  The salt is rotated daily, so IP addresses cannot be reversed after the day.

## Local development

analytics-rproxy relies on NATS as KV storage. So you'll need to run that locally first.

### Run NATS

With Docker:

```shell
pnpm docker:dev:nats
```

Or, install it on your system:

```shell
# Install server
brew install nats-server

# Run server with JetStream (required for persistent use cases like KV)
nats-server -js
```

```shell
# Install CLI
brew tap nats-io/nats-tools
brew install nats-io/nats-tools/nats
```

### Scripts

```shell
# Run analytics-rproxy dev server
# The server will only allow requests with POSTHOG_KEY
POSTHOG_KEY=phc_123 pnpm dev

# Run job that generates tomorrow's salt.
pnpm dev:prepare-salt

# Test docker
pnpm docker:build
pnpm docker:dev
```
