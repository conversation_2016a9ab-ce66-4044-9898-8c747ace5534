import md5 from "md5";
import defer from "../utils/defer";
import { addMessageListener, postMessage } from "../utils/messages";

const nodesToRemove: Set<BaseNode> = new Set();
figma.on("close", () => nodesToRemove.forEach((node) => node.remove()));

let hasCancelled = false;

addMessageListener("cancel", () => (hasCancelled = true));

// A list of properties we don't need for our purposes
const blacklist = [
  "absoluteTransform",
  "arcData",
  "autoRename",
  "backgroundStyleId",
  "backgrounds",
  "booleanOperation",
  "constrainProportions",
  "cornerSmoothing",
  "dashPattern",
  "description",
  "effectStyleId",
  "expanded",
  "exportSettings",
  "fillStyleId",
  "gridStyleId",
  "guides",
  "handleMirroring",
  "hasMissingFont",
  "innerRadius",
  "key",
  "layoutGrids",
  "locked",
  "masterComponent",
  "numberOfFixedChildren",
  "overflowDirection",
  "overlayBackground",
  "overlayBackgroundInteraction",
  "overlayPositionType",
  "paragraphIndent",
  "paragraphSpacing",
  "parent",
  "pointCount",
  "prototypeStartNode",
  "reactions",
  "remote",
  "removed",
  "scaleFactor",
  "selection",
  "selectedTextRange",
  "strokeCap",
  "strokeJoin",
  "strokeMiterLimit",
  "strokeStyleId",
  "textStyleId",
  "vectorNetwork",
  "vectorPaths",
  "horizontalPadding", // Blacklist to remove warnings
];

// A list of nodes we want to convert into an SVG
const vectorNodeTypes: Array<NodeType> = [
  "BOOLEAN_OPERATION",
  "VECTOR",
  "ELLIPSE",
  "STAR",
  "LINE",
  "POLYGON",
];

const MAIN_COMPONENT_KEY = "mainComponent";

/**
 * Attempts to determine if the object is a composed element based on the following heuristic:
 * its children are visible and vector type.
 */
function isComposedSvg(object: any) {
  if (!object.children?.length) return false;
  const visibleChildren = object.children.filter((node: any) => node.visible);

  if (!visibleChildren.length) return false;

  return visibleChildren.every((node: any) =>
    vectorNodeTypes.includes(node.type)
  );
}
// Serialize an array of Figma nodes into a compressed JSON format
const serialize = async (nodes: Array<any>) => {
  // Track progress during serialization for reporting to the UI
  let totalNodeCount = nodes.length;
  try {
    nodes.forEach(
      (node) =>
        typeof node.findAll === "function" &&
        (totalNodeCount += node.findAll().length)
    );
  } catch (e) {
    // Sometimes a Figma layer is corrupted, we can't even grab a reference
    // to tell the user where it is. Any API call that touches it throws
    figma.notify("⚠️ Unable to export, some layers may be corrupted.");
    return null;
  }
  let serializedNodeCount = 0;
  postMessage("setTotalNodeCount", totalNodeCount, false);
  const updateProgress = () =>
    postMessage(
      "setProgress",
      Math.min(serializedNodeCount / totalNodeCount, 1),
      false
    );

  // Store things that are normalized during serialization
  const keys = new Set<string>();
  const imageHashes = new Set<string>();
  const strings = new Set<string>();
  const svgs: { [hash: string]: string } = {};

  // Recursive function to serialize the entire tree
  const serialize = defer(async (object: any): Promise<any> => {
    if (hasCancelled) {
      return null;
    }

    if (typeof object === "boolean" && object === true) {
      // `1` is smaller than `true`
      return 1;
    } else if (typeof object === "string") {
      // Strings are normalized since there are lots of repeated strings (e.g. enums)
      strings.add(object);
      return [...strings].indexOf(object).toString(36);
    } else if (typeof object === "number") {
      return object;
    } else if (Array.isArray(object)) {
      const serialized: Array<any> = [];

      // Use `Promise.all` to parallelize
      await Promise.all(
        object.map(async (object, i) => {
          const item = await serialize(object);
          if (item !== null) {
            serialized[i] = item;
          }
        })
      );

      if (serialized.length) {
        return serialized;
      }
    } else if (typeof object === "object" && object !== null) {
      const serialized: { [key: string]: any } = {};

      let ownKeys = Object.keys(object);
      // If this is a custom class, look at the prototype and get those keys as well
      if (Object.getPrototypeOf(object) !== Object.getPrototypeOf({})) {
        serializedNodeCount++;
        updateProgress();
        ownKeys.push(
          ...Object.keys(
            Object.getOwnPropertyDescriptors(Object.getPrototypeOf(object))
          )
        );
      }

      // Filter out any keys we don't need, and any functions
      ownKeys = ownKeys.filter((key) => {
        try {
          return !blacklist.includes(key) && typeof object[key] !== "function";
        } catch {
          // Some getters might throw, skip those as well
          // See https://app.shortcut.com/plasmic/story/18688
          return false;
        }
      });

      // If anything can only be represented as a vector, not with the DOM, generate SVG
      let shouldImportAsSvg = vectorNodeTypes.includes(object.type);

      if (isComposedSvg(object)) {
        shouldImportAsSvg = true;
      }

      if (shouldImportAsSvg) {
        // Don't serialize children for vector nodes, just generate SVG data
        if (ownKeys.includes("children")) {
          ownKeys = ownKeys.filter((key) => key !== "children");
          // Update the progress to reflect that we serialized the children though
          serializedNodeCount += object.findAll().length;
          updateProgress();
        }
        // Clone the node so we can remove any transforms, effects on it
        // before exporting SVG data
        const clone = object.clone();
        nodesToRemove.add(clone);
        // Reset to identity transform since the rotation, scale and
        // translation is already applied to the DOM node this is used on
        clone.relativeTransform = [
          [1, 0, 0],
          [0, 1, 0],
        ];
        // Remove any drop shadow effects as these mess up the bounds of the
        // SVG, we can apply a filter using CSS to fix this later
        clone.effects = clone.effects.filter(
          (effect: Effect) => effect.type !== "DROP_SHADOW"
        );

        // Remove border radius from the export. Not all figma object
        // have border radius, here we'll check if the property exists
        // first before changing the value.
        if ("cornerRadius" in clone && clone.cornerRadius !== figma.mixed) {
          clone.cornerRadius = 0;
        }

        if ("topLeftRadius" in clone) {
          clone.topLeftRadius = 0;
          clone.topRightRadius = 0;
          clone.bottomLeftRadius = 0;
          clone.bottomRightRadius = 0;
        }

        try {
          // Mask cannot be exported as SVG.
          if (clone.isMask) {
            clone.isMask = false;
          }
          // The clone must be visible before exporting. Note that the original visible
          // value will remain the same.
          clone.visible = true;
          let data = [...(await clone.exportAsync({ format: "SVG" }))]
            .map((n) => String.fromCharCode(n))
            .join("");
          // Use SVGO (which can only run on the UI side) to compress a bit
          data = await postMessage("optimizeSVG", data);
          // Normalize SVGs, since they may be repeated across a document (e.g. a glyph)
          const hash = md5(data);
          svgs[hash] = data;
          keys.add("svgHash");
          serialized[[...keys].indexOf("svgHash").toString(36)] =
            await serialize(hash);
        } catch (e) {
          // If the element is invisible (e.g. opacity is 0), then it cannot
          // be exported.
          console.log(`failed to export as SVG ${object.name}`, object, e);
        }
        // Cleanup the clone
        clone.remove();
        nodesToRemove.delete(clone);
      }

      if (ownKeys.includes("imageHash") && object.imageHash) {
        // Save the image hash, we'll export base 64 data for these at the end
        imageHashes.add(object.imageHash);
      }

      const getKeyValue = async (key: string) => {
        // We need to check if the key is an object, to keep the references to the main component present
        if (key === MAIN_COMPONENT_KEY && typeof object[key] === "object") {
          // We will be limiting the values that are going to be extracted of the component,
          // as the component is a high level element, some of the fields will direct to
          // page references, which we don't want to process
          const element =
            // It requires checking the parent because that ComponentNode can be an variant
            object[key].parent?.type === "COMPONENT_SET"
              ? object[key].parent
              : object[key];
          return await serialize({
            id: element.id,
            name: element.name,
          });
        }

        return serialize(object[key]);
      };

      // Use `Promise.all` to parallelize
      await Promise.all(
        ownKeys.map(async (key) => {
          const value = await getKeyValue(key);
          if (value !== null) {
            keys.add(key);
            serialized[[...keys].indexOf(key).toString(36)] = value;
          }
        })
      );

      if (Object.keys(serialized).length) {
        return serialized;
      }
    }
    return null;
  });

  // Serialize the nodes
  const serializedNodes = await Promise.all(nodes.map(serialize));

  if (hasCancelled) {
    hasCancelled = false;
    return null;
  }

  // Convert images into base 64 data
  const images: { [hash: string]: string } = {};
  const imageData: string[] = await Promise.all(
    [...imageHashes].map(async (hash) => {
      const bytes = await figma.getImageByHash(hash).getBytesAsync();
      // Convert bytes into base 64 on the UI side
      return postMessage("imageBytesToBase64", bytes);
    })
  );
  [...imageHashes].forEach((hash, i) => (images[hash] = imageData[i]));

  return {
    version: "0.0.1",
    i: images,
    k: [...keys],
    n: serializedNodes,
    s: [...strings],
    v: svgs,
  };
};

export default serialize;
