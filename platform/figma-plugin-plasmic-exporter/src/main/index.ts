import { addMessageListener, initMain, postMessage } from "../utils/messages";
import serialize from "./serialize";

figma.showUI(__html__, { visible: false });

figma.ui.resize(250, 250);

initMain();

addMessageListener("notify", (message: string) => figma.notify(message));

addMessageListener("showUI", () => figma.ui.show());

addMessageListener("serializeCurrentPage", () =>
  serialize([figma.currentPage]).then(JSON.stringify)
);

addMessageListener("serializeRoot", () =>
  serialize([figma.root]).then(JSON.stringify)
);

addMessageListener("serializeSelection", () =>
  serialize([...figma.currentPage.selection]).then(JSON.stringify)
);

addMessageListener(
  "getHasSelectedLayers",
  () => figma.currentPage.selection.length > 0
);

figma.on("selectionchange", () =>
  postMessage("setHasSelectedLayers", figma.currentPage.selection.length > 0)
);
