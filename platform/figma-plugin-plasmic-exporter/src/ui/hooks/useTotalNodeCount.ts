import { useEffect, useState } from "react";
import {
  addMessageListener,
  removeMessageListener,
} from "../../utils/messages";

const useTotalNodeCount = () => {
  const [totalNodeCount, setTotalNodeCount] = useState<number>(0);

  useEffect(() => {
    addMessageListener("setTotalNodeCount", setTotalNodeCount);
    return () => removeMessageListener("setTotalNodeCount", setTotalNodeCount);
  });

  return totalNodeCount;
};

export default useTotalNodeCount;
