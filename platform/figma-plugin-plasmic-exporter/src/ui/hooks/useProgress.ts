import { useCallback, useEffect, useState } from "react";
import {
  addMessageListener,
  removeMessageListener,
} from "../../utils/messages";

const useProgress = (): [number | void, () => void] => {
  const [progress, setProgress] = useState<number | void>();

  useEffect(() => {
    addMessageListener("setProgress", setProgress);
    return () => removeMessageListener("setProgress", setProgress);
  });

  const clearProgress = useCallback(() => setProgress(), []);

  return [progress, clearProgress];
};

export default useProgress;
