import { useEffect, useState } from "react";
import {
  addMessageListener,
  postMessage,
  removeMessageListener,
} from "../../utils/messages";

const useHasSelectedLayers = () => {
  const [hasSelectedLayers, setHasSelectedLayers] = useState(false);

  useEffect(() => {
    postMessage("getHasSelectedLayers").then(setHasSelectedLayers);
    addMessageListener("setHasSelectedLayers", setHasSelectedLayers);
    return () =>
      removeMessageListener("setHasSelectedLayers", setHasSelectedLayers);
  });

  return hasSelectedLayers;
};

export default useHasSelectedLayers;
