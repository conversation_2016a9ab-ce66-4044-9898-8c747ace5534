import React from "react";
import styled from "styled-components";

type Props = {
  children?: string;
  isDisabled?: boolean;
  isPrimary?: boolean;
  onClick?: (e: React.MouseEvent<HTMLDivElement>) => void;
};

const Button = styled.div<Props>`
  ${({ isDisabled, isPrimary }) =>
    isDisabled
      ? {
          backgroundColor: "var(--disabled-button-background)",
          color: "var(--disabled-button-text)",
          pointerEvents: "none",
        }
      : isPrimary
      ? {
          backgroundColor: "var(--primary-button-background)",
          color: "var(--primary-button-text)",
        }
      : {
          backgroundColor: "var(--secondary-button-background)",
          color: "var(--secondary-button-text)",
        }}
  border-radius: 4px;
  font-size: 11px;
  line-height: 16px;
  padding: 4px 8px;
  text-align: center;
  transition: all 0.1s ease-in-out;
  user-select: none;
  &:active {
    ${({ isPrimary }) => ({
      backgroundColor: isPrimary
        ? "var(--primary-button-background-active)"
        : "var(--secondary-button-background-active)",
    })}
  }
`;

export default Button;
