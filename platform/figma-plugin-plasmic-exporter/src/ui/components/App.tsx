import { windowEndpoint, wrap } from "comlink";
import numeral from "numeral";
import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import { Deferred } from "ts-deferred";
import { v4 as uuid } from "uuid";
import { postMessage } from "../../utils/messages";
import logo from "../assets/logo.png";
import useHasSelectedLayers from "../hooks/useHasSelectedLayers";
import useProgress from "../hooks/useProgress";
import useTotalNodeCount from "../hooks/useTotalNodeCount";
import Button from "./Button";
import MetaText from "./MetaText";
import ProgressBar from "./ProgressBar";

interface ChildApi {
  setClipboardData(clipId: string, data: string): Promise<void>;
  trackEvent(eventName: string, data: {}): Promise<void>;
}

const App: React.FC = () => {
  const hasSelectedLayers = useHasSelectedLayers();
  const [progress, clearProgress] = useProgress();
  const totalNodeCount = numeral(useTotalNodeCount()).format("0,0");
  const iframeRef = useRef<HTMLIFrameElement | null>();
  const [connectionDeferred] = useState(new Deferred<ChildApi>());
  const [showCompletedNotice, setShowCompletedNotice] =
    useState<boolean>(false);
  useEffect(() => {
    (async () => {
      const comlink = await wrap(
        windowEndpoint(iframeRef.current.contentWindow)
      );
      connectionDeferred.resolve(comlink as any);
    })();
  });

  // Once the UI has rendered, show the plugin window
  useEffect(() => {
    postMessage("showUI");
  }, []);

  return (
    <StyledApp>
      <MainContent>
        <Logo />
        <div>
          <strong>WARNING:</strong> This plugin tries to reproduce exactly how
          you built your Figma file.{" "}
          <strong>
            Most Figma designs are not built with production layout in mind!
          </strong>
        </div>
        <div>
          <a
            target="_blank"
            href="https://docs.plasmic.app/learn/importing-from-figma"
          >
            Learn how to make best use of this plugin.
          </a>
        </div>
      </MainContent>
      {showCompletedNotice && (
        <Overlay
          onClick={() => {
            setShowCompletedNotice(false);
          }}
        >
          <Notice>
            <strong>Exported to clipboard!</strong> Now paste into a Plasmic
            project.
            <IconButton
              onClick={() => {
                setShowCompletedNotice(false);
              }}
            >
              ×
            </IconButton>
          </Notice>
        </Overlay>
      )}
      <Buttons>
        <Button
          isDisabled={!hasSelectedLayers}
          isPrimary
          onClick={async () => {
            // We must first generate a UUID so we can immediately write it to the clipboard.
            try {
              const clipId = uuid();
              // Not sure how to set a custom clipboard data type here, where we are not directly triggered by a copy event.
              // So just use this __clipType, and detect this later in Plasmic.
              const copySucceeded = copyText(
                JSON.stringify({
                  __clipType: "application/vnd.plasmic.clipboard+json",
                  clipId,
                })
              );
              if (!copySucceeded) {
                throw new Error();
              }
              const data = await postMessage("serializeSelection");
              const comlink = await connectionDeferred.promise;
              await comlink.setClipboardData(clipId, data);
              postMessage("notify", `✓ Exported layers to the clipboard.`);
              setShowCompletedNotice(true);
            } catch (e) {
              postMessage(
                "notify",
                "⚠️ Unexpected error. Please share this <NAME_EMAIL> and we'll fix this for you."
              );
            } finally {
              clearProgress();
            }
          }}
        >
          Export selected layers to clipboard
        </Button>
      </Buttons>
      {typeof progress === "number" && (
        <ProgressBarOverlay>
          <ProgressBarWrapper>
            <ProgressBar progress={progress} />
            <MetaText
              style={{ marginTop: 8, textAlign: "center" }}
            >{`Exporting ${totalNodeCount} layers…`}</MetaText>
          </ProgressBarWrapper>
          <Button onClick={() => postMessage("cancel")}>Cancel</Button>
        </ProgressBarOverlay>
      )}
      <iframe
        ref={iframeRef}
        // If developing against local figma-plugin-web, simply change this to:
        // src={"https://localhost:3006/figma-plugin-app"}
        src={"https://studio.plasmic.app/figma-plugin-app/"}
        style={{
          width: 0,
          height: 0,
          border: "none",
        }}
      />
    </StyledApp>
  );
};

export default App;

const StyledApp = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 16px 16px 16px;
  width: 100%;
`;

const MainContent = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  flex: 1;
  justify-content: center;
  position: relative;
  & > * {
    margin-top: 16px;
  }
`;

const Overlay = styled.div`
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  cursor: pointer;
`;

const Notice = styled.div`
  box-sizing: border-box;
  padding: 16px;
  width: 100vw;
  align-self: center;
  background: #222222;
  color: white;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.5);
`;

const IconButton = styled.button`
  box-sizing: border-box;
  display: none;
  border: none;
  padding: 16px;
  position: absolute;
  top: 0;
  right: 0;
  cursor: pointer;
  background: none;
  font-size: 13px;
  line-height: 1;
`;

const Logo = styled.div`
  background-image: url(${logo});
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 145px 40px;
  height: 40px;
`;

const Buttons = styled.div`
  position: relative;
  & > * {
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const ProgressBarOverlay = styled.div`
  background-color: var(--white);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  left: 0;
  padding: 8px;
  position: absolute;
  bottom: 0;
  width: 100%;
`;

const ProgressBarWrapper = styled.div`
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: center;
  padding: 0 8px;
`;

const copyText = (string: string) => {
  const input = document.createElement("input");
  input.setAttribute("value", string);
  document.body.appendChild(input);
  input.select();
  const succeeded = document.execCommand("copy");
  document.body.removeChild(input);
  return succeeded;
};
