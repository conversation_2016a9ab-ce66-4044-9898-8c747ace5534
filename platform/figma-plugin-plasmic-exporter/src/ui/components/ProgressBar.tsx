import React from "react";
import styled from "styled-components";

type Props = {
  progress: number;
};

const ProgressBar: React.FC<Props> = ({ progress }) => (
  <Background>
    <Foreground style={{ width: `${progress * 100}%` }} />
  </Background>
);

export default ProgressBar;

const Background = styled.div`
  background-color: var(--progress-bar-background);
  overflow: hidden;
  border-radius: 4px;
  height: 8px;
  position: relative;
`;

const Foreground = styled.div`
  background-color: var(--progress-bar-foreground);
  border-radius: 4px;
  height: 100%;
  left: 0;
  min-width: 8px;
  position: absolute;
  top: 0;
`;
