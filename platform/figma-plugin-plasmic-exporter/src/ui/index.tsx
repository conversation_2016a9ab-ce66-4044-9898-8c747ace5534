import FileType from "file-type";
import ImageCompressor from "js-image-compressor";
import React from "react";
import ReactDOM from "react-dom";
import { createGlobalStyle } from "styled-components";
import SVGO from "svgo";
import defer from "../utils/defer";
import { addMessageListener, initUI } from "../utils/messages";
import App from "./components/App";

// Load in Inter
const link = document.createElement("link");
link.href =
  "https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap";
link.rel = "stylesheet";
document.head.appendChild(link);

// Add a root element to render to
const appRoot = document.createElement("div");
document.body.appendChild(appRoot);

const GlobalStyle = createGlobalStyle`
  * {
    font-family: 'Inter', sans-serif;
    font-size: 11px;
  }

  body {
    margin: 0;

    --black-alpha-10: rgba(0, 0, 0, 0.1);
    --cyan-55: #0099E6;
    --cyan-65: #26B7FF;
    --gray-30: #4A4C4F;
    --gray-60: #96999E;
    --gray-85: #D7DADE;
    --gray-90: #E4E6EB;
    --gray-95: #F0F2F5;
    --white: #FFFFFF;

    --primary-button-background: var(--cyan-65);
    --primary-button-background-active: var(--cyan-55);
    --primary-button-text: var(--white);
    --secondary-button-background: var(--gray-95);
    --secondary-button-background-active: var(--gray-85);
    --secondary-button-text: var(--gray-30);
    --disabled-button-background: var(--gray-95);
    --disabled-button-text: var(--gray-60);
    --divider: var(--black-alpha-10);
    --progress-bar-background: var(--gray-90);
    --progress-bar-foreground: var(--cyan-65);
    --meta-text: var(--gray-60);
  }

  a, a:visited, a:hover, a:active {
    color: var(--cyan-55);
    text-decoration: none;
  }
`;

// When the fonts are ready, render the UI
// @ts-ignore
document.fonts.ready.then(() => {
  ReactDOM.render(
    <>
      <GlobalStyle />
      <App />
    </>,
    appRoot
  );
});

initUI();

addMessageListener("imageBytesToBase64", defer(processImageBytes));

const MaxImageDim = 4096;
// When the image data url size exceeds this number, we start downscaling -
// the downscaling may compress the image to a smaller one.
const DownscaleImageSizeThreshold = 4 * 1024 * 1024;

async function processImageBytes(bytes: Uint8Array): Promise<string> {
  const buffer = Buffer.from(bytes);
  const data: string = buffer.toString("base64");

  if (data.length < DownscaleImageSizeThreshold) {
    return data;
  }

  let fileType = await FileType.fromBuffer(buffer);
  if (
    !fileType ||
    !fileType.mime ||
    !["image/png", "image/jpeg", "image/webp"].includes(fileType.mime)
  ) {
    console.log("Didn't optimize image because mime type is ", fileType.mime);
    return data;
  }

  try {
    let resolve: (b: Blob) => void;
    const successPromise = new Promise<Blob>((res) => {
      resolve = res;
    });
    new ImageCompressor({
      file: new File([bytes.buffer], `img.${fileType.ext}`, {
        type: fileType.mime,
      }),
      quality: 0.9,
      mimeType: fileType.mime,
      maxWidth: MaxImageDim,
      maxHeight: MaxImageDim,
      success: (result: Blob) => {
        resolve(result);
      },
    });
    const arrayBuffer = await (await successPromise).arrayBuffer();

    const result = Buffer.from(new Uint8Array(arrayBuffer)).toString("base64");
    console.log(
      `Old size ${bytes.length / 1024}KB, new size ${result.length / 1024}KB`
    );
    return result;
  } catch (e) {
    console.log(`failed to downscale ${bytes.length / 1024}KB`, e);
    return data;
  }
}

const svgo = new SVGO();

addMessageListener("optimizeSVG", async (data: string) =>
  svgo.optimize(data).then(({ data }) => data)
);
