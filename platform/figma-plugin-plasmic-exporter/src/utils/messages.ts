import uuid from "uuid-random";

// Glue code to help with message passing across the Figma/UI boundary,
// returns a promise which resolves when the other side responds

// Listen for a message type and respond to it
export const addMessageListener = <Payload, Response>(
  type: string,
  listener: MessageListener<Payload, Response>
) => {
  if (!messageListenersByType[type]) {
    messageListenersByType[type] = [];
  }
  messageListenersByType[type].push(listener);
};

// Unregister a listener when it isn't needed anymore (e.g. in the cleanup
// of a `useEffect`)
export const removeMessageListener = <Payload, Response>(
  type: string,
  listener: MessageListener<Payload, Response>
) => {
  if (!messageListenersByType[type]) {
    return;
  }
  messageListenersByType[type] = messageListenersByType[type].filter(
    (thisListener) => thisListener !== listener
  );
  if (!messageListenersByType.length) {
    delete messageListenersByType[type];
  }
};

// Post a message to the other side of the bridge, and return a promise which
// resolves to a response
export let postMessage: <Payload = any, Response = any>(
  type: string,
  payload?: Payload,
  needsResponse?: boolean
) => Promise<Response>;

// Initialize the main side of the message bridge
export const initMain = () => {
  postMessage = async (type, payload, needsResponse = true) => {
    const responseType = needsResponse ? uuid() : undefined;
    const message: Message = { type, responseType, payload };
    figma.ui.postMessage(message);
    return new Promise((resolve) => {
      const listener = (payload: any) => {
        resolve(payload);
        removeMessageListener(responseType, listener);
      };
      addMessageListener(responseType, listener);
    });
  };

  figma.ui.onmessage = async ({ type, responseType, payload }: Message) => {
    if (messageListenersByType[type]) {
      messageListenersByType[type].forEach((listener) => {
        const response = listener(payload);
        if (responseType) {
          Promise.resolve(response).then((payload) => {
            const message: Message = { type: responseType, payload };
            figma.ui.postMessage(message);
          });
        }
      });
    }
  };
};

// Initialize the UI side of the message bridge
export const initUI = () => {
  postMessage = async (type, payload, needsResponse = true) => {
    const responseType = needsResponse ? uuid() : undefined;
    const message: Message = { type, responseType, payload };
    parent.postMessage({ pluginMessage: message }, "*");
    return new Promise((resolve) => {
      const listener = (payload: any) => {
        resolve(payload);
        removeMessageListener(responseType, listener);
      };
      addMessageListener(responseType, listener);
    });
  };

  window.onmessage = async (e: MessageEvent) => {
    const { type, responseType, payload } = e.data.pluginMessage || {};
    if (messageListenersByType[type]) {
      messageListenersByType[type].forEach((listener) => {
        const response = listener(payload);
        if (responseType) {
          Promise.resolve(response).then((payload) => {
            const message: Message = { type: responseType, payload };
            parent.postMessage({ pluginMessage: message }, "*");
          });
        }
      });
    }
  };
};

const messageListenersByType: {
  [type: string]: Array<MessageListener>;
} = {};

type Message = {
  type: string;
  responseType?: string;
  payload: any;
};

type MessageListener<Payload = any, Response = any> = (
  payload: Payload
) => Response | Promise<Response>;
