// Chunk out work by wrapping it in `setTimeout` and returning a Promise
// https://www.figma.com/plugin-docs/frozen-plugins/#my-plugin-needs-to-do-a-long-running-task-what-do-i-do
const defer = <Args extends Array<any>, Return>(
  callback: (...args: Args) => Return | Promise<Return>
) => (...args: Args): Promise<Return> =>
  new Promise((resolve) =>
    setTimeout(async () => resolve(await Promise.resolve(callback(...args))), 0)
  );

export default defer;
