# Plasmic Exporter

This Figma plugin crawls a Figma document and serializes all of the nodes into JSON.

# Setting up the package

1. `yarn install`
2. `yarn build`
3. In the [Figma desktop app](https://www.figma.com/downloads/) navigate to the “Plugins” page and click “Create your own plugin” in the right hand column.
4. Click on “Click to choose a manifest.json file” and navigate to this repository.
5. Select `./manifest.json` to load this plugin.

A `yarn start` script also exists, although you will need to relaunch the Figma plugin each time as hot reloading does not work.

# Dependencies

- Everything is bundled with `webpack`, Figma plugin UI code needs to be bundled into a single HTML file so we're using a couple plugins to achieve that.
- UI is written in React and [`styled-components`](https://styled-components.com/).

# Overall structure

Figma plugins run in two JavaScript environments, code that has access to the Figma document runs in a [sandboxed VM](https://www.figma.com/blog/how-we-built-the-figma-plugin-system/) in the Figma window itself and UI code runs in an `iframe`. These environments communicate via message passing using `postMessage`. Browser APIs (e.g. `btoa` for base 64 encoding image data) are only available within the `iframe` and so even the serialization code relies on an `iframe` to work.

- UI code is in `./src/ui`
- Main code (this runs in Figma) is in `./src/main`
- Any shared code is in `./src/utils`

There’s a small layer that helps with asynchronous responses to messages across the bridge, but everything else is more or less vanilla.

# JSON compression

JSON just dumped straight from Figma objects repeats tons of keys and string values (e.g. for enums) resulting in lots of redundant data. We may want to handle transformation of this data into Plasmic documents on the serverside, so keys and strings are normalized and stored at the root as a super basic form of compression. They are referenced using an index into that array, encoded into a base 36 number.

```json
{
  "test": "HELLO",
  "something": {
    "test": "HELLO"
  }
}
```

After compression, this translates into:

```json
{
  "k": ["test", "something"],
  "s": ["HELLO"],
  "n": {
    "1": "1",
    "2": {
      "1": "2"
    }
  }
}
```

Over hundreds or thousands of nodes with very similar sets of keys and strings, this simple compression adds up pretty fast.

# Publish steps

Follow https://coda.io/d/Plasmic-Wiki_dHQygjmQczq/Publishing-the-Figma-plugin_su7vf#_luQwq
