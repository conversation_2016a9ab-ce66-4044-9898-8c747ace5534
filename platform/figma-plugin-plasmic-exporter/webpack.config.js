const path = require("path");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const InlineChunkHtmlPlugin = require("react-dev-utils/InlineChunkHtmlPlugin");

module.exports = (env, argv) => ({
  mode: argv.mode === "production" ? "production" : "development",
  devtool: argv.mode === "production" ? false : "inline-source-map",
  entry: {
    main: "./src/main/index.ts",
    ui: "./src/ui/index.tsx",
  },
  module: {
    rules: [
      { test: /\.tsx?$/, exclude: /node_modules/, loader: "ts-loader" },
      // Transform SVGO so we can run it in the browser
      { test: /node_modules\/svgo\//, loader: "transform-loader?brfs" },
      { test: /\.(png)$/, loader: "url-loader" },
    ],
  },
  // This is also needed to patch out `fs` from SVGO
  node: {
    fs: "empty",
  },
  resolve: { extensions: [".tsx", ".ts", ".jsx", ".js"] },
  output: {
    filename: "[name].js",
    path: path.resolve(__dirname, "dist"),
  },
  plugins: [
    new HtmlWebpackPlugin({
      cache: false,
      chunks: ["ui"],
      filename: "ui.html",
      inject: true,
    }),
    new InlineChunkHtmlPlugin(HtmlWebpackPlugin, [/ui/]),
  ],
});
