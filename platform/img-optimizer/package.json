{"name": "img-optimizer", "version": "0.0.1", "description": "Plasmic image optimizer", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"start": "bash tools/run.bash src/main.ts", "build": "tsc --project tsconfig.build.json", "start:debug": "debug=1 yarn start", "test": "yarn --cwd=../.. test `pwd`", "typecheck": "tsc --noEmit"}, "author": "Plasmic Team", "devDependencies": {"@types/errorhandler": "^1.5.0", "@types/express": "^4.17.7", "@types/lodash": "^4.14.157", "@types/morgan": "^1.9.1", "@types/sharp": "^0.31.1", "typescript": "^5.4.5"}, "dependencies": {"@godaddy/terminus": "4.12.1", "@sentry/integrations": "^6.6.0", "@sentry/node": "^6.6.0", "@sentry/tracing": "^6.6.0", "aws-sdk": "^2.720.0", "dotenv": "^8.2.0", "errorhandler": "^1.5.1", "express": "^4.17.1", "express-async-errors": "^3.1.1", "express-prom-bundle": "^6.4.1", "lodash": "^4.17.21", "mime": "^3.0.0", "morgan": "^1.10.0", "prom-client": "^14.0.1", "sharp": "0.34.2", "ts-node": "^10.9.2"}}