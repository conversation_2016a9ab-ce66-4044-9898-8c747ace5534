import { createTerminus } from "@godaddy/terminus";
import * as http from "http";
import { createApp } from "./ImgServer";
import { spawn } from "./lang-utils";

spawn(run());

async function run() {
  const app = await createApp();
  const server = http.createServer(app);

  // Add graceful shutdown to the server.
  // Uses the "stoppable" package to change shutdown behavior so that:
  //  - new connections are not accepted
  //  - existing idle connections are closed
  //  - existing active connections are given a grace period before being
  //    terminated
  createTerminus(server, {
    // If the server is shutting down, healthcheck endpoints return 503. Else,
    // the handler function is run. This is useful for load balancers.
    healthChecks: {
      "/healthcheck": async () => true,
    },
    timeout: 5000, // wait this many ms before force closing active conns
    signals: ["SIGTERM", "SIGINT"],
    beforeShutdown: () => {
      console.log(`Received signal to shut down...`);
      // This has to be greater than the number of seconds defined
      // in the readiness probe "periodSeconds"
      return new Promise((resolve) => setTimeout(resolve, 5500));
    },
    onShutdown: async () => {
      console.log(`Shutdown complete, exiting...`);
    },
    logger: (msg, err) => {
      console.log(`Shutdown error:`, msg, err);
    },
    useExit0: true,
  });

  /**
   * Start Express server.
   */
  return server.listen(app.get("port"), () => {
    console.log(`
App is running at http://localhost:${app.get("port")} in ${app.get("env")} mode
Press CTRL-C to stop immediately.
Send SIGINT to shutdown gracefully: kill -INT ${process.pid}
`);
    // Let PM2 parent process know we're ready.
    if (process.send) {
      process.send("ready");
    }
  });
}
