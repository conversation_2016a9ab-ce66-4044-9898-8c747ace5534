import S3 from "aws-sdk/clients/s3";
import { Request, Response } from "express-serve-static-core";
import { isString } from "lodash";
import mime from "mime";
import sharp from "sharp";
import { config } from "./ImgServerConfig";
import { ApiError, AssertionError, BadRequestError } from "./custom-errors";
import {
  ImageFormat,
  ImageUpstream,
  checkFormat,
  detectContentType,
  getFormatTransformOptions,
} from "./img-utils";
import { assert, ensure, isUrl } from "./lang-utils";

/**
 * Used to check if the given src is pointing to our own host.
 */
function fixSrcParam(src: any) {
  if (typeof src !== "string") {
    return src;
  }
  // If the url is actually pointing to our own host, we are going to extract the S3 key
  if (src.includes(config.imgOptimizerHost)) {
    const lastSlash = src.lastIndexOf("/");
    if (lastSlash === -1 || lastSlash === src.length - 1) {
      return src;
    }
    return src.substring(lastSlash + 1);
  }
  return src;
}

function validateParams(req: Request) {
  try {
    const { imgId } = req.params;
    const { src: imgSrc, w: w, q: q, f: format } = req.query;
    const src = imgId ?? fixSrcParam(imgSrc);
    assert(!!src && isString(src), `Unexpected src = ${src}`);

    assert(!w || isString(w), `Unexpected width = ${w}`);
    const width = w ? parseInt(w, 10) : undefined;
    assert(
      !width || (width >= 0 && !isNaN(width)),
      `Unexpected width = ${width}`
    );

    assert(isString(q) || !q, `Unexpected quality = ${q}`);
    const quality = q ? parseInt(q, 10) : undefined;
    assert(
      !quality || (!isNaN(quality) && quality <= 100 && quality >= 1),
      `Unexpected quality = ${quality}`
    );

    assert(isString(format) || !format, `Unexpected format = ${format}`);
    const requiredFormat = format
      ? format === "jpg"
        ? "jpeg" // Sharp only accepts "jpeg"
        : format
      : "png"; // default to "png"
    assert(
      checkFormat(requiredFormat),
      `Unexpected requiredFormat = ${requiredFormat}`
    );

    return {
      src,
      width,
      quality,
      format: requiredFormat,
    };
  } catch (err) {
    if (err instanceof AssertionError) {
      throw new BadRequestError(err.message);
    } else {
      throw err;
    }
  }
}

function sendOptimizationResponse(
  res: Response,
  image: Buffer,
  mimeType: string | null,
  maxAge?: number
) {
  res.set("Access-Control-Allow-Origin", "*");
  if (mimeType) {
    res.set("Content-Type", mimeType);
  }
  // Cache it forever
  res.set(
    "Cache-control",
    `max-age=${maxAge !== undefined ? maxAge : 31536000}, s-maxage=31536000`
  );
  res.send(image);
}

function parseCacheControl(
  str: string | null | undefined
): Map<string, string> {
  const map = new Map<string, string>();
  if (!str) {
    return map;
  }
  for (let directive of str.split(",")) {
    let [key, value] = directive.trim().split("=", 2);
    key = key.toLowerCase();
    if (value) {
      value = value.toLowerCase();
    }
    map.set(key, value);
  }
  return map;
}

function getMaxAge(str: string | null | undefined): number {
  const map = parseCacheControl(str);
  if (map) {
    let age = map.get("s-maxage") || map.get("max-age") || "";
    if (age.startsWith('"') && age.endsWith('"')) {
      age = age.slice(1, -1);
    }
    const n = parseInt(age, 10);
    if (!isNaN(n)) {
      return n;
    }
  }
  return 0;
}

async function fetchS3Image(src: string): Promise<ImageUpstream> {
  try {
    const obj = await new S3({
      endpoint: config.S3Endpoint,
    })
      .getObject({
        Bucket: config.siteAssetsBucket,
        Key: src,
      })
      .promise();
    return {
      buffer: Buffer.from(
        ensure(obj.Body, "Unexpected undefined image") as string
      ),
      cacheControl: `max-age=31536000, s-maxage=31536000`,
      extension: src.split(".").pop()!,
    };
  } catch (err: any) {
    // Explicitly convert to ApiError to avoid reporting to Sentry
    if (err.name === "AccessDenied" || err.name === "NoSuchKey") {
      throw new ApiError(`Image not found: ${src}`, 404);
    }
    throw err;
  }
}

async function fetchExternalImage(src: string): Promise<ImageUpstream> {
  try {
    const res = await fetch(src);

    if (!res.ok) {
      throw new ApiError(
        `fetch from upstream ${src} responded ${res.status}`,
        502
      );
    }

    const buffer = Buffer.from(await res.arrayBuffer());
    const cacheControl = res.headers.get("Cache-Control");
    const contentType = res.headers.get("Content-Type");

    const extension = contentType
      ? mime.getExtension(contentType) ?? detectContentType(buffer)
      : detectContentType(buffer);
    if (checkFormat(extension) === false) {
      throw new BadRequestError(`unexpected content type ${contentType}`);
    }

    return { buffer, cacheControl, extension };
  } catch (err) {
    if (err instanceof ApiError) {
      throw err;
    }
    // Explicitly convert to ApiError to avoid reporting to Sentry
    throw new ApiError(`failed to fetch from upstream ${src}`, 500);
  }
}

export async function optimizeImage(
  buffer: Buffer,
  width: number | undefined,
  quality: number | undefined,
  format: ImageFormat
): Promise<{ buffer: Buffer; format: ImageFormat }> {
  const transformer = sharp(buffer, {
    sequentialRead: true,
    pages: -1,
  });

  const metadata = await transformer.metadata();

  // Don't do anything if...
  if (
    // Input is SVG
    metadata.format === "svg" ||
    // Format and width (if specified) are the same
    (format === metadata.format &&
      (width === undefined || width === metadata.width))
  ) {
    return {
      buffer,
      format: metadata.format as ImageFormat,
    };
  }

  const optimizedImage = await transformer
    .resize(width ? width : null)
    .toFormat(format, getFormatTransformOptions(format, { quality }))
    .toBuffer();

  return { buffer: optimizedImage, format: format };
}

export async function handleImageOptimization(req: Request, res: Response) {
  const { src, width, quality, format } = validateParams(req);

  const imageData = await (isUrl(src)
    ? fetchExternalImage(src)
    : fetchS3Image(src));

  // Try optimize image using sharp
  try {
    const { buffer: optimizedBuffer, format: endFormat } = await optimizeImage(
      imageData.buffer,
      width,
      quality,
      format
    );

    sendOptimizationResponse(
      res,
      optimizedBuffer,
      mime.getType(endFormat),
      getMaxAge(imageData.cacheControl)
    );
  } catch {
    // Sending the unoptimized image in case of error.
    sendOptimizationResponse(
      res,
      imageData.buffer,
      mime.getType(imageData.extension),
      getMaxAge(imageData.cacheControl)
    );
  }
}

/**
 * @deprecated
 */
export async function handleOldImageRoute(req: Request, res: Response) {
  const params = validateParams(req);
  const { src, width, quality } = params;
  let { format } = params;

  const { buffer: data, extension } = await fetchS3Image(src);

  // Try optimize image using sharp
  try {
    const { buffer: optimizedBuffer, format: endFormat } = await optimizeImage(
      data,
      width,
      quality,
      format
    );

    sendOptimizationResponse(
      res,
      optimizedBuffer,
      mime.getType(endFormat),
      31536000
    );
  } catch {
    // Sending the unoptimized image in case of error.
    sendOptimizationResponse(res, data, mime.getType(extension), 31536000);
  }
}
