import { readFile, writeFile } from "fs/promises";
import { optimizeImage } from "./image-optimizer";

// change to true to update files
const updateFiles = false;

describe("optimizeImage", () => {
  afterAll(() => {
    expect(updateFiles).toBe(false);
  });

  it("converts between formats", async () => {
    const input = await readFile(`platform/img-optimizer/testdata/plasmic.png`);
    for (const outputFormat of ["jpeg", "png", "webp"] as const) {
      const { buffer, format } = await optimizeImage(
        input,
        undefined,
        75,
        outputFormat
      );
      await expectBufferToEqualFile(
        buffer,
        `platform/img-optimizer/testdata/plasmic.${outputFormat}`
      );
      expect(format).toEqual(outputFormat);
    }
  });
  it("resizes images", async () => {
    const input = await readFile(`platform/img-optimizer/testdata/plasmic.png`);
    for (const outputFormat of ["jpeg", "png", "webp"] as const) {
      const { buffer, format } = await optimizeImage(
        input,
        32,
        75,
        outputFormat
      );
      await expectBufferToEqualFile(
        buffer,
        `platform/img-optimizer/testdata/plasmic-halfsize.${outputFormat}`
      );
      expect(format).toEqual(outputFormat);
    }
  });
  it("does not change the buffer if input format is svg", async () => {
    const input = await readFile(`platform/img-optimizer/testdata/plasmic.svg`);
    const { buffer, format } = await optimizeImage(
      input,
      undefined,
      undefined,
      "png"
    );
    expect(buffer).toBe(input);
    expect(format).toEqual("svg");
  });
  it("does not change the buffer if input and output have the same format", async () => {
    const input = await readFile(`platform/img-optimizer/testdata/plasmic.png`);
    const { buffer, format } = await optimizeImage(
      input,
      undefined,
      undefined,
      "png"
    );
    expect(buffer).toBe(input);
    expect(format).toEqual("png");
  });
  it("does not change the buffer if input and output have the same format and width", async () => {
    const input = await readFile(`platform/img-optimizer/testdata/plasmic.png`);
    const { buffer, format } = await optimizeImage(input, 64, undefined, "png");
    expect(buffer).toBe(input);
    expect(format).toEqual("png");
  });
});

async function expectBufferToEqualFile(buffer: Buffer, file: string) {
  if (updateFiles) {
    await writeFile(file, buffer);
  } else {
    expect(buffer).toEqual(await readFile(file));
  }
}
