const AVAILABLE_FORMATS = [
  "avif",
  "dz",
  "fits",
  "gif",
  "heif",
  "input",
  "jpeg",
  "magick",
  "openslide",
  "pdf",
  "png",
  "ppm",
  "raw",
  "svg",
  "tiff",
  "v",
  "webp",
] as const;

export type ImageFormat = (typeof AVAILABLE_FORMATS)[number];

export const LOSSY_FORMATS = ["jpeg", "webp", "tiff"] as const;

export type ImageUpstream = {
  buffer: Buffer;
  cacheControl: string | null | undefined;
  extension: string;
};

export function checkFormat(format: string): format is ImageFormat {
  return AVAILABLE_FORMATS.includes(format as any);
}

/**
 * Inspects the first few bytes of a buffer to determine if
 * it matches the "magic number" of known file signatures.
 * https://en.wikipedia.org/wiki/List_of_file_signatures
 *
 * Copied from the next image-optimizer
 * https://github.com/vercel/next.js/blob/canary/packages/next/src/server/image-optimizer.ts
 */
export function detectContentType(buffer: Buffer): ImageFormat {
  if ([0xff, 0xd8, 0xff].every((b, i) => buffer[i] === b)) {
    return "jpeg";
  }
  if (
    [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a].every(
      (b, i) => buffer[i] === b
    )
  ) {
    return "png";
  }
  if ([0x47, 0x49, 0x46, 0x38].every((b, i) => buffer[i] === b)) {
    return "gif";
  }
  if (
    [0x52, 0x49, 0x46, 0x46, 0, 0, 0, 0, 0x57, 0x45, 0x42, 0x50].every(
      (b, i) => !b || buffer[i] === b
    )
  ) {
    return "webp";
  }
  if ([0x3c, 0x3f, 0x78, 0x6d, 0x6c].every((b, i) => buffer[i] === b)) {
    return "svg";
  }
  if (
    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66].every(
      (b, i) => !b || buffer[i] === b
    )
  ) {
    return "avif";
  }
  return "webp";
}

export function getFormatTransformOptions(
  format: ImageFormat,
  opts: { quality: number | undefined }
) {
  return {
    quality: LOSSY_FORMATS.includes(format as any)
      ? opts.quality || 75
      : undefined,
    chromaSubsampling: format === "avif" ? "4:2:0" : undefined,
    progressive: format === "jpeg" ? true : undefined,
  };
}
