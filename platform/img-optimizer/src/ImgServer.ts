import * as Sentry from "@sentry/node";
import * as Tracing from "@sentry/tracing";
import * as bodyParser from "body-parser";
import errorHandler from "errorhandler";
import express from "express";
import "express-async-errors";
import promMetrics from "express-prom-bundle";
import { NextFunction, Request, Response } from "express-serve-static-core";
import morgan from "morgan";
import v8 from "v8";
import { config } from "./ImgServerConfig";
import { ApiError } from "./custom-errors";
import {
  handleImageOptimization,
  handleOldImageRoute,
} from "./image-optimizer";

function addSentry(app: express.Application) {
  if (!config.sentryDSN) {
    return;
  }
  console.log("Initializing Sentry with DSN:", config.sentryDSN);
  Sentry.init({
    dsn: config.sentryDSN,
    integrations: [
      // enable HTTP calls tracing
      new Sentry.Integrations.Http({ tracing: true }),
      // enable Express.js middleware tracing
      // to trace all requests to the default router
      new Tracing.Integrations.Express({
        app,
      }),
    ],
    // We recommend adjusting this value in production, or using tracesSampler
    // for finer control
    tracesSampleRate: 0,
  });
  app.use(Sentry.Handlers.requestHandler());
  app.use(Sentry.Handlers.tracingHandler());
}

function addSentryError(app: express.Application) {
  if (!config.sentryDSN) {
    return;
  }

  const shouldHandleError = (error: unknown) => {
    return !(error instanceof ApiError);
  };

  app.use(Sentry.Handlers.errorHandler({ shouldHandleError }));
}

function addMiddlewares(app: express.Application) {
  app.use(morgan("combined"));
  app.use(
    promMetrics({
      customLabels: {
        route: null,
        url: null,
      },
      includeMethod: true,
      includeStatusCode: true,
      includePath: true,
      transformLabels: (labels, req) => {
        labels.route = req.route?.path;
        labels.url = req.originalUrl;
      },
      promClient: {
        collectDefaultMetrics: {},
      },
    })
  );

  // Parse body further down to prevent unauthorized users from incurring large parses.
  app.use(bodyParser.json({ limit: "400mb" }));
  app.use(bodyParser.urlencoded({ extended: true }));
}

function addRoutes(app: express.Application) {
  app.use((req, res, next) => {
    console.log(req.ip);
    next();
  });

  /**
   * Primary app routes.
   */
  app.get("/img-optimizer/v1/img", withNext(handleImageOptimization));
  app.get("/img-optimizer/v1/img/:imgId", withNext(handleOldImageRoute));
}

function withNext(
  f: (req: Request, res: Response, next: NextFunction) => Promise<void>
) {
  return (req: Request, res: Response, next: NextFunction) => {
    f(req, res, next).then(
      () => next(),
      (err) => next(err)
    );
  };
}

function addEndErrorHandlers(app: express.Application) {
  // This transforms certain known errors into proper response codes and JSON.
  // This is only called if there was previously a next(error).
  app.use(
    async (err: Error, req: Request, res: Response, next: NextFunction) => {
      if (err instanceof ApiError) {
        res
          .status(err.statusCode)
          .json({ error: { ...err, message: err.message } });
      } else {
        next(err);
      }
    }
  );
}

export async function createApp(): Promise<express.Application> {
  const app = express();

  app.set("port", process.env.IMG_PORT || 3009);

  // Sentry setup needs to be first
  addSentry(app);

  if (config.production) {
    app.enable("trust proxy");
  }

  addMiddlewares(app);

  addRoutes(app);

  // Sentry error handler must go first
  addSentryError(app);

  addEndErrorHandlers(app);

  if (!config.production) {
    app.use(errorHandler());
  }

  // Don't leak infra info
  app.disable("x-powered-by");

  console.log(
    `Starting server with heap memory ${
      v8.getHeapStatistics().total_available_size / 1024 / 1024
    }MB`
  );

  return app;
}
