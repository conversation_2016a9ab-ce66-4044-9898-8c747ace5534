import { isString } from "lodash";
import { AssertionError } from "./custom-errors";

export function spawn(_: Promise<any>) {}

type StringGen = string | (() => string);

export function assert<T>(
  cond: T,
  msg: StringGen = "Assertion failed"
): asserts cond {
  if (!cond) {
    // We always generate an non empty message so that it doesn't get swallowed
    // by the async library.
    msg = (isString(msg) ? msg : msg()) || "Assertion failed";
    debugger;
    throw new AssertionError(msg);
  }
}

export function ensure<T>(x: T | null | undefined, msg: StringGen = ""): T {
  if (x === null || x === undefined) {
    debugger;
    msg = (isString(msg) ? msg : msg()) || "";
    throw new AssertionError(
      `Value must not be undefined or null${msg ? `- ${msg}` : ""}`
    );
  } else {
    return x;
  }
}

export function isUrl(s: string) {
  try {
    const _ = new URL(s);
    return true;
  } catch {
    return false;
  }
}
