# ── builder stage ──
FROM node:24-alpine AS builder
WORKDIR /app

# install deps (including devDeps if you need them to build)
COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

COPY . .
# make sure your tsconfig.json has "outDir": "dist"
RUN yarn build           # should emit /app/dist
# debug: you can insert
# RUN ls -R /app

# ── runtime stage ──
FROM node:24-alpine AS runtime
WORKDIR /app

# only prod deps
COPY --from=builder /app/package.json /app/yarn.lock ./
RUN yarn install --production --frozen-lockfile && yarn cache clean

# copy the built code
COPY --from=builder /app/dist /app/dist

USER node
CMD ["node", "dist/main.js"]

