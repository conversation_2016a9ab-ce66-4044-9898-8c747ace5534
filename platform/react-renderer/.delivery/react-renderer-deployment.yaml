apiVersion: apps/v1
kind: Deployment
metadata:
  name: react-renderer-deployment
spec:
  selector:
    matchLabels:
      app: react-renderer-pod
  template:
    metadata:
      labels:
        app: react-renderer-pod
      annotations:
        prometheus.io/scrape: 'true'
    spec:
      containers:
        - name: react-renderer
          image: 939375546786.dkr.ecr.us-west-2.amazonaws.com/react-renderer:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3010
          env:
            - name: CODEGEN_HOST
              value: https://codegen.plasmic.app
          readinessProbe:
            tcpSocket:
              port: 3010
            initialDelaySeconds: 5
            periodSeconds: 5
          resources:
            requests:
              cpu: "100m"
              memory: "800Mi"
            limits:
              memory: "800Mi"
# Add this back in if we want to force these to run on an "untrusted" node group.
#      nodeSelector:
#        alpha.eksctl.io/nodegroup-name: untrusted
#      tolerations:
#        - key: "nodegroup"
#          operator: "Equal"
#          value: "untrusted"
#          effect: "NoSchedule"
