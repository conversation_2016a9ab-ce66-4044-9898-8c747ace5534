{
  "compilerOptions": {
    "target": "es2017",
    "module": "CommonJS",
    "lib": [
      "dom",
      "dom.iterable",
      "es2016",
      "es2017",
      "es2018",
      "es2019",
      "es2020",
      "esnext",
      "esnext.asynciterable",
      "esnext.array"
    ],
    // .js were causing problems.  Getting "Cannot compile namespaces" errors
    // ([1]), and `String.format` in devtools.js was causing weird type
    // inference issues with strings (especially causing JQuery.data() to
    // break).
    //
    // [1]: https://github.com/Microsoft/TypeScript/issues/15230#issuecomment-466624686
    "allowJs": false,
    "allowSyntheticDefaultImports": true,
    "alwaysStrict": true,
    "baseUrl": "./",
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "moduleResolution": "node",
    "noEmit": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "strict": false,
    "strictFunctionTypes": true,
    "strictNullChecks": true
  },
  "include": ["src"],
  "exclude": [
    "node_modules",
    "build",
    "tools",
    "acceptance-tests",
    "webpack",
    "jest",
    "src/setupTests.ts",
    "__tests__"
  ]
}
