{"name": "react-renderer", "scripts": {"start": "npm run run-ts -- src/main.ts", "start:debug": "debug=1 bash tools/run.bash src/main.ts", "start-prod": "SERVICE_NAME=react-renderer npm run run-ts -- src/main.ts -c src/config.prod.json", "run-ts": "bash tools/run.bash"}, "dependencies": {"@godaddy/terminus": "^4.11.1", "@plasmicapp/loader-react": "^1.0.174", "@sentry/node": "^7.11.1", "bindings": "^1.3.0", "commander": "^9.4.0", "dotenv": "^16.0.1", "errorhandler": "^1.5.1", "esbuild": "^0.15.5", "esbuild-register": "^3.3.3", "execa": "^6.1.0", "express": "^4.18.1", "express-async-errors": "^3.1.1", "express-prom-bundle": "^6.5.0", "express-serve-static-core": "^0.1.1", "morgan": "^1.10.0", "prom-client": "^14.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "ts-node": "^10.9.1", "v8": "^0.1.0"}, "devDependencies": {"@types/node": "^18.7.13", "@types/react": "^18.0.17", "@types/react-dom": "^18.0.6", "typescript": "^4.8.2"}}