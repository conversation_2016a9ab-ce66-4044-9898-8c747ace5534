{
  "compilerOptions": {
    "target": "es2017",
    "module": "esnext",
    "moduleResolution": "node",
    "lib": ["es7", "dom", "esnext", "esnext.asynciterable", "esnext.array"],
    "skipLibCheck": true,
    // Following is for the compiler invoked by webstorm
    "outDir": "build",
    "sourceMap": true,
    "jsx": "react",
    "esModuleInterop": true
  },
  "files": ["src/index.ts"],
  "include": ["src/**/*"],
  "exclude": ["node_modules"]
}
