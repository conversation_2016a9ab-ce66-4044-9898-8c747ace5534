[["3K1rAiEsewrHD9dYYoLtD9", {"root": "39429001", "map": {"3374501": {"rows": [{"__ref": "3374502"}], "__type": "ArenaFrameGrid"}, "3374502": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3374503": {"rows": [{"__ref": "3374504"}], "__type": "ArenaFrameGrid"}, "3374504": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "39429001": {"components": [{"__ref": "39429002"}, {"__ref": "62876001"}, {"__ref": "62876022"}], "arenas": [], "pageArenas": [{"__ref": "39429063"}, {"__ref": "62876023"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "39429057"}], "userManagedFonts": [], "globalVariant": {"__ref": "39429070"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "39429078"}], "activeTheme": {"__ref": "39429078"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "39429057"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "39429002": {"uuid": "WEdE3DE13Do2", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "39429003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "39429007"}], "variantGroups": [], "pageMeta": {"__ref": "39429062"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "39429003": {"tag": "div", "name": null, "children": [{"__ref": "39429004"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_vWoqEkZzN2a", "parent": null, "locked": null, "vsettings": [{"__ref": "39429041"}, {"__ref": "39429054"}], "__type": "TplTag"}, "39429004": {"tag": "section", "name": null, "children": [{"__ref": "39429005"}, {"__ref": "61910062"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "mwsnbHxfYLje", "parent": {"__ref": "39429003"}, "locked": null, "vsettings": [{"__ref": "39429027"}], "__type": "TplTag"}, "39429005": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "0Mt5L40rFRp0", "parent": {"__ref": "39429004"}, "locked": null, "vsettings": [{"__ref": "39429006"}], "__type": "TplTag"}, "39429006": {"variants": [{"__ref": "39429007"}], "args": [], "attrs": {}, "rs": {"__ref": "39429009"}, "dataCond": null, "dataRep": null, "text": {"__ref": "61910061"}, "columnsConfig": null, "__type": "VariantSetting"}, "39429007": {"uuid": "alPSW_JsSvNP", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "39429009": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "margin-bottom": "32px"}, "mixins": [], "__type": "RuleSet"}, "39429027": {"variants": [{"__ref": "39429007"}], "args": [], "attrs": {}, "rs": {"__ref": "39429028"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "39429028": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "padding-left": "24px", "padding-right": "24px", "padding-bottom": "96px", "padding-top": "96px", "row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "39429041": {"variants": [{"__ref": "39429007"}], "args": [], "attrs": {}, "rs": {"__ref": "39429042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "39429042": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "39429054": {"variants": [{"__ref": "39429055"}], "args": [], "attrs": {}, "rs": {"__ref": "39429061"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "39429055": {"uuid": "iyZ4C-t2qhTHE", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "39429057"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "39429057": {"type": "global-screen", "param": {"__ref": "39429058"}, "uuid": "xZyG0nGSeCk95", "variants": [{"__ref": "39429055"}], "multi": true, "__type": "GlobalVariantGroup"}, "39429058": {"type": {"__ref": "39429060"}, "variable": {"__ref": "39429059"}, "uuid": "vj8CLJ0TxTxQi", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "39429059": {"name": "Screen", "uuid": "OgEagkjt0Sm-x", "__type": "Var"}, "39429060": {"name": "text", "__type": "Text"}, "39429061": {"values": {}, "mixins": [], "__type": "RuleSet"}, "39429062": {"path": "/", "params": {}, "query": {}, "title": "Index", "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "39429063": {"component": {"__ref": "39429002"}, "matrix": {"__ref": "39429064"}, "customMatrix": {"__ref": "3374501"}, "__type": "PageArena"}, "39429064": {"rows": [{"__ref": "39429065"}], "__type": "ArenaFrameGrid"}, "39429065": {"cols": [{"__ref": "39429066"}, {"__ref": "39429073"}], "rowKey": {"__ref": "39429007"}, "__type": "ArenaFrameRow"}, "39429066": {"frame": {"__ref": "39429067"}, "cellKey": null, "__type": "ArenaFrameCell"}, "39429067": {"uuid": "zQhkERTEWrlqY", "width": 1440, "height": 768, "container": {"__ref": "39429068"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "39429007"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "39429068": {"name": null, "component": {"__ref": "39429002"}, "uuid": "xXCoiz-ATimb_", "parent": null, "locked": null, "vsettings": [{"__ref": "39429069"}], "__type": "TplComponent"}, "39429069": {"variants": [{"__ref": "39429070"}], "args": [], "attrs": {}, "rs": {"__ref": "39429072"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "39429070": {"uuid": "CTIdtczD5oCQB", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "39429072": {"values": {}, "mixins": [], "__type": "RuleSet"}, "39429073": {"frame": {"__ref": "39429074"}, "cellKey": null, "__type": "ArenaFrameCell"}, "39429074": {"uuid": "1IIxwoL6TCvp3", "width": 414, "height": 736, "container": {"__ref": "39429075"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"iyZ4C-t2qhTHE": true}, "targetGlobalVariants": [{"__ref": "39429055"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "39429075": {"name": null, "component": {"__ref": "39429002"}, "uuid": "bsARGO5WQCSpJ", "parent": null, "locked": null, "vsettings": [{"__ref": "39429076"}], "__type": "TplComponent"}, "39429076": {"variants": [{"__ref": "39429070"}], "args": [], "attrs": {}, "rs": {"__ref": "39429077"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "39429077": {"values": {}, "mixins": [], "__type": "RuleSet"}, "39429078": {"defaultStyle": {"__ref": "39429079"}, "styles": [{"__ref": "39429094"}, {"__ref": "39429102"}, {"__ref": "39429110"}, {"__ref": "39429114"}, {"__ref": "39429122"}, {"__ref": "39429130"}, {"__ref": "39429155"}, {"__ref": "39429163"}, {"__ref": "39429188"}, {"__ref": "39429199"}, {"__ref": "39429210"}, {"__ref": "39429218"}, {"__ref": "39429225"}, {"__ref": "39429229"}, {"__ref": "39429232"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "39429079": {"name": "Default Typography", "rs": {"__ref": "39429080"}, "preview": null, "uuid": "2nlcU_Kc3J5A", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429080": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "39429094": {"selector": "h1", "style": {"__ref": "39429095"}, "__type": "ThemeStyle"}, "39429095": {"name": "Default \"h1\"", "rs": {"__ref": "39429096"}, "preview": null, "uuid": "krd7LExviEF4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429096": {"values": {"color": "#000000", "font-weight": "900", "font-size": "72px", "line-height": "1", "letter-spacing": "-4px"}, "mixins": [], "__type": "RuleSet"}, "39429102": {"selector": "h2", "style": {"__ref": "39429103"}, "__type": "ThemeStyle"}, "39429103": {"name": "Default \"h2\"", "rs": {"__ref": "39429104"}, "preview": null, "uuid": "kDbka4oZ_lR1", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429104": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "39429110": {"selector": "a", "style": {"__ref": "39429111"}, "__type": "ThemeStyle"}, "39429111": {"name": "Default \"a\"", "rs": {"__ref": "39429112"}, "preview": null, "uuid": "aTlFj8eDfITC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429112": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "39429114": {"selector": "h3", "style": {"__ref": "39429115"}, "__type": "ThemeStyle"}, "39429115": {"name": "Default \"h3\"", "rs": {"__ref": "39429116"}, "preview": null, "uuid": "Z81xLe4nzael", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429116": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "39429122": {"selector": "h4", "style": {"__ref": "39429123"}, "__type": "ThemeStyle"}, "39429123": {"name": "Default \"h4\"", "rs": {"__ref": "39429124"}, "preview": null, "uuid": "ofiYDn9zi4zW", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429124": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "39429130": {"selector": "code", "style": {"__ref": "39429131"}, "__type": "ThemeStyle"}, "39429131": {"name": "Default \"code\"", "rs": {"__ref": "39429132"}, "preview": null, "uuid": "M84PKAC0WwEJ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429132": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "39429155": {"selector": "blockquote", "style": {"__ref": "39429156"}, "__type": "ThemeStyle"}, "39429156": {"name": "Default \"blockquote\"", "rs": {"__ref": "39429157"}, "preview": null, "uuid": "SpVv9Dp8iYT3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429157": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "39429163": {"selector": "pre", "style": {"__ref": "39429164"}, "__type": "ThemeStyle"}, "39429164": {"name": "Default \"pre\"", "rs": {"__ref": "39429165"}, "preview": null, "uuid": "GelKkc1qOSYZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429165": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "39429188": {"selector": "ul", "style": {"__ref": "39429189"}, "__type": "ThemeStyle"}, "39429189": {"name": "Default \"ul\"", "rs": {"__ref": "39429190"}, "preview": null, "uuid": "kxAirjw-YQfJO", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429190": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "39429199": {"selector": "ol", "style": {"__ref": "39429200"}, "__type": "ThemeStyle"}, "39429200": {"name": "Default \"ol\"", "rs": {"__ref": "39429201"}, "preview": null, "uuid": "6NSAse1wpG94v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429201": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "39429210": {"selector": "h5", "style": {"__ref": "39429211"}, "__type": "ThemeStyle"}, "39429211": {"name": "Default \"h5\"", "rs": {"__ref": "39429212"}, "preview": null, "uuid": "Xw7htKTHDzkbE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429212": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "39429218": {"selector": "h6", "style": {"__ref": "39429219"}, "__type": "ThemeStyle"}, "39429219": {"name": "Default \"h6\"", "rs": {"__ref": "39429220"}, "preview": null, "uuid": "oOhjHmW-nQzq0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429220": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "39429225": {"selector": "a:hover", "style": {"__ref": "39429226"}, "__type": "ThemeStyle"}, "39429226": {"name": "Default \"a:hover\"", "rs": {"__ref": "39429227"}, "preview": null, "uuid": "fsPm6Qw_ihvyH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429227": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "39429229": {"selector": "li", "style": {"__ref": "39429230"}, "__type": "ThemeStyle"}, "39429230": {"name": "Default \"li\"", "rs": {"__ref": "39429231"}, "preview": null, "uuid": "ej-AOUGsSFit4", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429231": {"values": {}, "mixins": [], "__type": "RuleSet"}, "39429232": {"selector": "p", "style": {"__ref": "39429233"}, "__type": "ThemeStyle"}, "39429233": {"name": "Default \"p\"", "rs": {"__ref": "39429234"}, "preview": null, "uuid": "7AUd79fTWsb0F", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "39429234": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61910001": {"tag": "div", "name": null, "children": [{"__ref": "61910016"}], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "-px9QoJ1T", "parent": {"__ref": "62876024"}, "locked": null, "vsettings": [{"__ref": "61910002"}], "__type": "TplTag"}, "61910002": {"variants": [{"__ref": "62876025"}], "args": [], "attrs": {}, "rs": {"__ref": "61910003"}, "dataCond": null, "dataRep": null, "text": {"__ref": "61910015"}, "columnsConfig": null, "__type": "VariantSetting"}, "61910003": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "text-align": "center", "font-size": "48px"}, "mixins": [], "__type": "RuleSet"}, "61910015": {"markers": [{"__ref": "61910017"}], "text": "Hello, [child]!", "__type": "RawText"}, "61910016": {"tag": "span", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KIChR39jf", "parent": {"__ref": "61910001"}, "locked": null, "vsettings": [{"__ref": "61910018"}], "__type": "TplTag"}, "61910017": {"tpl": {"__ref": "61910016"}, "position": 7, "length": 7, "__type": "NodeMarker"}, "61910018": {"variants": [{"__ref": "62876025"}], "args": [], "attrs": {}, "rs": {"__ref": "61910019"}, "dataCond": null, "dataRep": null, "text": {"__ref": "61910023"}, "columnsConfig": null, "__type": "VariantSetting"}, "61910019": {"values": {}, "mixins": [], "__type": "RuleSet"}, "61910023": {"expr": {"__ref": "61910026"}, "html": false, "__type": "ExprText"}, "61910026": {"path": ["$ctx", "params", "name"], "fallback": {"__ref": "61910027"}, "__type": "ObjectPath"}, "61910027": {"code": "\"name\"", "fallback": null, "__type": "CustomCode"}, "61910028": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "uRrRw8QyT", "parent": {"__ref": "62876024"}, "locked": null, "vsettings": [{"__ref": "61910029"}], "__type": "TplTag"}, "61910029": {"variants": [{"__ref": "62876025"}], "args": [], "attrs": {}, "rs": {"__ref": "61910030"}, "dataCond": null, "dataRep": null, "text": {"__ref": "61910040"}, "columnsConfig": null, "__type": "VariantSetting"}, "61910030": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "text-align": "center", "font-size": "36px", "color": "#FF0000"}, "mixins": [], "__type": "RuleSet"}, "61910040": {"expr": {"__ref": "61910043"}, "html": false, "__type": "ExprText"}, "61910043": {"path": ["$ctx", "query", "q"], "fallback": {"__ref": "61910044"}, "__type": "ObjectPath"}, "61910044": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "61910045": {"name": null, "component": {"__ref": "62876001"}, "uuid": "g89NAzFnf", "parent": {"__ref": "62876024"}, "locked": null, "vsettings": [{"__ref": "61910046"}], "__type": "TplComponent"}, "61910046": {"variants": [{"__ref": "62876025"}], "args": [{"__ref": "61910051"}, {"__ref": "61910056"}], "attrs": {}, "rs": {"__ref": "61910047"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "61910047": {"values": {"max-width": "100%", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "61910051": {"param": {"__ref": "62876002"}, "expr": {"__ref": "61910054"}, "__type": "Arg"}, "61910054": {"path": ["$ctx", "params", "name"], "fallback": {"__ref": "61910055"}, "__type": "ObjectPath"}, "61910055": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "61910056": {"param": {"__ref": "62876003"}, "expr": {"__ref": "61910059"}, "__type": "Arg"}, "61910059": {"path": ["$ctx", "query", "q"], "fallback": {"__ref": "61910060"}, "__type": "ObjectPath"}, "61910060": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "61910061": {"markers": [], "text": "Say hello to someone:", "__type": "RawText"}, "61910062": {"tag": "a", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jIEHLczxn", "parent": {"__ref": "39429004"}, "locked": null, "vsettings": [{"__ref": "61910063"}], "__type": "TplTag"}, "61910063": {"variants": [{"__ref": "39429007"}], "args": [], "attrs": {"href": {"__ref": "61910091"}}, "rs": {"__ref": "61910064"}, "dataCond": null, "dataRep": {"__ref": "61910070"}, "text": {"__ref": "61910076"}, "columnsConfig": null, "__type": "VariantSetting"}, "61910064": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px", "font-size": "24px"}, "mixins": [], "__type": "RuleSet"}, "61910070": {"element": {"__ref": "61910071"}, "index": {"__ref": "61910072"}, "collection": {"__ref": "61910074"}, "__type": "Rep"}, "61910071": {"name": "currentItem", "uuid": "gQtrW-BAa", "__type": "Var"}, "61910072": {"name": "currentIndex", "uuid": "uppc6uLO7T", "__type": "Var"}, "61910074": {"code": "([\"<PERSON>\", \"<PERSON><PERSON><PERSON>\", \"<PERSON>\"])", "fallback": {"__ref": "61910075"}, "__type": "CustomCode"}, "61910075": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "61910076": {"expr": {"__ref": "61910079"}, "html": false, "__type": "ExprText"}, "61910079": {"path": ["currentItem"], "fallback": {"__ref": "61910080"}, "__type": "ObjectPath"}, "61910080": {"code": "\"Enter some text\"", "fallback": null, "__type": "CustomCode"}, "61910091": {"code": "(`/hello/${currentItem}?q=${currentItem.length}`)", "fallback": {"__ref": "61910092"}, "__type": "CustomCode"}, "61910092": {"page": {"__ref": "62876022"}, "params": {}, "__type": "PageHref", "query": {}, "fragment": null}, "62876001": {"uuid": "NYgUxD3cLk", "name": "hostless-plasmic-head", "params": [{"__ref": "62876002"}, {"__ref": "62876003"}, {"__ref": "62876004"}, {"__ref": "62876005"}], "states": [], "tplTree": {"__ref": "62876006"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "62876007"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "62876008"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "62876002": {"type": {"__ref": "62876010"}, "variable": {"__ref": "62876009"}, "uuid": "91oFsGuJ3G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "62876003": {"type": {"__ref": "62876012"}, "variable": {"__ref": "62876011"}, "uuid": "krGW1kGedD", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "62876004": {"type": {"__ref": "62876014"}, "variable": {"__ref": "62876013"}, "uuid": "SY5lOUIzgo", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "62876005": {"type": {"__ref": "62876016"}, "variable": {"__ref": "62876015"}, "uuid": "loca-tZ8iY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "62876006": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T1zoVK8dll", "parent": null, "locked": null, "vsettings": [{"__ref": "62876017"}], "__type": "TplTag"}, "62876007": {"uuid": "0znYo_pHJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62876008": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "62876009": {"name": "title", "uuid": "VyIm0_aYsK", "__type": "Var"}, "62876010": {"name": "text", "__type": "Text"}, "62876011": {"name": "description", "uuid": "btIhlSYjZv", "__type": "Var"}, "62876012": {"name": "text", "__type": "Text"}, "62876013": {"name": "image", "uuid": "ZhYDSzCXhT", "__type": "Var"}, "62876014": {"name": "img", "__type": "Img"}, "62876015": {"name": "canonical", "uuid": "MgQA1Ut40S", "__type": "Var"}, "62876016": {"name": "text", "__type": "Text"}, "62876017": {"variants": [{"__ref": "62876007"}], "args": [], "attrs": {}, "rs": {"__ref": "62876019"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62876019": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "62876022": {"uuid": "gueO5siQSF", "name": "Hello", "params": [], "states": [], "tplTree": {"__ref": "62876024"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "62876025"}], "variantGroups": [], "pageMeta": {"__ref": "62876026"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "62876023": {"component": {"__ref": "62876022"}, "matrix": {"__ref": "62876027"}, "customMatrix": {"__ref": "3374503"}, "__type": "PageArena"}, "62876024": {"tag": "div", "name": null, "children": [{"__ref": "61910001"}, {"__ref": "61910028"}, {"__ref": "61910045"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KPc0_f6nx", "parent": null, "locked": null, "vsettings": [{"__ref": "62876028"}], "__type": "TplTag"}, "62876025": {"uuid": "Dzpm0dVQJI", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "62876026": {"path": "/hello/[name]", "params": {"name": "World"}, "query": {"q": "42"}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "62876027": {"rows": [{"__ref": "62876030"}], "__type": "ArenaFrameGrid"}, "62876028": {"variants": [{"__ref": "62876025"}], "args": [], "attrs": {}, "rs": {"__ref": "62876031"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62876030": {"cols": [{"__ref": "62876032"}, {"__ref": "62876033"}], "rowKey": {"__ref": "62876025"}, "__type": "ArenaFrameRow"}, "62876031": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "justify-content": "center", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "62876032": {"frame": {"__ref": "62876041"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62876033": {"frame": {"__ref": "62876042"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62876041": {"uuid": "tt5nKbs_yr", "width": 1440, "height": 1024, "container": {"__ref": "62876043"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "62876025"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "62876042": {"uuid": "mZZ0FIotAH", "width": 414, "height": 736, "container": {"__ref": "62876044"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "62876025"}], "pinnedGlobalVariants": {"iyZ4C-t2qhTHE": true}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "62876043": {"name": null, "component": {"__ref": "62876022"}, "uuid": "4KdSCRT4cl", "parent": null, "locked": null, "vsettings": [{"__ref": "62876045"}], "__type": "TplComponent"}, "62876044": {"name": null, "component": {"__ref": "62876022"}, "uuid": "gUAdoUYXu0", "parent": null, "locked": null, "vsettings": [{"__ref": "62876046"}], "__type": "TplComponent"}, "62876045": {"variants": [{"__ref": "39429070"}], "args": [], "attrs": {}, "rs": {"__ref": "62876047"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62876046": {"variants": [{"__ref": "39429070"}], "args": [], "attrs": {}, "rs": {"__ref": "62876048"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "62876047": {"values": {}, "mixins": [], "__type": "RuleSet"}, "62876048": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "251-add-data-tokens"}]]