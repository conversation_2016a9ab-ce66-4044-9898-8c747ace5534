[["2T7kxx3QhjWFCxgEGTx4pw", {"root": "34512001", "map": {"4635801": {"rows": [{"__ref": "4635802"}], "__type": "ArenaFrameGrid"}, "4635802": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "30184001": {"uuid": "v9oZknpuLY", "name": "Badge", "params": [{"__ref": "30184002"}], "states": [], "tplTree": {"__ref": "30184003"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "30184004"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "30184005"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "30184002": {"type": {"__ref": "30184007"}, "variable": {"__ref": "30184006"}, "uuid": "4WzG3veQ8R", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "30184003": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_iHh1b02Oa", "parent": null, "locked": null, "vsettings": [{"__ref": "30184008"}], "__type": "TplTag"}, "30184004": {"uuid": "jF5oAD8ma", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "30184005": {"importPath": "", "defaultExport": false, "displayName": null, "importName": null, "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": true, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "30184006": {"name": "name", "uuid": "iy-0P5ktJM", "__type": "Var"}, "30184007": {"name": "text", "__type": "Text"}, "30184008": {"variants": [{"__ref": "30184004"}], "args": [], "attrs": {}, "rs": {"__ref": "30184010"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "30184010": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "30184013": {"name": null, "component": {"__ref": "30184001"}, "uuid": "pi-r5nUbd", "parent": {"__ref": "34512003"}, "locked": null, "vsettings": [{"__ref": "30184014"}], "__type": "TplComponent"}, "30184014": {"variants": [{"__ref": "34512006"}], "args": [{"__ref": "30184019"}], "attrs": {}, "rs": {"__ref": "30184015"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "30184015": {"values": {"max-width": "100%", "object-fit": "cover", "position": "relative", "background": "linear-gradient(#33FF00, #33FF00)"}, "mixins": [], "__type": "RuleSet"}, "30184019": {"param": {"__ref": "30184002"}, "expr": {"__ref": "30184020"}, "__type": "Arg"}, "30184020": {"code": "\"Plasmic\"", "fallback": null, "__type": "CustomCode"}, "34512001": {"components": [{"__ref": "34512002"}, {"__ref": "30184001"}, {"__ref": "35978001"}, {"__ref": "35978002"}], "arenas": [], "pageArenas": [{"__ref": "34512049"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "34512043"}], "userManagedFonts": [], "globalVariant": {"__ref": "34512056"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "34512064"}], "activeTheme": {"__ref": "34512064"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "34512043"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [{"__ref": "35978003"}], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "34512002": {"uuid": "gcVHhMv1e9V5", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "34512003"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "34512006"}], "variantGroups": [], "pageMeta": {"__ref": "34512048"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "34512003": {"tag": "div", "name": null, "children": [{"__ref": "34518010"}, {"__ref": "30184013"}, {"__ref": "35978039"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QyTTK0P3HGtl", "parent": null, "locked": null, "vsettings": [{"__ref": "34512026"}, {"__ref": "34512040"}], "__type": "TplTag"}, "34512006": {"uuid": "2l8DLrpvBhPM", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "34512026": {"variants": [{"__ref": "34512006"}], "args": [], "attrs": {}, "rs": {"__ref": "34512027"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "34512027": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "width": "stretch", "height": "stretch", "justify-content": "flex-start", "align-items": "center", "padding-top": "96px", "padding-right": "24px", "padding-bottom": "96px", "padding-left": "24px", "row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "34512040": {"variants": [{"__ref": "34512041"}], "args": [], "attrs": {}, "rs": {"__ref": "34512047"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "34512041": {"uuid": "G28aiX1thYbgY", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "34512043"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "34512043": {"type": "global-screen", "param": {"__ref": "34512044"}, "uuid": "RtdJ9uZGLH8Ut", "variants": [{"__ref": "34512041"}], "multi": true, "__type": "GlobalVariantGroup"}, "34512044": {"type": {"__ref": "34512046"}, "variable": {"__ref": "34512045"}, "uuid": "ONrfOZtVfuCCR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "34512045": {"name": "Screen", "uuid": "bWa2e9jwR4MWz", "__type": "Var"}, "34512046": {"name": "text", "__type": "Text"}, "34512047": {"values": {}, "mixins": [], "__type": "RuleSet"}, "34512048": {"path": "/badge", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "34512049": {"component": {"__ref": "34512002"}, "matrix": {"__ref": "34512050"}, "customMatrix": {"__ref": "4635801"}, "__type": "PageArena"}, "34512050": {"rows": [{"__ref": "34512051"}], "__type": "ArenaFrameGrid"}, "34512051": {"cols": [{"__ref": "34512052"}], "rowKey": {"__ref": "34512006"}, "__type": "ArenaFrameRow"}, "34512052": {"frame": {"__ref": "34512053"}, "cellKey": null, "__type": "ArenaFrameCell"}, "34512053": {"uuid": "uNBGiQklqHWjy", "width": 1440, "height": 768, "container": {"__ref": "34512054"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "34512006"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "34512054": {"name": null, "component": {"__ref": "34512002"}, "uuid": "eOe7hZ2Sv1Pfj", "parent": null, "locked": null, "vsettings": [{"__ref": "34512055"}], "__type": "TplComponent"}, "34512055": {"variants": [{"__ref": "34512056"}], "args": [], "attrs": {}, "rs": {"__ref": "34512058"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "34512056": {"uuid": "e9vwa5K-Mst-2", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "34512058": {"values": {}, "mixins": [], "__type": "RuleSet"}, "34512064": {"defaultStyle": {"__ref": "34512065"}, "styles": [{"__ref": "34512080"}, {"__ref": "34512088"}, {"__ref": "34512097"}, {"__ref": "34512101"}, {"__ref": "34512110"}, {"__ref": "34512119"}, {"__ref": "34512144"}, {"__ref": "34512152"}, {"__ref": "34512177"}, {"__ref": "34512188"}, {"__ref": "34512199"}, {"__ref": "34512208"}, {"__ref": "34512216"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "34512065": {"name": "Default Typography", "rs": {"__ref": "34512066"}, "preview": null, "uuid": "vXZXanGPGgfA", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512066": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "34512080": {"selector": "h1", "style": {"__ref": "34512081"}, "__type": "ThemeStyle"}, "34512081": {"name": "Default \"h1\"", "rs": {"__ref": "34512082"}, "preview": null, "uuid": "2G6BcQ6zWNal", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512082": {"values": {"color": "#000000", "font-weight": "900", "font-size": "72px", "line-height": "1", "letter-spacing": "-4px"}, "mixins": [], "__type": "RuleSet"}, "34512088": {"selector": "h2", "style": {"__ref": "34512089"}, "__type": "ThemeStyle"}, "34512089": {"name": "Default \"h2\"", "rs": {"__ref": "34512090"}, "preview": null, "uuid": "rqkd1Whlig74", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512090": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "34512097": {"selector": "a", "style": {"__ref": "34512098"}, "__type": "ThemeStyle"}, "34512098": {"name": "Default \"a\"", "rs": {"__ref": "34512099"}, "preview": null, "uuid": "Sa6HFxCXxRKX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512099": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "34512101": {"selector": "h3", "style": {"__ref": "34512102"}, "__type": "ThemeStyle"}, "34512102": {"name": "Default \"h3\"", "rs": {"__ref": "34512103"}, "preview": null, "uuid": "JJxUzQip0bGV", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512103": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "34512110": {"selector": "h4", "style": {"__ref": "34512111"}, "__type": "ThemeStyle"}, "34512111": {"name": "Default \"h4\"", "rs": {"__ref": "34512112"}, "preview": null, "uuid": "ElkFgY97me95", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512112": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "34512119": {"selector": "code", "style": {"__ref": "34512120"}, "__type": "ThemeStyle"}, "34512120": {"name": "Default \"code\"", "rs": {"__ref": "34512121"}, "preview": null, "uuid": "DOCZBD-M8czH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512121": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "34512144": {"selector": "blockquote", "style": {"__ref": "34512145"}, "__type": "ThemeStyle"}, "34512145": {"name": "Default \"blockquote\"", "rs": {"__ref": "34512146"}, "preview": null, "uuid": "XK5jHwXjKoTE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512146": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "34512152": {"selector": "pre", "style": {"__ref": "34512153"}, "__type": "ThemeStyle"}, "34512153": {"name": "Default \"pre\"", "rs": {"__ref": "34512154"}, "preview": null, "uuid": "CdtMTpOwW8Nx", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512154": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "34512177": {"selector": "ul", "style": {"__ref": "34512178"}, "__type": "ThemeStyle"}, "34512178": {"name": "Default \"ul\"", "rs": {"__ref": "34512179"}, "preview": null, "uuid": "utuIb11WNoMGY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512179": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "34512188": {"selector": "ol", "style": {"__ref": "34512189"}, "__type": "ThemeStyle"}, "34512189": {"name": "Default \"ol\"", "rs": {"__ref": "34512190"}, "preview": null, "uuid": "IHeLuvAwDuKqI", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512190": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "34512199": {"selector": "h5", "style": {"__ref": "34512200"}, "__type": "ThemeStyle"}, "34512200": {"name": "Default \"h5\"", "rs": {"__ref": "34512201"}, "preview": null, "uuid": "FDAcJ5E6guaJh", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512201": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "34512208": {"selector": "h6", "style": {"__ref": "34512209"}, "__type": "ThemeStyle"}, "34512209": {"name": "Default \"h6\"", "rs": {"__ref": "34512210"}, "preview": null, "uuid": "FhKsNfK9SyBbk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512210": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "34512216": {"selector": "a:hover", "style": {"__ref": "34512217"}, "__type": "ThemeStyle"}, "34512217": {"name": "Default \"a:hover\"", "rs": {"__ref": "34512218"}, "preview": null, "uuid": "CndjW2fbttfeR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "34512218": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "34518010": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "aUynQhqZs", "parent": {"__ref": "34512003"}, "locked": null, "vsettings": [{"__ref": "34518011"}], "__type": "TplTag"}, "34518011": {"variants": [{"__ref": "34512006"}], "args": [], "attrs": {}, "rs": {"__ref": "34518012"}, "dataCond": null, "dataRep": null, "text": {"__ref": "34518013"}, "columnsConfig": null, "__type": "VariantSetting"}, "34518012": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "800px"}, "mixins": [], "__type": "RuleSet"}, "34518013": {"markers": [], "text": "You won't believe what happens next.", "__type": "RawText"}, "35978001": {"uuid": "JPcGsO9CzY", "name": "<PERSON><PERSON><PERSON>", "params": [{"__ref": "35978004"}], "states": [], "tplTree": {"__ref": "35978005"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "35978006"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "35978007"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "35978002": {"uuid": "ljWCAon8Cy", "name": "FetcherCredentialsProvider", "params": [{"__ref": "35978008"}, {"__ref": "35978009"}], "states": [], "tplTree": {"__ref": "35978010"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "35978011"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "35978012"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "35978003": {"name": null, "component": {"__ref": "35978002"}, "uuid": "4dmJ34ppXD", "parent": null, "locked": null, "vsettings": [{"__ref": "35978013"}], "__type": "TplComponent"}, "35978004": {"type": {"__ref": "35978015"}, "tplSlot": {"__ref": "35978016"}, "variable": {"__ref": "35978014"}, "uuid": "ibiimv89eW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "35978005": {"tag": "div", "name": null, "children": [{"__ref": "35978016"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "hetra3XBHP", "parent": null, "locked": null, "vsettings": [{"__ref": "35978017"}], "__type": "TplTag"}, "35978006": {"uuid": "Fw4YOD6DH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "35978007": {"importPath": "", "defaultExport": false, "displayName": null, "importName": null, "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": true, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "35978008": {"type": {"__ref": "35978020"}, "variable": {"__ref": "35978019"}, "uuid": "kCOB3cBtEa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "35978009": {"type": {"__ref": "35978022"}, "tplSlot": {"__ref": "35978023"}, "variable": {"__ref": "35978021"}, "uuid": "42KyLMgND7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "35978010": {"tag": "div", "name": null, "children": [{"__ref": "35978023"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "WiaqUPiont", "parent": null, "locked": null, "vsettings": [{"__ref": "35978024"}], "__type": "TplTag"}, "35978011": {"uuid": "lIgnrgSR6r", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "35978012": {"importPath": "", "defaultExport": false, "displayName": null, "importName": null, "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": false, "isContext": true, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": true, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "35978013": {"variants": [{"__ref": "34512056"}], "args": [{"__ref": "35978037"}], "attrs": {}, "rs": {"__ref": "35978026"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "35978014": {"name": "children", "uuid": "7aUkB30Qmn", "__type": "Var"}, "35978015": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "35978016": {"param": {"__ref": "35978004"}, "defaultContents": [], "uuid": "IVginVD8DI", "parent": {"__ref": "35978005"}, "locked": null, "vsettings": [{"__ref": "35978027"}], "__type": "TplSlot"}, "35978017": {"variants": [{"__ref": "35978006"}], "args": [], "attrs": {}, "rs": {"__ref": "35978028"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "35978019": {"name": "token", "uuid": "ZY3OXiw-3Y", "__type": "Var"}, "35978020": {"name": "text", "__type": "Text"}, "35978021": {"name": "children", "uuid": "sxlMGq_SAB", "__type": "Var"}, "35978022": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "35978023": {"param": {"__ref": "35978009"}, "defaultContents": [], "uuid": "d2mYDONeX-", "parent": {"__ref": "35978010"}, "locked": null, "vsettings": [{"__ref": "35978029"}], "__type": "TplSlot"}, "35978024": {"variants": [{"__ref": "35978011"}], "args": [], "attrs": {}, "rs": {"__ref": "35978030"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "35978026": {"values": {}, "mixins": [], "__type": "RuleSet"}, "35978027": {"variants": [{"__ref": "35978006"}], "args": [], "attrs": {}, "rs": {"__ref": "35978031"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "35978028": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "35978029": {"variants": [{"__ref": "35978011"}], "args": [], "attrs": {}, "rs": {"__ref": "35978034"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "35978030": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "35978031": {"values": {}, "mixins": [], "__type": "RuleSet"}, "35978034": {"values": {}, "mixins": [], "__type": "RuleSet"}, "35978037": {"param": {"__ref": "35978008"}, "expr": {"__ref": "35978038"}, "__type": "Arg"}, "35978038": {"code": "\"super-secret\"", "fallback": null, "__type": "CustomCode"}, "35978039": {"name": null, "component": {"__ref": "35978001"}, "uuid": "qOGFa5C_M", "parent": {"__ref": "34512003"}, "locked": null, "vsettings": [{"__ref": "35978040"}], "__type": "TplComponent"}, "35978040": {"variants": [{"__ref": "34512006"}], "args": [{"__ref": "35978041"}], "attrs": {}, "rs": {"__ref": "35978042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "35978041": {"param": {"__ref": "35978004"}, "expr": {"__ref": "35978047"}, "__type": "Arg"}, "35978042": {"values": {"max-width": "100%", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "35978047": {"tpl": [{"__ref": "35978048"}], "__type": "RenderExpr"}, "35978048": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "nNxsLQ7VB", "parent": {"__ref": "35978039"}, "locked": null, "vsettings": [{"__ref": "35978049"}], "__type": "TplTag"}, "35978049": {"variants": [{"__ref": "34512006"}], "args": [], "attrs": {}, "rs": {"__ref": "35978050"}, "dataCond": null, "dataRep": null, "text": {"__ref": "35978053"}, "columnsConfig": null, "__type": "VariantSetting"}, "35978050": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "35978053": {"markers": [], "text": "I'm in the fetcher!", "__type": "RawText"}}, "deps": [], "version": "251-add-data-tokens"}]]