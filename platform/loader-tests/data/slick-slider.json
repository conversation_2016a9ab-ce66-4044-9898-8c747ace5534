[["1397401c-b15c-47b2-8659-9b44c397e887", {"root": "_ulhJT6MD4vt", "map": {"SyWL3qqJsmzT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MjSCeVR3_3Hq": {"name": "Default Typography", "rs": {"__ref": "SyWL3qqJsmzT"}, "preview": null, "uuid": "siau7nfWNqfK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "NFQceDYItLbu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "u7JAC32LSb-b": {"rs": {"__ref": "NFQceDYItLbu"}, "__type": "ThemeLayoutSettings"}, "WVqnko8qmdTN": {"defaultStyle": {"__ref": "MjSCeVR3_3Hq"}, "styles": [], "layout": {"__ref": "u7JAC32LSb-b"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "4XJEuEac9uPH": {"uuid": "LLZpJNz2A-ob", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "RDfYw54DLI_5": {"components": [{"__ref": "wKqxNVJ8Kt1w"}], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "4XJEuEac9uPH"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "WVqnko8qmdTN"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "5FY4hAf3PClt"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "wKqxNVJ8Kt1w": {"uuid": "WhE5kIYYO79P", "name": "hostless-slider", "params": [{"__ref": "ZEE1qbzSeuSN"}, {"__ref": "5axoDAZrXPhs"}, {"__ref": "mSkPF1UgISd-"}, {"__ref": "EueLVsoJC42u"}, {"__ref": "NSA5cI5Ck4Nz"}, {"__ref": "sjQ4vWY_DMBw"}, {"__ref": "UP_XQZGxG1Vt"}, {"__ref": "GrhfcJXcNbJ8"}, {"__ref": "CwHoZCvIBKH_"}, {"__ref": "tKSAZ4uR2wS0"}, {"__ref": "L6zftZNoipgU"}, {"__ref": "yx0y8GxlAfUW"}, {"__ref": "dYI44Bu4lcbE"}, {"__ref": "IMrb6NF2ZNw1"}, {"__ref": "TfzmHaaJmGOS"}, {"__ref": "-fwVgg306w5x"}, {"__ref": "I7gp9gLhj7qD"}, {"__ref": "Bao4rHYBC4RO"}, {"__ref": "RJGEczmjJjKT"}, {"__ref": "o_9lOZ-ydxCT"}, {"__ref": "n6GJIVnE9uPa"}, {"__ref": "PrPLAqdfWMBe"}, {"__ref": "ascjkNfMaSx8"}, {"__ref": "C3XaNeM45qzS"}, {"__ref": "rPgXZhfIYKSI"}, {"__ref": "3ENPIcWv4Xbq"}, {"__ref": "BCF3ABVS2URJ"}, {"__ref": "H4lDtfeTaGhC"}, {"__ref": "UBLl_UMg5fat"}, {"__ref": "roJOtp1BChlJ"}, {"__ref": "4hmw3KXDTGc5"}, {"__ref": "Vp_hXDgFuwu5"}, {"__ref": "xrmSZq_26vMg"}, {"__ref": "j1N2YJ-pQEPP"}, {"__ref": "NwFi-aKCT50n"}, {"__ref": "pJZZ4Njd8kAK"}, {"__ref": "HSsTXJSbs7q2"}], "states": [{"__ref": "biSqrpHXFLwK"}], "tplTree": {"__ref": "DTYAAI-RG-nX"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "SaWWm8HEPpbN"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "kRk0NoNgigis"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "ZEE1qbzSeuSN": {"type": {"__ref": "ejSM67D97QE-"}, "tplSlot": {"__ref": "jM2lMtPMnTOF"}, "variable": {"__ref": "-Jl8LJi4HATR"}, "uuid": "VXGWBDt-V-vT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5axoDAZrXPhs": {"type": {"__ref": "ymPXtZUQRRxI"}, "variable": {"__ref": "kmBS0J5ysIVe"}, "uuid": "1xWj3bP3AC8B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Accessibility", "about": "Enables tabbing and arrow key navigation", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "mSkPF1UgISd-": {"type": {"__ref": "p31Xajw1M8lH"}, "variable": {"__ref": "hF2VURvrWGSH"}, "uuid": "RF34FwuP6e2L", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Adaptive Height", "about": "Adjust the slide's height automatically", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "EueLVsoJC42u": {"type": {"__ref": "vpad6Ze_RsGy"}, "variable": {"__ref": "1SkR9PXr9AtE"}, "uuid": "r3jNguouTbfh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Arrows", "about": "Show next/prev arrows", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "NSA5cI5Ck4Nz": {"type": {"__ref": "nOglYMQFykdi"}, "variable": {"__ref": "a2hOs6RSRu6k"}, "uuid": "wUN6JHq4aS95", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Color of next/prev arrow buttons", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "sjQ4vWY_DMBw": {"type": {"__ref": "1SURNHBv2-O6"}, "variable": {"__ref": "2ddPT0mquGUp"}, "uuid": "smoDzRma8b3J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Auto Play", "about": "Automatically start scrolling", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "UP_XQZGxG1Vt": {"type": {"__ref": "Ac5ayIZMuPDK"}, "variable": {"__ref": "UYEWyLwkK47H"}, "uuid": "VpCrS2BQ5ej9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Auto Play Speed", "about": "Delay between each auto scroll, in milliseconds", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "GrhfcJXcNbJ8": {"type": {"__ref": "I9TPrvJN0Kig"}, "variable": {"__ref": "K0dshdRKRRl4"}, "uuid": "NlawPXztRcrm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Center Mode", "about": "Enables centered view with partial prev/next slides. Use with odd numbered slidesToShow counts", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "CwHoZCvIBKH_": {"type": {"__ref": "zbc45jpuu6cs"}, "variable": {"__ref": "NFHrAq0fFHC2"}, "uuid": "ABctGXXcBfy3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Center Padding", "about": "Side padding when in center mode (px or %)", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "tKSAZ4uR2wS0": {"type": {"__ref": "QzhyaskdrOeF"}, "variable": {"__ref": "-G7Z39mbqpMl"}, "uuid": "yIK0Xulh5CPA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Dots", "about": "Show dots for each slide", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "L6zftZNoipgU": {"type": {"__ref": "7buRmakOyfIe"}, "variable": {"__ref": "FloXDWAgrv52"}, "uuid": "x7NKOtVYGkh8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Draggable", "about": "Enables mouse dragging on desktop", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "yx0y8GxlAfUW": {"type": {"__ref": "90W9qywy2zN_"}, "variable": {"__ref": "y5gLfJoxEryl"}, "uuid": "mu0CGXFvYy4L", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Easing", "about": "Easing method for transition", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "dYI44Bu4lcbE": {"type": {"__ref": "jgavHjxTrwST"}, "variable": {"__ref": "QX-VAY000AIo"}, "uuid": "tyjdF6-d2hl_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Easing", "about": "Easing method for transition", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "IMrb6NF2ZNw1": {"type": {"__ref": "TTiw4x7lnIb2"}, "variable": {"__ref": "XO4Y_Vx4O-Ow"}, "uuid": "gHKZVeZtg1MB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Fade", "about": "Cross-fade between slides", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "TfzmHaaJmGOS": {"type": {"__ref": "s8xyh43h5zaL"}, "variable": {"__ref": "JFRl4JVHui-W"}, "uuid": "mnei0tdfrEGM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Focus On Select", "about": "Go to slide on click", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-fwVgg306w5x": {"type": {"__ref": "ryUx4Xwpt2eh"}, "variable": {"__ref": "Kw4BjTsf4GR9"}, "uuid": "TazM5qvLO2s4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Infinite", "about": "Infinitely wrap around contents", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "I7gp9gLhj7qD": {"type": {"__ref": "ITIEbGASLDMK"}, "variable": {"__ref": "INiS0vfaa9zY"}, "uuid": "HltYaQBOck34", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Lazy Load", "about": "Load images or render components on demand or progressively", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Bao4rHYBC4RO": {"type": {"__ref": "DK5ZnzwnDXcK"}, "variable": {"__ref": "gWpWQlDbIf5a"}, "uuid": "8QjfRT2zxc5m", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Pause On Dots Hover", "about": "Prevents autoplay while hovering on dots", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "RJGEczmjJjKT": {"type": {"__ref": "F6KafuKTlQnI"}, "variable": {"__ref": "w6ZG6bOscQuI"}, "uuid": "kYYPZzdz5aZu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Pause On Focus", "about": "Prevents autoplay while focused on slides", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "o_9lOZ-ydxCT": {"type": {"__ref": "gMMKvKC8oiQA"}, "variable": {"__ref": "DZGEOVWcDs5v"}, "uuid": "ndoYvOXpbtbd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Pause On Hover", "about": "Prevents autoplay while hovering on track", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "n6GJIVnE9uPa": {"type": {"__ref": "4IzhrndeTWxH"}, "variable": {"__ref": "YObcqDkKElTE"}, "uuid": "uEXQknK1KVss", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Rows", "about": "Number of rows per slide (enables grid mode)", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "PrPLAqdfWMBe": {"type": {"__ref": "iXxF67fW0nU1"}, "variable": {"__ref": "-ohGZ4LNJYN4"}, "uuid": "CVQrMVUuPq3w", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Reverse", "about": "Reverses the slide order", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "ascjkNfMaSx8": {"type": {"__ref": "QFEZqqYOsy75"}, "variable": {"__ref": "oNl1X-gjOXIB"}, "uuid": "3wbbZotfzFhQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Slides Per Row", "about": "Number of slides to display in grid mode, this is useful with rows option", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "C3XaNeM45qzS": {"type": {"__ref": "4RI0bGLm77Hv"}, "variable": {"__ref": "PViksBkI749A"}, "uuid": "LBF9yjF_G9BP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Slides To <PERSON>roll", "about": "Number of slides to scroll at once", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "rPgXZhfIYKSI": {"type": {"__ref": "ExnARtun_iyL"}, "variable": {"__ref": "YKrGqElm1jcB"}, "uuid": "E2uvWQR24C68", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Slides To Show", "about": "Number of slides to show in one frame", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "3ENPIcWv4Xbq": {"type": {"__ref": "0NpWPUwuZ3RW"}, "variable": {"__ref": "L4MM6KsG5mIq"}, "uuid": "vjoxDiktHc0n", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Speed", "about": "Transition speed in milliseconds", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "BCF3ABVS2URJ": {"type": {"__ref": "0p4iEpPzv173"}, "variable": {"__ref": "osWp8ESsmb7a"}, "uuid": "fnzV0PCb9Ii2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Swipe", "about": "Enable swiping to change slides", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "H4lDtfeTaGhC": {"type": {"__ref": "GLEEhqge6_D5"}, "variable": {"__ref": "tf-cIpyKBDy3"}, "uuid": "Doyfqb655irb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Swipe To Slide", "about": "Enable drag/swipe irrespective of 'slidesToScroll'", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "UBLl_UMg5fat": {"type": {"__ref": "eA_cTMPN4mzp"}, "variable": {"__ref": "QopCldq4VZF4"}, "uuid": "8sr2VEV_v2Ek", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Touch Move", "about": "Enable slide moving on touch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "roJOtp1BChlJ": {"type": {"__ref": "dXmvAgCbAsOk"}, "variable": {"__ref": "nqlr5lVneSbi"}, "uuid": "7TPcNaKe-dkl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Touch Threshold", "about": "Swipe distance threshold in pixels", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "4hmw3KXDTGc5": {"type": {"__ref": "E0GxOa9se5jn"}, "variable": {"__ref": "JXV7Psvkz9BE"}, "uuid": "vRGxBwKtVulT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Use CSS", "about": "Enable/Disable CSS Transitions", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Vp_hXDgFuwu5": {"type": {"__ref": "hYbzM3vkurKP"}, "variable": {"__ref": "9LkV-iIz6RZe"}, "uuid": "9WN3-UP41QjS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Use Transform", "about": "Enable/Disable CSS Transforms", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "xrmSZq_26vMg": {"type": {"__ref": "J02zorS2M0tZ"}, "variable": {"__ref": "_MgURsKv5Vd7"}, "uuid": "koJVISH2AXPv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable Width", "about": "Variable width slides", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "j1N2YJ-pQEPP": {"type": {"__ref": "lzv2WUy4WsH8"}, "variable": {"__ref": "0rdr3SVlT4xM"}, "uuid": "llrRZpAmWxFM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Vertical", "about": "Vertical slide mode", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "NwFi-aKCT50n": {"type": {"__ref": "ob4rgKlnhfp9"}, "variable": {"__ref": "6KTA8fbm_idO"}, "uuid": "sM7mqFwRV1zU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "DTYAAI-RG-nX": {"tag": "div", "name": null, "children": [{"__ref": "jM2lMtPMnTOF"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "k8sKoLjPwUyP", "parent": null, "locked": null, "vsettings": [{"__ref": "tO5605NqxXAj"}], "__type": "TplTag"}, "SaWWm8HEPpbN": {"uuid": "4Js8NsZh5_IZ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kRk0NoNgigis": {"importPath": "@plasmicpkgs/react-slick", "defaultExport": false, "displayName": "Slider Carousel", "importName": "SliderWrapper", "description": "[See tutorial video](https://www.youtube.com/watch?v=GMgXLbNHX8c)", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": {"__ref": "pVD2xi5KF87W"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": true, "isRepeatable": true, "styleSections": null, "helpers": {"__ref": "oQXzi44tP6I4"}, "defaultSlotContents": {"children": [{"type": "vbox", "children": {"type": "img", "src": "https://static1.plasmic.app/components/react-slick/slide1.png", "styles": {"maxWidth": "100%"}}}, {"type": "vbox", "children": {"type": "img", "src": "https://static1.plasmic.app/components/react-slick/slide2.png", "styles": {"maxWidth": "100%"}}}, {"type": "vbox", "children": {"type": "img", "src": "https://static1.plasmic.app/components/react-slick/slide3.png", "styles": {"maxWidth": "100%"}}}]}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "ejSM67D97QE-": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "-Jl8LJi4HATR": {"name": "children", "uuid": "ZYZSMKqwpMTE", "__type": "Var"}, "ymPXtZUQRRxI": {"name": "bool", "__type": "BoolType"}, "kmBS0J5ysIVe": {"name": "accessibility", "uuid": "xUpNpAJo3cJo", "__type": "Var"}, "p31Xajw1M8lH": {"name": "bool", "__type": "BoolType"}, "hF2VURvrWGSH": {"name": "adaptiveHeight", "uuid": "1FaZCG1kYHHh", "__type": "Var"}, "vpad6Ze_RsGy": {"name": "bool", "__type": "BoolType"}, "1SkR9PXr9AtE": {"name": "arrows", "uuid": "93iZv1GWVIUG", "__type": "Var"}, "nOglYMQFykdi": {"name": "color", "noDeref": false, "__type": "ColorPropType"}, "a2hOs6RSRu6k": {"name": "arrowColor", "uuid": "TTkzZoAHqOWs", "__type": "Var"}, "1SURNHBv2-O6": {"name": "bool", "__type": "BoolType"}, "2ddPT0mquGUp": {"name": "autoplay", "uuid": "KxAHziL7NXBi", "__type": "Var"}, "Ac5ayIZMuPDK": {"name": "num", "__type": "<PERSON><PERSON>"}, "UYEWyLwkK47H": {"name": "autoplaySpeed", "uuid": "ZJM7uiODd3kX", "__type": "Var"}, "I9TPrvJN0Kig": {"name": "bool", "__type": "BoolType"}, "K0dshdRKRRl4": {"name": "centerMode", "uuid": "iCoRvUZ17Gox", "__type": "Var"}, "zbc45jpuu6cs": {"name": "text", "__type": "Text"}, "NFHrAq0fFHC2": {"name": "centerPadding", "uuid": "cW5iIbokG9Zb", "__type": "Var"}, "QzhyaskdrOeF": {"name": "bool", "__type": "BoolType"}, "-G7Z39mbqpMl": {"name": "dots", "uuid": "goV9UbwPaCMj", "__type": "Var"}, "7buRmakOyfIe": {"name": "bool", "__type": "BoolType"}, "FloXDWAgrv52": {"name": "draggable", "uuid": "hzkMdUYAZFqP", "__type": "Var"}, "90W9qywy2zN_": {"name": "text", "__type": "Text"}, "y5gLfJoxEryl": {"name": "cssEase", "uuid": "WC8SxgTJgm6T", "__type": "Var"}, "jgavHjxTrwST": {"name": "text", "__type": "Text"}, "QX-VAY000AIo": {"name": "easing", "uuid": "zBIbo1V1OyfP", "__type": "Var"}, "TTiw4x7lnIb2": {"name": "bool", "__type": "BoolType"}, "XO4Y_Vx4O-Ow": {"name": "fade", "uuid": "eiE9ECmJdo6Q", "__type": "Var"}, "s8xyh43h5zaL": {"name": "bool", "__type": "BoolType"}, "JFRl4JVHui-W": {"name": "focusOnSelect", "uuid": "3w9bQ4pLJFd5", "__type": "Var"}, "ryUx4Xwpt2eh": {"name": "bool", "__type": "BoolType"}, "Kw4BjTsf4GR9": {"name": "infinite", "uuid": "J6AETEXchNtd", "__type": "Var"}, "ITIEbGASLDMK": {"name": "choice", "options": ["ondemand", "progressive"], "__type": "Choice"}, "INiS0vfaa9zY": {"name": "lazyLoad", "uuid": "v_sPZaoUYHJ6", "__type": "Var"}, "DK5ZnzwnDXcK": {"name": "bool", "__type": "BoolType"}, "gWpWQlDbIf5a": {"name": "pauseOnDotsHover", "uuid": "svUuV5QLEsRd", "__type": "Var"}, "F6KafuKTlQnI": {"name": "bool", "__type": "BoolType"}, "w6ZG6bOscQuI": {"name": "pauseOnFocus", "uuid": "3aDustWDS0CC", "__type": "Var"}, "gMMKvKC8oiQA": {"name": "bool", "__type": "BoolType"}, "DZGEOVWcDs5v": {"name": "pauseOnHover", "uuid": "5NxOCY1TxtaO", "__type": "Var"}, "4IzhrndeTWxH": {"name": "num", "__type": "<PERSON><PERSON>"}, "YObcqDkKElTE": {"name": "rows", "uuid": "MEsZV87cxW2d", "__type": "Var"}, "iXxF67fW0nU1": {"name": "bool", "__type": "BoolType"}, "-ohGZ4LNJYN4": {"name": "rtl", "uuid": "Wefvj3hzEgK3", "__type": "Var"}, "QFEZqqYOsy75": {"name": "num", "__type": "<PERSON><PERSON>"}, "oNl1X-gjOXIB": {"name": "slidesPerRow", "uuid": "pMyz6tBoGbny", "__type": "Var"}, "4RI0bGLm77Hv": {"name": "num", "__type": "<PERSON><PERSON>"}, "PViksBkI749A": {"name": "slidesToScroll", "uuid": "SzVTnmQaG6r9", "__type": "Var"}, "ExnARtun_iyL": {"name": "num", "__type": "<PERSON><PERSON>"}, "YKrGqElm1jcB": {"name": "slidesToShow", "uuid": "TT-6PlTWQKkt", "__type": "Var"}, "0NpWPUwuZ3RW": {"name": "num", "__type": "<PERSON><PERSON>"}, "L4MM6KsG5mIq": {"name": "speed", "uuid": "3Vy0S7upy4Hm", "__type": "Var"}, "0p4iEpPzv173": {"name": "bool", "__type": "BoolType"}, "osWp8ESsmb7a": {"name": "swipe", "uuid": "Do4xWGzZmIk0", "__type": "Var"}, "GLEEhqge6_D5": {"name": "bool", "__type": "BoolType"}, "tf-cIpyKBDy3": {"name": "swipeToSlide", "uuid": "DozEyY9nOdOW", "__type": "Var"}, "eA_cTMPN4mzp": {"name": "bool", "__type": "BoolType"}, "QopCldq4VZF4": {"name": "touchMove", "uuid": "5QZHP2LXpWw6", "__type": "Var"}, "dXmvAgCbAsOk": {"name": "num", "__type": "<PERSON><PERSON>"}, "nqlr5lVneSbi": {"name": "touchThreshold", "uuid": "75GmadX63Mnl", "__type": "Var"}, "E0GxOa9se5jn": {"name": "bool", "__type": "BoolType"}, "JXV7Psvkz9BE": {"name": "useCSS", "uuid": "NdkwBido5he_", "__type": "Var"}, "hYbzM3vkurKP": {"name": "bool", "__type": "BoolType"}, "9LkV-iIz6RZe": {"name": "useTransform", "uuid": "ZSS5T1XsYpmd", "__type": "Var"}, "J02zorS2M0tZ": {"name": "bool", "__type": "BoolType"}, "_MgURsKv5Vd7": {"name": "variableWidth", "uuid": "QUbCg7j6Azwl", "__type": "Var"}, "lzv2WUy4WsH8": {"name": "bool", "__type": "BoolType"}, "0rdr3SVlT4xM": {"name": "vertical", "uuid": "qycDjAPPfT8c", "__type": "Var"}, "ob4rgKlnhfp9": {"name": "func", "params": [{"__ref": "p8n49wVu-xhR"}], "__type": "FunctionType"}, "6KTA8fbm_idO": {"name": "beforeChange", "uuid": "i6w0XUr6TMWL", "__type": "Var"}, "jM2lMtPMnTOF": {"param": {"__ref": "ZEE1qbzSeuSN"}, "defaultContents": [], "uuid": "x977sDdvaRtB", "parent": {"__ref": "DTYAAI-RG-nX"}, "locked": null, "vsettings": [{"__ref": "0-mtquXJA6It"}], "__type": "TplSlot"}, "tO5605NqxXAj": {"variants": [{"__ref": "SaWWm8HEPpbN"}], "args": [], "attrs": {}, "rs": {"__ref": "-z62fJsAjuty"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pVD2xi5KF87W": {"values": {"width": "stretch", "max-width": "100%", "flex-direction": "column"}, "mixins": [], "__type": "RuleSet"}, "oQXzi44tP6I4": {"importPath": "@plasmicpkgs/react-slick", "importName": "sliderHelpers", "defaultExport": false, "__type": "CodeComponentHelper"}, "p8n49wVu-xhR": {"name": "arg", "argName": "currentSlide", "displayName": null, "type": {"__ref": "SjoGhh15pdLl"}, "__type": "ArgType"}, "0-mtquXJA6It": {"variants": [{"__ref": "SaWWm8HEPpbN"}], "args": [], "attrs": {}, "rs": {"__ref": "e5_auDW5mTmn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-z62fJsAjuty": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "SjoGhh15pdLl": {"name": "num", "__type": "<PERSON><PERSON>"}, "e5_auDW5mTmn": {"values": {}, "mixins": [], "__type": "RuleSet"}, "OuQSZJSvPbDj": {"name": "styleScopeClassName", "scopeName": "slider", "__type": "StyleScopeClassNamePropType"}, "nSNCtAJEv9hi": {"name": "sliderScopeClassName", "uuid": "J39U1H7hA4sX", "__type": "Var"}, "pJZZ4Njd8kAK": {"type": {"__ref": "OuQSZJSvPbDj"}, "variable": {"__ref": "nSNCtAJEv9hi"}, "uuid": "fRJ3bgVYJFCN", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "biSqrpHXFLwK": {"variableType": "number", "name": "currentSlide", "param": {"__ref": "HSsTXJSbs7q2"}, "accessType": "writable", "onChangeParam": {"__ref": "NwFi-aKCT50n"}, "tplNode": null, "implicitState": null, "__type": "NamedState"}, "HSsTXJSbs7q2": {"type": {"__ref": "cr1GU_6cGrZM"}, "state": {"__ref": "biSqrpHXFLwK"}, "variable": {"__ref": "fmNNC74VDKgk"}, "uuid": "w0y1vCmb_gAc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "xZsJT1hI6BI2"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Initial Slide", "about": "Index of the first visible slide (first is 0), accounting for multiple slides per view if applicable.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "5FY4hAf3PClt": {"name": "react-slick", "npmPkg": ["react-slick", "slick-carousel"], "cssImport": ["slick-carousel/slick/slick-theme.css", "slick-carousel/slick/slick.css"], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "cr1GU_6cGrZM": {"name": "num", "__type": "<PERSON><PERSON>"}, "fmNNC74VDKgk": {"name": "initialSlide", "uuid": "F8MVLA9ixsqN", "__type": "Var"}, "xZsJT1hI6BI2": {"code": "0", "fallback": null, "__type": "CustomCode"}, "_ulhJT6MD4vt": {"uuid": "ight4xPOa8GJ", "pkgId": "12a8a62d-5636-49e3-99c9-d362445593db", "projectId": "dtpfrjZFM3t3SYcVXkS3iy", "version": "1.0.0", "name": "Imported Dep", "site": {"__ref": "RDfYw54DLI_5"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}], ["pB3UMJfcoLdnpZ6A3yU2Yp", {"root": "B7IAYDjpkj8q", "map": {"DeBvWFhG62r6": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "zLtCSRybt7DD": {"name": "Default Typography", "rs": {"__ref": "DeBvWFhG62r6"}, "preview": null, "uuid": "JRbw3WVVfLE_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "R1Uh_tFIyzJn": {"values": {}, "mixins": [], "__type": "RuleSet"}, "R8enDqA15WC2": {"rs": {"__ref": "R1Uh_tFIyzJn"}, "__type": "ThemeLayoutSettings"}, "QHRi4Bky0vEd": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "3zv01Fo6aMv5": {"name": "Default \"h1\"", "rs": {"__ref": "QHRi4Bky0vEd"}, "preview": null, "uuid": "KjsdJ-zRg6Xk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nxTBpXVAxuh6": {"selector": "h1", "style": {"__ref": "3zv01Fo6aMv5"}, "__type": "ThemeStyle"}, "CHo8l4qy8D1E": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "juS8OM1liI21": {"name": "Default \"h2\"", "rs": {"__ref": "CHo8l4qy8D1E"}, "preview": null, "uuid": "ELkoMROlY4DM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "QnnsJLh9eNGx": {"selector": "h2", "style": {"__ref": "juS8OM1liI21"}, "__type": "ThemeStyle"}, "LH2UYjRXPqdt": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "ZsUSJeCnogYV": {"name": "Default \"h3\"", "rs": {"__ref": "LH2UYjRXPqdt"}, "preview": null, "uuid": "ur9K-4emsn7v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DQH9LKEQLQ8U": {"selector": "h3", "style": {"__ref": "ZsUSJeCnogYV"}, "__type": "ThemeStyle"}, "zTVrbzNB6k3a": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "JvEwFZpZJye9": {"name": "Default \"h4\"", "rs": {"__ref": "zTVrbzNB6k3a"}, "preview": null, "uuid": "Jr7AZwSJzdt3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Vms0eTeiC0Ha": {"selector": "h4", "style": {"__ref": "JvEwFZpZJye9"}, "__type": "ThemeStyle"}, "PIvHwaeVjg6x": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "OEqB_kWdW2yb": {"name": "Default \"h5\"", "rs": {"__ref": "PIvHwaeVjg6x"}, "preview": null, "uuid": "zGfWy7y3vxb2", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5EqznwWchl20": {"selector": "h5", "style": {"__ref": "OEqB_kWdW2yb"}, "__type": "ThemeStyle"}, "WvzUgFbPd605": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "leo90i6FV1VJ": {"name": "Default \"h6\"", "rs": {"__ref": "WvzUgFbPd605"}, "preview": null, "uuid": "PonjQtJsiTU9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "gGYvrQvmffrf": {"selector": "h6", "style": {"__ref": "leo90i6FV1VJ"}, "__type": "ThemeStyle"}, "UQZCHuXy850M": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "m0MuVQvE1Gzj": {"name": "Default \"a\"", "rs": {"__ref": "UQZCHuXy850M"}, "preview": null, "uuid": "TGrw1C1Motna", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "GwRT6vwP_Mst": {"selector": "a", "style": {"__ref": "m0MuVQvE1Gzj"}, "__type": "ThemeStyle"}, "cgAiyrivxAbh": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "neKBve1pgKHI": {"name": "Default \"a:hover\"", "rs": {"__ref": "cgAiyrivxAbh"}, "preview": null, "uuid": "-A2qgNjyGnPY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "0VFmUIMhQQ1b": {"selector": "a:hover", "style": {"__ref": "neKBve1pgKHI"}, "__type": "ThemeStyle"}, "6Vd5AS7VVTFu": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "fuArpqANGjWl": {"name": "Default \"blockquote\"", "rs": {"__ref": "6Vd5AS7VVTFu"}, "preview": null, "uuid": "tWO7NHm_apmq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "kOdsC0xpEbJB": {"selector": "blockquote", "style": {"__ref": "fuArpqANGjWl"}, "__type": "ThemeStyle"}, "6FbwrmzMlvYg": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "_GDlY-9psi6V": {"name": "Default \"code\"", "rs": {"__ref": "6FbwrmzMlvYg"}, "preview": null, "uuid": "9R-eDQGWOeRy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "qIjncmRcNhlz": {"selector": "code", "style": {"__ref": "_GDlY-9psi6V"}, "__type": "ThemeStyle"}, "sAKqgb7EIETF": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "bt6_cWaApM-F": {"name": "Default \"pre\"", "rs": {"__ref": "sAKqgb7EIETF"}, "preview": null, "uuid": "4rKK8G50tbLV", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "XhBNDU2IqipT": {"selector": "pre", "style": {"__ref": "bt6_cWaApM-F"}, "__type": "ThemeStyle"}, "aUOoWdBlMpkl": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "IR8JL8EOKimt": {"name": "Default \"ol\"", "rs": {"__ref": "aUOoWdBlMpkl"}, "preview": null, "uuid": "zRrGanNH-FLk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xLv_txyn7qp9": {"selector": "ol", "style": {"__ref": "IR8JL8EOKimt"}, "__type": "ThemeStyle"}, "SamrrGDfXbQU": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "B67f_T5Ajrb8": {"name": "Default \"ul\"", "rs": {"__ref": "SamrrGDfXbQU"}, "preview": null, "uuid": "rMpL--OqfpBS", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "fWQTIm4crJko": {"selector": "ul", "style": {"__ref": "B67f_T5Ajrb8"}, "__type": "ThemeStyle"}, "N0XXcV4-Nh_T": {"defaultStyle": {"__ref": "zLtCSRybt7DD"}, "styles": [{"__ref": "nxTBpXVAxuh6"}, {"__ref": "QnnsJLh9eNGx"}, {"__ref": "DQH9LKEQLQ8U"}, {"__ref": "Vms0eTeiC0Ha"}, {"__ref": "5EqznwWchl20"}, {"__ref": "gGYvrQvmffrf"}, {"__ref": "GwRT6vwP_Mst"}, {"__ref": "0VFmUIMhQQ1b"}, {"__ref": "kOdsC0xpEbJB"}, {"__ref": "qIjncmRcNhlz"}, {"__ref": "XhBNDU2IqipT"}, {"__ref": "xLv_txyn7qp9"}, {"__ref": "fWQTIm4crJko"}], "layout": {"__ref": "R8enDqA15WC2"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8VN47wSZvJB3": {"name": "text", "__type": "Text"}, "bzdWAWU54w0B": {"name": "Screen", "uuid": "iRrqVLgaP6np", "__type": "Var"}, "7azPaMr25ExX": {"type": {"__ref": "8VN47wSZvJB3"}, "variable": {"__ref": "bzdWAWU54w0B"}, "uuid": "5t2vob9zGZTr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "V3nxOceRraCt": {"type": "global-screen", "param": {"__ref": "7azPaMr25ExX"}, "uuid": "i-eIbFVMXIfj", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "R80oJYEapP1X": {"uuid": "-W4huwjPqSHr", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "B7IAYDjpkj8q": {"components": [{"__ref": "Jddf52kCcj6O"}, {"__ref": "5eA2NrDMnU4V"}, {"__ref": "ZOm9KRROsVGC"}, {"__ref": "P8hUdZkGBYov"}, {"__ref": "CUEv0dZhpMRo"}], "arenas": [{"__ref": "WbMHp2cweymH"}], "pageArenas": [{"__ref": "UTZQfVbb6_JD"}], "componentArenas": [{"__ref": "nF3BO_KVW0Q5"}, {"__ref": "0zqw-xWELi39"}], "globalVariantGroups": [{"__ref": "V3nxOceRraCt"}], "userManagedFonts": [], "globalVariant": {"__ref": "R80oJYEapP1X"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "N0XXcV4-Nh_T"}], "activeTheme": {"__ref": "N0XXcV4-Nh_T"}, "imageAssets": [{"__ref": "yw5bb3sWMkVn"}, {"__ref": "qkP78v4NuNLB"}, {"__ref": "7ucfpJr_iSOE"}], "projectDependencies": [{"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "_ulhJT6MD4vt"}}], "activeScreenVariantGroup": {"__ref": "V3nxOceRraCt"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {"button": {"__ref": "P8hUdZkGBYov"}, "text-input": {"__ref": "CUEv0dZhpMRo"}}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "WbMHp2cweymH": {"name": "Custom arena 1", "children": [{"__ref": "ZJlnhBX_1PVH"}], "__type": "Arena"}, "Jddf52kCcj6O": {"uuid": "Io_jd47Qovs-", "name": "hostless-plasmic-head", "params": [{"__ref": "XEPISKeF1Su5"}, {"__ref": "ZYQJsrIPWCU3"}, {"__ref": "eP8Hinr4Hzva"}, {"__ref": "eat3INyYbtkw"}], "states": [], "tplTree": {"__ref": "fDzZFAmpxtkl"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "clZY-Qnojxb_"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "9KkRbz2mglXu"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "5eA2NrDMnU4V": {"uuid": "bbfPDeUbdIxB", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "tyiDQptJ2skf"}, {"__ref": "3SnJrkIiJ2QF"}, {"__ref": "DCu0AYYGniuE"}, {"__ref": "5ie_qHuzg-20"}, {"__ref": "odv9vRwCmePD"}], "states": [], "tplTree": {"__ref": "iNkiXHh50sjM"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "CJ4tSAmYxkXs"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "tZi7_1bvOWrn"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "XEPISKeF1Su5": {"type": {"__ref": "mkofqeljrS4N"}, "variable": {"__ref": "vWcWZgeUzczW"}, "uuid": "WHPuws0v5Vvc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "ZYQJsrIPWCU3": {"type": {"__ref": "DkdsQv6NvshM"}, "variable": {"__ref": "4ZJhIyGbmMmM"}, "uuid": "K0LVqN4-Xsad", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "eP8Hinr4Hzva": {"type": {"__ref": "EYp80MAhkPxK"}, "variable": {"__ref": "QndIxyxS_VL_"}, "uuid": "vYgM3dxTIOhm", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "eat3INyYbtkw": {"type": {"__ref": "MuoVo-A7Pndq"}, "variable": {"__ref": "dNZdjzM1I-RX"}, "uuid": "_SOZsQfI2DO3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "fDzZFAmpxtkl": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E7cgRmPoij1c", "parent": null, "locked": null, "vsettings": [{"__ref": "aGGtTiBITzck"}], "__type": "TplTag"}, "clZY-Qnojxb_": {"uuid": "w3ghudFCDemR", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "9KkRbz2mglXu": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "tyiDQptJ2skf": {"type": {"__ref": "JSvpuXAShe9I"}, "variable": {"__ref": "MtlqxL0SSMI_"}, "uuid": "SJAlOyLQ1HoK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "3SnJrkIiJ2QF": {"type": {"__ref": "LQeREkFEE4bD"}, "variable": {"__ref": "lP5zjgKlTfTF"}, "uuid": "EzBw_I4AB1tj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "DCu0AYYGniuE": {"type": {"__ref": "6OZYN3ghbHnt"}, "tplSlot": {"__ref": "XP-6trsq_c9y"}, "variable": {"__ref": "Q83aWr3hwOHh"}, "uuid": "7Gh1cCKP-PHS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5ie_qHuzg-20": {"type": {"__ref": "6DHpFiQQFNFP"}, "variable": {"__ref": "UdHrfASr6tAt"}, "uuid": "xQJjTrfj2opw", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "odv9vRwCmePD": {"type": {"__ref": "3WDSMQBITA1y"}, "variable": {"__ref": "hqjbqae-xz9d"}, "uuid": "slN0fBw1JPTQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "iNkiXHh50sjM": {"tag": "div", "name": null, "children": [{"__ref": "XP-6trsq_c9y"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "1gHhkhI1rbUf", "parent": null, "locked": null, "vsettings": [{"__ref": "SiXR6z5_eTzi"}], "__type": "TplTag"}, "CJ4tSAmYxkXs": {"uuid": "2luJu6CMiCSm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "tZi7_1bvOWrn": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "mkofqeljrS4N": {"name": "text", "__type": "Text"}, "vWcWZgeUzczW": {"name": "title", "uuid": "GjFsRPOES1-t", "__type": "Var"}, "DkdsQv6NvshM": {"name": "text", "__type": "Text"}, "4ZJhIyGbmMmM": {"name": "description", "uuid": "vh_8v8EvtspW", "__type": "Var"}, "EYp80MAhkPxK": {"name": "img", "__type": "Img"}, "QndIxyxS_VL_": {"name": "image", "uuid": "c7cCGaYt_sYj", "__type": "Var"}, "MuoVo-A7Pndq": {"name": "text", "__type": "Text"}, "dNZdjzM1I-RX": {"name": "canonical", "uuid": "C9k2AylYM44U", "__type": "Var"}, "aGGtTiBITzck": {"variants": [{"__ref": "clZY-Qnojxb_"}], "args": [], "attrs": {}, "rs": {"__ref": "dB9_XRfBRLh-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JSvpuXAShe9I": {"name": "any", "__type": "AnyType"}, "MtlqxL0SSMI_": {"name": "dataOp", "uuid": "TLOZXEvirvrv", "__type": "Var"}, "LQeREkFEE4bD": {"name": "text", "__type": "Text"}, "lP5zjgKlTfTF": {"name": "name", "uuid": "NvwU_kZNe7Nw", "__type": "Var"}, "6OZYN3ghbHnt": {"name": "renderFunc", "params": [{"__ref": "MgF_FZCbldme"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "Q83aWr3hwOHh": {"name": "children", "uuid": "Y-1nafwjkKUt", "__type": "Var"}, "6DHpFiQQFNFP": {"name": "num", "__type": "<PERSON><PERSON>"}, "UdHrfASr6tAt": {"name": "pageSize", "uuid": "KZ6-sofJXGJR", "__type": "Var"}, "3WDSMQBITA1y": {"name": "num", "__type": "<PERSON><PERSON>"}, "hqjbqae-xz9d": {"name": "pageIndex", "uuid": "NzS0IXE6hq2F", "__type": "Var"}, "XP-6trsq_c9y": {"param": {"__ref": "DCu0AYYGniuE"}, "defaultContents": [], "uuid": "K-BrrUbgJNXS", "parent": {"__ref": "iNkiXHh50sjM"}, "locked": null, "vsettings": [{"__ref": "bKoa0JMYLknQ"}], "__type": "TplSlot"}, "SiXR6z5_eTzi": {"variants": [{"__ref": "CJ4tSAmYxkXs"}], "args": [], "attrs": {}, "rs": {"__ref": "9nGpGG3iZUaA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dB9_XRfBRLh-": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MgF_FZCbldme": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "Uu6YSPIC6Swr"}, "__type": "ArgType"}, "bKoa0JMYLknQ": {"variants": [{"__ref": "CJ4tSAmYxkXs"}], "args": [], "attrs": {}, "rs": {"__ref": "EOO86k4lIQ3O"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9nGpGG3iZUaA": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "Uu6YSPIC6Swr": {"name": "any", "__type": "AnyType"}, "EOO86k4lIQ3O": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZOm9KRROsVGC": {"uuid": "tzTZqK5Ok7W6", "name": "slick-slider-test", "params": [{"__ref": "yU-sAzP93ZAH"}, {"__ref": "6TtLlqMsJnp-"}, {"__ref": "PFQ10r8suLTv"}, {"__ref": "IemXgdpbSkOM"}], "states": [{"__ref": "FhvN3uw4MLXp"}, {"__ref": "KZC_mHpkY-mp"}], "tplTree": {"__ref": "Y7fjLAMILdxu"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "UBLrIWhgqr4b"}], "variantGroups": [], "pageMeta": {"__ref": "NgqHcaR6_jpS"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "UTZQfVbb6_JD": {"component": {"__ref": "ZOm9KRROsVGC"}, "matrix": {"__ref": "IHrINqJ94pKU"}, "customMatrix": {"__ref": "A46bptkUAR3H"}, "__type": "PageArena"}, "ZJlnhBX_1PVH": {"uuid": "RFjJKdK1cObk", "width": 800, "height": 800, "container": {"__ref": "Xf6jMHklCDZ5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "UBLrIWhgqr4b"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 61, "left": 268, "__type": "ArenaFrame"}, "Y7fjLAMILdxu": {"tag": "div", "name": null, "children": [{"__ref": "pMiH7SOm3-Dz"}, {"__ref": "pc80HsOmoeS2"}, {"__ref": "ErwfHvfoHdvb"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "MpJFKLKUB33g", "parent": null, "locked": null, "vsettings": [{"__ref": "Tzr2WoPsBt3Z"}], "__type": "TplTag"}, "UBLrIWhgqr4b": {"uuid": "R8FSPwJQd0rq", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "NgqHcaR6_jpS": {"path": "/slick-slider-test", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "IHrINqJ94pKU": {"rows": [{"__ref": "PtPD5VnGjEdM"}], "__type": "ArenaFrameGrid"}, "A46bptkUAR3H": {"rows": [{"__ref": "o3P5TYmxk2Kg"}], "__type": "ArenaFrameGrid"}, "Xf6jMHklCDZ5": {"name": null, "component": {"__ref": "ZOm9KRROsVGC"}, "uuid": "2QNrWg6DN2wG", "parent": null, "locked": null, "vsettings": [{"__ref": "e9VIODb5FYrl"}], "__type": "TplComponent"}, "Tzr2WoPsBt3Z": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "n1AojUrRo0p8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PtPD5VnGjEdM": {"cols": [{"__ref": "90mKHuvVXKk9"}], "rowKey": {"__ref": "UBLrIWhgqr4b"}, "__type": "ArenaFrameRow"}, "o3P5TYmxk2Kg": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "e9VIODb5FYrl": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "leFyVjkvS3k0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "n1AojUrRo0p8": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "90mKHuvVXKk9": {"frame": {"__ref": "nRLFcHSF9BhQ"}, "cellKey": null, "__type": "ArenaFrameCell"}, "leFyVjkvS3k0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "nRLFcHSF9BhQ": {"uuid": "3784GRjhVFP3", "width": 1366, "height": 768, "container": {"__ref": "AGZkSilIXmLt"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "UBLrIWhgqr4b"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "AGZkSilIXmLt": {"name": null, "component": {"__ref": "ZOm9KRROsVGC"}, "uuid": "01_17qoRWegk", "parent": null, "locked": null, "vsettings": [{"__ref": "e7qACaXJCM6J"}], "__type": "TplComponent"}, "e7qACaXJCM6J": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "m6ubjb7VBvBq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "m6ubjb7VBvBq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "yw5bb3sWMkVn": {"uuid": "Slk6FiYO8PwN", "name": "check.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHN0eWxlPSJmaWxsOiBjdXJyZW50Y29sb3I7Ij4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQxNiA1Ljg3NmEuNzUuNzUgMCAwMS4yMDggMS4wNEwxMS40MiAxNy43MjFhMS43NSAxLjc1IDAgMDEtMi44NzEuMDZsLTMuMTU2LTQuMzRhLjc1Ljc1IDAgMTExLjIxNC0uODgybDMuMTU1IDQuMzM5YS4yNS4yNSAwIDAwLjQxLS4wMDlsNy4yMDQtMTAuODA1YS43NS43NSAwIDAxMS4wNC0uMjA4eiIgZmlsbD0iY3VycmVudENvbG9yIi8+Cjwvc3ZnPg==", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "qkP78v4NuNLB": {"uuid": "-JTRe3YcQ8y8", "name": "icon", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHN0cm9rZT0iY3VycmVudENvbG9yIiBmaWxsPSJjdXJyZW50Q29sb3IiIHN0cm9rZS13aWR0aD0iMCIgdmlld0JveD0iMCAwIDE2IDE2IiBoZWlnaHQ9IjFlbSIgd2lkdGg9IjFlbSI+CiAgPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMSA4YS41LjUgMCAwMS41LS41aDExLjc5M2wtMy4xNDctMy4xNDZhLjUuNSAwIDAxLjcwOC0uNzA4bDQgNGEuNS41IDAgMDEwIC43MDhsLTQgNGEuNS41IDAgMDEtLjcwOC0uNzA4TDEzLjI5MyA4LjVIMS41QS41LjUgMCAwMTEgOHoiIHN0cm9rZT0ibm9uZSIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": 1000000, "__type": "ImageAsset"}, "P8hUdZkGBYov": {"uuid": "gGghalT6VlIe", "name": "<PERSON><PERSON>", "params": [{"__ref": "jiGTb077efD0"}, {"__ref": "6DhgXtQjefCE"}, {"__ref": "w55AHRoFEOMX"}, {"__ref": "58W11N2MXQvz"}, {"__ref": "ALGBMmJdKGZS"}, {"__ref": "k_8afnpCqfRo"}, {"__ref": "qRz7HtxLAUj6"}, {"__ref": "7AVuiOxAqqQY"}, {"__ref": "iEvYm2Z4TPap"}, {"__ref": "b5c0od5FYtYG"}, {"__ref": "RyTf8a1CCMf5"}, {"__ref": "P93iYlcRNSTo"}, {"__ref": "7Ay6TBTLgpjt"}, {"__ref": "UDa9Ltne4JXy"}, {"__ref": "KZaq274Ga5UM"}, {"__ref": "RQTViumsrywk"}, {"__ref": "BR2vWqsJjLVm"}, {"__ref": "7nY5D9SLla9z"}], "states": [{"__ref": "cmzOAJY2a6xs"}, {"__ref": "zBTtJSxb6nN0"}, {"__ref": "1u9XDJqOK363"}, {"__ref": "s_9w1932EkFV"}, {"__ref": "54osMRfWmlu_"}, {"__ref": "aCdQJke5MlSY"}], "tplTree": {"__ref": "fFseKFIkyfcp"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "qObSIcZZM9pK"}, {"__ref": "3aIdJRYyilVr"}, {"__ref": "-PxzIgp4Gipn"}, {"__ref": "Gf2SAp8FjA8g"}, {"__ref": "FBRFKN3-GALk"}], "variantGroups": [{"__ref": "9I-n0ianRSYF"}, {"__ref": "QLbYT6TAl0e5"}, {"__ref": "-9a0hdnTsCSM"}, {"__ref": "W0-uDb6WjCBT"}, {"__ref": "pA3Hz3N_ERjN"}, {"__ref": "lprUB35u6EM3"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "wscpjnelYdwN"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true, "__type": "Component", "updatedAt": null}, "nF3BO_KVW0Q5": {"component": {"__ref": "P8hUdZkGBYov"}, "matrix": {"__ref": "iQ2sJDi8t-H8"}, "customMatrix": {"__ref": "wutDehf2t4v1"}, "__type": "ComponentArena"}, "jiGTb077efD0": {"type": {"__ref": "bEwAJzn4ynZP"}, "tplSlot": {"__ref": "_1Fst0YnGkXE"}, "variable": {"__ref": "7GIvy8a1qDOG"}, "uuid": "U4sEnEw-QLR3", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": true, "isLocalizable": false, "__type": "SlotParam"}, "6DhgXtQjefCE": {"type": {"__ref": "6gsKiiiTFidC"}, "state": {"__ref": "cmzOAJY2a6xs"}, "variable": {"__ref": "oveau0Ver1lb"}, "uuid": "g7d0QmUout3U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "w55AHRoFEOMX": {"type": {"__ref": "2VmhW_8dEbny"}, "state": {"__ref": "zBTtJSxb6nN0"}, "variable": {"__ref": "hcjZqPeqQXht"}, "uuid": "AS05sThqHKAP", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "58W11N2MXQvz": {"type": {"__ref": "FJEykkIv_kRs"}, "tplSlot": {"__ref": "K2g4qcigFUHh"}, "variable": {"__ref": "luIiebJieIdE"}, "uuid": "9VhypiDs-hLl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "ALGBMmJdKGZS": {"type": {"__ref": "TXDm70PXNeuY"}, "tplSlot": {"__ref": "nP1Y6u--8ss4"}, "variable": {"__ref": "1PobtVuUU8sd"}, "uuid": "s5X4JOU0WGfE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "k_8afnpCqfRo": {"type": {"__ref": "oi__Bu9hBM-4"}, "state": {"__ref": "1u9XDJqOK363"}, "variable": {"__ref": "DCKjAdunV_Mf"}, "uuid": "YAaK9IoaWQNu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "qRz7HtxLAUj6": {"type": {"__ref": "AwFoCiUTSKl2"}, "variable": {"__ref": "M9Eu9pfV4JWS"}, "uuid": "d_Ec0_zH9HiC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "7AVuiOxAqqQY": {"type": {"__ref": "a1wkBfDXv-jI"}, "state": {"__ref": "aCdQJke5MlSY"}, "variable": {"__ref": "9g0sdX3K5k1U"}, "uuid": "FtpQlUJ2zlOK", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "iEvYm2Z4TPap": {"type": {"__ref": "IEaw6Y_IfSrp"}, "state": {"__ref": "54osMRfWmlu_"}, "variable": {"__ref": "fFrQtE8eF0L4"}, "uuid": "HBcy_GT0_JnH", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "b5c0od5FYtYG": {"type": {"__ref": "HTdMSU9wybBe"}, "state": {"__ref": "s_9w1932EkFV"}, "variable": {"__ref": "0G2s2RDcpET3"}, "uuid": "A5Dzop2QUBPl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "RyTf8a1CCMf5": {"type": {"__ref": "ZAQi5iRbtLYY"}, "variable": {"__ref": "HHRO3P4SaVzo"}, "uuid": "bPHUTHxBCgvY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Submits form?", "about": "Whether clicking on this button submits the enclosing form or not", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "P93iYlcRNSTo": {"type": {"__ref": "j5BFYLKco4YA"}, "variable": {"__ref": "aTsmnQ2EdNNP"}, "uuid": "BzII1FNRPHv_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Open in new tab?", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "7Ay6TBTLgpjt": {"type": {"__ref": "UXPLgIxtu8Jf"}, "state": {"__ref": "cmzOAJY2a6xs"}, "variable": {"__ref": "sEFwrpzwUB-f"}, "uuid": "MydSjr5P9_AB", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "UDa9Ltne4JXy": {"type": {"__ref": "LSHnuWFF71pn"}, "state": {"__ref": "zBTtJSxb6nN0"}, "variable": {"__ref": "beuP9cckcldN"}, "uuid": "yE2_28Uquk6s", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "KZaq274Ga5UM": {"type": {"__ref": "xygAgRuMBXeJ"}, "state": {"__ref": "1u9XDJqOK363"}, "variable": {"__ref": "zcoKW6fBToqQ"}, "uuid": "dA2RPqXOoRVN", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "RQTViumsrywk": {"type": {"__ref": "QQsopFPl-IMB"}, "state": {"__ref": "s_9w1932EkFV"}, "variable": {"__ref": "5iw7M9yU-UH0"}, "uuid": "-v9-qrud<PERSON>ii", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "BR2vWqsJjLVm": {"type": {"__ref": "wo9llow40QRR"}, "state": {"__ref": "54osMRfWmlu_"}, "variable": {"__ref": "2wfaJqaDIu00"}, "uuid": "RSAYVvFb33Os", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "7nY5D9SLla9z": {"type": {"__ref": "KunGwh2c5NZZ"}, "state": {"__ref": "aCdQJke5MlSY"}, "variable": {"__ref": "P9DPUlDarn2c"}, "uuid": "5IA02GYFoHvM", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "cmzOAJY2a6xs": {"variantGroup": {"__ref": "9I-n0ianRSYF"}, "param": {"__ref": "6DhgXtQjefCE"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "7Ay6TBTLgpjt"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "zBTtJSxb6nN0": {"variantGroup": {"__ref": "QLbYT6TAl0e5"}, "param": {"__ref": "w55AHRoFEOMX"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "UDa9Ltne4JXy"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "1u9XDJqOK363": {"variantGroup": {"__ref": "-9a0hdnTsCSM"}, "param": {"__ref": "k_8afnpCqfRo"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "KZaq274Ga5UM"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "s_9w1932EkFV": {"variantGroup": {"__ref": "W0-uDb6WjCBT"}, "param": {"__ref": "b5c0od5FYtYG"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "RQTViumsrywk"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "54osMRfWmlu_": {"variantGroup": {"__ref": "pA3Hz3N_ERjN"}, "param": {"__ref": "iEvYm2Z4TPap"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "BR2vWqsJjLVm"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "aCdQJke5MlSY": {"variantGroup": {"__ref": "lprUB35u6EM3"}, "param": {"__ref": "7AVuiOxAqqQY"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "7nY5D9SLla9z"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "fFseKFIkyfcp": {"tag": "button", "name": null, "children": [{"__ref": "-AD8PM10wriG"}, {"__ref": "mqZoSA7vgpx-"}, {"__ref": "jD8cgJw-RL4A"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "JWgTr-GGzxe6", "parent": null, "locked": null, "vsettings": [{"__ref": "A54GNtpC_HCL"}, {"__ref": "rjSCDhVA3VpS"}, {"__ref": "GNRRFnVsU1-O"}, {"__ref": "ruWmyog4Lnw9"}, {"__ref": "lnrOhP2L2c33"}, {"__ref": "cBG_FRzu9QCC"}, {"__ref": "Nmqwt_qxIu7S"}, {"__ref": "0hyVZzYisg-N"}, {"__ref": "FrCaEI9CYCKO"}, {"__ref": "Vk3QpjUGJrHP"}, {"__ref": "DXfBBGgxeUnl"}, {"__ref": "14nKN44oXOGT"}, {"__ref": "QPWPN0w9o21r"}, {"__ref": "8Lg19Toi-fNx"}, {"__ref": "it2Jciqc6Nns"}, {"__ref": "oDot2N0oz-Q1"}, {"__ref": "WRpmjT0GCbMr"}, {"__ref": "jwX7iGmcJX8G"}, {"__ref": "9Of3GY2NmpCS"}, {"__ref": "KwjPxeqpCqKp"}, {"__ref": "37L03mOmi1h7"}, {"__ref": "QkmhXjGZ1oli"}, {"__ref": "VEN2t1uIv8iB"}, {"__ref": "SjA6GypP95Pe"}, {"__ref": "1leLUzkM70Dv"}, {"__ref": "wnNLXQh7oggh"}, {"__ref": "otuO8yaqInX-"}, {"__ref": "EpvQCTlzDhX4"}, {"__ref": "SNQBsymGHC7D"}, {"__ref": "-W2y29fr9vAE"}, {"__ref": "XiASrLfWKeDp"}, {"__ref": "NIyPWi6lAw4n"}, {"__ref": "k3qL8RLz0aOc"}, {"__ref": "Z40LqcLou5pK"}, {"__ref": "j8EU16gG2BU0"}, {"__ref": "307WhFIhT4Dn"}, {"__ref": "d3laJAO1BNKs"}, {"__ref": "k8q-yccKiC9X"}, {"__ref": "QTK31q0oVppU"}, {"__ref": "Ikl6VLBixrsM"}, {"__ref": "29OndlS1vktu"}, {"__ref": "AkBePQZLqO1U"}, {"__ref": "04sViG-BA6qp"}, {"__ref": "I-430G1YCJ1x"}, {"__ref": "DA0X0X6q23Nd"}, {"__ref": "g-20h3EkuelP"}, {"__ref": "2FPu5zUfppWP"}, {"__ref": "EBTbS4Jw4NUt"}, {"__ref": "hlvo5G73mVl4"}, {"__ref": "_aEu40ijXdYo"}, {"__ref": "mGq5elyyTMjF"}, {"__ref": "xg679t_1avfB"}, {"__ref": "PqZ_VRqPk8it"}, {"__ref": "1MgJxVunmo90"}, {"__ref": "DLkRGAq3Ewqg"}, {"__ref": "vN1IWWyX3Xtz"}, {"__ref": "r5Dy5Ie5Has6"}, {"__ref": "lEH5SEefpsnj"}, {"__ref": "_oIZhAjvGRPK"}, {"__ref": "MTbvZs4U2UoR"}], "__type": "TplTag"}, "qObSIcZZM9pK": {"uuid": "fggmbBjKd8X5", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3aIdJRYyilVr": {"uuid": "TvExhE7v6SJP", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "-PxzIgp4Gipn": {"uuid": "mA0RAMqaCfuU", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Gf2SAp8FjA8g": {"uuid": "SQuVbDKUbDVg", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "FBRFKN3-GALk": {"uuid": "Ze8uNuGvFl6S", "name": "", "selectors": [":active"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "9I-n0ianRSYF": {"type": "component", "param": {"__ref": "6DhgXtQjefCE"}, "linkedState": {"__ref": "cmzOAJY2a6xs"}, "uuid": "osEtz1LFB6Nw", "variants": [{"__ref": "DDjI7dHvC5Rj"}], "multi": false, "__type": "ComponentVariantGroup"}, "QLbYT6TAl0e5": {"type": "component", "param": {"__ref": "w55AHRoFEOMX"}, "linkedState": {"__ref": "zBTtJSxb6nN0"}, "uuid": "ltA_DhdKMOnl", "variants": [{"__ref": "dAo57WBDNsb4"}], "multi": false, "__type": "ComponentVariantGroup"}, "-9a0hdnTsCSM": {"type": "component", "param": {"__ref": "k_8afnpCqfRo"}, "linkedState": {"__ref": "1u9XDJqOK363"}, "uuid": "vESgfdMHGLGW", "variants": [{"__ref": "1v9tOoWcYoSm"}], "multi": false, "__type": "ComponentVariantGroup"}, "W0-uDb6WjCBT": {"type": "component", "param": {"__ref": "b5c0od5FYtYG"}, "linkedState": {"__ref": "s_9w1932EkFV"}, "uuid": "H1zCGxw1AsHB", "variants": [{"__ref": "j27i2xhJuV02"}, {"__ref": "kYC798MFgATQ"}, {"__ref": "zsocOgY1UFfj"}], "multi": false, "__type": "ComponentVariantGroup"}, "pA3Hz3N_ERjN": {"type": "component", "param": {"__ref": "iEvYm2Z4TPap"}, "linkedState": {"__ref": "54osMRfWmlu_"}, "uuid": "oHoRlJEGuLvi", "variants": [{"__ref": "mK8gueDbkXfE"}, {"__ref": "D3aNhhliIcew"}], "multi": false, "__type": "ComponentVariantGroup"}, "lprUB35u6EM3": {"type": "component", "param": {"__ref": "7AVuiOxAqqQY"}, "linkedState": {"__ref": "aCdQJke5MlSY"}, "uuid": "gs7EAVThr2Ce", "variants": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "U_QKl4g8LNK2"}, {"__ref": "IYROIQz1Yz-b"}, {"__ref": "qNKoqn3Hxm6D"}, {"__ref": "gnsJ1gtzl8L3"}, {"__ref": "3KS--Wn8B19X"}, {"__ref": "u4gK4cQ_2ph0"}, {"__ref": "JUTZ28X-ovlz"}, {"__ref": "EQRY3Rdwscdu"}, {"__ref": "8i5qF7bP-BNc"}, {"__ref": "SintozooODqR"}, {"__ref": "aS6dmVOUOIaB"}, {"__ref": "82IaSV404DmZ"}], "multi": false, "__type": "ComponentVariantGroup"}, "wscpjnelYdwN": {"type": "button", "__type": "PlumeInfo"}, "iQ2sJDi8t-H8": {"rows": [{"__ref": "NwzOa4spO18O"}, {"__ref": "URTxOim8CMyU"}, {"__ref": "4EjOHgS3n93-"}, {"__ref": "t66Q0YY37mqN"}, {"__ref": "rDFA7eUR6vnc"}, {"__ref": "pPr4acM9Ys6B"}, {"__ref": "YLIEZ_-2trPT"}], "__type": "ArenaFrameGrid"}, "wutDehf2t4v1": {"rows": [{"__ref": "e9r2peIKShmD"}], "__type": "ArenaFrameGrid"}, "bEwAJzn4ynZP": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "7GIvy8a1qDOG": {"name": "children", "uuid": "xtsBNYTWUiG3", "__type": "Var"}, "6gsKiiiTFidC": {"name": "any", "__type": "AnyType"}, "oveau0Ver1lb": {"name": "Show Start Icon", "uuid": "ZufAOsftY_n2", "__type": "Var"}, "2VmhW_8dEbny": {"name": "any", "__type": "AnyType"}, "hcjZqPeqQXht": {"name": "Show End Icon", "uuid": "itgEraMww8qB", "__type": "Var"}, "FJEykkIv_kRs": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "luIiebJieIdE": {"name": "start icon", "uuid": "Vn_tbJ3mDeid", "__type": "Var"}, "TXDm70PXNeuY": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "1PobtVuUU8sd": {"name": "end icon", "uuid": "IlgloO_VEwzF", "__type": "Var"}, "oi__Bu9hBM-4": {"name": "any", "__type": "AnyType"}, "DCKjAdunV_Mf": {"name": "Is Disabled", "uuid": "Hg5KTU3wKAx3", "__type": "Var"}, "AwFoCiUTSKl2": {"name": "href", "__type": "HrefType"}, "M9Eu9pfV4JWS": {"name": "link", "uuid": "HRikpS-kLgJK", "__type": "Var"}, "a1wkBfDXv-jI": {"name": "any", "__type": "AnyType"}, "9g0sdX3K5k1U": {"name": "Color", "uuid": "o4u17_fklKnG", "__type": "Var"}, "IEaw6Y_IfSrp": {"name": "any", "__type": "AnyType"}, "fFrQtE8eF0L4": {"name": "Size", "uuid": "Hb0AXT2EQGhm", "__type": "Var"}, "HTdMSU9wybBe": {"name": "any", "__type": "AnyType"}, "0G2s2RDcpET3": {"name": "<PERSON><PERSON><PERSON>", "uuid": "8cYj_MRlEz2V", "__type": "Var"}, "ZAQi5iRbtLYY": {"name": "bool", "__type": "BoolType"}, "HHRO3P4SaVzo": {"name": "submitsForm", "uuid": "vN4myjGofSxF", "__type": "Var"}, "j5BFYLKco4YA": {"name": "bool", "__type": "BoolType"}, "aTsmnQ2EdNNP": {"name": "target", "uuid": "hGKcfeBHnXz1", "__type": "Var"}, "UXPLgIxtu8Jf": {"name": "func", "params": [{"__ref": "-MeG4Vd3jEAp"}], "__type": "FunctionType"}, "sEFwrpzwUB-f": {"name": "On Show Start Icon change", "uuid": "cT8P2ki71xLX", "__type": "Var"}, "LSHnuWFF71pn": {"name": "func", "params": [{"__ref": "zfQyLrn99fWF"}], "__type": "FunctionType"}, "beuP9cckcldN": {"name": "On Show End Icon change", "uuid": "ghsr-gJJY5QQ", "__type": "Var"}, "xygAgRuMBXeJ": {"name": "func", "params": [{"__ref": "W-8QLiL3J1Oq"}], "__type": "FunctionType"}, "zcoKW6fBToqQ": {"name": "On Is Disabled change", "uuid": "s1mvoMollcKK", "__type": "Var"}, "QQsopFPl-IMB": {"name": "func", "params": [{"__ref": "aTymHUTCKnBW"}], "__type": "FunctionType"}, "5iw7M9yU-UH0": {"name": "On Shape change", "uuid": "iM1PamoxHwZG", "__type": "Var"}, "wo9llow40QRR": {"name": "func", "params": [{"__ref": "K2soOGUqTLsy"}], "__type": "FunctionType"}, "2wfaJqaDIu00": {"name": "On Size change", "uuid": "A6f1SkJK2jD7", "__type": "Var"}, "KunGwh2c5NZZ": {"name": "func", "params": [{"__ref": "ldW4X4svCPJy"}], "__type": "FunctionType"}, "P9DPUlDarn2c": {"name": "On Color change", "uuid": "jYbYGxuVyVDw", "__type": "Var"}, "-AD8PM10wriG": {"tag": "div", "name": "start icon container", "children": [{"__ref": "K2g4qcigFUHh"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SlIM2u86pG_5", "parent": {"__ref": "fFseKFIkyfcp"}, "locked": null, "vsettings": [{"__ref": "5m7SKmOpt2fN"}, {"__ref": "Lnzamr2EbVUU"}, {"__ref": "ykcMuIV6wXcV"}, {"__ref": "lFkazTBZDRc-"}], "__type": "TplTag"}, "mqZoSA7vgpx-": {"tag": "div", "name": "content container", "children": [{"__ref": "_1Fst0YnGkXE"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "j2fRZoGpQItg", "parent": {"__ref": "fFseKFIkyfcp"}, "locked": null, "vsettings": [{"__ref": "tSdfslfvx309"}, {"__ref": "uc9euTIJQdZx"}, {"__ref": "27QKycyLvecZ"}, {"__ref": "bRxv-37EU5yu"}, {"__ref": "5qdB8nkXZp-L"}], "__type": "TplTag"}, "jD8cgJw-RL4A": {"tag": "div", "name": "end icon container", "children": [{"__ref": "nP1Y6u--8ss4"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7cNpf5AsPmW8", "parent": {"__ref": "fFseKFIkyfcp"}, "locked": null, "vsettings": [{"__ref": "UM7cvKRzjnJV"}, {"__ref": "5fn7vo57zsZ2"}, {"__ref": "Mkknc_Vell_8"}, {"__ref": "zR5kh9ejB93Q"}], "__type": "TplTag"}, "A54GNtpC_HCL": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "d40qHDa2VKTL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "rjSCDhVA3VpS": {"variants": [{"__ref": "3aIdJRYyilVr"}], "args": [], "attrs": {}, "rs": {"__ref": "L-pwTagvfKOj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GNRRFnVsU1-O": {"variants": [{"__ref": "-PxzIgp4Gipn"}], "args": [], "attrs": {}, "rs": {"__ref": "tFlPMjUwayQu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ruWmyog4Lnw9": {"variants": [{"__ref": "1v9tOoWcYoSm"}], "args": [], "attrs": {}, "rs": {"__ref": "lI5jBL04Zbjf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lnrOhP2L2c33": {"variants": [{"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "YGIpyp_ayiS_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cBG_FRzu9QCC": {"variants": [{"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "bVIHZyv4PvWr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Nmqwt_qxIu7S": {"variants": [{"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "_Bai_asF63PE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0hyVZzYisg-N": {"variants": [{"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "UCZHwfDtPUeF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FrCaEI9CYCKO": {"variants": [{"__ref": "u4gK4cQ_2ph0"}], "args": [], "attrs": {}, "rs": {"__ref": "7xc2de1D73D8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Vk3QpjUGJrHP": {"variants": [{"__ref": "u4gK4cQ_2ph0"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "Aam3E3mWhc4e"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DXfBBGgxeUnl": {"variants": [{"__ref": "u4gK4cQ_2ph0"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "9uzr09dDNniU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14nKN44oXOGT": {"variants": [{"__ref": "JUTZ28X-ovlz"}], "args": [], "attrs": {}, "rs": {"__ref": "cru99_cu<PERSON>aqu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QPWPN0w9o21r": {"variants": [{"__ref": "JUTZ28X-ovlz"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "i-LU6qWSN6C6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8Lg19Toi-fNx": {"variants": [{"__ref": "JUTZ28X-ovlz"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "7cE5-vrLGISz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "it2Jciqc6Nns": {"variants": [{"__ref": "IYROIQz1Yz-b"}], "args": [], "attrs": {}, "rs": {"__ref": "HWvoJA_evyT2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oDot2N0oz-Q1": {"variants": [{"__ref": "IYROIQz1Yz-b"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "yOAugOM358em"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WRpmjT0GCbMr": {"variants": [{"__ref": "IYROIQz1Yz-b"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "YpHcVRgjqoRg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jwX7iGmcJX8G": {"variants": [{"__ref": "qNKoqn3Hxm6D"}], "args": [], "attrs": {}, "rs": {"__ref": "ZhSzyrx5NYbT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9Of3GY2NmpCS": {"variants": [{"__ref": "qNKoqn3Hxm6D"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "yZFdSqVuDHw4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KwjPxeqpCqKp": {"variants": [{"__ref": "qNKoqn3Hxm6D"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "oH2FGtPtGNR4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "37L03mOmi1h7": {"variants": [{"__ref": "EQRY3Rdwscdu"}], "args": [], "attrs": {}, "rs": {"__ref": "pKQVvkPhd2PO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QkmhXjGZ1oli": {"variants": [{"__ref": "EQRY3Rdwscdu"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "wwuHJWg-64-m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VEN2t1uIv8iB": {"variants": [{"__ref": "EQRY3Rdwscdu"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "6ehZX6Tm_970"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SjA6GypP95Pe": {"variants": [{"__ref": "8i5qF7bP-BNc"}], "args": [], "attrs": {}, "rs": {"__ref": "wOQQ6lkF_-Zk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1leLUzkM70Dv": {"variants": [{"__ref": "8i5qF7bP-BNc"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "YjNdAaWyUUC5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wnNLXQh7oggh": {"variants": [{"__ref": "8i5qF7bP-BNc"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "YLG3yGWsjbcp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "otuO8yaqInX-": {"variants": [{"__ref": "SintozooODqR"}], "args": [], "attrs": {}, "rs": {"__ref": "z_XIBvjo3oBQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EpvQCTlzDhX4": {"variants": [{"__ref": "SintozooODqR"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "f4804g-TOr4G"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "SNQBsymGHC7D": {"variants": [{"__ref": "SintozooODqR"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "nZf8jqsMf90Y"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-W2y29fr9vAE": {"variants": [{"__ref": "8nj5f8HiUeee"}], "args": [], "attrs": {}, "rs": {"__ref": "bpoahhj88nWi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XiASrLfWKeDp": {"variants": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "jO1mVplB4EBp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NIyPWi6lAw4n": {"variants": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "KKW1gnNupoaX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "k3qL8RLz0aOc": {"variants": [{"__ref": "U_QKl4g8LNK2"}], "args": [], "attrs": {}, "rs": {"__ref": "p9I1aAOD6OQK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Z40LqcLou5pK": {"variants": [{"__ref": "U_QKl4g8LNK2"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "lY-C-7GXBUh_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "j8EU16gG2BU0": {"variants": [{"__ref": "U_QKl4g8LNK2"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "2wR0nln_FpKL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "307WhFIhT4Dn": {"variants": [{"__ref": "gnsJ1gtzl8L3"}], "args": [], "attrs": {}, "rs": {"__ref": "gKTuVPFmTo_2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "d3laJAO1BNKs": {"variants": [{"__ref": "gnsJ1gtzl8L3"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "FaDUspdRAYRh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "k8q-yccKiC9X": {"variants": [{"__ref": "gnsJ1gtzl8L3"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "YR8aelD5mE_x"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QTK31q0oVppU": {"variants": [{"__ref": "mK8gueDbkXfE"}], "args": [], "attrs": {}, "rs": {"__ref": "CPfe5w6TgdnF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Ikl6VLBixrsM": {"variants": [{"__ref": "mK8gueDbkXfE"}, {"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "82bj6MCZt5zL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "29OndlS1vktu": {"variants": [{"__ref": "mK8gueDbkXfE"}, {"__ref": "DDjI7dHvC5Rj"}, {"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "1mBhILZbW3sa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AkBePQZLqO1U": {"variants": [{"__ref": "mK8gueDbkXfE"}, {"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "3VPRTeQ_mnvH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "04sViG-BA6qp": {"variants": [{"__ref": "j27i2xhJuV02"}], "args": [], "attrs": {}, "rs": {"__ref": "KRO72XVLz6VV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I-430G1YCJ1x": {"variants": [{"__ref": "j27i2xhJuV02"}, {"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "DBc_7eeJ4wuO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DA0X0X6q23Nd": {"variants": [{"__ref": "dAo57WBDNsb4"}, {"__ref": "j27i2xhJuV02"}], "args": [], "attrs": {}, "rs": {"__ref": "UY-I9fPZJ2mq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "g-20h3EkuelP": {"variants": [{"__ref": "mK8gueDbkXfE"}, {"__ref": "j27i2xhJuV02"}], "args": [], "attrs": {}, "rs": {"__ref": "Oo4Q34i1IoPJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2FPu5zUfppWP": {"variants": [{"__ref": "aS6dmVOUOIaB"}], "args": [], "attrs": {}, "rs": {"__ref": "w1-SrxDoU0e6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EBTbS4Jw4NUt": {"variants": [{"__ref": "aS6dmVOUOIaB"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "b_D8F2VtUpiA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hlvo5G73mVl4": {"variants": [{"__ref": "aS6dmVOUOIaB"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "DG7-K9_1Yi4q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_aEu40ijXdYo": {"variants": [{"__ref": "kYC798MFgATQ"}], "args": [], "attrs": {}, "rs": {"__ref": "cM2wTleI_a7u"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mGq5elyyTMjF": {"variants": [{"__ref": "kYC798MFgATQ"}, {"__ref": "mK8gueDbkXfE"}], "args": [], "attrs": {}, "rs": {"__ref": "aSErqcgRrHj1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xg679t_1avfB": {"variants": [{"__ref": "82IaSV404DmZ"}], "args": [], "attrs": {}, "rs": {"__ref": "6_p_Bi-5l4Wn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PqZ_VRqPk8it": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "l5exzh0ZL2Ku"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1MgJxVunmo90": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "-XBVdjHnwAQz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DLkRGAq3Ewqg": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "D3aNhhliIcew"}], "args": [], "attrs": {}, "rs": {"__ref": "vSYZ1eFZPfCx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vN1IWWyX3Xtz": {"variants": [{"__ref": "D3aNhhliIcew"}], "args": [], "attrs": {}, "rs": {"__ref": "LjuE2YHNfT95"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r5Dy5Ie5Has6": {"variants": [{"__ref": "3KS--Wn8B19X"}], "args": [], "attrs": {}, "rs": {"__ref": "HL8OXkImi-_3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lEH5SEefpsnj": {"variants": [{"__ref": "3KS--Wn8B19X"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "tD_pUoX8HHLy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_oIZhAjvGRPK": {"variants": [{"__ref": "3KS--Wn8B19X"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "hpqooKS-d1A4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MTbvZs4U2UoR": {"variants": [{"__ref": "zsocOgY1UFfj"}], "args": [], "attrs": {}, "rs": {"__ref": "z-Kn_TpLe_oW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DDjI7dHvC5Rj": {"uuid": "WsIPd1sj3_09", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "9I-n0ianRSYF"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "dAo57WBDNsb4": {"uuid": "obis-wyUVjbu", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "QLbYT6TAl0e5"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "1v9tOoWcYoSm": {"uuid": "T6umNc1TqbxG", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "-9a0hdnTsCSM"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "j27i2xhJuV02": {"uuid": "TaVwuiNQF5as", "name": "Rounded", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "W0-uDb6WjCBT"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kYC798MFgATQ": {"uuid": "Yn35qem2AB15", "name": "Round", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "W0-uDb6WjCBT"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zsocOgY1UFfj": {"uuid": "xh5xcKUFsod8", "name": "<PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "W0-uDb6WjCBT"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mK8gueDbkXfE": {"uuid": "7OF38Ou3MmUU", "name": "Compact", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "pA3Hz3N_ERjN"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "D3aNhhliIcew": {"uuid": "rWi4G0o130Ab", "name": "Minimal", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "pA3Hz3N_ERjN"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8nj5f8HiUeee": {"uuid": "FWOaXnK7boFj", "name": "Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "U_QKl4g8LNK2": {"uuid": "-GqXC2UlRyc-", "name": "Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "IYROIQz1Yz-b": {"uuid": "JQt8nfNTAKnt", "name": "Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "qNKoqn3Hxm6D": {"uuid": "eu2C3OGySijd", "name": "Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gnsJ1gtzl8L3": {"uuid": "LoA9u4R5jsM3", "name": "Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3KS--Wn8B19X": {"uuid": "2353hUSjuwFq", "name": "White", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "u4gK4cQ_2ph0": {"uuid": "-zHJZF4NInv_", "name": "Soft Blue", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "JUTZ28X-ovlz": {"uuid": "KvJ48uPGun0d", "name": "Soft Green", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "EQRY3Rdwscdu": {"uuid": "hPK3vkfXWRHV", "name": "Soft Yellow", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8i5qF7bP-BNc": {"uuid": "ylpCLkih5Ncs", "name": "Soft Red", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "SintozooODqR": {"uuid": "Gv-prOq8O1d0", "name": "Soft Sand", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "aS6dmVOUOIaB": {"uuid": "c4tlQ_JzhFRe", "name": "Clear", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "82IaSV404DmZ": {"uuid": "QW_NSfSoVhoJ", "name": "Link", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "lprUB35u6EM3"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "NwzOa4spO18O": {"cols": [{"__ref": "VFHK5vtdFJa8"}, {"__ref": "Lhxf5-1eBMOZ"}, {"__ref": "fHw57BmV3Qun"}, {"__ref": "hpIithQp7fIO"}, {"__ref": "UdrV_DmKErOl"}], "rowKey": null, "__type": "ArenaFrameRow"}, "URTxOim8CMyU": {"cols": [{"__ref": "JhEVcCxbgFdI"}], "rowKey": {"__ref": "9I-n0ianRSYF"}, "__type": "ArenaFrameRow"}, "4EjOHgS3n93-": {"cols": [{"__ref": "ixX7LujQqcMS"}], "rowKey": {"__ref": "QLbYT6TAl0e5"}, "__type": "ArenaFrameRow"}, "t66Q0YY37mqN": {"cols": [{"__ref": "nP06c7N3cwjA"}], "rowKey": {"__ref": "-9a0hdnTsCSM"}, "__type": "ArenaFrameRow"}, "rDFA7eUR6vnc": {"cols": [{"__ref": "LguiJbNK5IHZ"}, {"__ref": "ntYsFmMi7eid"}, {"__ref": "JJF-tuCFdeVh"}], "rowKey": {"__ref": "W0-uDb6WjCBT"}, "__type": "ArenaFrameRow"}, "pPr4acM9Ys6B": {"cols": [{"__ref": "HJ2DGPLzIM-E"}, {"__ref": "NWOIVDKlcZb1"}], "rowKey": {"__ref": "pA3Hz3N_ERjN"}, "__type": "ArenaFrameRow"}, "YLIEZ_-2trPT": {"cols": [{"__ref": "r33a_wHzGrpg"}, {"__ref": "V1Pq5tR1fxRB"}, {"__ref": "k7FGw8o5Hg_T"}, {"__ref": "eGVNtYVPtxEb"}, {"__ref": "7Smna96tIZRZ"}, {"__ref": "heMDyM5OlD50"}, {"__ref": "r6jfWZCfcBr3"}, {"__ref": "zoKG4hAKhcr1"}, {"__ref": "N46YvD16kAqZ"}, {"__ref": "AUxjYariqqAq"}, {"__ref": "VQ4kXRcgjiQa"}, {"__ref": "Ba17HyQlUxs6"}, {"__ref": "TxorrJ3tixo9"}], "rowKey": {"__ref": "lprUB35u6EM3"}, "__type": "ArenaFrameRow"}, "e9r2peIKShmD": {"cols": [{"__ref": "Gi5vITx1Gi4f"}, {"__ref": "nvUAqp1UW4o8"}, {"__ref": "XVOoKSdcRHUZ"}, {"__ref": "KINgshQQWjeq"}, {"__ref": "uK3CIC_da4G7"}, {"__ref": "rh78pFIhpOSU"}, {"__ref": "G5TNxZb_oJr5"}, {"__ref": "0mSDIRGzjlAy"}, {"__ref": "uHOrk2Pi0z6j"}, {"__ref": "uPzywbnSc3aV"}, {"__ref": "e35DKgSDae4d"}, {"__ref": "irv7Vb11nJva"}, {"__ref": "VDWi7xn1NM4f"}, {"__ref": "-0JEprueuD8X"}, {"__ref": "oKvJSWsRZMA_"}, {"__ref": "AMuT25TRYVaT"}, {"__ref": "hiy1rA97g7yP"}, {"__ref": "1TXa9sl12Znr"}, {"__ref": "_f3gqxvrAWQH"}, {"__ref": "gGzuAt9bcdTS"}, {"__ref": "r-oXjfvjb0ql"}, {"__ref": "e2UHlY4CLxjr"}, {"__ref": "1T5oKzQKrIy6"}, {"__ref": "RUqBksWgbTLl"}, {"__ref": "lITm_GyUNKP5"}, {"__ref": "8McB2ZgX50DG"}, {"__ref": "dfJx45LLA-UU"}, {"__ref": "VOMsdV_YhlQD"}, {"__ref": "Pj-eX4opw-fE"}], "rowKey": null, "__type": "ArenaFrameRow"}, "-MeG4Vd3jEAp": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "Nuih57tGmjNn"}, "__type": "ArgType"}, "zfQyLrn99fWF": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "a0FIwfQWAeOq"}, "__type": "ArgType"}, "W-8QLiL3J1Oq": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "aLtdrh4XLKti"}, "__type": "ArgType"}, "aTymHUTCKnBW": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "Qf7Ib6ZAlu1w"}, "__type": "ArgType"}, "K2soOGUqTLsy": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "OaQNpXDACepR"}, "__type": "ArgType"}, "ldW4X4svCPJy": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "R2SM_fZJ0xzX"}, "__type": "ArgType"}, "K2g4qcigFUHh": {"param": {"__ref": "58W11N2MXQvz"}, "defaultContents": [{"__ref": "Hc9RYFyNCXv5"}], "uuid": "sTZePNTBshfR", "parent": {"__ref": "-AD8PM10wriG"}, "locked": null, "vsettings": [{"__ref": "1vHCtNG76Ppx"}, {"__ref": "aPq1gGg4gb7G"}, {"__ref": "gEC-n9tyx98X"}, {"__ref": "ltuis_Ln1hWo"}, {"__ref": "19av7qQ9_uz2"}, {"__ref": "YQ6uZ9idDonN"}, {"__ref": "tUX8_ddx369u"}, {"__ref": "x08-Pgy21DEB"}, {"__ref": "jfQvbRpXmZ5v"}, {"__ref": "4qwjoevkfFC1"}, {"__ref": "vFFDou-YBskg"}, {"__ref": "Ff0-hXfx2nLH"}, {"__ref": "C0reYx-aGFCi"}, {"__ref": "CtBNQ3JwBYNr"}], "__type": "TplSlot"}, "5m7SKmOpt2fN": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "4B9jYH8d2oIg"}, "dataCond": {"__ref": "D5cb5C3Pspnc"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Lnzamr2EbVUU": {"variants": [{"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "I-aB--B7j<PERSON>la"}, "dataCond": {"__ref": "WqtItaSU2wIy"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ykcMuIV6wXcV": {"variants": [{"__ref": "8nj5f8HiUeee"}], "args": [], "attrs": {}, "rs": {"__ref": "Hg-UyGaW-E6S"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lFkazTBZDRc-": {"variants": [{"__ref": "j27i2xhJuV02"}, {"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "i_bi8DOR9qAV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_1Fst0YnGkXE": {"param": {"__ref": "jiGTb077efD0"}, "defaultContents": [{"__ref": "t3hYJ-IW8WpW"}], "uuid": "HkF3rsfA0WBQ", "parent": {"__ref": "mqZoSA7vgpx-"}, "locked": null, "vsettings": [{"__ref": "QDdn2A225A2y"}, {"__ref": "VrmHykI_yJ75"}, {"__ref": "qQpYTnpuOlNn"}, {"__ref": "ddvlL6V4aVdz"}, {"__ref": "J5Bd1E45Id9G"}, {"__ref": "zeNYADdP6hYi"}, {"__ref": "kBqkl16n-8Rc"}, {"__ref": "xQ9EIJLlgzqv"}, {"__ref": "73VH3qzktqqo"}, {"__ref": "t4nzdDvTylcb"}, {"__ref": "F9r8mw9d26dB"}, {"__ref": "UnS3lFQeaX4L"}, {"__ref": "LG1yZ0Hvd0HO"}, {"__ref": "7jji9or5ARrK"}, {"__ref": "8A_PIviOBVuF"}, {"__ref": "ik06BP1iOkcB"}, {"__ref": "x5mAexUn9Ni1"}, {"__ref": "OyNKBKOgTXu8"}, {"__ref": "YmAQzVMaZQUf"}, {"__ref": "fIfc5tLc722K"}, {"__ref": "fTktzPizQ5am"}, {"__ref": "jh9rxd7ape8M"}, {"__ref": "hv-poB8dFaQN"}, {"__ref": "rrTZdT7FHdLf"}, {"__ref": "4hJwSmtbgfA2"}, {"__ref": "yfgS9CF9GbdU"}], "__type": "TplSlot"}, "tSdfslfvx309": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "28q2s9oRv7_8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uc9euTIJQdZx": {"variants": [{"__ref": "1v9tOoWcYoSm"}], "args": [], "attrs": {}, "rs": {"__ref": "dbiDc4tiYaHw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "27QKycyLvecZ": {"variants": [{"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "XNIjAMWsgosr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bRxv-37EU5yu": {"variants": [{"__ref": "3aIdJRYyilVr"}], "args": [], "attrs": {}, "rs": {"__ref": "id6dMbn5QBeW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5qdB8nkXZp-L": {"variants": [{"__ref": "j27i2xhJuV02"}], "args": [], "attrs": {}, "rs": {"__ref": "Jq-dTElBLx9D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nP1Y6u--8ss4": {"param": {"__ref": "ALGBMmJdKGZS"}, "defaultContents": [{"__ref": "2SVx_51IHejC"}], "uuid": "G6xWqgnZp1J6", "parent": {"__ref": "jD8cgJw-RL4A"}, "locked": null, "vsettings": [{"__ref": "lz1RbmqTkahQ"}, {"__ref": "3g62QvkvMYRf"}, {"__ref": "05XPAvsfCzEQ"}, {"__ref": "6mw-pHL47rmS"}, {"__ref": "x_K20WbPq7B2"}, {"__ref": "Kh4EDrgxEw3x"}, {"__ref": "atC-TTkfypxs"}, {"__ref": "kz2fa-C62mSo"}, {"__ref": "5hm0GwaFLvIX"}, {"__ref": "QsbilZW34NKD"}, {"__ref": "uDis-jv3pXAJ"}, {"__ref": "Uc72VLiOcNKK"}, {"__ref": "42wysGtVl41q"}], "__type": "TplSlot"}, "UM7cvKRzjnJV": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "mvGKJeoV0Gut"}, "dataCond": {"__ref": "bkwylC010fk9"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5fn7vo57zsZ2": {"variants": [{"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "tPjczqUlop_M"}, "dataCond": {"__ref": "GX96svNtWkyb"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Mkknc_Vell_8": {"variants": [{"__ref": "IYROIQz1Yz-b"}], "args": [], "attrs": {}, "rs": {"__ref": "1pPQTCxnfiJG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zR5kh9ejB93Q": {"variants": [{"__ref": "3KS--Wn8B19X"}], "args": [], "attrs": {}, "rs": {"__ref": "L6gkyIEIICR1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "d40qHDa2VKTL": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "center", "justify-content": "center", "padding-top": "12px", "padding-right": "20px", "padding-bottom": "12px", "padding-left": "20px", "background": "linear-gradient(#232320, #232320)", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "cursor": "pointer", "transition-property": "background", "transition-duration": "0.1s", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "column-gap": "8px"}, "mixins": [], "__type": "RuleSet"}, "L-pwTagvfKOj": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "tFlPMjUwayQu": {"values": {"box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "lI5jBL04Zbjf": {"values": {"cursor": "not-allowed", "opacity": "0.6"}, "mixins": [], "__type": "RuleSet"}, "YGIpyp_ayiS_": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "bVIHZyv4PvWr": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "_Bai_asF63PE": {"values": {"background": "linear-gradient(#282826, #282826)"}, "mixins": [], "__type": "RuleSet"}, "UCZHwfDtPUeF": {"values": {"background": "linear-gradient(#2E2E2B, #2E2E2B)"}, "mixins": [], "__type": "RuleSet"}, "7xc2de1D73D8": {"values": {"background": "linear-gradient(#EDF6FF, #EDF6FF)"}, "mixins": [], "__type": "RuleSet"}, "Aam3E3mWhc4e": {"values": {"background": "linear-gradient(#E1F0FF, #E1F0FF)"}, "mixins": [], "__type": "RuleSet"}, "9uzr09dDNniU": {"values": {"background": "linear-gradient(#CEE7FE, #CEE7FE)"}, "mixins": [], "__type": "RuleSet"}, "cru99_cuKaqu": {"values": {"background": "linear-gradient(#E9F9EE, #E9F9EE)"}, "mixins": [], "__type": "RuleSet"}, "i-LU6qWSN6C6": {"values": {"background": "linear-gradient(#CCEBD7, #CCEBD7)"}, "mixins": [], "__type": "RuleSet"}, "7cE5-vrLGISz": {"values": {"background": "linear-gradient(#DDF3E4, #DDF3E4)"}, "mixins": [], "__type": "RuleSet"}, "HWvoJA_evyT2": {"values": {"background": "linear-gradient(#F5D90A, #F5D90A)"}, "mixins": [], "__type": "RuleSet"}, "yOAugOM358em": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "YpHcVRgjqoRg": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "ZhSzyrx5NYbT": {"values": {"background": "linear-gradient(#E54D2E, #E54D2E)"}, "mixins": [], "__type": "RuleSet"}, "yZFdSqVuDHw4": {"values": {"background": "linear-gradient(#EC5E41, #EC5E41)"}, "mixins": [], "__type": "RuleSet"}, "oH2FGtPtGNR4": {"values": {"background": "linear-gradient(#F16A50, #F16A50)"}, "mixins": [], "__type": "RuleSet"}, "pKQVvkPhd2PO": {"values": {"background": "linear-gradient(#FFFBD1, #FFFBD1)"}, "mixins": [], "__type": "RuleSet"}, "wwuHJWg-64-m": {"values": {"background": "linear-gradient(#FEF2A4, #FEF2A4)"}, "mixins": [], "__type": "RuleSet"}, "6ehZX6Tm_970": {"values": {"background": "linear-gradient(#FFF8BB, #FFF8BB)"}, "mixins": [], "__type": "RuleSet"}, "wOQQ6lkF_-Zk": {"values": {"background": "linear-gradient(#FFF0EE, #FFF0EE)"}, "mixins": [], "__type": "RuleSet"}, "YjNdAaWyUUC5": {"values": {"background": "linear-gradient(#FDD8D3, #FDD8D3)"}, "mixins": [], "__type": "RuleSet"}, "YLG3yGWsjbcp": {"values": {"background": "linear-gradient(#FFE6E2, #FFE6E2)"}, "mixins": [], "__type": "RuleSet"}, "z_XIBvjo3oBQ": {"values": {"background": "linear-gradient(#EEEEEC, #EEEEEC)"}, "mixins": [], "__type": "RuleSet"}, "f4804g-TOr4G": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "nZf8jqsMf90Y": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "bpoahhj88nWi": {"values": {"background": "linear-gradient(#0091FF, #0091FF)"}, "mixins": [], "__type": "RuleSet"}, "jO1mVplB4EBp": {"values": {"background": "linear-gradient(#369EFF, #369EFF)"}, "mixins": [], "__type": "RuleSet"}, "KKW1gnNupoaX": {"values": {"background": "linear-gradient(#52A9FF, #52A9FF)"}, "mixins": [], "__type": "RuleSet"}, "p9I1aAOD6OQK": {"values": {"background": "linear-gradient(#30A46C, #30A46C)"}, "mixins": [], "__type": "RuleSet"}, "lY-C-7GXBUh_": {"values": {"background": "linear-gradient(#3CB179, #3CB179)"}, "mixins": [], "__type": "RuleSet"}, "2wR0nln_FpKL": {"values": {"background": "linear-gradient(#4CC38A, #4CC38A)"}, "mixins": [], "__type": "RuleSet"}, "gKTuVPFmTo_2": {"values": {"background": "linear-gradient(#717069, #717069)"}, "mixins": [], "__type": "RuleSet"}, "FaDUspdRAYRh": {"values": {"background": "linear-gradient(#7F7E77, #7F7E77)"}, "mixins": [], "__type": "RuleSet"}, "YR8aelD5mE_x": {"values": {"background": "linear-gradient(#A1A09A, #A1A09A)"}, "mixins": [], "__type": "RuleSet"}, "CPfe5w6TgdnF": {"values": {"padding-top": "6px", "padding-right": "16px", "padding-bottom": "6px", "padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "82bj6MCZt5zL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1mBhILZbW3sa": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3VPRTeQ_mnvH": {"values": {}, "mixins": [], "__type": "RuleSet"}, "KRO72XVLz6VV": {"values": {"border-top-left-radius": "999px", "border-top-right-radius": "999px", "border-bottom-right-radius": "999px", "border-bottom-left-radius": "999px", "padding-left": "20px", "padding-right": "20px", "min-width": "100px"}, "mixins": [], "__type": "RuleSet"}, "DBc_7eeJ4wuO": {"values": {"padding-left": "16px"}, "mixins": [], "__type": "RuleSet"}, "UY-I9fPZJ2mq": {"values": {"padding-right": "16px"}, "mixins": [], "__type": "RuleSet"}, "Oo4Q34i1IoPJ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "w1-SrxDoU0e6": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "b_D8F2VtUpiA": {"values": {"background": "linear-gradient(#E9E9E6, #E9E9E6)"}, "mixins": [], "__type": "RuleSet"}, "DG7-K9_1Yi4q": {"values": {"background": "linear-gradient(#E3E3E0, #E3E3E0)"}, "mixins": [], "__type": "RuleSet"}, "cM2wTleI_a7u": {"values": {"padding-top": "12px", "padding-right": "12px", "padding-bottom": "12px", "padding-left": "12px", "border-top-left-radius": "50%", "border-top-right-radius": "50%", "border-bottom-right-radius": "50%", "border-bottom-left-radius": "50%"}, "mixins": [], "__type": "RuleSet"}, "aSErqcgRrHj1": {"values": {"padding-top": "6px", "padding-right": "6px", "padding-bottom": "6px", "padding-left": "6px"}, "mixins": [], "__type": "RuleSet"}, "6_p_Bi-5l4Wn": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "l5exzh0ZL2Ku": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "-XBVdjHnwAQz": {"values": {"background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "vSYZ1eFZPfCx": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LjuE2YHNfT95": {"values": {"padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "HL8OXkImi-_3": {"values": {"background": "linear-gradient(#FFFFFF, #FFFFFF)"}, "mixins": [], "__type": "RuleSet"}, "tD_pUoX8HHLy": {"values": {"background": "linear-gradient(#FFEF5C, #FFEF5C)"}, "mixins": [], "__type": "RuleSet"}, "hpqooKS-d1A4": {"values": {"background": "linear-gradient(#F0C000, #F0C000)"}, "mixins": [], "__type": "RuleSet"}, "z-Kn_TpLe_oW": {"values": {"border-top-left-radius": "0px", "border-top-right-radius": "0px", "border-bottom-right-radius": "0px", "border-bottom-left-radius": "0px"}, "mixins": [], "__type": "RuleSet"}, "VFHK5vtdFJa8": {"frame": {"__ref": "KiIKbpGrdVSM"}, "cellKey": {"__ref": "qObSIcZZM9pK"}, "__type": "ArenaFrameCell"}, "Lhxf5-1eBMOZ": {"frame": {"__ref": "tDSaXyJA_Bmn"}, "cellKey": {"__ref": "3aIdJRYyilVr"}, "__type": "ArenaFrameCell"}, "fHw57BmV3Qun": {"frame": {"__ref": "aMZs90MvnU_m"}, "cellKey": {"__ref": "-PxzIgp4Gipn"}, "__type": "ArenaFrameCell"}, "hpIithQp7fIO": {"frame": {"__ref": "e3Yu4BtMPQPw"}, "cellKey": {"__ref": "Gf2SAp8FjA8g"}, "__type": "ArenaFrameCell"}, "UdrV_DmKErOl": {"frame": {"__ref": "nqhwRMWuS-xh"}, "cellKey": {"__ref": "FBRFKN3-GALk"}, "__type": "ArenaFrameCell"}, "JhEVcCxbgFdI": {"frame": {"__ref": "E2UE68aqramn"}, "cellKey": {"__ref": "DDjI7dHvC5Rj"}, "__type": "ArenaFrameCell"}, "ixX7LujQqcMS": {"frame": {"__ref": "1AETCe6TowyR"}, "cellKey": {"__ref": "dAo57WBDNsb4"}, "__type": "ArenaFrameCell"}, "nP06c7N3cwjA": {"frame": {"__ref": "Eu23212pkCLu"}, "cellKey": {"__ref": "1v9tOoWcYoSm"}, "__type": "ArenaFrameCell"}, "LguiJbNK5IHZ": {"frame": {"__ref": "bIlGjtyvTjbi"}, "cellKey": {"__ref": "j27i2xhJuV02"}, "__type": "ArenaFrameCell"}, "ntYsFmMi7eid": {"frame": {"__ref": "pS4U0727DhRy"}, "cellKey": {"__ref": "kYC798MFgATQ"}, "__type": "ArenaFrameCell"}, "JJF-tuCFdeVh": {"frame": {"__ref": "6N7Nf23xbeoL"}, "cellKey": {"__ref": "zsocOgY1UFfj"}, "__type": "ArenaFrameCell"}, "HJ2DGPLzIM-E": {"frame": {"__ref": "YSTe-hBn_UpN"}, "cellKey": {"__ref": "mK8gueDbkXfE"}, "__type": "ArenaFrameCell"}, "NWOIVDKlcZb1": {"frame": {"__ref": "k95TU6pAIetq"}, "cellKey": {"__ref": "D3aNhhliIcew"}, "__type": "ArenaFrameCell"}, "r33a_wHzGrpg": {"frame": {"__ref": "p351uIdJRVIQ"}, "cellKey": {"__ref": "8nj5f8HiUeee"}, "__type": "ArenaFrameCell"}, "V1Pq5tR1fxRB": {"frame": {"__ref": "eoQIVBj_z5dz"}, "cellKey": {"__ref": "U_QKl4g8LNK2"}, "__type": "ArenaFrameCell"}, "k7FGw8o5Hg_T": {"frame": {"__ref": "xAGHRUbcj3NS"}, "cellKey": {"__ref": "IYROIQz1Yz-b"}, "__type": "ArenaFrameCell"}, "eGVNtYVPtxEb": {"frame": {"__ref": "QUeNwtLS-GHD"}, "cellKey": {"__ref": "qNKoqn3Hxm6D"}, "__type": "ArenaFrameCell"}, "7Smna96tIZRZ": {"frame": {"__ref": "Duxi0BHTNL8Y"}, "cellKey": {"__ref": "gnsJ1gtzl8L3"}, "__type": "ArenaFrameCell"}, "heMDyM5OlD50": {"frame": {"__ref": "5nAl4oCHk-_J"}, "cellKey": {"__ref": "3KS--Wn8B19X"}, "__type": "ArenaFrameCell"}, "r6jfWZCfcBr3": {"frame": {"__ref": "MChV2evqM_Ne"}, "cellKey": {"__ref": "u4gK4cQ_2ph0"}, "__type": "ArenaFrameCell"}, "zoKG4hAKhcr1": {"frame": {"__ref": "z7MT96VYIpMX"}, "cellKey": {"__ref": "JUTZ28X-ovlz"}, "__type": "ArenaFrameCell"}, "N46YvD16kAqZ": {"frame": {"__ref": "DPAQO4H8zNly"}, "cellKey": {"__ref": "EQRY3Rdwscdu"}, "__type": "ArenaFrameCell"}, "AUxjYariqqAq": {"frame": {"__ref": "EQFjQk7w-su5"}, "cellKey": {"__ref": "8i5qF7bP-BNc"}, "__type": "ArenaFrameCell"}, "VQ4kXRcgjiQa": {"frame": {"__ref": "dJqzz0Oh_jF_"}, "cellKey": {"__ref": "SintozooODqR"}, "__type": "ArenaFrameCell"}, "Ba17HyQlUxs6": {"frame": {"__ref": "SwF3g_TM_5P3"}, "cellKey": {"__ref": "aS6dmVOUOIaB"}, "__type": "ArenaFrameCell"}, "TxorrJ3tixo9": {"frame": {"__ref": "Eznku1_6Z56E"}, "cellKey": {"__ref": "82IaSV404DmZ"}, "__type": "ArenaFrameCell"}, "Gi5vITx1Gi4f": {"frame": {"__ref": "MyFXOIlfxBDw"}, "cellKey": [{"__ref": "u4gK4cQ_2ph0"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "nvUAqp1UW4o8": {"frame": {"__ref": "l3XfQTA5UKf_"}, "cellKey": [{"__ref": "u4gK4cQ_2ph0"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "XVOoKSdcRHUZ": {"frame": {"__ref": "vYoYf05QTlZQ"}, "cellKey": [{"__ref": "JUTZ28X-ovlz"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "KINgshQQWjeq": {"frame": {"__ref": "PD59KPXEV5zr"}, "cellKey": [{"__ref": "JUTZ28X-ovlz"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "uK3CIC_da4G7": {"frame": {"__ref": "kgNaaTI1HeoN"}, "cellKey": [{"__ref": "IYROIQz1Yz-b"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "rh78pFIhpOSU": {"frame": {"__ref": "-kuTfrA_rkXl"}, "cellKey": [{"__ref": "IYROIQz1Yz-b"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "G5TNxZb_oJr5": {"frame": {"__ref": "biwf6GSu63nF"}, "cellKey": [{"__ref": "qNKoqn3Hxm6D"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "0mSDIRGzjlAy": {"frame": {"__ref": "yAfA13i4cWri"}, "cellKey": [{"__ref": "qNKoqn3Hxm6D"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "uHOrk2Pi0z6j": {"frame": {"__ref": "P0INQ6liSAux"}, "cellKey": [{"__ref": "EQRY3Rdwscdu"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "uPzywbnSc3aV": {"frame": {"__ref": "jI1X7bSKEAfl"}, "cellKey": [{"__ref": "EQRY3Rdwscdu"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "e35DKgSDae4d": {"frame": {"__ref": "ievubZFBGcLX"}, "cellKey": [{"__ref": "8i5qF7bP-BNc"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "irv7Vb11nJva": {"frame": {"__ref": "leOAGW-9UPKJ"}, "cellKey": [{"__ref": "8i5qF7bP-BNc"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "VDWi7xn1NM4f": {"frame": {"__ref": "ySuDZXusBB10"}, "cellKey": [{"__ref": "SintozooODqR"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "-0JEprueuD8X": {"frame": {"__ref": "-T_F-aCmDFoU"}, "cellKey": [{"__ref": "SintozooODqR"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "oKvJSWsRZMA_": {"frame": {"__ref": "w9rp4qHfgiwy"}, "cellKey": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "AMuT25TRYVaT": {"frame": {"__ref": "DpGk1G-U6sIg"}, "cellKey": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "hiy1rA97g7yP": {"frame": {"__ref": "XZLqQ4bkd7EL"}, "cellKey": [{"__ref": "U_QKl4g8LNK2"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "1TXa9sl12Znr": {"frame": {"__ref": "kbFNoFBgz6ua"}, "cellKey": [{"__ref": "U_QKl4g8LNK2"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "_f3gqxvrAWQH": {"frame": {"__ref": "BGTy6QbPJs0Z"}, "cellKey": [{"__ref": "gnsJ1gtzl8L3"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "gGzuAt9bcdTS": {"frame": {"__ref": "XcaJQvSdZ33C"}, "cellKey": [{"__ref": "gnsJ1gtzl8L3"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "r-oXjfvjb0ql": {"frame": {"__ref": "T7yIG6PcAqI7"}, "cellKey": [{"__ref": "j27i2xhJuV02"}, {"__ref": "DDjI7dHvC5Rj"}], "__type": "ArenaFrameCell"}, "e2UHlY4CLxjr": {"frame": {"__ref": "YRzQMqBrRozR"}, "cellKey": [{"__ref": "dAo57WBDNsb4"}, {"__ref": "j27i2xhJuV02"}], "__type": "ArenaFrameCell"}, "1T5oKzQKrIy6": {"frame": {"__ref": "9pK4Z8CVv28t"}, "cellKey": [{"__ref": "aS6dmVOUOIaB"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "RUqBksWgbTLl": {"frame": {"__ref": "r4Q7xD9m511v"}, "cellKey": [{"__ref": "aS6dmVOUOIaB"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "lITm_GyUNKP5": {"frame": {"__ref": "WtEL3nj-afwk"}, "cellKey": [{"__ref": "kYC798MFgATQ"}, {"__ref": "mK8gueDbkXfE"}], "__type": "ArenaFrameCell"}, "8McB2ZgX50DG": {"frame": {"__ref": "CeYI9aPBbut3"}, "cellKey": [{"__ref": "82IaSV404DmZ"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "dfJx45LLA-UU": {"frame": {"__ref": "KQ6QjxMXTTP6"}, "cellKey": [{"__ref": "82IaSV404DmZ"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "VOMsdV_YhlQD": {"frame": {"__ref": "x9miYHwlUSf8"}, "cellKey": [{"__ref": "3KS--Wn8B19X"}, {"__ref": "Gf2SAp8FjA8g"}], "__type": "ArenaFrameCell"}, "Pj-eX4opw-fE": {"frame": {"__ref": "HhBOs_XM1rK8"}, "cellKey": [{"__ref": "3KS--Wn8B19X"}, {"__ref": "FBRFKN3-GALk"}], "__type": "ArenaFrameCell"}, "Nuih57tGmjNn": {"name": "any", "__type": "AnyType"}, "a0FIwfQWAeOq": {"name": "any", "__type": "AnyType"}, "aLtdrh4XLKti": {"name": "any", "__type": "AnyType"}, "Qf7Ib6ZAlu1w": {"name": "any", "__type": "AnyType"}, "OaQNpXDACepR": {"name": "any", "__type": "AnyType"}, "R2SM_fZJ0xzX": {"name": "any", "__type": "AnyType"}, "Hc9RYFyNCXv5": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "kIVk5iYBed4W", "parent": {"__ref": "K2g4qcigFUHh"}, "locked": null, "vsettings": [{"__ref": "ucc_GZh9SCAx"}], "__type": "TplTag"}, "1vHCtNG76Ppx": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "STTZc0CBpov1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aPq1gGg4gb7G": {"variants": [{"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "iw2JRkAlgX2Q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gEC-n9tyx98X": {"variants": [{"__ref": "8nj5f8HiUeee"}], "args": [], "attrs": {}, "rs": {"__ref": "vo7K7eUgX5BO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ltuis_Ln1hWo": {"variants": [{"__ref": "u4gK4cQ_2ph0"}], "args": [], "attrs": {}, "rs": {"__ref": "WG_yliTkPeHg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "19av7qQ9_uz2": {"variants": [{"__ref": "JUTZ28X-ovlz"}], "args": [], "attrs": {}, "rs": {"__ref": "-b2ipyWWr97G"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YQ6uZ9idDonN": {"variants": [{"__ref": "EQRY3Rdwscdu"}], "args": [], "attrs": {}, "rs": {"__ref": "SHTASAC9JdMF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tUX8_ddx369u": {"variants": [{"__ref": "8i5qF7bP-BNc"}], "args": [], "attrs": {}, "rs": {"__ref": "fDgJtfK2_6Br"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x08-Pgy21DEB": {"variants": [{"__ref": "SintozooODqR"}], "args": [], "attrs": {}, "rs": {"__ref": "0oBjc1GjS2tq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jfQvbRpXmZ5v": {"variants": [{"__ref": "IYROIQz1Yz-b"}], "args": [], "attrs": {}, "rs": {"__ref": "YcLoWHeKeRVZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4qwjoevkfFC1": {"variants": [{"__ref": "82IaSV404DmZ"}], "args": [], "attrs": {}, "rs": {"__ref": "bhCkjp6ClAyE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vFFDou-YBskg": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "V10aoFLRNJw3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Ff0-hXfx2nLH": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "jeh23JVpCxgM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "C0reYx-aGFCi": {"variants": [{"__ref": "aS6dmVOUOIaB"}], "args": [], "attrs": {}, "rs": {"__ref": "rTRoOZnbzWFb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CtBNQ3JwBYNr": {"variants": [{"__ref": "3KS--Wn8B19X"}], "args": [], "attrs": {}, "rs": {"__ref": "BQp4xfiGdPP-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4B9jYH8d2oIg": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "D5cb5C3Pspnc": {"code": "false", "fallback": null, "__type": "CustomCode"}, "I-aB--B7jIla": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "WqtItaSU2wIy": {"code": "true", "fallback": null, "__type": "CustomCode"}, "Hg-UyGaW-E6S": {"values": {}, "mixins": [], "__type": "RuleSet"}, "i_bi8DOR9qAV": {"values": {}, "mixins": [], "__type": "RuleSet"}, "t3hYJ-IW8WpW": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "NPt25vAV6G1o", "parent": {"__ref": "_1Fst0YnGkXE"}, "locked": null, "vsettings": [{"__ref": "yC45UGlHj9WE"}], "__type": "TplTag"}, "QDdn2A225A2y": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "7LezCEkvC_T6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VrmHykI_yJ75": {"variants": [{"__ref": "-PxzIgp4Gipn"}], "args": [], "attrs": {}, "rs": {"__ref": "XjFrdTdWpTdT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qQpYTnpuOlNn": {"variants": [{"__ref": "3aIdJRYyilVr"}], "args": [], "attrs": {}, "rs": {"__ref": "8wJaIgoSpc-q"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ddvlL6V4aVdz": {"variants": [{"__ref": "DDjI7dHvC5Rj"}], "args": [], "attrs": {}, "rs": {"__ref": "JhWaB_98kajv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "J5Bd1E45Id9G": {"variants": [{"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "TuD45fIfm8Gw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zeNYADdP6hYi": {"variants": [{"__ref": "1v9tOoWcYoSm"}], "args": [], "attrs": {}, "rs": {"__ref": "h_IT7yOnSNsT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kBqkl16n-8Rc": {"variants": [{"__ref": "u4gK4cQ_2ph0"}], "args": [], "attrs": {}, "rs": {"__ref": "iqqNDwoe7svS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xQ9EIJLlgzqv": {"variants": [{"__ref": "JUTZ28X-ovlz"}], "args": [], "attrs": {}, "rs": {"__ref": "apggDJTFHYfT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "73VH3qzktqqo": {"variants": [{"__ref": "IYROIQz1Yz-b"}], "args": [], "attrs": {}, "rs": {"__ref": "actf0wUbtN_m"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "t4nzdDvTylcb": {"variants": [{"__ref": "EQRY3Rdwscdu"}], "args": [], "attrs": {}, "rs": {"__ref": "jJM5iF4_CqyO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F9r8mw9d26dB": {"variants": [{"__ref": "8i5qF7bP-BNc"}], "args": [], "attrs": {}, "rs": {"__ref": "Q9KY8mBuhEy0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "UnS3lFQeaX4L": {"variants": [{"__ref": "SintozooODqR"}], "args": [], "attrs": {}, "rs": {"__ref": "mMzQg_WaX1s7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LG1yZ0Hvd0HO": {"variants": [{"__ref": "8nj5f8HiUeee"}], "args": [], "attrs": {}, "rs": {"__ref": "Tp2xd0Z5u6TM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7jji9or5ARrK": {"variants": [{"__ref": "U_QKl4g8LNK2"}], "args": [], "attrs": {}, "rs": {"__ref": "k9ynqCGP5ixk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8A_PIviOBVuF": {"variants": [{"__ref": "gnsJ1gtzl8L3"}], "args": [], "attrs": {}, "rs": {"__ref": "kZBrzggE0yF-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ik06BP1iOkcB": {"variants": [{"__ref": "qNKoqn3Hxm6D"}], "args": [], "attrs": {}, "rs": {"__ref": "s5hkkrVWj3JQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x5mAexUn9Ni1": {"variants": [{"__ref": "j27i2xhJuV02"}], "args": [], "attrs": {}, "rs": {"__ref": "lZ_ZyJAxYISl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OyNKBKOgTXu8": {"variants": [{"__ref": "aS6dmVOUOIaB"}], "args": [], "attrs": {}, "rs": {"__ref": "SNWAYQuleKjW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "YmAQzVMaZQUf": {"variants": [{"__ref": "82IaSV404DmZ"}], "args": [], "attrs": {}, "rs": {"__ref": "WS8S9UE0ATM6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fIfc5tLc722K": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "Azmj18gAoALD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fTktzPizQ5am": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "VTBj0PZVDJbk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jh9rxd7ape8M": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "D3aNhhliIcew"}], "args": [], "attrs": {}, "rs": {"__ref": "I34HvxtJA7ez"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hv-poB8dFaQN": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "D3aNhhliIcew"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "_YwLYjtPwJc5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "rrTZdT7FHdLf": {"variants": [{"__ref": "D3aNhhliIcew"}], "args": [], "attrs": {}, "rs": {"__ref": "y2QajZYRdqZQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4hJwSmtbgfA2": {"variants": [{"__ref": "D3aNhhliIcew"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "p6Ru5nkdmrxG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yfgS9CF9GbdU": {"variants": [{"__ref": "3KS--Wn8B19X"}], "args": [], "attrs": {}, "rs": {"__ref": "eqdwgLMtJNlF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "28q2s9oRv7_8": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "dbiDc4tiYaHw": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XNIjAMWsgosr": {"values": {}, "mixins": [], "__type": "RuleSet"}, "id6dMbn5QBeW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Jq-dTElBLx9D": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2SVx_51IHejC": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "dMFGDFA16Y0z", "parent": {"__ref": "nP1Y6u--8ss4"}, "locked": null, "vsettings": [{"__ref": "VfnRXGHQBC-A"}], "__type": "TplTag"}, "lz1RbmqTkahQ": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "Rp_xfBqqqFc6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3g62QvkvMYRf": {"variants": [{"__ref": "dAo57WBDNsb4"}], "args": [], "attrs": {}, "rs": {"__ref": "BVBT9Jg7Rnip"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "05XPAvsfCzEQ": {"variants": [{"__ref": "u4gK4cQ_2ph0"}], "args": [], "attrs": {}, "rs": {"__ref": "ldoQ5jL0gRFK"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6mw-pHL47rmS": {"variants": [{"__ref": "JUTZ28X-ovlz"}], "args": [], "attrs": {}, "rs": {"__ref": "yXIfUasFqS_r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x_K20WbPq7B2": {"variants": [{"__ref": "EQRY3Rdwscdu"}], "args": [], "attrs": {}, "rs": {"__ref": "ZlYdyrId5DNU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Kh4EDrgxEw3x": {"variants": [{"__ref": "8i5qF7bP-BNc"}], "args": [], "attrs": {}, "rs": {"__ref": "i_n-2NCpE8Lc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "atC-TTkfypxs": {"variants": [{"__ref": "SintozooODqR"}], "args": [], "attrs": {}, "rs": {"__ref": "-o6slhgl0Eq8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kz2fa-C62mSo": {"variants": [{"__ref": "IYROIQz1Yz-b"}], "args": [], "attrs": {}, "rs": {"__ref": "xfvPJZUxyueb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5hm0GwaFLvIX": {"variants": [{"__ref": "82IaSV404DmZ"}], "args": [], "attrs": {}, "rs": {"__ref": "a-oeBOi823HY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QsbilZW34NKD": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "Gf2SAp8FjA8g"}], "args": [], "attrs": {}, "rs": {"__ref": "v0LpWq4pjfcs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uDis-jv3pXAJ": {"variants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "FBRFKN3-GALk"}], "args": [], "attrs": {}, "rs": {"__ref": "_aGf8bpgyLVB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Uc72VLiOcNKK": {"variants": [{"__ref": "aS6dmVOUOIaB"}], "args": [], "attrs": {}, "rs": {"__ref": "ewI6aZ-k4taA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "42wysGtVl41q": {"variants": [{"__ref": "3KS--Wn8B19X"}], "args": [], "attrs": {}, "rs": {"__ref": "oZwseslhxoEs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mvGKJeoV0Gut": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "bkwylC010fk9": {"code": "false", "fallback": null, "__type": "CustomCode"}, "tPjczqUlop_M": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "GX96svNtWkyb": {"code": "true", "fallback": null, "__type": "CustomCode"}, "1pPQTCxnfiJG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "L6gkyIEIICR1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "KiIKbpGrdVSM": {"uuid": "AyzAooBQY8V1", "width": 1180, "height": 540, "container": {"__ref": "ySlntG5RR3c9"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "qObSIcZZM9pK"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "tDSaXyJA_Bmn": {"uuid": "Cc028bMSULur", "width": 1180, "height": 540, "container": {"__ref": "XIYJ-3-zrFA-"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3aIdJRYyilVr"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "aMZs90MvnU_m": {"uuid": "ZBP5SHOocIFg", "width": 1180, "height": 540, "container": {"__ref": "_Jw6KWXBQj2A"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "-PxzIgp4Gipn"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "e3Yu4BtMPQPw": {"uuid": "W951bgZEIrDq", "width": 1180, "height": 540, "container": {"__ref": "Bt4Rs6Kb3MWR"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "nqhwRMWuS-xh": {"uuid": "6b_R1EShHz7O", "width": 1180, "height": 540, "container": {"__ref": "PrDv3dKCOLHe"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "E2UE68aqramn": {"uuid": "uMquG5bsr1UH", "width": 1180, "height": 540, "container": {"__ref": "pz-TQYi_4-Hr"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "DDjI7dHvC5Rj"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "1AETCe6TowyR": {"uuid": "arq8XaodsX2i", "width": 1180, "height": 540, "container": {"__ref": "pYpGNi-flJsJ"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "dAo57WBDNsb4"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Eu23212pkCLu": {"uuid": "o3gDjuw_JWjd", "width": 1180, "height": 540, "container": {"__ref": "ibBsctmMT_Mp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "1v9tOoWcYoSm"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "bIlGjtyvTjbi": {"uuid": "pOtuYwD10T8X", "width": 1180, "height": 540, "container": {"__ref": "u5hJAUbktZyD"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "j27i2xhJuV02"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "pS4U0727DhRy": {"uuid": "_wvVVpHJbm06", "width": 1180, "height": 540, "container": {"__ref": "CHAK32NpEUX2"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kYC798MFgATQ"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6N7Nf23xbeoL": {"uuid": "TWyZ55YhOM1n", "width": 1180, "height": 540, "container": {"__ref": "Wvj7CmmDXD9n"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "zsocOgY1UFfj"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "YSTe-hBn_UpN": {"uuid": "fCuLJFcAvqas", "width": 1180, "height": 540, "container": {"__ref": "CqXZh2YOVIaS"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "mK8gueDbkXfE"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "k95TU6pAIetq": {"uuid": "ZcJ7EDLQD57_", "width": 1180, "height": 540, "container": {"__ref": "jHPHpuVFYHQ9"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "D3aNhhliIcew"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "p351uIdJRVIQ": {"uuid": "Un0YY4OZjzTQ", "width": 1180, "height": 540, "container": {"__ref": "_cz2Ppgz2A64"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8nj5f8HiUeee"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "eoQIVBj_z5dz": {"uuid": "yQTLUDshXTcH", "width": 1180, "height": 540, "container": {"__ref": "N9bYiR5kVCDL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "U_QKl4g8LNK2"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "xAGHRUbcj3NS": {"uuid": "cI4yhfiP4K6h", "width": 1180, "height": 540, "container": {"__ref": "EfuszlV6YpaF"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "IYROIQz1Yz-b"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "QUeNwtLS-GHD": {"uuid": "SKCfptJJ3UQb", "width": 1180, "height": 540, "container": {"__ref": "CNYLtGrXZOEf"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "qNKoqn3Hxm6D"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Duxi0BHTNL8Y": {"uuid": "q9-SSR-419W4", "width": 1180, "height": 540, "container": {"__ref": "ilQgPqm-MDlo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gnsJ1gtzl8L3"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "5nAl4oCHk-_J": {"uuid": "D7w1h7e9J-08", "width": 1180, "height": 540, "container": {"__ref": "uIg5MfmCwfsW"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3KS--Wn8B19X"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MChV2evqM_Ne": {"uuid": "db70c2TteJ3e", "width": 1180, "height": 540, "container": {"__ref": "eNvnlRYNaxLK"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "u4gK4cQ_2ph0"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "z7MT96VYIpMX": {"uuid": "QOPBNxzVigD-", "width": 1180, "height": 540, "container": {"__ref": "pOHDyIojD7_M"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "JUTZ28X-ovlz"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "DPAQO4H8zNly": {"uuid": "peRo790v6ohp", "width": 1180, "height": 540, "container": {"__ref": "F6enPpSMtMvR"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "EQRY3Rdwscdu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "EQFjQk7w-su5": {"uuid": "rulYBhhvJood", "width": 1180, "height": 540, "container": {"__ref": "1byp1aCMQeu5"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8i5qF7bP-BNc"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "dJqzz0Oh_jF_": {"uuid": "LUzhoQvzlqaZ", "width": 1180, "height": 540, "container": {"__ref": "vLafIAsKObBp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "SintozooODqR"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "SwF3g_TM_5P3": {"uuid": "vU5jT1a3TiAp", "width": 1180, "height": 540, "container": {"__ref": "kvlgIS_CrU4u"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aS6dmVOUOIaB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Eznku1_6Z56E": {"uuid": "uZLL2ApKn6o9", "width": 1180, "height": 540, "container": {"__ref": "nzSsqY-Tbycx"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "82IaSV404DmZ"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "MyFXOIlfxBDw": {"uuid": "vyEDsSzW-IHT", "width": 1180, "height": 540, "container": {"__ref": "9_1LvwcXVJzd"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "u4gK4cQ_2ph0"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "l3XfQTA5UKf_": {"uuid": "Mqy8WXhrLRRg", "width": 1180, "height": 540, "container": {"__ref": "_h0UFEUOF2MS"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "u4gK4cQ_2ph0"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "vYoYf05QTlZQ": {"uuid": "CZsBfiE7azb4", "width": 1180, "height": 540, "container": {"__ref": "dzDyifDl48Cj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "JUTZ28X-ovlz"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "PD59KPXEV5zr": {"uuid": "krteraC3oT-a", "width": 1180, "height": 540, "container": {"__ref": "QPBjAo8Ne9ci"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "JUTZ28X-ovlz"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "kgNaaTI1HeoN": {"uuid": "CeB1I6UQwEco", "width": 1180, "height": 540, "container": {"__ref": "1A_skGi0lp0s"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "IYROIQz1Yz-b"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "-kuTfrA_rkXl": {"uuid": "M99lmr7qKJHO", "width": 1180, "height": 540, "container": {"__ref": "u0Ushx35_gna"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "IYROIQz1Yz-b"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "biwf6GSu63nF": {"uuid": "BjSU6m2tcpB7", "width": 1180, "height": 540, "container": {"__ref": "wVoIf717P5mp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "qNKoqn3Hxm6D"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "yAfA13i4cWri": {"uuid": "kGjJfSflR8t8", "width": 1180, "height": 540, "container": {"__ref": "-q-rgrbo2ysj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "qNKoqn3Hxm6D"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "P0INQ6liSAux": {"uuid": "u6nlNjIoQGrw", "width": 1180, "height": 540, "container": {"__ref": "8SaCNG1dIkgF"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "EQRY3Rdwscdu"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "jI1X7bSKEAfl": {"uuid": "8ggzv9LZ_XhS", "width": 1180, "height": 540, "container": {"__ref": "vlwcA197fo0O"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "EQRY3Rdwscdu"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ievubZFBGcLX": {"uuid": "mURv9Yrx_DTI", "width": 1180, "height": 540, "container": {"__ref": "d4MjzflmZsC_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8i5qF7bP-BNc"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "leOAGW-9UPKJ": {"uuid": "VFKCh5krjywO", "width": 1180, "height": 540, "container": {"__ref": "EVhoEeymh407"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8i5qF7bP-BNc"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ySuDZXusBB10": {"uuid": "LOllKoZFX6sE", "width": 1180, "height": 540, "container": {"__ref": "Vw3lqB5UocDT"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "SintozooODqR"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "-T_F-aCmDFoU": {"uuid": "6A3W_Yqv-lU_", "width": 1180, "height": 540, "container": {"__ref": "xHxiY8FZ3JsC"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "SintozooODqR"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "w9rp4qHfgiwy": {"uuid": "9DBzDs6pUhbc", "width": 1180, "height": 540, "container": {"__ref": "aFAjlY4RiJ0e"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "DpGk1G-U6sIg": {"uuid": "RFAomxUy3r6C", "width": 1180, "height": 540, "container": {"__ref": "h8ZnRyTdlR6h"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "8nj5f8HiUeee"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "XZLqQ4bkd7EL": {"uuid": "o2aZJiZlqwFK", "width": 1180, "height": 540, "container": {"__ref": "VvR3YVmpJg28"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "U_QKl4g8LNK2"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "kbFNoFBgz6ua": {"uuid": "QVqZC_lQwfaA", "width": 1180, "height": 540, "container": {"__ref": "FFd_NC5Bk6XY"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "U_QKl4g8LNK2"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "BGTy6QbPJs0Z": {"uuid": "Zfdl2qJJBrZA", "width": 1180, "height": 540, "container": {"__ref": "QHp1lF3wsoxj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gnsJ1gtzl8L3"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "XcaJQvSdZ33C": {"uuid": "79TH7BZTjBkR", "width": 1180, "height": 540, "container": {"__ref": "8foO8K-VDeEj"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "gnsJ1gtzl8L3"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "T7yIG6PcAqI7": {"uuid": "Q0dKnIy773J1", "width": 1180, "height": 540, "container": {"__ref": "KMUu3PUohZci"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "j27i2xhJuV02"}, {"__ref": "DDjI7dHvC5Rj"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "YRzQMqBrRozR": {"uuid": "337i_EZ4P2EU", "width": 1180, "height": 540, "container": {"__ref": "uoTb06nl831i"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "dAo57WBDNsb4"}, {"__ref": "j27i2xhJuV02"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "9pK4Z8CVv28t": {"uuid": "ej1XStpQnAzR", "width": 1180, "height": 540, "container": {"__ref": "EhA-6UIEoeur"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aS6dmVOUOIaB"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "r4Q7xD9m511v": {"uuid": "zQXFAgXkRzVG", "width": 1180, "height": 540, "container": {"__ref": "GbUW4atuLcj-"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "aS6dmVOUOIaB"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "WtEL3nj-afwk": {"uuid": "ITefi13Jc29T", "width": 1180, "height": 540, "container": {"__ref": "u61_cO4ZGAnS"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kYC798MFgATQ"}, {"__ref": "mK8gueDbkXfE"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "CeYI9aPBbut3": {"uuid": "jxU03wkhbYMO", "width": 1180, "height": 540, "container": {"__ref": "pzO6QOv24g4Q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "KQ6QjxMXTTP6": {"uuid": "IvkPIWS4yQY3", "width": 1180, "height": 540, "container": {"__ref": "01_M486a-5nP"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "82IaSV404DmZ"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "x9miYHwlUSf8": {"uuid": "FXlQ236B75ZE", "width": 1180, "height": 540, "container": {"__ref": "QlSXEzOb0YIX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3KS--Wn8B19X"}, {"__ref": "Gf2SAp8FjA8g"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "HhBOs_XM1rK8": {"uuid": "wxAaoaVtOGNT", "width": 1180, "height": 540, "container": {"__ref": "lG10rGba0z_U"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "3KS--Wn8B19X"}, {"__ref": "FBRFKN3-GALk"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ucc_GZh9SCAx": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {"outerHTML": {"__ref": "ru7ojKm39pjn"}}, "rs": {"__ref": "8t5_wIV2_2Rr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "STTZc0CBpov1": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "iw2JRkAlgX2Q": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vo7K7eUgX5BO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WG_yliTkPeHg": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "-b2ipyWWr97G": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "SHTASAC9JdMF": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "fDgJtfK2_6Br": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "0oBjc1GjS2tq": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "YcLoWHeKeRVZ": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "bhCkjp6ClAyE": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "V10aoFLRNJw3": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "jeh23JVpCxgM": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "rTRoOZnbzWFb": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "BQp4xfiGdPP-": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "yC45UGlHj9WE": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {}, "rs": {"__ref": "20T2zvFoxwH1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "aq6UI8qvzWIr"}, "columnsConfig": null, "__type": "VariantSetting"}, "7LezCEkvC_T6": {"values": {"color": "#EDEDEC", "font-weight": "500", "white-space": "nowrap"}, "mixins": [], "__type": "RuleSet"}, "XjFrdTdWpTdT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8wJaIgoSpc-q": {"values": {}, "mixins": [], "__type": "RuleSet"}, "JhWaB_98kajv": {"values": {}, "mixins": [], "__type": "RuleSet"}, "TuD45fIfm8Gw": {"values": {}, "mixins": [], "__type": "RuleSet"}, "h_IT7yOnSNsT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "iqqNDwoe7svS": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "apggDJTFHYfT": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "actf0wUbtN_m": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "jJM5iF4_CqyO": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "Q9KY8mBuhEy0": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "mMzQg_WaX1s7": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "Tp2xd0Z5u6TM": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "k9ynqCGP5ixk": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "kZBrzggE0yF-": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "s5hkkrVWj3JQ": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "lZ_ZyJAxYISl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SNWAYQuleKjW": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "WS8S9UE0ATM6": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "Azmj18gAoALD": {"values": {"color": "#0081F1", "text-decoration-line": "underline"}, "mixins": [], "__type": "RuleSet"}, "VTBj0PZVDJbk": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "I34HvxtJA7ez": {"values": {}, "mixins": [], "__type": "RuleSet"}, "_YwLYjtPwJc5": {"values": {}, "mixins": [], "__type": "RuleSet"}, "y2QajZYRdqZQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "p6Ru5nkdmrxG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "eqdwgLMtJNlF": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "VfnRXGHQBC-A": {"variants": [{"__ref": "qObSIcZZM9pK"}], "args": [], "attrs": {"outerHTML": {"__ref": "IB8u9wqzE1p2"}}, "rs": {"__ref": "0g815DH2KvA5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Rp_xfBqqqFc6": {"values": {"color": "#EDEDEC"}, "mixins": [], "__type": "RuleSet"}, "BVBT9Jg7Rnip": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ldoQ5jL0gRFK": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "yXIfUasFqS_r": {"values": {"color": "#18794E"}, "mixins": [], "__type": "RuleSet"}, "ZlYdyrId5DNU": {"values": {"color": "#946800"}, "mixins": [], "__type": "RuleSet"}, "i_n-2NCpE8Lc": {"values": {"color": "#CA3214"}, "mixins": [], "__type": "RuleSet"}, "-o6slhgl0Eq8": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "xfvPJZUxyueb": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "a-oeBOi823HY": {"values": {"color": "#0091FF"}, "mixins": [], "__type": "RuleSet"}, "v0LpWq4pjfcs": {"values": {"color": "#0081F1"}, "mixins": [], "__type": "RuleSet"}, "_aGf8bpgyLVB": {"values": {"color": "#006ADC"}, "mixins": [], "__type": "RuleSet"}, "ewI6aZ-k4taA": {"values": {"color": "#1B1B18"}, "mixins": [], "__type": "RuleSet"}, "oZwseslhxoEs": {"values": {"color": "#35290F"}, "mixins": [], "__type": "RuleSet"}, "ySlntG5RR3c9": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "PCOCI92-rZ6s", "parent": null, "locked": null, "vsettings": [{"__ref": "O6wm3KozNSFt"}], "__type": "TplComponent"}, "XIYJ-3-zrFA-": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "EhOCO0QP20FH", "parent": null, "locked": null, "vsettings": [{"__ref": "tMFXTul6SsR2"}], "__type": "TplComponent"}, "_Jw6KWXBQj2A": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "yAJ_OPvgm7lR", "parent": null, "locked": null, "vsettings": [{"__ref": "ln51y_cfCN_p"}], "__type": "TplComponent"}, "Bt4Rs6Kb3MWR": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "MViFPA_cNqeF", "parent": null, "locked": null, "vsettings": [{"__ref": "ThdPfSiq1K3H"}], "__type": "TplComponent"}, "PrDv3dKCOLHe": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "G7OxwsXStr-q", "parent": null, "locked": null, "vsettings": [{"__ref": "RmcELQY_9jPJ"}], "__type": "TplComponent"}, "pz-TQYi_4-Hr": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "IsqrV76iEeEd", "parent": null, "locked": null, "vsettings": [{"__ref": "FsKwFpVip1PE"}], "__type": "TplComponent"}, "pYpGNi-flJsJ": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "ykmWJ2Egxi3H", "parent": null, "locked": null, "vsettings": [{"__ref": "EW754Q2A058z"}], "__type": "TplComponent"}, "ibBsctmMT_Mp": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "aPbi7evFyTLN", "parent": null, "locked": null, "vsettings": [{"__ref": "nMY6envkw4Ou"}], "__type": "TplComponent"}, "u5hJAUbktZyD": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "aKtqkm5qu4-T", "parent": null, "locked": null, "vsettings": [{"__ref": "wwEhkXilAsr5"}], "__type": "TplComponent"}, "CHAK32NpEUX2": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "JXXoRc0Wvpdb", "parent": null, "locked": null, "vsettings": [{"__ref": "ytOH6p19yU9D"}], "__type": "TplComponent"}, "Wvj7CmmDXD9n": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "Du7QedXbaT8_", "parent": null, "locked": null, "vsettings": [{"__ref": "-vXzCn1Pcjr3"}], "__type": "TplComponent"}, "CqXZh2YOVIaS": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "PJQKGFn8B56t", "parent": null, "locked": null, "vsettings": [{"__ref": "tlPy5ovYdSxI"}], "__type": "TplComponent"}, "jHPHpuVFYHQ9": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "e6HezBUeT5O1", "parent": null, "locked": null, "vsettings": [{"__ref": "uy6vZTyP0lqD"}], "__type": "TplComponent"}, "_cz2Ppgz2A64": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "pxrnyaONhzDw", "parent": null, "locked": null, "vsettings": [{"__ref": "1pwhkaOZ7ivT"}], "__type": "TplComponent"}, "N9bYiR5kVCDL": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "yFwzRwxIxO_n", "parent": null, "locked": null, "vsettings": [{"__ref": "14D3S3t7S0Rf"}], "__type": "TplComponent"}, "EfuszlV6YpaF": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "TCiBjPUL5E3d", "parent": null, "locked": null, "vsettings": [{"__ref": "vP6SjGhvUTKE"}], "__type": "TplComponent"}, "CNYLtGrXZOEf": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "yzepAO7cygZg", "parent": null, "locked": null, "vsettings": [{"__ref": "8fFQU7ZYn92I"}], "__type": "TplComponent"}, "ilQgPqm-MDlo": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "bdgG_ki2IogN", "parent": null, "locked": null, "vsettings": [{"__ref": "m816nPyTA662"}], "__type": "TplComponent"}, "uIg5MfmCwfsW": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "-UptRBXgGefG", "parent": null, "locked": null, "vsettings": [{"__ref": "ZDosdVQgWlEU"}], "__type": "TplComponent"}, "eNvnlRYNaxLK": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "ttdRyg7WZyU3", "parent": null, "locked": null, "vsettings": [{"__ref": "Xk5gcE3o6KeV"}], "__type": "TplComponent"}, "pOHDyIojD7_M": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "OIdms5BSUVWX", "parent": null, "locked": null, "vsettings": [{"__ref": "nBwocd9BzpXT"}], "__type": "TplComponent"}, "F6enPpSMtMvR": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "IAXSTTu1BvW6", "parent": null, "locked": null, "vsettings": [{"__ref": "jHhYXvDYdYKI"}], "__type": "TplComponent"}, "1byp1aCMQeu5": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "F7Ltjl-5Mo1j", "parent": null, "locked": null, "vsettings": [{"__ref": "1a6b_DT2ZcGh"}], "__type": "TplComponent"}, "vLafIAsKObBp": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "bBaNfJm_ZWln", "parent": null, "locked": null, "vsettings": [{"__ref": "fRfKRuSUu6zb"}], "__type": "TplComponent"}, "kvlgIS_CrU4u": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "P_oQ5V0Cio04", "parent": null, "locked": null, "vsettings": [{"__ref": "akFzZfvraflv"}], "__type": "TplComponent"}, "nzSsqY-Tbycx": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "0s5M7DIaSrYd", "parent": null, "locked": null, "vsettings": [{"__ref": "0UfeRcFtlLa1"}], "__type": "TplComponent"}, "9_1LvwcXVJzd": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "gsm4lKvXCcFg", "parent": null, "locked": null, "vsettings": [{"__ref": "don7t3Zl-ZFT"}], "__type": "TplComponent"}, "_h0UFEUOF2MS": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "ekrLzAUb0wYB", "parent": null, "locked": null, "vsettings": [{"__ref": "zYpMTZU77UDH"}], "__type": "TplComponent"}, "dzDyifDl48Cj": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "MF1d2hYx8Lar", "parent": null, "locked": null, "vsettings": [{"__ref": "3Te_oleh-wLo"}], "__type": "TplComponent"}, "QPBjAo8Ne9ci": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "Yw7wNmYf_DUA", "parent": null, "locked": null, "vsettings": [{"__ref": "6udhfYdU_Uz-"}], "__type": "TplComponent"}, "1A_skGi0lp0s": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "KHhKr-vgrVLj", "parent": null, "locked": null, "vsettings": [{"__ref": "rzY2Xq7KO41X"}], "__type": "TplComponent"}, "u0Ushx35_gna": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "SHvMIJOmv2iX", "parent": null, "locked": null, "vsettings": [{"__ref": "HCA6XRNUT4iG"}], "__type": "TplComponent"}, "wVoIf717P5mp": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "d56SNhgDVF9_", "parent": null, "locked": null, "vsettings": [{"__ref": "TVBWK3oKck8S"}], "__type": "TplComponent"}, "-q-rgrbo2ysj": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "XKYo_3rhFBI5", "parent": null, "locked": null, "vsettings": [{"__ref": "d8WHt0uov_7R"}], "__type": "TplComponent"}, "8SaCNG1dIkgF": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "1K4vzP6Za6-p", "parent": null, "locked": null, "vsettings": [{"__ref": "qN-aeAb-GWbQ"}], "__type": "TplComponent"}, "vlwcA197fo0O": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "OgoszQ0O9cse", "parent": null, "locked": null, "vsettings": [{"__ref": "cGQI3ApT4bJV"}], "__type": "TplComponent"}, "d4MjzflmZsC_": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "1S-FcHb0CJ6W", "parent": null, "locked": null, "vsettings": [{"__ref": "Ev98h5LvDn25"}], "__type": "TplComponent"}, "EVhoEeymh407": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "H4F9fL2rWEZ9", "parent": null, "locked": null, "vsettings": [{"__ref": "x539PN-ZYA0T"}], "__type": "TplComponent"}, "Vw3lqB5UocDT": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "nAMF4LeUIPyd", "parent": null, "locked": null, "vsettings": [{"__ref": "_UMfM8S4mNho"}], "__type": "TplComponent"}, "xHxiY8FZ3JsC": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "6jlFSu1iqzAw", "parent": null, "locked": null, "vsettings": [{"__ref": "ToO7HVyKnatJ"}], "__type": "TplComponent"}, "aFAjlY4RiJ0e": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "BI2CzIg6c6iL", "parent": null, "locked": null, "vsettings": [{"__ref": "eqJMd5L3OZwJ"}], "__type": "TplComponent"}, "h8ZnRyTdlR6h": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "92l-5hYHBx-6", "parent": null, "locked": null, "vsettings": [{"__ref": "zgE50JMoayFw"}], "__type": "TplComponent"}, "VvR3YVmpJg28": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "ArC5V_Wp0lei", "parent": null, "locked": null, "vsettings": [{"__ref": "jcjVOwilEMr8"}], "__type": "TplComponent"}, "FFd_NC5Bk6XY": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "ZXIO_GX_RfwP", "parent": null, "locked": null, "vsettings": [{"__ref": "2zr-lEV4ijm7"}], "__type": "TplComponent"}, "QHp1lF3wsoxj": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "EB7yfuraWlGk", "parent": null, "locked": null, "vsettings": [{"__ref": "PK8s-WvKSrDz"}], "__type": "TplComponent"}, "8foO8K-VDeEj": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "8O3sN3X9diqd", "parent": null, "locked": null, "vsettings": [{"__ref": "t0WfibiCzJHk"}], "__type": "TplComponent"}, "KMUu3PUohZci": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "M3pvrzIn6i-o", "parent": null, "locked": null, "vsettings": [{"__ref": "8n8EPbac1FcJ"}], "__type": "TplComponent"}, "uoTb06nl831i": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "-Uif8otSc23n", "parent": null, "locked": null, "vsettings": [{"__ref": "x6yImxisoT1v"}], "__type": "TplComponent"}, "EhA-6UIEoeur": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "OXevAQ6q-utA", "parent": null, "locked": null, "vsettings": [{"__ref": "ZYCN7GK3nIuX"}], "__type": "TplComponent"}, "GbUW4atuLcj-": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "YXOudOqs9EDt", "parent": null, "locked": null, "vsettings": [{"__ref": "GmgInNdYIwtn"}], "__type": "TplComponent"}, "u61_cO4ZGAnS": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "dX9slq2E66la", "parent": null, "locked": null, "vsettings": [{"__ref": "zoG5rW8DDyNQ"}], "__type": "TplComponent"}, "pzO6QOv24g4Q": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "5AZCh-8qnfep", "parent": null, "locked": null, "vsettings": [{"__ref": "TsxXv6dufQ35"}], "__type": "TplComponent"}, "01_M486a-5nP": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "JTj80g5SBhqq", "parent": null, "locked": null, "vsettings": [{"__ref": "FQ0qfvSwKGvq"}], "__type": "TplComponent"}, "QlSXEzOb0YIX": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "0GDTZuUBaVhu", "parent": null, "locked": null, "vsettings": [{"__ref": "032DiX5Gloai"}], "__type": "TplComponent"}, "lG10rGba0z_U": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "Uz8qjRJvD6am", "parent": null, "locked": null, "vsettings": [{"__ref": "gPZ-K4MHnuJA"}], "__type": "TplComponent"}, "ru7ojKm39pjn": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "8t5_wIV2_2Rr": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "20T2zvFoxwH1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aq6UI8qvzWIr": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "IB8u9wqzE1p2": {"asset": {"__ref": "qkP78v4NuNLB"}, "__type": "ImageAssetRef"}, "0g815DH2KvA5": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "O6wm3KozNSFt": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "TNV-GLolaIEf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tMFXTul6SsR2": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "FzbsaXP0Ik82"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ln51y_cfCN_p": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "CJw_SAdZfYaz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ThdPfSiq1K3H": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "IKicxBO5H3Tr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RmcELQY_9jPJ": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "I75ftAg-gFo_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FsKwFpVip1PE": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "7AujqOLdFWwu"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EW754Q2A058z": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "bVP4tlSAHk4C"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nMY6envkw4Ou": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "jSrVBQ-HxhZW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wwEhkXilAsr5": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "6vQA38gKWwwp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ytOH6p19yU9D": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "5B4rvIePp5jQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-vXzCn1Pcjr3": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "SCXUSn0zF1QG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tlPy5ovYdSxI": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "3xCZmxC4FEds"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uy6vZTyP0lqD": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "E8qlQq_akFQ1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1pwhkaOZ7ivT": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "wfJjC2ln_qlP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "14D3S3t7S0Rf": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "uFnptTGcb9H4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vP6SjGhvUTKE": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "RY5bHPQzRdBD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8fFQU7ZYn92I": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "xZyhQ31awN71"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "m816nPyTA662": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "rI17A_0pRgSf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZDosdVQgWlEU": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "9SVhV1_poC2N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Xk5gcE3o6KeV": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "6vtCjrr008uD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nBwocd9BzpXT": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "_yXqNgcySA0z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jHhYXvDYdYKI": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "eOOOwoyFb18K"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1a6b_DT2ZcGh": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "jEybZ3G_JxVJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fRfKRuSUu6zb": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "M12uxUe8lrL2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "akFzZfvraflv": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "uzxWPAzKEcPL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0UfeRcFtlLa1": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "hxeFCTHxne2I"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "don7t3Zl-ZFT": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "WgFnpU4qBqrb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zYpMTZU77UDH": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "zzjNFe71pPKw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3Te_oleh-wLo": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "b8tE7pi_hTBa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6udhfYdU_Uz-": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "ld9xToZRMszs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "rzY2Xq7KO41X": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "HSMfpx-BcV8Z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HCA6XRNUT4iG": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "6b7G_g6cBBXz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TVBWK3oKck8S": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "fv_9zFwyEdcA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "d8WHt0uov_7R": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "pFy2tOXLRIxG"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qN-aeAb-GWbQ": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "LHrZz1hgLvW6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cGQI3ApT4bJV": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "l5AMduOgb8jF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Ev98h5LvDn25": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "Fjx4wdwiR6vX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x539PN-ZYA0T": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "7aT3mUCmdozt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_UMfM8S4mNho": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "br1Q225TEJ6o"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ToO7HVyKnatJ": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "XpubSJC6vbyh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eqJMd5L3OZwJ": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "W3O9DkpBcgrS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zgE50JMoayFw": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "TdWMmSk2iXAf"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jcjVOwilEMr8": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "d8ISvOuhC3CT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2zr-lEV4ijm7": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "ZD2bPULRa-U7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PK8s-WvKSrDz": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "q7V1EoiRfwBI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "t0WfibiCzJHk": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "vF1G1GVNCqoW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8n8EPbac1FcJ": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "MbDMSGg2MVAC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x6yImxisoT1v": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "aLjEdB6_XW5B"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ZYCN7GK3nIuX": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "8DDCWJEW_0F5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GmgInNdYIwtn": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "AmUkWOpyfo2v"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zoG5rW8DDyNQ": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "cIll0ct_ZOyl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TsxXv6dufQ35": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "HwI8WryAmt8s"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FQ0qfvSwKGvq": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "G21CW89cucxi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "032DiX5Gloai": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "TRi3TMMnsb6N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gPZ-K4MHnuJA": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "WoYZREFBY7tF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "TNV-GLolaIEf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FzbsaXP0Ik82": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CJw_SAdZfYaz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "IKicxBO5H3Tr": {"values": {}, "mixins": [], "__type": "RuleSet"}, "I75ftAg-gFo_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7AujqOLdFWwu": {"values": {}, "mixins": [], "__type": "RuleSet"}, "bVP4tlSAHk4C": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jSrVBQ-HxhZW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6vQA38gKWwwp": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5B4rvIePp5jQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SCXUSn0zF1QG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "3xCZmxC4FEds": {"values": {}, "mixins": [], "__type": "RuleSet"}, "E8qlQq_akFQ1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "wfJjC2ln_qlP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uFnptTGcb9H4": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RY5bHPQzRdBD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xZyhQ31awN71": {"values": {}, "mixins": [], "__type": "RuleSet"}, "rI17A_0pRgSf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9SVhV1_poC2N": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6vtCjrr008uD": {"values": {}, "mixins": [], "__type": "RuleSet"}, "_yXqNgcySA0z": {"values": {}, "mixins": [], "__type": "RuleSet"}, "eOOOwoyFb18K": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jEybZ3G_JxVJ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "M12uxUe8lrL2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "uzxWPAzKEcPL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "hxeFCTHxne2I": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WgFnpU4qBqrb": {"values": {}, "mixins": [], "__type": "RuleSet"}, "zzjNFe71pPKw": {"values": {}, "mixins": [], "__type": "RuleSet"}, "b8tE7pi_hTBa": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ld9xToZRMszs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HSMfpx-BcV8Z": {"values": {}, "mixins": [], "__type": "RuleSet"}, "6b7G_g6cBBXz": {"values": {}, "mixins": [], "__type": "RuleSet"}, "fv_9zFwyEdcA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pFy2tOXLRIxG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "LHrZz1hgLvW6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "l5AMduOgb8jF": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Fjx4wdwiR6vX": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7aT3mUCmdozt": {"values": {}, "mixins": [], "__type": "RuleSet"}, "br1Q225TEJ6o": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XpubSJC6vbyh": {"values": {}, "mixins": [], "__type": "RuleSet"}, "W3O9DkpBcgrS": {"values": {}, "mixins": [], "__type": "RuleSet"}, "TdWMmSk2iXAf": {"values": {}, "mixins": [], "__type": "RuleSet"}, "d8ISvOuhC3CT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZD2bPULRa-U7": {"values": {}, "mixins": [], "__type": "RuleSet"}, "q7V1EoiRfwBI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vF1G1GVNCqoW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MbDMSGg2MVAC": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aLjEdB6_XW5B": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8DDCWJEW_0F5": {"values": {}, "mixins": [], "__type": "RuleSet"}, "AmUkWOpyfo2v": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cIll0ct_ZOyl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HwI8WryAmt8s": {"values": {}, "mixins": [], "__type": "RuleSet"}, "G21CW89cucxi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "TRi3TMMnsb6N": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WoYZREFBY7tF": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7ucfpJr_iSOE": {"uuid": "Q6fexms3-yeK", "name": "search.svg", "type": "icon", "dataUri": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGZpbGw9Im5vbmUiIHZpZXdCb3g9IjAgMCAyNCAyNCIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iPgogIDxwYXRoIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIHN0cm9rZS13aWR0aD0iMS41IiBkPSJNMTkuMjUgMTkuMjVMMTUuNSAxNS41TTQuNzUgMTFhNi4yNSA2LjI1IDAgMTExMi41IDAgNi4yNSA2LjI1IDAgMDEtMTIuNSAweiIvPgo8L3N2Zz4=", "width": 150, "height": 150, "aspectRatio": null, "__type": "ImageAsset"}, "CUEv0dZhpMRo": {"uuid": "9qNAm3tH_YLt", "name": "TextInput", "params": [{"__ref": "X9lkwBR8lqF3"}, {"__ref": "LjsPTdMeS_YE"}, {"__ref": "5RnDF2mqKK-e"}, {"__ref": "dVAwhtLKiZ-8"}, {"__ref": "D1bxhYh1_zS6"}, {"__ref": "1k_jJY9uZX45"}, {"__ref": "ML7dwC77fMUp"}, {"__ref": "8668kr0ZBwkU"}, {"__ref": "rfszjDs32IhJ"}, {"__ref": "_J2VetYg2j3v"}, {"__ref": "8wmSFIVianRp"}, {"__ref": "P9LTJOVxphhH"}, {"__ref": "wZKKLAOTtJQV"}, {"__ref": "-OitD2Gvn9g_"}, {"__ref": "faryUDDQmpdD"}, {"__ref": "6CftqvpQOwKV"}, {"__ref": "C8vzzPnk0bIS"}, {"__ref": "ik3wSZqCf6fJ"}, {"__ref": "kSiaVR3NKdZj"}, {"__ref": "wFE5XLJA0JR3"}], "states": [{"__ref": "PfBZ4FBhyTXo"}, {"__ref": "6pyIDKE5kQ9Q"}, {"__ref": "f3evdF8Nb3fM"}, {"__ref": "ZXgj8LB0KQqt"}, {"__ref": "rACuoZOnrwhm"}, {"__ref": "crFLw4Kom8Fn"}], "tplTree": {"__ref": "lH4Ojxex3iGJ"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "ClVr8qWCPCxD"}, {"__ref": "qJxv_MUzvG8a"}, {"__ref": "ffBjCjPNoy24"}, {"__ref": "Vej_jrCtd-af"}, {"__ref": "AzTg488qrmzK"}, {"__ref": "iDrc8sPFxl9P"}], "variantGroups": [{"__ref": "FUjHEcTQCszC"}, {"__ref": "s_sV5v946GUw"}, {"__ref": "fpZTuqQO9yJ-"}, {"__ref": "Lm2uQeXgj5UB"}], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": {"__ref": "34zcG1yODwz6"}, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": true, "__type": "Component", "updatedAt": null}, "0zqw-xWELi39": {"component": {"__ref": "CUEv0dZhpMRo"}, "matrix": {"__ref": "RhgJUbb3ndNI"}, "customMatrix": {"__ref": "SIEOm12tmYZN"}, "__type": "ComponentArena"}, "X9lkwBR8lqF3": {"type": {"__ref": "NBJJKdBMLLHv"}, "variable": {"__ref": "h0xqlhSBYUXZ"}, "uuid": "IHW_cQIPlE-O", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "oJDQX99ZjmMJ"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "LjsPTdMeS_YE": {"type": {"__ref": "FND4XsxD25M4"}, "tplSlot": {"__ref": "fwpogDKrkMbm"}, "variable": {"__ref": "vJ-onWU0SojN"}, "uuid": "3xSzblSvS8J2", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "5RnDF2mqKK-e": {"type": {"__ref": "S1K71nxCSFXr"}, "tplSlot": {"__ref": "vLAWtG9Z6Vl8"}, "variable": {"__ref": "OThPoZjreuw-"}, "uuid": "lhzPBbcb1ITT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "dVAwhtLKiZ-8": {"type": {"__ref": "RdNkF92h6rnh"}, "state": {"__ref": "PfBZ4FBhyTXo"}, "variable": {"__ref": "GRtI_xtz81FV"}, "uuid": "hZ_5iFpSyhCl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "D1bxhYh1_zS6": {"type": {"__ref": "LrXUmCiGCvh9"}, "state": {"__ref": "6pyIDKE5kQ9Q"}, "variable": {"__ref": "MNTPiUD7H5wU"}, "uuid": "qgVHk1ECn1Op", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "1k_jJY9uZX45": {"type": {"__ref": "dXujfrK-W47F"}, "state": {"__ref": "f3evdF8Nb3fM"}, "variable": {"__ref": "g9cwM7GGAYjR"}, "uuid": "QHBQlMBk5jw_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "ML7dwC77fMUp": {"type": {"__ref": "nnkZwh9Vhg_O"}, "state": {"__ref": "ZXgj8LB0KQqt"}, "variable": {"__ref": "B0PBSr56dCIV"}, "uuid": "HRNh1_VzmlZX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "8668kr0ZBwkU": {"type": {"__ref": "0FompRKxgK-R"}, "state": {"__ref": "rACuoZOnrwhm"}, "variable": {"__ref": "cOnItoHFCdmi"}, "uuid": "zf0ORzkQlICz", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "QYCxPfEl1p1c"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "rfszjDs32IhJ": {"type": {"__ref": "N1Go8ZK1nzLE"}, "variable": {"__ref": "A2JZLF8W9K2H"}, "uuid": "GwmPP_62cY6D", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "_J2VetYg2j3v": {"type": {"__ref": "OSWhEdWiwnJA"}, "variable": {"__ref": "CACa9s8aVs3I"}, "uuid": "g-mGQqkKBGlh", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "8wmSFIVianRp": {"type": {"__ref": "WdwfLKXDb0Tu"}, "variable": {"__ref": "F8PPLn8ZdbUJ"}, "uuid": "E2MW0SQ0TPK_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "P9LTJOVxphhH": {"type": {"__ref": "mdefzOw0IeT3"}, "variable": {"__ref": "pzP_p8Ciy41b"}, "uuid": "1YK0xbgEEAqM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "wZKKLAOTtJQV": {"type": {"__ref": "TlXOIIeVYzNL"}, "variable": {"__ref": "BSfIR5h6gx87"}, "uuid": "0RvNpTHyOco-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-OitD2Gvn9g_": {"type": {"__ref": "GDkooOSy4xH_"}, "variable": {"__ref": "H235Kre67Aiq"}, "uuid": "sXEYRD3pSsAJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "faryUDDQmpdD": {"type": {"__ref": "SLvwOTE_QZUa"}, "state": {"__ref": "PfBZ4FBhyTXo"}, "variable": {"__ref": "Tt6_u_wYvgXf"}, "uuid": "-rhFIDoxdZ4d", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "6CftqvpQOwKV": {"type": {"__ref": "DHFwGNYAwl3e"}, "state": {"__ref": "6pyIDKE5kQ9Q"}, "variable": {"__ref": "OEwbXGIYldeN"}, "uuid": "eyOSmOPZBIdp", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "C8vzzPnk0bIS": {"type": {"__ref": "NMjZIpALqWfq"}, "state": {"__ref": "f3evdF8Nb3fM"}, "variable": {"__ref": "yUyCq7IlmAgq"}, "uuid": "AFxvu6LN__oA", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "ik3wSZqCf6fJ": {"type": {"__ref": "RB4R3uo5L61e"}, "state": {"__ref": "ZXgj8LB0KQqt"}, "variable": {"__ref": "kKdvEjva8nu2"}, "uuid": "rLYI1bVFCsUy", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "kSiaVR3NKdZj": {"type": {"__ref": "yr7JgUVAkJQI"}, "state": {"__ref": "crFLw4Kom8Fn"}, "variable": {"__ref": "42CpaZmzr7e6"}, "uuid": "29i9uT2Q2nEd", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "wFE5XLJA0JR3": {"type": {"__ref": "Q5psTSkBJNcC"}, "state": {"__ref": "crFLw4Kom8Fn"}, "variable": {"__ref": "SEC34BYvOy5Q"}, "uuid": "DkQaDSEQt7HQ", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "PfBZ4FBhyTXo": {"variantGroup": {"__ref": "FUjHEcTQCszC"}, "param": {"__ref": "dVAwhtLKiZ-8"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "faryUDDQmpdD"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "6pyIDKE5kQ9Q": {"variantGroup": {"__ref": "s_sV5v946GUw"}, "param": {"__ref": "D1bxhYh1_zS6"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "6CftqvpQOwKV"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "f3evdF8Nb3fM": {"variantGroup": {"__ref": "fpZTuqQO9yJ-"}, "param": {"__ref": "1k_jJY9uZX45"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "C8vzzPnk0bIS"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "ZXgj8LB0KQqt": {"variantGroup": {"__ref": "Lm2uQeXgj5UB"}, "param": {"__ref": "ML7dwC77fMUp"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "ik3wSZqCf6fJ"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "rACuoZOnrwhm": {"variableType": "text", "name": "value", "param": {"__ref": "8668kr0ZBwkU"}, "accessType": "writable", "onChangeParam": {"__ref": "wZKKLAOTtJQV"}, "tplNode": null, "implicitState": null, "__type": "NamedState"}, "crFLw4Kom8Fn": {"variableType": "text", "name": "Value", "param": {"__ref": "kSiaVR3NKdZj"}, "accessType": "private", "onChangeParam": {"__ref": "wFE5XLJA0JR3"}, "tplNode": {"__ref": "bzDHl9Tm_Uo9"}, "implicitState": null, "__type": "NamedState"}, "lH4Ojxex3iGJ": {"tag": "div", "name": null, "children": [{"__ref": "f1UY-ODRvs8e"}, {"__ref": "bzDHl9Tm_Uo9"}, {"__ref": "KMRS16SPuNLf"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "jk-5ui-4JA1g", "parent": null, "locked": null, "vsettings": [{"__ref": "HRip8mraUHgr"}, {"__ref": "5eKBnpd5m8p6"}, {"__ref": "ybTV7l49M1fw"}, {"__ref": "jMcPVWmmhMUu"}, {"__ref": "Wv6f3F3evWry"}, {"__ref": "ssidu_4r93yw"}, {"__ref": "w3LNs4DIa9Ch"}], "__type": "TplTag"}, "ClVr8qWCPCxD": {"uuid": "kRlhEjtc1yd_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "qJxv_MUzvG8a": {"uuid": "NE58uuzrID9L", "name": "", "selectors": [":focus"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "bzDHl9Tm_Uo9"}, "__type": "<PERSON><PERSON><PERSON>"}, "ffBjCjPNoy24": {"uuid": "D3rclBD4DM_D", "name": "", "selectors": [":hover"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "Vej_jrCtd-af": {"uuid": "33fHB0tm1QHG", "name": "", "selectors": [":focus-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "AzTg488qrmzK": {"uuid": "11bO1d8gPEX3", "name": "", "selectors": [":focus-visible-within"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "iDrc8sPFxl9P": {"uuid": "2Ss7xvDrXvYG", "name": "", "selectors": ["::placeholder"], "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": {"__ref": "bzDHl9Tm_Uo9"}, "__type": "<PERSON><PERSON><PERSON>"}, "FUjHEcTQCszC": {"type": "component", "param": {"__ref": "dVAwhtLKiZ-8"}, "linkedState": {"__ref": "PfBZ4FBhyTXo"}, "uuid": "K7hEHVfKKHkJ", "variants": [{"__ref": "A1fu_SZPgH3Q"}], "multi": false, "__type": "ComponentVariantGroup"}, "s_sV5v946GUw": {"type": "component", "param": {"__ref": "D1bxhYh1_zS6"}, "linkedState": {"__ref": "6pyIDKE5kQ9Q"}, "uuid": "mV9DNxEYl70S", "variants": [{"__ref": "SnEjJz5N5SgP"}], "multi": false, "__type": "ComponentVariantGroup"}, "fpZTuqQO9yJ-": {"type": "component", "param": {"__ref": "1k_jJY9uZX45"}, "linkedState": {"__ref": "f3evdF8Nb3fM"}, "uuid": "JsIWn0-rAziu", "variants": [{"__ref": "CZKAnHANVcc-"}], "multi": false, "__type": "ComponentVariantGroup"}, "Lm2uQeXgj5UB": {"type": "component", "param": {"__ref": "ML7dwC77fMUp"}, "linkedState": {"__ref": "ZXgj8LB0KQqt"}, "uuid": "XgdZdDYZxbSL", "variants": [{"__ref": "SZoR3XpS4OLI"}], "multi": false, "__type": "ComponentVariantGroup"}, "34zcG1yODwz6": {"type": "text-input", "__type": "PlumeInfo"}, "RhgJUbb3ndNI": {"rows": [{"__ref": "NtdfngNANrcJ"}, {"__ref": "tymi71VJKaax"}, {"__ref": "Jy7t25IeRiXJ"}, {"__ref": "n9xn_XIemCtN"}, {"__ref": "BtphPKtjmJLi"}], "__type": "ArenaFrameGrid"}, "SIEOm12tmYZN": {"rows": [{"__ref": "kX9atuqM7gbL"}], "__type": "ArenaFrameGrid"}, "NBJJKdBMLLHv": {"name": "text", "__type": "Text"}, "h0xqlhSBYUXZ": {"name": "placeholder", "uuid": "knOPRy2sBf0i", "__type": "Var"}, "oJDQX99ZjmMJ": {"code": "\"Enter something…\"", "fallback": null, "__type": "CustomCode"}, "FND4XsxD25M4": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "vJ-onWU0SojN": {"name": "end icon", "uuid": "uWaqcfidkfu-", "__type": "Var"}, "S1K71nxCSFXr": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "OThPoZjreuw-": {"name": "start icon", "uuid": "x6fQ3pC3Ltmj", "__type": "Var"}, "RdNkF92h6rnh": {"name": "any", "__type": "AnyType"}, "GRtI_xtz81FV": {"name": "Show Start Icon", "uuid": "PLDhg_0O7oOW", "__type": "Var"}, "LrXUmCiGCvh9": {"name": "any", "__type": "AnyType"}, "MNTPiUD7H5wU": {"name": "Show End Icon", "uuid": "XsukTHat3hAX", "__type": "Var"}, "dXujfrK-W47F": {"name": "any", "__type": "AnyType"}, "g9cwM7GGAYjR": {"name": "Is Disabled", "uuid": "p7Rd7IzUWJ-q", "__type": "Var"}, "nnkZwh9Vhg_O": {"name": "any", "__type": "AnyType"}, "B0PBSr56dCIV": {"name": "Color", "uuid": "wrHs6BdWBxQz", "__type": "Var"}, "0FompRKxgK-R": {"name": "text", "__type": "Text"}, "cOnItoHFCdmi": {"name": "value", "uuid": "lzISbkKZIGeZ", "__type": "Var"}, "QYCxPfEl1p1c": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "N1Go8ZK1nzLE": {"name": "text", "__type": "Text"}, "A2JZLF8W9K2H": {"name": "name", "uuid": "GEokL4LDA3z0", "__type": "Var"}, "OSWhEdWiwnJA": {"name": "bool", "__type": "BoolType"}, "CACa9s8aVs3I": {"name": "required", "uuid": "HTX9N-8I0Qtq", "__type": "Var"}, "WdwfLKXDb0Tu": {"name": "text", "__type": "Text"}, "F8PPLn8ZdbUJ": {"name": "aria-label", "uuid": "yTpubIZZ-KsD", "__type": "Var"}, "mdefzOw0IeT3": {"name": "text", "__type": "Text"}, "pzP_p8Ciy41b": {"name": "aria-<PERSON>by", "uuid": "n9lP3ojM3yEF", "__type": "Var"}, "TlXOIIeVYzNL": {"name": "func", "params": [{"__ref": "HOiGYJlKu_mG"}], "__type": "FunctionType"}, "BSfIR5h6gx87": {"name": "onChange", "uuid": "3tnZ1YUp8D0-", "__type": "Var"}, "GDkooOSy4xH_": {"name": "choice", "options": ["text", "password", "hidden", "number", "date", "datetime-local", "time", "email", "tel"], "__type": "Choice"}, "H235Kre67Aiq": {"name": "type", "uuid": "ncZU_-IOYMvu", "__type": "Var"}, "SLvwOTE_QZUa": {"name": "func", "params": [{"__ref": "blGlV7SWLbzr"}], "__type": "FunctionType"}, "Tt6_u_wYvgXf": {"name": "On Show Start Icon change", "uuid": "PGWeB_R8E1PX", "__type": "Var"}, "DHFwGNYAwl3e": {"name": "func", "params": [{"__ref": "wMcioyPyGRW6"}], "__type": "FunctionType"}, "OEwbXGIYldeN": {"name": "On Show End Icon change", "uuid": "B2tY9UMaL6qr", "__type": "Var"}, "NMjZIpALqWfq": {"name": "func", "params": [{"__ref": "Vqg9oqvasgxP"}], "__type": "FunctionType"}, "yUyCq7IlmAgq": {"name": "On Is Disabled change", "uuid": "tzT2rmf5Gebg", "__type": "Var"}, "RB4R3uo5L61e": {"name": "func", "params": [{"__ref": "w4sJWn2o4uNl"}], "__type": "FunctionType"}, "kKdvEjva8nu2": {"name": "On Color change", "uuid": "GMiei677ilwX", "__type": "Var"}, "yr7JgUVAkJQI": {"name": "text", "__type": "Text"}, "42CpaZmzr7e6": {"name": "input Value", "uuid": "Sh53guE-075u", "__type": "Var"}, "Q5psTSkBJNcC": {"name": "func", "params": [{"__ref": "QXQJR7NaIwbB"}], "__type": "FunctionType"}, "SEC34BYvOy5Q": {"name": "On input Value change", "uuid": "dcLjpIvyKjk_", "__type": "Var"}, "f1UY-ODRvs8e": {"tag": "div", "name": "start icon container", "children": [{"__ref": "vLAWtG9Z6Vl8"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "y2lAlzSzDwWV", "parent": {"__ref": "lH4Ojxex3iGJ"}, "locked": null, "vsettings": [{"__ref": "q3pV2VBOuDAf"}, {"__ref": "3mPdHdss3xk9"}, {"__ref": "JAJE9hgAO2R3"}, {"__ref": "K8wFOYpwoYt8"}, {"__ref": "9qj3Ciys3NlA"}, {"__ref": "dPVyXmauNu5M"}], "__type": "TplTag"}, "bzDHl9Tm_Uo9": {"tag": "input", "name": "input", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Lym3wYCmKvMQ", "parent": {"__ref": "lH4Ojxex3iGJ"}, "locked": null, "vsettings": [{"__ref": "m5swl_bZnKAE"}, {"__ref": "xphgqBaNDGtA"}, {"__ref": "zBWRWQSX6fzk"}, {"__ref": "l4Vlq-kJlrwR"}, {"__ref": "OVEbi9hOVkQ1"}, {"__ref": "lv_5ISUCDxxM"}, {"__ref": "XvmoOdnxZLLd"}, {"__ref": "9dF8607EBTKd"}, {"__ref": "RFRxCX6yIiLf"}, {"__ref": "o07wq7tqxloI"}, {"__ref": "7IGwCu_YyhzP"}, {"__ref": "t9bP1N4ayL3j"}], "__type": "TplTag"}, "KMRS16SPuNLf": {"tag": "div", "name": "end icon container", "children": [{"__ref": "fwpogDKrkMbm"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2uCYubIn0kO4", "parent": {"__ref": "lH4Ojxex3iGJ"}, "locked": null, "vsettings": [{"__ref": "bnnR3pwn697S"}, {"__ref": "6ojRjUeC8fsj"}, {"__ref": "RsnLw-HvfZno"}], "__type": "TplTag"}, "HRip8mraUHgr": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {}, "rs": {"__ref": "8o7oo7dux5jl"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5eKBnpd5m8p6": {"variants": [{"__ref": "ffBjCjPNoy24"}], "args": [], "attrs": {}, "rs": {"__ref": "Yl_ckbW18Cpa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ybTV7l49M1fw": {"variants": [{"__ref": "Vej_jrCtd-af"}], "args": [], "attrs": {}, "rs": {"__ref": "duKElgi1tn_z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "jMcPVWmmhMUu": {"variants": [{"__ref": "AzTg488qrmzK"}], "args": [], "attrs": {}, "rs": {"__ref": "ZgOYGipxhORy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Wv6f3F3evWry": {"variants": [{"__ref": "CZKAnHANVcc-"}], "args": [], "attrs": {}, "rs": {"__ref": "_ZukGFXa_hOW"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ssidu_4r93yw": {"variants": [{"__ref": "A1fu_SZPgH3Q"}], "args": [], "attrs": {}, "rs": {"__ref": "YgljhoDvbi01"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "w3LNs4DIa9Ch": {"variants": [{"__ref": "SZoR3XpS4OLI"}], "args": [], "attrs": {}, "rs": {"__ref": "2hVXlOrZTfPn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "A1fu_SZPgH3Q": {"uuid": "wQVl-yBK1msh", "name": "Show Start Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "FUjHEcTQCszC"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "SnEjJz5N5SgP": {"uuid": "VD9-zyZAZxiZ", "name": "Show End Icon", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "s_sV5v946GUw"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "CZKAnHANVcc-": {"uuid": "JCZpCeDhxzN_", "name": "Is Disabled", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "fpZTuqQO9yJ-"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "SZoR3XpS4OLI": {"uuid": "arMIWe_8Q44h", "name": "Dark", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "Lm2uQeXgj5UB"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "NtdfngNANrcJ": {"cols": [{"__ref": "wn89k0pYht7j"}, {"__ref": "vQVScnaIqPsd"}, {"__ref": "z73qutlswEtM"}, {"__ref": "CwcBcxi9Pmqy"}], "rowKey": null, "__type": "ArenaFrameRow"}, "tymi71VJKaax": {"cols": [{"__ref": "QOb_Ia2KYzN2"}], "rowKey": {"__ref": "FUjHEcTQCszC"}, "__type": "ArenaFrameRow"}, "Jy7t25IeRiXJ": {"cols": [{"__ref": "skRvS8IBNoRR"}], "rowKey": {"__ref": "s_sV5v946GUw"}, "__type": "ArenaFrameRow"}, "n9xn_XIemCtN": {"cols": [{"__ref": "eaEP0w57vAVh"}], "rowKey": {"__ref": "fpZTuqQO9yJ-"}, "__type": "ArenaFrameRow"}, "BtphPKtjmJLi": {"cols": [{"__ref": "Z0Oag86NozG-"}], "rowKey": {"__ref": "Lm2uQeXgj5UB"}, "__type": "ArenaFrameRow"}, "kX9atuqM7gbL": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "HOiGYJlKu_mG": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "ibAzpMPFDSCW"}, "__type": "ArgType"}, "blGlV7SWLbzr": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "KJLPJAX6Rpi0"}, "__type": "ArgType"}, "wMcioyPyGRW6": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "W-mu92xaUFIo"}, "__type": "ArgType"}, "Vqg9oqvasgxP": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "XZ4bpILj2_Z7"}, "__type": "ArgType"}, "w4sJWn2o4uNl": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "G2J1Jb6aTjBo"}, "__type": "ArgType"}, "QXQJR7NaIwbB": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "1p2nN27oG8cq"}, "__type": "ArgType"}, "vLAWtG9Z6Vl8": {"param": {"__ref": "5RnDF2mqKK-e"}, "defaultContents": [{"__ref": "sy7BQKecYzUd"}], "uuid": "fOSgi8oQiO3C", "parent": {"__ref": "f1UY-ODRvs8e"}, "locked": null, "vsettings": [{"__ref": "7GYGUEQjo4uK"}, {"__ref": "qTqRkIRKIg1g"}, {"__ref": "cvBHCeAMYMxh"}], "__type": "TplSlot"}, "q3pV2VBOuDAf": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {}, "rs": {"__ref": "sCCZcL_OT9GB"}, "dataCond": {"__ref": "Opv3MRSxc1YH"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3mPdHdss3xk9": {"variants": [{"__ref": "AzTg488qrmzK"}], "args": [], "attrs": {}, "rs": {"__ref": "4McELYWxL8Wg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "JAJE9hgAO2R3": {"variants": [{"__ref": "A1fu_SZPgH3Q"}], "args": [], "attrs": {}, "rs": {"__ref": "lKhrbqDQ1dfA"}, "dataCond": {"__ref": "zsWORSxHFSu7"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "K8wFOYpwoYt8": {"variants": [{"__ref": "ffBjCjPNoy24"}], "args": [], "attrs": {}, "rs": {"__ref": "HlAVdw7Edge-"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9qj3Ciys3NlA": {"variants": [{"__ref": "CZKAnHANVcc-"}], "args": [], "attrs": {}, "rs": {"__ref": "5WxCejL2rltQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dPVyXmauNu5M": {"variants": [{"__ref": "SZoR3XpS4OLI"}], "args": [], "attrs": {}, "rs": {"__ref": "YIPWHxg5vTGO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "m5swl_bZnKAE": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {"placeholder": {"__ref": "pa5Mcbix_3Me"}, "value": {"__ref": "2aiy6-wYhnDt"}, "name": {"__ref": "VlSGTwXrNPrZ"}, "aria-label": {"__ref": "_PuwIbJnr3xS"}, "aria-labelledby": {"__ref": "aH3TNslcF97z"}, "required": {"__ref": "VVC53K6kEX_5"}, "type": {"__ref": "1rTscuIGgOsl"}}, "rs": {"__ref": "Ak8U4dLqLUnT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "xphgqBaNDGtA": {"variants": [{"__ref": "qJxv_MUzvG8a"}], "args": [], "attrs": {}, "rs": {"__ref": "KpLspF6_ilX0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zBWRWQSX6fzk": {"variants": [{"__ref": "CZKAnHANVcc-"}], "args": [], "attrs": {"disabled": {"__ref": "gBFqHuMA5DWO"}}, "rs": {"__ref": "fqDY-cXVnwfs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "l4Vlq-kJlrwR": {"variants": [{"__ref": "iDrc8sPFxl9P"}], "args": [], "attrs": {}, "rs": {"__ref": "AO7-AadEh1oN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OVEbi9hOVkQ1": {"variants": [{"__ref": "A1fu_SZPgH3Q"}], "args": [], "attrs": {}, "rs": {"__ref": "brS2ugu9ZOLa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lv_5ISUCDxxM": {"variants": [{"__ref": "ffBjCjPNoy24"}], "args": [], "attrs": {}, "rs": {"__ref": "UZehkudnjjon"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XvmoOdnxZLLd": {"variants": [{"__ref": "AzTg488qrmzK"}], "args": [], "attrs": {}, "rs": {"__ref": "cpSZXozreKKA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9dF8607EBTKd": {"variants": [{"__ref": "Vej_jrCtd-af"}], "args": [], "attrs": {}, "rs": {"__ref": "ZIN0GrNjg78r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RFRxCX6yIiLf": {"variants": [{"__ref": "Vej_jrCtd-af"}, {"__ref": "qJxv_MUzvG8a"}], "args": [], "attrs": {}, "rs": {"__ref": "WsV8LFnBafzi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "o07wq7tqxloI": {"variants": [{"__ref": "Vej_jrCtd-af"}, {"__ref": "iDrc8sPFxl9P"}], "args": [], "attrs": {}, "rs": {"__ref": "y7aUjNex3s2z"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7IGwCu_YyhzP": {"variants": [{"__ref": "SZoR3XpS4OLI"}], "args": [], "attrs": {}, "rs": {"__ref": "cXDiC8tmqJSn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "t9bP1N4ayL3j": {"variants": [{"__ref": "SZoR3XpS4OLI"}, {"__ref": "iDrc8sPFxl9P"}], "args": [], "attrs": {}, "rs": {"__ref": "kLhgrMyEcpAT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "fwpogDKrkMbm": {"param": {"__ref": "LjsPTdMeS_YE"}, "defaultContents": [{"__ref": "v_FQPtUN-TrO"}], "uuid": "AbnT7tJNITRR", "parent": {"__ref": "KMRS16SPuNLf"}, "locked": null, "vsettings": [{"__ref": "y6dY5u9wlYhf"}, {"__ref": "pzAtVUDfgSgs"}, {"__ref": "r7ddzyPxTXpw"}], "__type": "TplSlot"}, "bnnR3pwn697S": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {}, "rs": {"__ref": "3wLLZs8HONVv"}, "dataCond": {"__ref": "dl7yh2bVaFOL"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6ojRjUeC8fsj": {"variants": [{"__ref": "SnEjJz5N5SgP"}], "args": [], "attrs": {}, "rs": {"__ref": "5KjfYSeMmtU0"}, "dataCond": {"__ref": "mjJVYBN41wbj"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RsnLw-HvfZno": {"variants": [{"__ref": "SZoR3XpS4OLI"}], "args": [], "attrs": {}, "rs": {"__ref": "b6OtmzKITZya"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8o7oo7dux5jl": {"values": {"display": "flex", "flex-direction": "row", "width": "stretch", "height": "wrap", "align-items": "center", "justify-content": "flex-start", "border-top-color": "#DBDBD7", "border-right-color": "#DBDBD7", "border-bottom-color": "#DBDBD7", "border-left-color": "#DBDBD7", "border-top-style": "solid", "border-right-style": "solid", "border-bottom-style": "solid", "border-left-style": "solid", "border-top-width": "1px", "border-right-width": "1px", "border-bottom-width": "1px", "border-left-width": "1px", "border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "background": "linear-gradient(#FFFFFF, #FFFFFF)", "position": "sticky", "padding-top": "7px", "padding-right": "11px", "padding-bottom": "7px", "padding-left": "11px"}, "mixins": [], "__type": "RuleSet"}, "Yl_ckbW18Cpa": {"values": {"border-top-color": "#C8C7C1", "border-right-color": "#C8C7C1", "border-bottom-color": "#C8C7C1", "border-left-color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "duKElgi1tn_z": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "ZgOYGipxhORy": {"values": {"border-top-left-radius": "6px", "border-top-right-radius": "6px", "border-bottom-right-radius": "6px", "border-bottom-left-radius": "6px", "box-shadow": "0px 0px 0px 3px #96C7F2"}, "mixins": [], "__type": "RuleSet"}, "_ZukGFXa_hOW": {"values": {}, "mixins": [], "__type": "RuleSet"}, "YgljhoDvbi01": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2hVXlOrZTfPn": {"values": {"background": "linear-gradient(#232320, #232320)", "border-top-color": "#717069", "border-right-color": "#717069", "border-bottom-color": "#717069", "border-left-color": "#717069"}, "mixins": [], "__type": "RuleSet"}, "wn89k0pYht7j": {"frame": {"__ref": "2i28dPjrlyFb"}, "cellKey": {"__ref": "ClVr8qWCPCxD"}, "__type": "ArenaFrameCell"}, "vQVScnaIqPsd": {"frame": {"__ref": "7u-L5P3JgxmD"}, "cellKey": {"__ref": "ffBjCjPNoy24"}, "__type": "ArenaFrameCell"}, "z73qutlswEtM": {"frame": {"__ref": "B3q8ObbZdOqm"}, "cellKey": {"__ref": "Vej_jrCtd-af"}, "__type": "ArenaFrameCell"}, "CwcBcxi9Pmqy": {"frame": {"__ref": "It9bLyMR6bG5"}, "cellKey": {"__ref": "AzTg488qrmzK"}, "__type": "ArenaFrameCell"}, "QOb_Ia2KYzN2": {"frame": {"__ref": "Q9DSSJVTfJoE"}, "cellKey": {"__ref": "A1fu_SZPgH3Q"}, "__type": "ArenaFrameCell"}, "skRvS8IBNoRR": {"frame": {"__ref": "1kY2n-Hbl1M-"}, "cellKey": {"__ref": "SnEjJz5N5SgP"}, "__type": "ArenaFrameCell"}, "eaEP0w57vAVh": {"frame": {"__ref": "u_EFg_vL5cA0"}, "cellKey": {"__ref": "CZKAnHANVcc-"}, "__type": "ArenaFrameCell"}, "Z0Oag86NozG-": {"frame": {"__ref": "768CRyuSrTgg"}, "cellKey": {"__ref": "SZoR3XpS4OLI"}, "__type": "ArenaFrameCell"}, "ibAzpMPFDSCW": {"name": "any", "__type": "AnyType"}, "KJLPJAX6Rpi0": {"name": "any", "__type": "AnyType"}, "W-mu92xaUFIo": {"name": "any", "__type": "AnyType"}, "XZ4bpILj2_Z7": {"name": "any", "__type": "AnyType"}, "G2J1Jb6aTjBo": {"name": "any", "__type": "AnyType"}, "1p2nN27oG8cq": {"name": "text", "__type": "Text"}, "sy7BQKecYzUd": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "nT0Qf7KZU0GO", "parent": {"__ref": "vLAWtG9Z6Vl8"}, "locked": null, "vsettings": [{"__ref": "lfYBnYPCo1AC"}], "__type": "TplTag"}, "7GYGUEQjo4uK": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {}, "rs": {"__ref": "6pxPIcFoW7Te"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qTqRkIRKIg1g": {"variants": [{"__ref": "A1fu_SZPgH3Q"}], "args": [], "attrs": {}, "rs": {"__ref": "mDajgA5IK9aT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cvBHCeAMYMxh": {"variants": [{"__ref": "SZoR3XpS4OLI"}], "args": [], "attrs": {}, "rs": {"__ref": "br8SLY3J6UZ8"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sCCZcL_OT9GB": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-right": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "Opv3MRSxc1YH": {"code": "true", "fallback": null, "__type": "CustomCode"}, "4McELYWxL8Wg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lKhrbqDQ1dfA": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "zsWORSxHFSu7": {"code": "true", "fallback": null, "__type": "CustomCode"}, "HlAVdw7Edge-": {"values": {}, "mixins": [], "__type": "RuleSet"}, "5WxCejL2rltQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "YIPWHxg5vTGO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pa5Mcbix_3Me": {"variable": {"__ref": "h0xqlhSBYUXZ"}, "__type": "VarRef"}, "2aiy6-wYhnDt": {"variable": {"__ref": "cOnItoHFCdmi"}, "__type": "VarRef"}, "VlSGTwXrNPrZ": {"variable": {"__ref": "A2JZLF8W9K2H"}, "__type": "VarRef"}, "_PuwIbJnr3xS": {"variable": {"__ref": "F8PPLn8ZdbUJ"}, "__type": "VarRef"}, "aH3TNslcF97z": {"variable": {"__ref": "pzP_p8Ciy41b"}, "__type": "VarRef"}, "VVC53K6kEX_5": {"variable": {"__ref": "CACa9s8aVs3I"}, "__type": "VarRef"}, "1rTscuIGgOsl": {"variable": {"__ref": "H235Kre67Aiq"}, "__type": "VarRef"}, "Ak8U4dLqLUnT": {"values": {"width": "stretch", "left": "auto", "top": "auto", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "border-top-width": "0px", "border-right-width": "0px", "border-bottom-width": "0px", "border-left-width": "0px", "background": "linear-gradient(#FFFFFF00, #FFFFFF00)"}, "mixins": [], "__type": "RuleSet"}, "KpLspF6_ilX0": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gBFqHuMA5DWO": {"code": "true", "fallback": null, "__type": "CustomCode"}, "fqDY-cXVnwfs": {"values": {"cursor": "not-allowed"}, "mixins": [], "__type": "RuleSet"}, "AO7-AadEh1oN": {"values": {"color": "#706F6C"}, "mixins": [], "__type": "RuleSet"}, "brS2ugu9ZOLa": {"values": {}, "mixins": [], "__type": "RuleSet"}, "UZehkudnjjon": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cpSZXozreKKA": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZIN0GrNjg78r": {"values": {}, "mixins": [], "__type": "RuleSet"}, "WsV8LFnBafzi": {"values": {}, "mixins": [], "__type": "RuleSet"}, "y7aUjNex3s2z": {"values": {}, "mixins": [], "__type": "RuleSet"}, "cXDiC8tmqJSn": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "kLhgrMyEcpAT": {"values": {"color": "#C8C7C1"}, "mixins": [], "__type": "RuleSet"}, "v_FQPtUN-TrO": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "oamaTsO_6jWb", "parent": {"__ref": "fwpogDKrkMbm"}, "locked": null, "vsettings": [{"__ref": "MAli5Behl1qY"}], "__type": "TplTag"}, "y6dY5u9wlYhf": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {}, "rs": {"__ref": "KOT_Xy_97e5L"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pzAtVUDfgSgs": {"variants": [{"__ref": "SnEjJz5N5SgP"}], "args": [], "attrs": {}, "rs": {"__ref": "XGpUUkZDD_Tx"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r7ddzyPxTXpw": {"variants": [{"__ref": "SZoR3XpS4OLI"}], "args": [], "attrs": {}, "rs": {"__ref": "ZwdVb-zLinJV"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3wLLZs8HONVv": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start", "left": "auto", "top": "auto", "margin-left": "8px", "plasmic-display-none": "true"}, "mixins": [], "__type": "RuleSet"}, "dl7yh2bVaFOL": {"code": "true", "fallback": null, "__type": "CustomCode"}, "5KjfYSeMmtU0": {"values": {"plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "mjJVYBN41wbj": {"code": "true", "fallback": null, "__type": "CustomCode"}, "b6OtmzKITZya": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2i28dPjrlyFb": {"uuid": "dvoVFwYd3pIu", "width": 1180, "height": 540, "container": {"__ref": "jlR_pNAElnYX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "ClVr8qWCPCxD"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "7u-L5P3JgxmD": {"uuid": "1F7S5xw1_nvF", "width": 1180, "height": 540, "container": {"__ref": "stb1jgMKsKqR"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "ffBjCjPNoy24"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "B3q8ObbZdOqm": {"uuid": "zEM88hPUVbsI", "width": 1180, "height": 540, "container": {"__ref": "VHYvEUsCPsID"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Vej_jrCtd-af"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "It9bLyMR6bG5": {"uuid": "5xG6Y-tocWdu", "width": 1180, "height": 540, "container": {"__ref": "lxF6yxdcOZNn"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "AzTg488qrmzK"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "Q9DSSJVTfJoE": {"uuid": "xLTTjqvUn7yi", "width": 1180, "height": 540, "container": {"__ref": "XT6s8f_61KKm"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "A1fu_SZPgH3Q"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "1kY2n-Hbl1M-": {"uuid": "6vE4Q3uex1Dv", "width": 1180, "height": 540, "container": {"__ref": "ZJWQeSZ2tDpH"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "SnEjJz5N5SgP"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "u_EFg_vL5cA0": {"uuid": "k5QY4lKXeZCw", "width": 1180, "height": 540, "container": {"__ref": "VBXmuO1AJ_Np"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "CZKAnHANVcc-"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "768CRyuSrTgg": {"uuid": "09VUupuVh_YJ", "width": 1180, "height": 540, "container": {"__ref": "kOoog6_Mufi6"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "SZoR3XpS4OLI"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "lfYBnYPCo1AC": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {"outerHTML": {"__ref": "xQrdNryQ9wt9"}}, "rs": {"__ref": "1m8jdEUR3XCJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6pxPIcFoW7Te": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mDajgA5IK9aT": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "br8SLY3J6UZ8": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "MAli5Behl1qY": {"variants": [{"__ref": "ClVr8qWCPCxD"}], "args": [], "attrs": {"outerHTML": {"__ref": "fJqvvpNPzfIa"}}, "rs": {"__ref": "rvoJ0odg4UWO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KOT_Xy_97e5L": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XGpUUkZDD_Tx": {"values": {"color": "#90908C"}, "mixins": [], "__type": "RuleSet"}, "ZwdVb-zLinJV": {"values": {"color": "#FFFFFF"}, "mixins": [], "__type": "RuleSet"}, "jlR_pNAElnYX": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "amrOz-nZLUO0", "parent": null, "locked": null, "vsettings": [{"__ref": "HtnfgOonWl_m"}], "__type": "TplComponent"}, "stb1jgMKsKqR": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "hjQjlUzvWLsQ", "parent": null, "locked": null, "vsettings": [{"__ref": "bXYNJN_M1X0O"}], "__type": "TplComponent"}, "VHYvEUsCPsID": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "NfzigOm2AN17", "parent": null, "locked": null, "vsettings": [{"__ref": "HbvJg4C5kZR_"}], "__type": "TplComponent"}, "lxF6yxdcOZNn": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "kui2XxSVUZkm", "parent": null, "locked": null, "vsettings": [{"__ref": "AoyBF2_9rli0"}], "__type": "TplComponent"}, "XT6s8f_61KKm": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "0LV2k2uiOJ3C", "parent": null, "locked": null, "vsettings": [{"__ref": "zdrc9r4bfP7n"}], "__type": "TplComponent"}, "ZJWQeSZ2tDpH": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "TsGPN8Bbf2mU", "parent": null, "locked": null, "vsettings": [{"__ref": "QrqKJG5gy5sw"}], "__type": "TplComponent"}, "VBXmuO1AJ_Np": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "o2hGqvvyynE0", "parent": null, "locked": null, "vsettings": [{"__ref": "gSihtstIG7Ij"}], "__type": "TplComponent"}, "kOoog6_Mufi6": {"name": null, "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "sSD-NqGwpCUI", "parent": null, "locked": null, "vsettings": [{"__ref": "CjmWWQu-M3vz"}], "__type": "TplComponent"}, "xQrdNryQ9wt9": {"asset": {"__ref": "7ucfpJr_iSOE"}, "__type": "ImageAssetRef"}, "1m8jdEUR3XCJ": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "fJqvvpNPzfIa": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "rvoJ0odg4UWO": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "HtnfgOonWl_m": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "mcVUXKOoiXXL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bXYNJN_M1X0O": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "HYflaQyTDFAy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "HbvJg4C5kZR_": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "J3_wDYohz7XB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AoyBF2_9rli0": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "NlWRM6AI7Pq1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zdrc9r4bfP7n": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "C7NxVg5onf8E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "QrqKJG5gy5sw": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "C59qxicbiJXQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gSihtstIG7Ij": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "aQGfndSQ3EET"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CjmWWQu-M3vz": {"variants": [{"__ref": "R80oJYEapP1X"}], "args": [], "attrs": {}, "rs": {"__ref": "pe1zbLB5Rl3X"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mcVUXKOoiXXL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "HYflaQyTDFAy": {"values": {}, "mixins": [], "__type": "RuleSet"}, "J3_wDYohz7XB": {"values": {}, "mixins": [], "__type": "RuleSet"}, "NlWRM6AI7Pq1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "C7NxVg5onf8E": {"values": {}, "mixins": [], "__type": "RuleSet"}, "C59qxicbiJXQ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "aQGfndSQ3EET": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pe1zbLB5Rl3X": {"values": {}, "mixins": [], "__type": "RuleSet"}, "pMiH7SOm3-Dz": {"name": "Slider Carousel", "component": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "wKqxNVJ8Kt1w"}}, "uuid": "rOnEMY-iJofr", "parent": {"__ref": "Y7fjLAMILdxu"}, "locked": null, "vsettings": [{"__ref": "8Z9mr155B97M"}], "__type": "TplComponent"}, "8Z9mr155B97M": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "1T-tSpVmihK-"}, {"__ref": "GzrmVu9wmNBx"}, {"__ref": "7BOGZscCFsUj"}, {"__ref": "9sxirOWC8G50"}, {"__ref": "cO8G6NcAsI22"}, {"__ref": "fPdRPRtZjmqe"}, {"__ref": "sIBHkEF9NQFs"}], "attrs": {}, "rs": {"__ref": "MSgQDCVdOFlP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1T-tSpVmihK-": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "ZEE1qbzSeuSN"}}, "expr": {"__ref": "aIy9HFAoS3yq"}, "__type": "Arg"}, "MSgQDCVdOFlP": {"values": {"width": "stretch", "max-width": "100%", "flex-direction": "column", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "sMPQJULwoPLG": {"tag": "div", "name": null, "children": [{"__ref": "_XI2UNrL2BvZ"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "6ZiwbgQJH9VC", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "OFHOZKVOxgxx"}], "__type": "TplTag"}, "r4RjaqThlS9Y": {"tag": "div", "name": null, "children": [{"__ref": "oIO49osJDgvX"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "515wxjg7kzVE", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "Lco2ceRnIpeB"}], "__type": "TplTag"}, "mwVRSb--5lTy": {"tag": "div", "name": null, "children": [{"__ref": "RtbAfKD8Qkzt"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uKp2jFmTQXrt", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "jINmqXh_rcS-"}], "__type": "TplTag"}, "_XI2UNrL2BvZ": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "J-f_VCGTTv9e", "parent": {"__ref": "sMPQJULwoPLG"}, "locked": null, "vsettings": [{"__ref": "NPpe7N4z593U"}], "__type": "TplTag"}, "OFHOZKVOxgxx": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "yQmFG92lu2Mr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "oIO49osJDgvX": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "08dVcIouOlCl", "parent": {"__ref": "r4RjaqThlS9Y"}, "locked": null, "vsettings": [{"__ref": "sVEfTTT-YhlS"}], "__type": "TplTag"}, "Lco2ceRnIpeB": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "kSNrwQWUpfgD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RtbAfKD8Qkzt": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "UGKmTSrLlsnh", "parent": {"__ref": "mwVRSb--5lTy"}, "locked": null, "vsettings": [{"__ref": "8gFjlqe5jkg1"}], "__type": "TplTag"}, "jINmqXh_rcS-": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "0J5Llc32bvJ0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NPpe7N4z593U": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "JDjH4U-Qyxrf"}}, "rs": {"__ref": "Mre1Dk0nsA3E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yQmFG92lu2Mr": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "sVEfTTT-YhlS": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "SALeGv2fkcJH"}}, "rs": {"__ref": "xyHTdZ42e5Ki"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kSNrwQWUpfgD": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "8gFjlqe5jkg1": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "zTom54Fy_blF"}}, "rs": {"__ref": "Q8UepxguIlBP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0J5Llc32bvJ0": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "JDjH4U-Qyxrf": {"code": "\"https://static1.plasmic.app/components/react-slick/slide1.png\"", "fallback": null, "__type": "CustomCode"}, "Mre1Dk0nsA3E": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "SALeGv2fkcJH": {"code": "\"https://static1.plasmic.app/components/react-slick/slide2.png\"", "fallback": null, "__type": "CustomCode"}, "xyHTdZ42e5Ki": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "zTom54Fy_blF": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "Q8UepxguIlBP": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "GzrmVu9wmNBx": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "sjQ4vWY_DMBw"}}, "expr": {"__ref": "wRN5OxpIWOEI"}, "__type": "Arg"}, "7BOGZscCFsUj": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "UP_XQZGxG1Vt"}}, "expr": {"__ref": "uiCJq3hrnLj6"}, "__type": "Arg"}, "uiCJq3hrnLj6": {"code": "1000", "fallback": null, "__type": "CustomCode"}, "9sxirOWC8G50": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "n6GJIVnE9uPa"}}, "expr": {"__ref": "hScBQEtV6OjC"}, "__type": "Arg"}, "hScBQEtV6OjC": {"code": "2", "fallback": null, "__type": "CustomCode"}, "cO8G6NcAsI22": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "ascjkNfMaSx8"}}, "expr": {"__ref": "7fA6OMZxr6bQ"}, "__type": "Arg"}, "7fA6OMZxr6bQ": {"code": "3", "fallback": null, "__type": "CustomCode"}, "1tYGvQA1lJUK": {"tag": "div", "name": null, "children": [{"__ref": "PGbg_sF6JgPs"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fnS7Ln7Q4fd6", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "Kw7NcwD5zAvl"}], "__type": "TplTag"}, "PUAgXYjnZlKU": {"tag": "div", "name": null, "children": [{"__ref": "LnQX5UeZUMg-"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "MT7qI0NQGm4P", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "KJfd9xDd-06Z"}], "__type": "TplTag"}, "qmR6wLG3BOkY": {"tag": "div", "name": null, "children": [{"__ref": "9ZRy0zRYHrOP"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "0D2Rpd3nJ3To", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "tYFGUossnYAh"}], "__type": "TplTag"}, "2SxEF-4snzPq": {"tag": "div", "name": null, "children": [{"__ref": "U3swgnPWBQVQ"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "VLJVCYRQ6PQE", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "nH54J44kasMX"}], "__type": "TplTag"}, "lhaGZIDc9Orp": {"tag": "div", "name": null, "children": [{"__ref": "WDmNFT1iED4d"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8IQbQO4wVSrw", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "E4mRZT18cXNV"}], "__type": "TplTag"}, "8dzG2sk1NSSf": {"tag": "div", "name": null, "children": [{"__ref": "_9-Q3JbhMwC8"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ztDdGEBz4hwY", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "0w9mHL8yOAk_"}], "__type": "TplTag"}, "PGbg_sF6JgPs": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "reeHDqu4mpug", "parent": {"__ref": "1tYGvQA1lJUK"}, "locked": null, "vsettings": [{"__ref": "nklchLpEoLPa"}], "__type": "TplTag"}, "Kw7NcwD5zAvl": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "Xcm5pZocsN3g"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LnQX5UeZUMg-": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "r6R6rw-Hero1", "parent": {"__ref": "PUAgXYjnZlKU"}, "locked": null, "vsettings": [{"__ref": "1xVkea3vp-qh"}], "__type": "TplTag"}, "KJfd9xDd-06Z": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "XRv8NSRyr3D0"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9ZRy0zRYHrOP": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "4g1JSm_yemw0", "parent": {"__ref": "qmR6wLG3BOkY"}, "locked": null, "vsettings": [{"__ref": "BdhnzDZ51ib9"}], "__type": "TplTag"}, "tYFGUossnYAh": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "KsWItlItWkvq"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "U3swgnPWBQVQ": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "mzYus37GuloY", "parent": {"__ref": "2SxEF-4snzPq"}, "locked": null, "vsettings": [{"__ref": "1UyZ3Fhpn767"}], "__type": "TplTag"}, "nH54J44kasMX": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "pL22tqgzTRid"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "WDmNFT1iED4d": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "TZj06sm7sszc", "parent": {"__ref": "lhaGZIDc9Orp"}, "locked": null, "vsettings": [{"__ref": "vacPXbwViurz"}], "__type": "TplTag"}, "E4mRZT18cXNV": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "IvlnKz0d80zo"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_9-Q3JbhMwC8": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "91aCIOjlIVqp", "parent": {"__ref": "8dzG2sk1NSSf"}, "locked": null, "vsettings": [{"__ref": "nxGqtxPohiXA"}], "__type": "TplTag"}, "0w9mHL8yOAk_": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "EFfzb7JfZaYi"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nklchLpEoLPa": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "qaL_IfqB5K89"}}, "rs": {"__ref": "JgthmgwCcHKk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Xcm5pZocsN3g": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "1xVkea3vp-qh": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "K0egejoHS3KA"}}, "rs": {"__ref": "W8056ojDdeRY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XRv8NSRyr3D0": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "BdhnzDZ51ib9": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "e--Kzzgs-fYO"}}, "rs": {"__ref": "B5fQ49i2PGS5"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "KsWItlItWkvq": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "1UyZ3Fhpn767": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "rAwa1-zVnbQy"}}, "rs": {"__ref": "wTWpVzSDtJ6D"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pL22tqgzTRid": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "vacPXbwViurz": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "xKrUk2OxPtVC"}}, "rs": {"__ref": "Tq32OceRQ9hM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "IvlnKz0d80zo": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "nxGqtxPohiXA": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "y53aO0k3pT3W"}}, "rs": {"__ref": "3C7y1jte60Co"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EFfzb7JfZaYi": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "qaL_IfqB5K89": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "JgthmgwCcHKk": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "K0egejoHS3KA": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "W8056ojDdeRY": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "e--Kzzgs-fYO": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "B5fQ49i2PGS5": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "rAwa1-zVnbQy": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "wTWpVzSDtJ6D": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "xKrUk2OxPtVC": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "Tq32OceRQ9hM": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "y53aO0k3pT3W": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "3C7y1jte60Co": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "fPdRPRtZjmqe": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "tKSAZ4uR2wS0"}}, "expr": {"__ref": "2IbcCPFcE6HM"}, "__type": "Arg"}, "2IbcCPFcE6HM": {"code": "true", "fallback": null, "__type": "CustomCode"}, "po6xYbYIfYdq": {"tag": "div", "name": null, "children": [{"__ref": "ER3S7Ar50qmX"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "cy4zmfof07Q2", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "WBrNLfCxO_Lu"}], "__type": "TplTag"}, "0ZgPTH-Pr37d": {"tag": "div", "name": null, "children": [{"__ref": "i_w6NeUguphX"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9vZlAL7wVFDA", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "TwQyP09F-S4y"}], "__type": "TplTag"}, "vjoGinCVmCIo": {"tag": "div", "name": null, "children": [{"__ref": "EKB49oF_4U_c"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7L71i6FPHvba", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "Zk6m04IiuhhZ"}], "__type": "TplTag"}, "YE5BlAIGvFiL": {"tag": "div", "name": null, "children": [{"__ref": "3PUplw3loimE"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "H0Mxe1yOocLR", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "2CGq8EW4tMeI"}], "__type": "TplTag"}, "ER3S7Ar50qmX": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "FViAIQa6YQzt", "parent": {"__ref": "po6xYbYIfYdq"}, "locked": null, "vsettings": [{"__ref": "D80J7XyUvz47"}], "__type": "TplTag"}, "WBrNLfCxO_Lu": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "DRx-GxNXrxaJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "i_w6NeUguphX": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "uM7FllIPKyGA", "parent": {"__ref": "0ZgPTH-Pr37d"}, "locked": null, "vsettings": [{"__ref": "1PtBJYopDo2Z"}], "__type": "TplTag"}, "TwQyP09F-S4y": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "0yw0hKxCuQkd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EKB49oF_4U_c": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "N7KQJUAz1P0i", "parent": {"__ref": "vjoGinCVmCIo"}, "locked": null, "vsettings": [{"__ref": "fTXMT0pn63MC"}], "__type": "TplTag"}, "Zk6m04IiuhhZ": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "-wycd5WfcG5t"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3PUplw3loimE": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "CNMCpQznDGbu", "parent": {"__ref": "YE5BlAIGvFiL"}, "locked": null, "vsettings": [{"__ref": "JGYwp3TLgp-B"}], "__type": "TplTag"}, "2CGq8EW4tMeI": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "cXiosR3ED9OO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "D80J7XyUvz47": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "zFnPXA6bJhUr"}}, "rs": {"__ref": "Xf5Y5RmP7Yq7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DRx-GxNXrxaJ": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "1PtBJYopDo2Z": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "998_y5YnSDQ_"}}, "rs": {"__ref": "ktljPjDcGHxB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0yw0hKxCuQkd": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "fTXMT0pn63MC": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "mVjV1uBHA2LS"}}, "rs": {"__ref": "qLZY0FbgrNKn"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-wycd5WfcG5t": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "JGYwp3TLgp-B": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "VpUa5J9sUzem"}}, "rs": {"__ref": "JvMPP4ZvKyCp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "cXiosR3ED9OO": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "zFnPXA6bJhUr": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "Xf5Y5RmP7Yq7": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "998_y5YnSDQ_": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "ktljPjDcGHxB": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "mVjV1uBHA2LS": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "qLZY0FbgrNKn": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "VpUa5J9sUzem": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "JvMPP4ZvKyCp": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "gJi9P6haDhzs": {"tag": "div", "name": null, "children": [{"__ref": "iuGNyuj-idK_"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "oHgPPMbR5az1", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "Vxl91SeF2Lkg"}], "__type": "TplTag"}, "0dwNrulPAGQ3": {"tag": "div", "name": null, "children": [{"__ref": "F6goqqN_X6oh"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "bK4fypyBMDiC", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "gh50LeA61t14"}], "__type": "TplTag"}, "iuGNyuj-idK_": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Zq7YjEbl87tz", "parent": {"__ref": "gJi9P6haDhzs"}, "locked": null, "vsettings": [{"__ref": "dfq_4os-CkYf"}], "__type": "TplTag"}, "Vxl91SeF2Lkg": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "PEtN_MzKsCb_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F6goqqN_X6oh": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "x_BAbm5Auz--", "parent": {"__ref": "0dwNrulPAGQ3"}, "locked": null, "vsettings": [{"__ref": "WhyWrCAWdKqU"}], "__type": "TplTag"}, "gh50LeA61t14": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "sCokMvyiwkSj"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dfq_4os-CkYf": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "cmD9n54udgVD"}}, "rs": {"__ref": "HoHS0G0orXx1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PEtN_MzKsCb_": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "WhyWrCAWdKqU": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "QaAKurX8_qmp"}}, "rs": {"__ref": "0WK4wRlcnmpa"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sCokMvyiwkSj": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "cmD9n54udgVD": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "HoHS0G0orXx1": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "QaAKurX8_qmp": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "0WK4wRlcnmpa": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "AI9ZuOQ2aA4x": {"tag": "div", "name": null, "children": [{"__ref": "rOV2vUg9gqaL"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "gxf-DO38_F2Z", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "Kbv7TICBFuQN"}], "__type": "TplTag"}, "_K94Q86wjDaR": {"tag": "div", "name": null, "children": [{"__ref": "eJtnL3EWgKy7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "WfMGHx3YL7A8", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "vwFEgl499NMb"}], "__type": "TplTag"}, "rOV2vUg9gqaL": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "tsgsSfPEM-Hi", "parent": {"__ref": "AI9ZuOQ2aA4x"}, "locked": null, "vsettings": [{"__ref": "eh7GvqsL5Slr"}], "__type": "TplTag"}, "Kbv7TICBFuQN": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "bd0MMuE2EA32"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eJtnL3EWgKy7": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "AyWsbweS9-AY", "parent": {"__ref": "_K94Q86wjDaR"}, "locked": null, "vsettings": [{"__ref": "B1RI6y0c0lI0"}], "__type": "TplTag"}, "vwFEgl499NMb": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "wEa0lOazduxS"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eh7GvqsL5Slr": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "YHdBIXKY9iMR"}}, "rs": {"__ref": "5DolN31G-nX7"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bd0MMuE2EA32": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "B1RI6y0c0lI0": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "54HFvtudxidp"}}, "rs": {"__ref": "YkB_qiKkiJxD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "wEa0lOazduxS": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "YHdBIXKY9iMR": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "5DolN31G-nX7": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "54HFvtudxidp": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "YkB_qiKkiJxD": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "aIy9HFAoS3yq": {"tpl": [{"__ref": "sMPQJULwoPLG"}, {"__ref": "r4RjaqThlS9Y"}, {"__ref": "mwVRSb--5lTy"}, {"__ref": "1tYGvQA1lJUK"}, {"__ref": "PUAgXYjnZlKU"}, {"__ref": "qmR6wLG3BOkY"}, {"__ref": "2SxEF-4snzPq"}, {"__ref": "lhaGZIDc9Orp"}, {"__ref": "8dzG2sk1NSSf"}, {"__ref": "po6xYbYIfYdq"}, {"__ref": "0ZgPTH-Pr37d"}, {"__ref": "vjoGinCVmCIo"}, {"__ref": "YE5BlAIGvFiL"}, {"__ref": "gJi9P6haDhzs"}, {"__ref": "0dwNrulPAGQ3"}, {"__ref": "AI9ZuOQ2aA4x"}, {"__ref": "_K94Q86wjDaR"}, {"__ref": "EdEWfPAL9XEu"}, {"__ref": "FjUu9nSxOO-o"}], "__type": "RenderExpr"}, "EdEWfPAL9XEu": {"tag": "div", "name": null, "children": [{"__ref": "fgVm5QAeQ40c"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Qahst3wDHaVX", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "QWjI3XFD5869"}], "__type": "TplTag"}, "FjUu9nSxOO-o": {"tag": "div", "name": null, "children": [{"__ref": "6C5w7l32HfS4"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "P9V021Nj82El", "parent": {"__ref": "pMiH7SOm3-Dz"}, "locked": null, "vsettings": [{"__ref": "S6y7vVswcdis"}], "__type": "TplTag"}, "fgVm5QAeQ40c": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "63b5KdpD3Fyb", "parent": {"__ref": "EdEWfPAL9XEu"}, "locked": null, "vsettings": [{"__ref": "lKP8mgElVcLF"}], "__type": "TplTag"}, "QWjI3XFD5869": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "Id-KOLRPGw4f"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6C5w7l32HfS4": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "vbK2hld_8RrU", "parent": {"__ref": "FjUu9nSxOO-o"}, "locked": null, "vsettings": [{"__ref": "XW26yOd_EgFP"}], "__type": "TplTag"}, "S6y7vVswcdis": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "9O0Z24btqjvZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lKP8mgElVcLF": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "u-nIgj3OwvPF"}}, "rs": {"__ref": "BQlaSvLj7eSh"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Id-KOLRPGw4f": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XW26yOd_EgFP": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"src": {"__ref": "1X-NxtI_-45R"}}, "rs": {"__ref": "-K4LeFl1x_UL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9O0Z24btqjvZ": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "u-nIgj3OwvPF": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "BQlaSvLj7eSh": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "1X-NxtI_-45R": {"code": "\"https://static1.plasmic.app/components/react-slick/slide3.png\"", "fallback": null, "__type": "CustomCode"}, "-K4LeFl1x_UL": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "ErwfHvfoHdvb": {"tag": "div", "name": null, "children": [{"__ref": "lA-D_K9pGAiB"}, {"__ref": "BhHdBKIunvfo"}, {"__ref": "ec2MJQKQ-xU2"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_Tuyh9zE5RFf", "parent": {"__ref": "Y7fjLAMILdxu"}, "locked": null, "vsettings": [{"__ref": "MeQB8H1qNxBl"}], "__type": "TplTag"}, "MeQB8H1qNxBl": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"id": {"__ref": "XcSsk7mPcCQ5"}}, "rs": {"__ref": "B8lbhGiQsTbJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "B8lbhGiQsTbJ": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "margin-top": "0px", "margin-right": "0px", "margin-bottom": "0px", "margin-left": "0px"}, "mixins": [], "__type": "RuleSet"}, "lA-D_K9pGAiB": {"tag": "div", "name": null, "children": [{"__ref": "T-liOtUg9YE3"}, {"__ref": "taJWYm-BkR_6"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "j5m3uytjQxC5", "parent": {"__ref": "ErwfHvfoHdvb"}, "locked": null, "vsettings": [{"__ref": "z_L2taSqJJQR"}], "__type": "TplTag"}, "z_L2taSqJJQR": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "MbLytd_hAixT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MbLytd_hAixT": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "T-liOtUg9YE3": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "QcLQq07SQo6J", "parent": {"__ref": "lA-D_K9pGAiB"}, "locked": null, "vsettings": [{"__ref": "PsNIt7C0lHao"}], "__type": "TplComponent"}, "PsNIt7C0lHao": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "8zFOB5gEo-TH"}, {"__ref": "SAVaXzBydLPS"}, {"__ref": "X5zzEMX0UYA7"}], "attrs": {"onClick": {"__ref": "D6nfysofxvCb"}}, "rs": {"__ref": "IZ7kFA72uoxp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8zFOB5gEo-TH": {"param": {"__ref": "58W11N2MXQvz"}, "expr": {"__ref": "OL649EyUL3C_"}, "__type": "Arg"}, "SAVaXzBydLPS": {"param": {"__ref": "jiGTb077efD0"}, "expr": {"__ref": "qjmcmu0idA21"}, "__type": "Arg"}, "X5zzEMX0UYA7": {"param": {"__ref": "ALGBMmJdKGZS"}, "expr": {"__ref": "CwNypk8tvdJk"}, "__type": "Arg"}, "IZ7kFA72uoxp": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "OL649EyUL3C_": {"tpl": [{"__ref": "XFy3p570UFKI"}], "__type": "VirtualRenderExpr"}, "CwNypk8tvdJk": {"tpl": [{"__ref": "4FEzXx62GeGr"}], "__type": "VirtualRenderExpr"}, "XFy3p570UFKI": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "00NxPMFcVRf3", "parent": {"__ref": "T-liOtUg9YE3"}, "locked": null, "vsettings": [{"__ref": "f8aqlZlD1ZT6"}], "__type": "TplTag"}, "MpELGievSByK": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ClvKdo5EBFOB", "parent": {"__ref": "T-liOtUg9YE3"}, "locked": null, "vsettings": [{"__ref": "sa5vlzKELX0H"}], "__type": "TplTag"}, "4FEzXx62GeGr": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "GcywCOEoNnJx", "parent": {"__ref": "T-liOtUg9YE3"}, "locked": null, "vsettings": [{"__ref": "hKP9Ss4eePyl"}], "__type": "TplTag"}, "f8aqlZlD1ZT6": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "zqY3mExSWcYZ"}}, "rs": {"__ref": "3NO3_gycQpsy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sa5vlzKELX0H": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "lzQcHwnq5JYI"}, "dataCond": null, "dataRep": null, "text": {"__ref": "eaPU71BmCGIN"}, "columnsConfig": null, "__type": "VariantSetting"}, "hKP9Ss4eePyl": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "p0TnjEzkao41"}}, "rs": {"__ref": "ls8aTniS5YGD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zqY3mExSWcYZ": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "3NO3_gycQpsy": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "lzQcHwnq5JYI": {"values": {}, "mixins": [], "__type": "RuleSet"}, "p0TnjEzkao41": {"asset": {"__ref": "qkP78v4NuNLB"}, "__type": "ImageAssetRef"}, "ls8aTniS5YGD": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "D6nfysofxvCb": {"interactions": [{"__ref": "q08lPGHHsIjF"}], "__type": "EventHandler"}, "q08lPGHHsIjF": {"interactionName": "Run action on Slider Carousel", "actionName": "invokeRefAction", "args": [{"__ref": "rij-9lm6DeZY"}, {"__ref": "MJF9cAezn0u7"}], "condExpr": null, "conditionalMode": "always", "uuid": "ki0ywKXh3Bvb", "parent": {"__ref": "D6nfysofxvCb"}, "__type": "Interaction"}, "FNwk-lRP1vD7": {"tpl": {"__ref": "pMiH7SOm3-Dz"}, "__type": "TplRef"}, "rij-9lm6DeZY": {"name": "tplRef", "expr": {"__ref": "FNwk-lRP1vD7"}, "__type": "NameArg"}, "MJF9cAezn0u7": {"name": "action", "expr": {"__ref": "uagyz2S_Z3Jr"}, "__type": "NameArg"}, "uagyz2S_Z3Jr": {"code": "\"slickPrev\"", "fallback": null, "__type": "CustomCode"}, "qjmcmu0idA21": {"tpl": [{"__ref": "MpELGievSByK"}], "__type": "RenderExpr"}, "eaPU71BmCGIN": {"expr": {"__ref": "4IhGDGvMdaTD"}, "html": false, "__type": "ExprText"}, "4IhGDGvMdaTD": {"code": "(\"Back\")", "fallback": {"__ref": "C3I_J7hLwX85"}, "__type": "CustomCode"}, "C3I_J7hLwX85": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "taJWYm-BkR_6": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "SZ873TF3ppyJ", "parent": {"__ref": "lA-D_K9pGAiB"}, "locked": null, "vsettings": [{"__ref": "bcBsJ6TnAO2S"}], "__type": "TplComponent"}, "bcBsJ6TnAO2S": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "MTVUFlAIl52D"}, {"__ref": "cc0d7NiVZEjw"}, {"__ref": "8xbXE4_nMHsz"}], "attrs": {"onClick": {"__ref": "33hcpMQe2k1_"}}, "rs": {"__ref": "u6R-rAU8Xqhv"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MTVUFlAIl52D": {"param": {"__ref": "58W11N2MXQvz"}, "expr": {"__ref": "8Nt1Nwmi6TsX"}, "__type": "Arg"}, "cc0d7NiVZEjw": {"param": {"__ref": "jiGTb077efD0"}, "expr": {"__ref": "HDSHzn7IjlP6"}, "__type": "Arg"}, "8xbXE4_nMHsz": {"param": {"__ref": "ALGBMmJdKGZS"}, "expr": {"__ref": "4u15GcRyDvY5"}, "__type": "Arg"}, "33hcpMQe2k1_": {"interactions": [{"__ref": "xQKYUR24ZAHp"}], "__type": "EventHandler"}, "u6R-rAU8Xqhv": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "8Nt1Nwmi6TsX": {"tpl": [{"__ref": "WjyAiRsNOzp7"}], "__type": "VirtualRenderExpr"}, "HDSHzn7IjlP6": {"tpl": [{"__ref": "Pld2fQqCKH9b"}], "__type": "RenderExpr"}, "4u15GcRyDvY5": {"tpl": [{"__ref": "SPCar7Bbnxf_"}], "__type": "VirtualRenderExpr"}, "xQKYUR24ZAHp": {"interactionName": "Run action on Slider Carousel", "actionName": "invokeRefAction", "args": [{"__ref": "5kWQqptgIMnH"}, {"__ref": "Z4RxMJxI3Gih"}], "condExpr": null, "conditionalMode": "always", "uuid": "nZtoZ7TFh762", "parent": {"__ref": "33hcpMQe2k1_"}, "__type": "Interaction"}, "WjyAiRsNOzp7": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "NnV6i2F1eHbs", "parent": {"__ref": "taJWYm-BkR_6"}, "locked": null, "vsettings": [{"__ref": "eSjONuzXHf7c"}], "__type": "TplTag"}, "Pld2fQqCKH9b": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "WKDLO01lYIEo", "parent": {"__ref": "taJWYm-BkR_6"}, "locked": null, "vsettings": [{"__ref": "-T-YpnT6geIr"}], "__type": "TplTag"}, "SPCar7Bbnxf_": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "0U1BNgP7QTp4", "parent": {"__ref": "taJWYm-BkR_6"}, "locked": null, "vsettings": [{"__ref": "lnbyh5kmkjWF"}], "__type": "TplTag"}, "eSjONuzXHf7c": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "Sl0CMDP52ne1"}}, "rs": {"__ref": "J6NInLMnl6BE"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-T-YpnT6geIr": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "vf0uT9ybCPur"}, "dataCond": null, "dataRep": null, "text": {"__ref": "Du315im2UKAz"}, "columnsConfig": null, "__type": "VariantSetting"}, "lnbyh5kmkjWF": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "l7W5WJPfahT5"}}, "rs": {"__ref": "eORxLdEFwhjF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AzUIcdfvW4u8": {"tpl": {"__ref": "pMiH7SOm3-Dz"}, "__type": "TplRef"}, "Sl0CMDP52ne1": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "J6NInLMnl6BE": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "vf0uT9ybCPur": {"values": {}, "mixins": [], "__type": "RuleSet"}, "l7W5WJPfahT5": {"asset": {"__ref": "qkP78v4NuNLB"}, "__type": "ImageAssetRef"}, "eORxLdEFwhjF": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "Du315im2UKAz": {"expr": {"__ref": "wlKOno74oqGN"}, "html": false, "__type": "ExprText"}, "wlKOno74oqGN": {"code": "(\"Next\")", "fallback": {"__ref": "go8iIYsekSC9"}, "__type": "CustomCode"}, "go8iIYsekSC9": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "5kWQqptgIMnH": {"name": "tplRef", "expr": {"__ref": "AzUIcdfvW4u8"}, "__type": "NameArg"}, "Z4RxMJxI3Gih": {"name": "action", "expr": {"__ref": "fZtfjPokY7wx"}, "__type": "NameArg"}, "fZtfjPokY7wx": {"code": "\"slickNext\"", "fallback": null, "__type": "CustomCode"}, "BhHdBKIunvfo": {"tag": "div", "name": null, "children": [{"__ref": "IP9GPmBb8iYX"}, {"__ref": "uM36VddeFiP2"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "pysd3kdKYohV", "parent": {"__ref": "ErwfHvfoHdvb"}, "locked": null, "vsettings": [{"__ref": "Sw020PkHU8CE"}], "__type": "TplTag"}, "IP9GPmBb8iYX": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "VJ47ov2MT1DD", "parent": {"__ref": "BhHdBKIunvfo"}, "locked": null, "vsettings": [{"__ref": "dDF3vaMyJFc6"}], "__type": "TplComponent"}, "uM36VddeFiP2": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "j5AzcjbfIYRx", "parent": {"__ref": "BhHdBKIunvfo"}, "locked": null, "vsettings": [{"__ref": "F_pVB8-cc9Iu"}], "__type": "TplComponent"}, "Sw020PkHU8CE": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "1CM1EKl2JiTO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dDF3vaMyJFc6": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "-iZtt_5795vS"}, {"__ref": "ILwnollnKJ1I"}, {"__ref": "0fPiyQc40wxn"}], "attrs": {"onClick": {"__ref": "Ke7gHeO1LYRu"}}, "rs": {"__ref": "nS49A32T-tup"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F_pVB8-cc9Iu": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "a2xNqDzYB4pB"}, {"__ref": "wfZLc1keZaY2"}, {"__ref": "FPPuaeo615HO"}], "attrs": {"onClick": {"__ref": "KIDL0719bE_q"}}, "rs": {"__ref": "c4kPWvgYPePM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1CM1EKl2JiTO": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "-iZtt_5795vS": {"param": {"__ref": "58W11N2MXQvz"}, "expr": {"__ref": "o2ljG3xoZGn6"}, "__type": "Arg"}, "ILwnollnKJ1I": {"param": {"__ref": "jiGTb077efD0"}, "expr": {"__ref": "oKm3MEbeB1Df"}, "__type": "Arg"}, "0fPiyQc40wxn": {"param": {"__ref": "ALGBMmJdKGZS"}, "expr": {"__ref": "mEQSDzK8uzyP"}, "__type": "Arg"}, "Ke7gHeO1LYRu": {"interactions": [{"__ref": "iiO9YayfBSGt"}], "__type": "EventHandler"}, "nS49A32T-tup": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "a2xNqDzYB4pB": {"param": {"__ref": "58W11N2MXQvz"}, "expr": {"__ref": "ISqQ4zRgSUxe"}, "__type": "Arg"}, "wfZLc1keZaY2": {"param": {"__ref": "jiGTb077efD0"}, "expr": {"__ref": "TiDOZB-86qch"}, "__type": "Arg"}, "FPPuaeo615HO": {"param": {"__ref": "ALGBMmJdKGZS"}, "expr": {"__ref": "nZ8f99UGMN53"}, "__type": "Arg"}, "KIDL0719bE_q": {"interactions": [{"__ref": "60jcF9dqCXzf"}], "__type": "EventHandler"}, "c4kPWvgYPePM": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "o2ljG3xoZGn6": {"tpl": [{"__ref": "9VN5l2Fmn1_R"}], "__type": "VirtualRenderExpr"}, "oKm3MEbeB1Df": {"tpl": [{"__ref": "ulmalPz6fxDa"}], "__type": "RenderExpr"}, "mEQSDzK8uzyP": {"tpl": [{"__ref": "6Hc7AOoTg3R0"}], "__type": "VirtualRenderExpr"}, "iiO9YayfBSGt": {"interactionName": "Run action on Slider Carousel", "actionName": "invokeRefAction", "args": [{"__ref": "v2nujDZ8qbk2"}, {"__ref": "MfD31JQhRjRy"}], "condExpr": null, "conditionalMode": "always", "uuid": "1WFNswcW9uwv", "parent": {"__ref": "Ke7gHeO1LYRu"}, "__type": "Interaction"}, "ISqQ4zRgSUxe": {"tpl": [{"__ref": "05ZvdBcTfhlP"}], "__type": "VirtualRenderExpr"}, "TiDOZB-86qch": {"tpl": [{"__ref": "UX8BICTxQxXP"}], "__type": "RenderExpr"}, "nZ8f99UGMN53": {"tpl": [{"__ref": "LNRIzg2M075D"}], "__type": "VirtualRenderExpr"}, "60jcF9dqCXzf": {"interactionName": "Run action on Slider Carousel", "actionName": "invokeRefAction", "args": [{"__ref": "Euz0FYNdvXP1"}, {"__ref": "XMj3JRGi_GR1"}], "condExpr": null, "conditionalMode": "always", "uuid": "6eR3AiBVbYpY", "parent": {"__ref": "KIDL0719bE_q"}, "__type": "Interaction"}, "9VN5l2Fmn1_R": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "whFF3_mAslFl", "parent": {"__ref": "IP9GPmBb8iYX"}, "locked": null, "vsettings": [{"__ref": "iu40j2SmFWD5"}], "__type": "TplTag"}, "ulmalPz6fxDa": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "_dmYL_02Eymz", "parent": {"__ref": "IP9GPmBb8iYX"}, "locked": null, "vsettings": [{"__ref": "OGorbdbDmTtT"}], "__type": "TplTag"}, "6Hc7AOoTg3R0": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "2PUFI7j14wXF", "parent": {"__ref": "IP9GPmBb8iYX"}, "locked": null, "vsettings": [{"__ref": "RI1RhB00FQ9J"}], "__type": "TplTag"}, "05ZvdBcTfhlP": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "cwLjbJjNb_yJ", "parent": {"__ref": "uM36VddeFiP2"}, "locked": null, "vsettings": [{"__ref": "QGEiLR98c2Tp"}], "__type": "TplTag"}, "UX8BICTxQxXP": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "r9zOmfGo5rJT", "parent": {"__ref": "uM36VddeFiP2"}, "locked": null, "vsettings": [{"__ref": "aph1QyvVaYNt"}], "__type": "TplTag"}, "LNRIzg2M075D": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "ULHdyLEbB9PF", "parent": {"__ref": "uM36VddeFiP2"}, "locked": null, "vsettings": [{"__ref": "2njiwlNvM6cb"}], "__type": "TplTag"}, "iu40j2SmFWD5": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "JILPlfZD5Rlm"}}, "rs": {"__ref": "-UroDnLpNbjs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "OGorbdbDmTtT": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "dAOVGamNFACR"}, "dataCond": null, "dataRep": null, "text": {"__ref": "3IyYp4kP1uJo"}, "columnsConfig": null, "__type": "VariantSetting"}, "RI1RhB00FQ9J": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "q34GZQGY_K6Z"}}, "rs": {"__ref": "WTjDsyzoPmQQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zMpOqNShb9sN": {"tpl": {"__ref": "pMiH7SOm3-Dz"}, "__type": "TplRef"}, "QGEiLR98c2Tp": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "dqlM9xK-tvQt"}}, "rs": {"__ref": "jJF5129ltR6N"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "aph1QyvVaYNt": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "CihcT_Qicfa5"}, "dataCond": null, "dataRep": null, "text": {"__ref": "WORqDwO7FCDI"}, "columnsConfig": null, "__type": "VariantSetting"}, "2njiwlNvM6cb": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "mKgCKrXev_QR"}}, "rs": {"__ref": "xmUCpZFkryCy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "AHx2UtLsiK5j": {"tpl": {"__ref": "pMiH7SOm3-Dz"}, "__type": "TplRef"}, "JILPlfZD5Rlm": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "-UroDnLpNbjs": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "dAOVGamNFACR": {"values": {}, "mixins": [], "__type": "RuleSet"}, "q34GZQGY_K6Z": {"asset": {"__ref": "qkP78v4NuNLB"}, "__type": "ImageAssetRef"}, "WTjDsyzoPmQQ": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "dqlM9xK-tvQt": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "jJF5129ltR6N": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "CihcT_Qicfa5": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mKgCKrXev_QR": {"asset": {"__ref": "qkP78v4NuNLB"}, "__type": "ImageAssetRef"}, "xmUCpZFkryCy": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "3IyYp4kP1uJo": {"expr": {"__ref": "HAq9AqROllL9"}, "html": false, "__type": "ExprText"}, "HAq9AqROllL9": {"code": "(\"Pause\")", "fallback": {"__ref": "_LIQ74S1FKKj"}, "__type": "CustomCode"}, "_LIQ74S1FKKj": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "v2nujDZ8qbk2": {"name": "tplRef", "expr": {"__ref": "zMpOqNShb9sN"}, "__type": "NameArg"}, "MfD31JQhRjRy": {"name": "action", "expr": {"__ref": "SnavEG7AcGK3"}, "__type": "NameArg"}, "SnavEG7AcGK3": {"code": "\"slickPause\"", "fallback": null, "__type": "CustomCode"}, "WORqDwO7FCDI": {"expr": {"__ref": "dfreYSQez2zD"}, "html": false, "__type": "ExprText"}, "dfreYSQez2zD": {"code": "(\"Play\")", "fallback": {"__ref": "iAZYHLP5-_d4"}, "__type": "CustomCode"}, "iAZYHLP5-_d4": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "Euz0FYNdvXP1": {"name": "tplRef", "expr": {"__ref": "AHx2UtLsiK5j"}, "__type": "NameArg"}, "XMj3JRGi_GR1": {"name": "action", "expr": {"__ref": "U2Yaw_gD6wNh"}, "__type": "NameArg"}, "U2Yaw_gD6wNh": {"code": "\"slickPlay\"", "fallback": null, "__type": "CustomCode"}, "ec2MJQKQ-xU2": {"tag": "div", "name": null, "children": [{"__ref": "7YNYeQUBBuS8"}, {"__ref": "FQ_W0Bup9zSu"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uIIKAkrAY2bN", "parent": {"__ref": "ErwfHvfoHdvb"}, "locked": null, "vsettings": [{"__ref": "YRYJpMXLuoAM"}], "__type": "TplTag"}, "FQ_W0Bup9zSu": {"name": null, "component": {"__ref": "P8hUdZkGBYov"}, "uuid": "6cR2CGhMwNmx", "parent": {"__ref": "ec2MJQKQ-xU2"}, "locked": null, "vsettings": [{"__ref": "a_IdsCsjYJh2"}], "__type": "TplComponent"}, "YRYJpMXLuoAM": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "pT18fc8IIziD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "a_IdsCsjYJh2": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "_68gpicxFqYL"}, {"__ref": "8YvmkYf_VfLX"}, {"__ref": "zBPCjaVmEf8Q"}], "attrs": {"onClick": {"__ref": "Juas2WAQAr6C"}}, "rs": {"__ref": "7_9ir2glA_yz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "pT18fc8IIziD": {"values": {"display": "flex", "position": "relative", "flex-direction": "row", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "_68gpicxFqYL": {"param": {"__ref": "58W11N2MXQvz"}, "expr": {"__ref": "dz0QEONjNo5Y"}, "__type": "Arg"}, "8YvmkYf_VfLX": {"param": {"__ref": "jiGTb077efD0"}, "expr": {"__ref": "ocJ-_g9iXEGP"}, "__type": "Arg"}, "zBPCjaVmEf8Q": {"param": {"__ref": "ALGBMmJdKGZS"}, "expr": {"__ref": "qba1dD2AaE5U"}, "__type": "Arg"}, "Juas2WAQAr6C": {"interactions": [{"__ref": "nJof7I6HdyYm"}], "__type": "EventHandler"}, "7_9ir2glA_yz": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "dz0QEONjNo5Y": {"tpl": [{"__ref": "pn0U3jZ2gYd6"}], "__type": "VirtualRenderExpr"}, "ocJ-_g9iXEGP": {"tpl": [{"__ref": "dV9vvN7_OCTM"}], "__type": "RenderExpr"}, "qba1dD2AaE5U": {"tpl": [{"__ref": "dpbluANPTmyl"}], "__type": "VirtualRenderExpr"}, "nJof7I6HdyYm": {"interactionName": "Run action on Slider Carousel", "actionName": "invokeRefAction", "args": [{"__ref": "_I_adnx7B2r1"}, {"__ref": "AdykSlMpDieF"}, {"__ref": "fWX-R_hARRnZ"}], "condExpr": null, "conditionalMode": "always", "uuid": "XKVKOlF_C44c", "parent": {"__ref": "Juas2WAQAr6C"}, "__type": "Interaction"}, "pn0U3jZ2gYd6": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "g4UlBJy6O5m2", "parent": {"__ref": "FQ_W0Bup9zSu"}, "locked": null, "vsettings": [{"__ref": "n2JpPGR2J6Me"}], "__type": "TplTag"}, "dV9vvN7_OCTM": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "VKLIvodJtR7g", "parent": {"__ref": "FQ_W0Bup9zSu"}, "locked": null, "vsettings": [{"__ref": "EEAJKTqzOKt7"}], "__type": "TplTag"}, "dpbluANPTmyl": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "DJWpKisaW6RM", "parent": {"__ref": "FQ_W0Bup9zSu"}, "locked": null, "vsettings": [{"__ref": "pE3tvYfvJGuv"}], "__type": "TplTag"}, "n2JpPGR2J6Me": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "1bMLA5ttocsQ"}}, "rs": {"__ref": "f6DP-zWO2N3V"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "EEAJKTqzOKt7": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {}, "rs": {"__ref": "z5tjL3RW6RE_"}, "dataCond": null, "dataRep": null, "text": {"__ref": "MCsvxiSbN7mQ"}, "columnsConfig": null, "__type": "VariantSetting"}, "pE3tvYfvJGuv": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "9k2O5nyXZYEc"}}, "rs": {"__ref": "VbmhnxdkbJMY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "csPxvNYl37E7": {"tpl": {"__ref": "pMiH7SOm3-Dz"}, "__type": "TplRef"}, "1bMLA5ttocsQ": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "f6DP-zWO2N3V": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "z5tjL3RW6RE_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "9k2O5nyXZYEc": {"asset": {"__ref": "qkP78v4NuNLB"}, "__type": "ImageAssetRef"}, "VbmhnxdkbJMY": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "7YNYeQUBBuS8": {"name": "TextInput", "component": {"__ref": "CUEv0dZhpMRo"}, "uuid": "QSP4VKgeK4si", "parent": {"__ref": "ec2MJQKQ-xU2"}, "locked": null, "vsettings": [{"__ref": "SIPuTd21Z4vc"}], "__type": "TplComponent"}, "FhvN3uw4MLXp": {"param": {"__ref": "yU-sAzP93ZAH"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "6TtLlqMsJnp-"}, "tplNode": {"__ref": "7YNYeQUBBuS8"}, "implicitState": {"__ref": "rACuoZOnrwhm"}, "__type": "State"}, "yU-sAzP93ZAH": {"type": {"__ref": "c8dq_nbyygpH"}, "state": {"__ref": "FhvN3uw4MLXp"}, "variable": {"__ref": "1DrGL9AKYdd6"}, "uuid": "kx_mJDlGxcRr", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "6TtLlqMsJnp-": {"type": {"__ref": "N_CBDGqRDYc9"}, "state": {"__ref": "FhvN3uw4MLXp"}, "variable": {"__ref": "2MfDAzq1VzXF"}, "uuid": "DUjjzc6XHl79", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "SIPuTd21Z4vc": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [{"__ref": "XyeLaBG99tpB"}, {"__ref": "_9xCxVfAZkI_"}, {"__ref": "TbGuJ9cInZT7"}], "attrs": {}, "rs": {"__ref": "sQEM_-jKAoWc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "c8dq_nbyygpH": {"name": "text", "__type": "Text"}, "1DrGL9AKYdd6": {"name": "TextInput value", "uuid": "5loKo9muFs9G", "__type": "Var"}, "N_CBDGqRDYc9": {"name": "func", "params": [{"__ref": "lDbyWEZO-SeT"}], "__type": "FunctionType"}, "2MfDAzq1VzXF": {"name": "On TextInput value change", "uuid": "AUl_FqDaZu9W", "__type": "Var"}, "XyeLaBG99tpB": {"param": {"__ref": "5RnDF2mqKK-e"}, "expr": {"__ref": "R2imT-aIAOsW"}, "__type": "Arg"}, "_9xCxVfAZkI_": {"param": {"__ref": "LjsPTdMeS_YE"}, "expr": {"__ref": "6dwvgiiJ5vTE"}, "__type": "Arg"}, "sQEM_-jKAoWc": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "lDbyWEZO-SeT": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "s23bh4Rp1IZQ"}, "__type": "ArgType"}, "R2imT-aIAOsW": {"tpl": [{"__ref": "cl6xtiJ9D3oa"}], "__type": "VirtualRenderExpr"}, "6dwvgiiJ5vTE": {"tpl": [{"__ref": "KKl812EFxo8e"}], "__type": "VirtualRenderExpr"}, "s23bh4Rp1IZQ": {"name": "text", "__type": "Text"}, "cl6xtiJ9D3oa": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "2H37W1SVGvb3", "parent": {"__ref": "7YNYeQUBBuS8"}, "locked": null, "vsettings": [{"__ref": "ZfWeSWDtC8rG"}], "__type": "TplTag"}, "KKl812EFxo8e": {"tag": "svg", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "LL0vffCAr6iT", "parent": {"__ref": "7YNYeQUBBuS8"}, "locked": null, "vsettings": [{"__ref": "7wJnaulNxI3v"}], "__type": "TplTag"}, "ZfWeSWDtC8rG": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "ydo-Q_pmVKNg"}}, "rs": {"__ref": "8mNkNr9vFPCm"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7wJnaulNxI3v": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"outerHTML": {"__ref": "9oN8ncbkhTym"}}, "rs": {"__ref": "9DonTOUfZ2jz"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ydo-Q_pmVKNg": {"asset": {"__ref": "7ucfpJr_iSOE"}, "__type": "ImageAssetRef"}, "8mNkNr9vFPCm": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "9oN8ncbkhTym": {"asset": {"__ref": "yw5bb3sWMkVn"}, "__type": "ImageAssetRef"}, "9DonTOUfZ2jz": {"values": {"position": "relative", "object-fit": "cover", "width": "wrap", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "TbGuJ9cInZT7": {"param": {"__ref": "rfszjDs32IhJ"}, "expr": {"__ref": "XQ0yEYnaIx1n"}, "__type": "Arg"}, "XQ0yEYnaIx1n": {"text": ["jump-to-slide"], "__type": "TemplatedString"}, "SYxM2jHnY0Wr": {"code": "\"slickGoTo\"", "fallback": null, "__type": "CustomCode"}, "_I_adnx7B2r1": {"name": "tplRef", "expr": {"__ref": "csPxvNYl37E7"}, "__type": "NameArg"}, "AdykSlMpDieF": {"name": "action", "expr": {"__ref": "SYxM2jHnY0Wr"}, "__type": "NameArg"}, "fWX-R_hARRnZ": {"name": "args", "expr": {"__ref": "2OnFVcddnPF3"}, "__type": "NameArg"}, "2OnFVcddnPF3": {"exprs": [{"__ref": "5Dg3slmAgfYH"}], "__type": "CollectionExpr"}, "5Dg3slmAgfYH": {"argType": {"__ref": "RS_AOJVdVeDa"}, "uuid": "iLKWl3vEFZWV", "expr": {"__ref": "RX3xCeaW7zpA"}, "__type": "StrongFunctionArg"}, "RS_AOJVdVeDa": {"name": "arg", "argName": "index", "displayName": "Slide index", "type": {"__ref": "m1js4KCswLCp"}, "__type": "ArgType"}, "RX3xCeaW7zpA": {"path": ["$state", "textInput", "value"], "fallback": {"__ref": "brdbTHnYFC9O"}, "__type": "ObjectPath"}, "m1js4KCswLCp": {"name": "num", "__type": "<PERSON><PERSON>"}, "brdbTHnYFC9O": {"code": "undefined", "fallback": null, "__type": "CustomCode"}, "MCsvxiSbN7mQ": {"expr": {"__ref": "fyspbdUNXwni"}, "html": false, "__type": "ExprText"}, "fyspbdUNXwni": {"code": "(\"Go\")", "fallback": {"__ref": "7xJeTZUIwW74"}, "__type": "CustomCode"}, "7xJeTZUIwW74": {"code": "\"Button\"", "fallback": null, "__type": "CustomCode"}, "XcSsk7mPcCQ5": {"text": ["actions-container"], "__type": "TemplatedString"}, "sIBHkEF9NQFs": {"param": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "HSsTXJSbs7q2"}}, "expr": {"__ref": "G2Dprjhynxiq"}, "__type": "Arg"}, "G2Dprjhynxiq": {"code": "1", "fallback": null, "__type": "CustomCode"}, "wRN5OxpIWOEI": {"code": "true", "fallback": null, "__type": "CustomCode"}, "KZC_mHpkY-mp": {"param": {"__ref": "PFQ10r8suLTv"}, "accessType": "private", "variableType": "number", "onChangeParam": {"__ref": "IemXgdpbSkOM"}, "tplNode": {"__ref": "pMiH7SOm3-Dz"}, "implicitState": {"__xref": {"uuid": "1397401c-b15c-47b2-8659-9b44c397e887", "iid": "biSqrpHXFLwK"}}, "__type": "State"}, "PFQ10r8suLTv": {"type": {"__ref": "C6gKXMgyQDUR"}, "state": {"__ref": "KZC_mHpkY-mp"}, "variable": {"__ref": "4iOYDOoygTlV"}, "uuid": "6GZrw_edUcSb", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "number", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "IemXgdpbSkOM": {"type": {"__ref": "mm3mN3nmZInZ"}, "state": {"__ref": "KZC_mHpkY-mp"}, "variable": {"__ref": "LPjaeBsi76kL"}, "uuid": "N-1pE_YccrO-", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "C6gKXMgyQDUR": {"name": "num", "__type": "<PERSON><PERSON>"}, "4iOYDOoygTlV": {"name": "Slider Carousel initialSlide", "uuid": "3i7MHOIovSSp", "__type": "Var"}, "mm3mN3nmZInZ": {"name": "func", "params": [{"__ref": "fA_ET0a0012s"}], "__type": "FunctionType"}, "LPjaeBsi76kL": {"name": "On Slider Carousel initialSlide change", "uuid": "aZqZMcFyctLw", "__type": "Var"}, "fA_ET0a0012s": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "szE9gvLNYXJr"}, "__type": "ArgType"}, "szE9gvLNYXJr": {"name": "num", "__type": "<PERSON><PERSON>"}, "pc80HsOmoeS2": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "5RAodgr8XWF-", "parent": {"__ref": "Y7fjLAMILdxu"}, "locked": null, "vsettings": [{"__ref": "re8lLnitTHcy"}], "__type": "TplTag"}, "re8lLnitTHcy": {"variants": [{"__ref": "UBLrIWhgqr4b"}], "args": [], "attrs": {"id": {"__ref": "hn0XJ114mf5C"}}, "rs": {"__ref": "DlELWxD254L5"}, "dataCond": null, "dataRep": null, "text": {"__ref": "rLvTG5MeAZR5"}, "columnsConfig": null, "__type": "VariantSetting"}, "DlELWxD254L5": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "rLvTG5MeAZR5": {"expr": {"__ref": "KVSBusPh1Qmn"}, "html": false, "__type": "ExprText"}, "KVSBusPh1Qmn": {"path": ["$state", "sliderCarousel", "currentSlide"], "fallback": {"__ref": "xqkZrTKJuCcI"}, "__type": "ObjectPath"}, "xqkZrTKJuCcI": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "hn0XJ114mf5C": {"text": ["slider-state-text"], "__type": "TemplatedString"}}, "deps": ["1397401c-b15c-47b2-8659-9b44c397e887"], "version": "251-add-data-tokens"}]]