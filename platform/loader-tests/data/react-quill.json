[["b911462e-59ce-455d-b8d3-0d84f50a35dd", {"root": "2MJOQVsfX1N8", "map": {"eV_AQIkDysaG": {"values": {}, "mixins": [], "__type": "RuleSet"}, "SHG-ZaOleOje": {"name": "Default Typography", "rs": {"__ref": "eV_AQIkDysaG"}, "preview": null, "uuid": "QBEeukYrVfYo", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "6wI6XnONKWII": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Dyvj5ViGZIA-": {"rs": {"__ref": "6wI6XnONKWII"}, "__type": "ThemeLayoutSettings"}, "w29Kz5zy9Qx7": {"defaultStyle": {"__ref": "SHG-ZaOleOje"}, "styles": [], "layout": {"__ref": "Dyvj5ViGZIA-"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "pn48FXinGJiS": {"uuid": "HYzjsp1TstIa", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "0awK3tSMDG6p": {"components": [{"__ref": "SXPBseNfiyZw"}], "arenas": [], "pageArenas": [], "componentArenas": [], "globalVariantGroups": [], "userManagedFonts": [], "globalVariant": {"__ref": "pn48FXinGJiS"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "w29Kz5zy9Qx7"}], "activeTheme": null, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": null, "flags": {}, "hostLessPackageInfo": {"__ref": "d9JeJ8LNZd9L"}, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "SXPBseNfiyZw": {"uuid": "06EUvxKSgH0w", "name": "hostless-react-quill", "params": [{"__ref": "AAPjx70LrziJ"}, {"__ref": "CsOwIl66ubIg"}, {"__ref": "uONFW7p8nvr-"}, {"__ref": "AvedZsqm8LG0"}, {"__ref": "QCQIyowdac-t"}, {"__ref": "HmjQ9jDliqsH"}, {"__ref": "U6VtfBslZQn6"}, {"__ref": "rIWEioRq2Xot"}, {"__ref": "E8Q9jUiCkg77"}, {"__ref": "e_MtuomF_L9s"}, {"__ref": "Wy5sEvknPtw8"}, {"__ref": "-iJFqWQHjMcP"}, {"__ref": "6D-Nq7hNNxpi"}], "states": [{"__ref": "i7nio1EgDgQD"}], "tplTree": {"__ref": "h0TxAK66DRt7"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "vfuUlQGZ9ATv"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "MSbu92ghjIc0"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "AAPjx70LrziJ": {"type": {"__ref": "9nGE7_S0fwMs"}, "state": {"__ref": "i7nio1EgDgQD"}, "variable": {"__ref": "eMlxI621enHl"}, "uuid": "IvpjuP2JPvk6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": "defaultValue", "description": null, "displayName": "HTML Value", "about": "Contents of the editor", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "CsOwIl66ubIg": {"type": {"__ref": "THh4ynMxMC4O"}, "variable": {"__ref": "6Zs43lSoQ2LL"}, "uuid": "1FJ7eao_kLZU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "eO3yU-9MT0U4"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Customize the toolbar to show/hide controls", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "uONFW7p8nvr-": {"type": {"__ref": "3JbNd7lV9nab"}, "variable": {"__ref": "FKpw8plxY9JI"}, "uuid": "Q0fzmDm0XnLT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Custom toolbar configuration for Quill editor. Overrides the existing toolbar.", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "AvedZsqm8LG0": {"type": {"__ref": "s8jDv3E0xyZ8"}, "variable": {"__ref": "9ozRCCC9Wq1H"}, "uuid": "nS1F8MhOiThk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "QCQIyowdac-t": {"type": {"__ref": "cGYCrKn04GPu"}, "variable": {"__ref": "lZZ1KYJBCoki"}, "uuid": "U9XDWq264Omu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "roXI0eRAQUE3"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Prevents Quill from collapsing continuous whitespaces on paste", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "HmjQ9jDliqsH": {"type": {"__ref": "cW52dJImuW98"}, "variable": {"__ref": "ivz-EUpe4p5e"}, "uuid": "UIegjurj--cT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "K5jWoNOre7l9"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": "Prevents user from changing the contents of the editor", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "U6VtfBslZQn6": {"type": {"__ref": "lbz2Z2esn27s"}, "variable": {"__ref": "AYkIp9kjETh-"}, "uuid": "POAdkVqR6Xuu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "rIWEioRq2Xot": {"type": {"__ref": "JUD5xVMx0WhJ"}, "variable": {"__ref": "DxLoJCzUT1yG"}, "uuid": "r5Es5AEa1JxN", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "E8Q9jUiCkg77": {"type": {"__ref": "iCI3H5vx4sMM"}, "variable": {"__ref": "FDy2N98SyAN1"}, "uuid": "KYRvUrbXbPBT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "e_MtuomF_L9s": {"type": {"__ref": "NM1hyz-oPfPV"}, "variable": {"__ref": "7wlwVdARfJlD"}, "uuid": "NeBz446QF7ej", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Wy5sEvknPtw8": {"type": {"__ref": "4urL-nYBpduU"}, "variable": {"__ref": "jJ6qzmEc5G7A"}, "uuid": "4CQ39IFtbRZU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-iJFqWQHjMcP": {"type": {"__ref": "Y9T_l89wOXdF"}, "variable": {"__ref": "RrVDAXgrYps5"}, "uuid": "q1j2MfYEwP39", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "6D-Nq7hNNxpi": {"type": {"__ref": "SYSkS-EntCtE"}, "variable": {"__ref": "0AHOVev15FEx"}, "uuid": "grAZFdLfIF1y", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "i7nio1EgDgQD": {"variableType": "text", "name": "value", "param": {"__ref": "AAPjx70LrziJ"}, "accessType": "writable", "onChangeParam": {"__ref": "U6VtfBslZQn6"}, "tplNode": null, "implicitState": null, "__type": "NamedState"}, "h0TxAK66DRt7": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "iMHUJURcRKxy", "parent": null, "locked": null, "vsettings": [{"__ref": "Vt0HkYcT5H6E"}], "__type": "TplTag"}, "vfuUlQGZ9ATv": {"uuid": "nvr-u6AU7lM8", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "MSbu92ghjIc0": {"importPath": "@plasmicpkgs/react-quill", "defaultExport": false, "displayName": "Rich Text Editor", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": "containerClassName", "refProp": null, "defaultStyles": {"__ref": "Ko7mhg1mzEXU"}, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": null, "helpers": {"__ref": "vaRg84Qpwlre"}, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "9nGE7_S0fwMs": {"name": "text", "__type": "Text"}, "eMlxI621enHl": {"name": "value", "uuid": "c-iX8wkIG9vA", "__type": "Var"}, "THh4ynMxMC4O": {"name": "any", "__type": "AnyType"}, "6Zs43lSoQ2LL": {"name": "toolbar", "uuid": "binfhxRC-DPN", "__type": "Var"}, "eO3yU-9MT0U4": {"code": "{\"textStyle\":[\"bold\",\"italic\",\"underline\",\"strikethrough\"],\"colors\":[\"text color\",\"text background\"],\"script\":true,\"fontFamily\":true,\"heading\":[\"Heading 1\",\"Heading 2\",\"Heading 3\",\"Heading 4\",\"Heading 5\",\"Heading 6\",\"Body\"],\"fontSizes\":[\"small\",\"medium\",\"large\",\"huge\"],\"formatting\":[\"alignment\",\"list\",\"indentation\",\"text direction\",\"clear formatting\"],\"inputTypes\":[\"link\",\"blockquote\",\"image\",\"video\",\"code-block\",\"formula\"]}", "fallback": null, "__type": "CustomCode"}, "3JbNd7lV9nab": {"name": "any", "__type": "AnyType"}, "FKpw8plxY9JI": {"name": "customToolbar", "uuid": "DEbvRU3OXIX1", "__type": "Var"}, "s8jDv3E0xyZ8": {"name": "text", "__type": "Text"}, "9ozRCCC9Wq1H": {"name": "placeholder", "uuid": "0vqXXhAioZCe", "__type": "Var"}, "cGYCrKn04GPu": {"name": "bool", "__type": "BoolType"}, "lZZ1KYJBCoki": {"name": "preserveWhitespace", "uuid": "vmtVn-PBwNAN", "__type": "Var"}, "roXI0eRAQUE3": {"code": "true", "fallback": null, "__type": "CustomCode"}, "cW52dJImuW98": {"name": "bool", "__type": "BoolType"}, "ivz-EUpe4p5e": {"name": "readOnly", "uuid": "oRPlIqFssIGv", "__type": "Var"}, "K5jWoNOre7l9": {"code": "false", "fallback": null, "__type": "CustomCode"}, "lbz2Z2esn27s": {"name": "func", "params": [{"__ref": "2Lx5thB8Vr2U"}, {"__ref": "I8mzQo3hI2eX"}, {"__ref": "cOHGP7UaACg8"}, {"__ref": "i4TpRwdivegM"}], "__type": "FunctionType"}, "AYkIp9kjETh-": {"name": "onChange", "uuid": "B2jTCZKK5Fce", "__type": "Var"}, "JUD5xVMx0WhJ": {"name": "func", "params": [{"__ref": "fOsZTya5ObK2"}, {"__ref": "STAXkehQwNvE"}, {"__ref": "prnSAB1VVodf"}], "__type": "FunctionType"}, "DxLoJCzUT1yG": {"name": "onChangeSelection", "uuid": "32cleo7MI3EO", "__type": "Var"}, "iCI3H5vx4sMM": {"name": "func", "params": [{"__ref": "ysVWwd20iKMP"}, {"__ref": "AiqM0dbXrc_3"}, {"__ref": "tA6Ixneas6Gg"}], "__type": "FunctionType"}, "FDy2N98SyAN1": {"name": "onFocus", "uuid": "MiHjPQHxiIAc", "__type": "Var"}, "NM1hyz-oPfPV": {"name": "func", "params": [{"__ref": "DavdYQkaVyC2"}, {"__ref": "uSD2_JkmUKSq"}, {"__ref": "WEtWYPk3UBtX"}], "__type": "FunctionType"}, "7wlwVdARfJlD": {"name": "onBlur", "uuid": "-h0ysyDAuxHM", "__type": "Var"}, "4urL-nYBpduU": {"name": "func", "params": [{"__ref": "lMKs5ONUmPvz"}], "__type": "FunctionType"}, "jJ6qzmEc5G7A": {"name": "onKeyPress", "uuid": "YQyL6bkel4OX", "__type": "Var"}, "Y9T_l89wOXdF": {"name": "func", "params": [{"__ref": "JPcyCtizjzzf"}], "__type": "FunctionType"}, "RrVDAXgrYps5": {"name": "onKeyDown", "uuid": "7DkJ6_rBEpOO", "__type": "Var"}, "SYSkS-EntCtE": {"name": "func", "params": [{"__ref": "oxs_Wl1SIKMH"}], "__type": "FunctionType"}, "0AHOVev15FEx": {"name": "onKeyUp", "uuid": "XcpeWHddJs7q", "__type": "Var"}, "Vt0HkYcT5H6E": {"variants": [{"__ref": "vfuUlQGZ9ATv"}], "args": [], "attrs": {}, "rs": {"__ref": "lnJA3JpLSlB9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Ko7mhg1mzEXU": {"values": {"width": "stretch"}, "mixins": [], "__type": "RuleSet"}, "vaRg84Qpwlre": {"importPath": "@plasmicpkgs/react-quill", "importName": "quillHelpers", "defaultExport": false, "__type": "CodeComponentHelper"}, "2Lx5thB8Vr2U": {"name": "arg", "argName": "content", "displayName": null, "type": {"__ref": "BwTDgBkqkqKA"}, "__type": "ArgType"}, "I8mzQo3hI2eX": {"name": "arg", "argName": "delta", "displayName": null, "type": {"__ref": "RgUZjElNqp7S"}, "__type": "ArgType"}, "cOHGP7UaACg8": {"name": "arg", "argName": "source", "displayName": null, "type": {"__ref": "k57EchKXUBht"}, "__type": "ArgType"}, "i4TpRwdivegM": {"name": "arg", "argName": "editor", "displayName": null, "type": {"__ref": "Z4PnjzK3v9mE"}, "__type": "ArgType"}, "fOsZTya5ObK2": {"name": "arg", "argName": "range", "displayName": null, "type": {"__ref": "O0qKT31SiUyS"}, "__type": "ArgType"}, "STAXkehQwNvE": {"name": "arg", "argName": "source", "displayName": null, "type": {"__ref": "1m0U52QGE4rv"}, "__type": "ArgType"}, "prnSAB1VVodf": {"name": "arg", "argName": "editor", "displayName": null, "type": {"__ref": "Mu7Gy22H5zd6"}, "__type": "ArgType"}, "ysVWwd20iKMP": {"name": "arg", "argName": "range", "displayName": null, "type": {"__ref": "yfsn-9Fy5HG6"}, "__type": "ArgType"}, "AiqM0dbXrc_3": {"name": "arg", "argName": "source", "displayName": null, "type": {"__ref": "9eHrKmGXgUTB"}, "__type": "ArgType"}, "tA6Ixneas6Gg": {"name": "arg", "argName": "editor", "displayName": null, "type": {"__ref": "isaLYkRXV_UQ"}, "__type": "ArgType"}, "DavdYQkaVyC2": {"name": "arg", "argName": "previousRange", "displayName": null, "type": {"__ref": "0liVbVn9B9x9"}, "__type": "ArgType"}, "uSD2_JkmUKSq": {"name": "arg", "argName": "source", "displayName": null, "type": {"__ref": "F3XJ2mSR9kPj"}, "__type": "ArgType"}, "WEtWYPk3UBtX": {"name": "arg", "argName": "editor", "displayName": null, "type": {"__ref": "IXuGLzgzPzLQ"}, "__type": "ArgType"}, "lMKs5ONUmPvz": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "YH3GMDF8JQal"}, "__type": "ArgType"}, "JPcyCtizjzzf": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "X5E0wJgISN70"}, "__type": "ArgType"}, "oxs_Wl1SIKMH": {"name": "arg", "argName": "event", "displayName": null, "type": {"__ref": "zABprXC8EDjd"}, "__type": "ArgType"}, "lnJA3JpLSlB9": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "BwTDgBkqkqKA": {"name": "text", "__type": "Text"}, "RgUZjElNqp7S": {"name": "any", "__type": "AnyType"}, "k57EchKXUBht": {"name": "text", "__type": "Text"}, "Z4PnjzK3v9mE": {"name": "any", "__type": "AnyType"}, "O0qKT31SiUyS": {"name": "any", "__type": "AnyType"}, "1m0U52QGE4rv": {"name": "text", "__type": "Text"}, "Mu7Gy22H5zd6": {"name": "any", "__type": "AnyType"}, "yfsn-9Fy5HG6": {"name": "any", "__type": "AnyType"}, "9eHrKmGXgUTB": {"name": "text", "__type": "Text"}, "isaLYkRXV_UQ": {"name": "any", "__type": "AnyType"}, "0liVbVn9B9x9": {"name": "any", "__type": "AnyType"}, "F3XJ2mSR9kPj": {"name": "text", "__type": "Text"}, "IXuGLzgzPzLQ": {"name": "any", "__type": "AnyType"}, "YH3GMDF8JQal": {"name": "any", "__type": "AnyType"}, "X5E0wJgISN70": {"name": "any", "__type": "AnyType"}, "zABprXC8EDjd": {"name": "any", "__type": "AnyType"}, "d9JeJ8LNZd9L": {"name": "react-quill", "npmPkg": ["react-quill"], "cssImport": ["react-quill/dist/quill.snow.css", "react-quill/dist/quill.bubble.css"], "deps": [], "registerCalls": [], "minimumReactVersion": null, "__type": "HostLessPackageInfo"}, "2MJOQVsfX1N8": {"uuid": "m2Of0x9SmxPU", "pkgId": "d112ee37-06b3-440a-9c58-3cf663ec69d8", "projectId": "jTfgPrZDdHd2gnNJucR2cH", "version": "0.0.3", "name": "react-quill hostless", "site": {"__ref": "0awK3tSMDG6p"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}], ["cRtVyRWvicLbjosCMUq7U", {"root": "ignT_N3nrYJw", "map": {"mMeTgv7uV710": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "rxOju-DdHWmT": {"name": "Default Typography", "rs": {"__ref": "mMeTgv7uV710"}, "preview": null, "uuid": "Hnrt74CJtn9H", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "sOvLlSR8l353": {"values": {}, "mixins": [], "__type": "RuleSet"}, "Eb4BF8L1gv7u": {"rs": {"__ref": "sOvLlSR8l353"}, "__type": "ThemeLayoutSettings"}, "THQ2adIBF_R2": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "xd825Irc-K4G": {"name": "Default \"h1\"", "rs": {"__ref": "THQ2adIBF_R2"}, "preview": null, "uuid": "s8eYBtA7MZfb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5oqnStzLdV00": {"selector": "h1", "style": {"__ref": "xd825Irc-K4G"}, "__type": "ThemeStyle"}, "htouBJsv2e47": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "oAzyHmlqzkJz": {"name": "Default \"h2\"", "rs": {"__ref": "htouBJsv2e47"}, "preview": null, "uuid": "tBjIQKBWmoMy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Vam8bzf046xA": {"selector": "h2", "style": {"__ref": "oAzyHmlqzkJz"}, "__type": "ThemeStyle"}, "-2i3NzsQJA_g": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "uLkjG1T8OF6A": {"name": "Default \"h3\"", "rs": {"__ref": "-2i3NzsQJA_g"}, "preview": null, "uuid": "NhzKHhQSFG7v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PTLzhBnhGwYm": {"selector": "h3", "style": {"__ref": "uLkjG1T8OF6A"}, "__type": "ThemeStyle"}, "L1RWmdAxjk6b": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "3aRbqrmBL8Q_": {"name": "Default \"h4\"", "rs": {"__ref": "L1RWmdAxjk6b"}, "preview": null, "uuid": "qQ9ReJw73ZaF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "RGRzScAdnSHz": {"selector": "h4", "style": {"__ref": "3aRbqrmBL8Q_"}, "__type": "ThemeStyle"}, "2ksHdEgJqzxt": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "iv3uCjf4rryO": {"name": "Default \"h5\"", "rs": {"__ref": "2ksHdEgJqzxt"}, "preview": null, "uuid": "hZO3xz0fbTC0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "CFBBqhUyDsqg": {"selector": "h5", "style": {"__ref": "iv3uCjf4rryO"}, "__type": "ThemeStyle"}, "eTKXVzzT-1T2": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "U85hAQE97h9b": {"name": "Default \"h6\"", "rs": {"__ref": "eTKXVzzT-1T2"}, "preview": null, "uuid": "NibHQsp3UgMW", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "C8rcljcGcr0F": {"selector": "h6", "style": {"__ref": "U85hAQE97h9b"}, "__type": "ThemeStyle"}, "voTxwrJ-ikeo": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "ilNB3B_gN4kc": {"name": "Default \"a\"", "rs": {"__ref": "voTxwrJ-ikeo"}, "preview": null, "uuid": "tHfqL7_bRnbM", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "en9kLwvQ32B9": {"selector": "a", "style": {"__ref": "ilNB3B_gN4kc"}, "__type": "ThemeStyle"}, "KfuTU_8H2G_W": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "r92Eoxn4JdDV": {"name": "Default \"a:hover\"", "rs": {"__ref": "KfuTU_8H2G_W"}, "preview": null, "uuid": "jLH4RicSbo7a", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "qN8nb8ZPzn3O": {"selector": "a:hover", "style": {"__ref": "r92Eoxn4JdDV"}, "__type": "ThemeStyle"}, "t1qAqcH7D7k-": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "af15pugtqbyC": {"name": "Default \"blockquote\"", "rs": {"__ref": "t1qAqcH7D7k-"}, "preview": null, "uuid": "zVLoLS5VaTbr", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EgK4dqmIlNMA": {"selector": "blockquote", "style": {"__ref": "af15pugtqbyC"}, "__type": "ThemeStyle"}, "1l_01s0ZyFj0": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "-_DfKOkPtTFA": {"name": "Default \"code\"", "rs": {"__ref": "1l_01s0ZyFj0"}, "preview": null, "uuid": "Jf8D9NW5p_RI", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9Yvu3pg8rxyj": {"selector": "code", "style": {"__ref": "-_DfKOkPtTFA"}, "__type": "ThemeStyle"}, "cCM_qy9woMHh": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "FH2wQnLLlAd0": {"name": "Default \"pre\"", "rs": {"__ref": "cCM_qy9woMHh"}, "preview": null, "uuid": "ntAbpPu7J-Vv", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "6e5YiQPnqFa0": {"selector": "pre", "style": {"__ref": "FH2wQnLLlAd0"}, "__type": "ThemeStyle"}, "-kWewYKPXTuq": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "eykebKA4ecVf": {"name": "Default \"ol\"", "rs": {"__ref": "-kWewYKPXTuq"}, "preview": null, "uuid": "Th-Qha-KVMvV", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "KYfwyEgVenx1": {"selector": "ol", "style": {"__ref": "eykebKA4ecVf"}, "__type": "ThemeStyle"}, "zMlv8l4MhZrF": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "FtcQfuA6cp1M": {"name": "Default \"ul\"", "rs": {"__ref": "zMlv8l4MhZrF"}, "preview": null, "uuid": "AESQX_biMLh0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7nX4QBcCqLdm": {"selector": "ul", "style": {"__ref": "FtcQfuA6cp1M"}, "__type": "ThemeStyle"}, "kG8y5Aw7b_rc": {"defaultStyle": {"__ref": "rxOju-DdHWmT"}, "styles": [{"__ref": "5oqnStzLdV00"}, {"__ref": "Vam8bzf046xA"}, {"__ref": "PTLzhBnhGwYm"}, {"__ref": "RGRzScAdnSHz"}, {"__ref": "CFBBqhUyDsqg"}, {"__ref": "C8rcljcGcr0F"}, {"__ref": "en9kLwvQ32B9"}, {"__ref": "qN8nb8ZPzn3O"}, {"__ref": "EgK4dqmIlNMA"}, {"__ref": "9Yvu3pg8rxyj"}, {"__ref": "6e5YiQPnqFa0"}, {"__ref": "KYfwyEgVenx1"}, {"__ref": "7nX4QBcCqLdm"}], "layout": {"__ref": "Eb4BF8L1gv7u"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "-At_00oYrG3d": {"name": "text", "__type": "Text"}, "T_33XK0l7i2G": {"name": "Screen", "uuid": "Y3z28YIG1lny", "__type": "Var"}, "Eg5FClGV_ryq": {"type": {"__ref": "-At_00oYrG3d"}, "variable": {"__ref": "T_33XK0l7i2G"}, "uuid": "cloQXiNExHBQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "NzqWRuyw3kzm": {"type": "global-screen", "param": {"__ref": "Eg5FClGV_ryq"}, "uuid": "nrvs1OlvXZo7", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "bB9TU9admzr1": {"uuid": "GiEPZJzvlozX", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ignT_N3nrYJw": {"components": [{"__ref": "QNsV5126SXlX"}, {"__ref": "y8dVXw9o8iKa"}, {"__ref": "curkqibc0Nq0"}], "arenas": [{"__ref": "p3i-uTxMSC0T"}], "pageArenas": [{"__ref": "qwvrJ2-zm5JB"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "NzqWRuyw3kzm"}], "userManagedFonts": [], "globalVariant": {"__ref": "bB9TU9admzr1"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "kG8y5Aw7b_rc"}], "activeTheme": {"__ref": "kG8y5Aw7b_rc"}, "imageAssets": [], "projectDependencies": [{"__xref": {"uuid": "b911462e-59ce-455d-b8d3-0d84f50a35dd", "iid": "2MJOQVsfX1N8"}}], "activeScreenVariantGroup": {"__ref": "NzqWRuyw3kzm"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "p3i-uTxMSC0T": {"name": "Custom arena 1", "children": [{"__ref": "KwQu9JkmCel-"}], "__type": "Arena"}, "QNsV5126SXlX": {"uuid": "rJnAMhs5OV_1", "name": "hostless-plasmic-head", "params": [{"__ref": "aqtElFLoCdaS"}, {"__ref": "JjqCmTe6AChM"}, {"__ref": "SAwll7XAsUCJ"}, {"__ref": "ye1yZXQhd27B"}], "states": [], "tplTree": {"__ref": "J6uAEQy2D0xP"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "pFYMfUotLkL_"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "3FsLTgU3D-hu"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "y8dVXw9o8iKa": {"uuid": "hyQ1hF-5XRFT", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "foM50D4gZAnk"}, {"__ref": "J4L10OMf5P4f"}, {"__ref": "5jhJ0sVmiLXY"}, {"__ref": "HzJTsvl4nSTV"}, {"__ref": "mJhLI9ojIgFJ"}], "states": [], "tplTree": {"__ref": "AAV48UJHMJ-2"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "cm98a4v_47xs"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "JM_hjJDseTSW"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "aqtElFLoCdaS": {"type": {"__ref": "DQOIIrFQ41yT"}, "variable": {"__ref": "7es968uy0OmS"}, "uuid": "xRa9ENrjelP5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "JjqCmTe6AChM": {"type": {"__ref": "eEPb_fHUN5_H"}, "variable": {"__ref": "2yMrikjtcuIB"}, "uuid": "dbul7esF-U_u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "SAwll7XAsUCJ": {"type": {"__ref": "7MrLOPUVbbbx"}, "variable": {"__ref": "VL2pD_tVNK-y"}, "uuid": "rnifMMKJgARe", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "ye1yZXQhd27B": {"type": {"__ref": "qeqK4yDdTWkG"}, "variable": {"__ref": "EZwarPYhuHsS"}, "uuid": "BJcWDmqwcqvX", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "J6uAEQy2D0xP": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "tnZIjT7PiA2v", "parent": null, "locked": null, "vsettings": [{"__ref": "Vx4vh_DEilUk"}], "__type": "TplTag"}, "pFYMfUotLkL_": {"uuid": "wrJ5YPfitjr9", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "3FsLTgU3D-hu": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "foM50D4gZAnk": {"type": {"__ref": "iEzUBJRG0-7W"}, "variable": {"__ref": "zOhmvNj5QbGE"}, "uuid": "jhDVR1Jhh7sJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "J4L10OMf5P4f": {"type": {"__ref": "jbWRjgQGZmHz"}, "variable": {"__ref": "yau7EG7KV06Q"}, "uuid": "m-fLSBbZQAZg", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "5jhJ0sVmiLXY": {"type": {"__ref": "h1AvTIUWlumD"}, "tplSlot": {"__ref": "Lhk3hZo-kH2m"}, "variable": {"__ref": "ejjtjSSlWKkA"}, "uuid": "6uNfRN-Y18o6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "HzJTsvl4nSTV": {"type": {"__ref": "9TOmyJa7BHV7"}, "variable": {"__ref": "4nH6pidFeAuY"}, "uuid": "6bOm_NuSPRAb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "mJhLI9ojIgFJ": {"type": {"__ref": "l2eyA4ix98Jj"}, "variable": {"__ref": "1-7QqOJcDEJs"}, "uuid": "jOIYmo0GCZku", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "AAV48UJHMJ-2": {"tag": "div", "name": null, "children": [{"__ref": "Lhk3hZo-kH2m"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "BEITk_T295dO", "parent": null, "locked": null, "vsettings": [{"__ref": "HKUPd6zLMLx9"}], "__type": "TplTag"}, "cm98a4v_47xs": {"uuid": "IWxhFYphTh8j", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "JM_hjJDseTSW": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "DQOIIrFQ41yT": {"name": "text", "__type": "Text"}, "7es968uy0OmS": {"name": "title", "uuid": "NcwgIwOuNmJZ", "__type": "Var"}, "eEPb_fHUN5_H": {"name": "text", "__type": "Text"}, "2yMrikjtcuIB": {"name": "description", "uuid": "n-Ps8Gb8-xaL", "__type": "Var"}, "7MrLOPUVbbbx": {"name": "img", "__type": "Img"}, "VL2pD_tVNK-y": {"name": "image", "uuid": "_g4WKO-t2hDF", "__type": "Var"}, "qeqK4yDdTWkG": {"name": "text", "__type": "Text"}, "EZwarPYhuHsS": {"name": "canonical", "uuid": "zkoDfjnPeRnU", "__type": "Var"}, "Vx4vh_DEilUk": {"variants": [{"__ref": "pFYMfUotLkL_"}], "args": [], "attrs": {}, "rs": {"__ref": "vizIMCzNU3-t"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "iEzUBJRG0-7W": {"name": "any", "__type": "AnyType"}, "zOhmvNj5QbGE": {"name": "dataOp", "uuid": "s2418cFskfbU", "__type": "Var"}, "jbWRjgQGZmHz": {"name": "text", "__type": "Text"}, "yau7EG7KV06Q": {"name": "name", "uuid": "N8DyTgeqwMp4", "__type": "Var"}, "h1AvTIUWlumD": {"name": "renderFunc", "params": [{"__ref": "s0gY8o2gv2ri"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "ejjtjSSlWKkA": {"name": "children", "uuid": "VxJukl5VikLT", "__type": "Var"}, "9TOmyJa7BHV7": {"name": "num", "__type": "<PERSON><PERSON>"}, "4nH6pidFeAuY": {"name": "pageSize", "uuid": "7-ZAeSsqyb7-", "__type": "Var"}, "l2eyA4ix98Jj": {"name": "num", "__type": "<PERSON><PERSON>"}, "1-7QqOJcDEJs": {"name": "pageIndex", "uuid": "LCeS32j5m6QD", "__type": "Var"}, "Lhk3hZo-kH2m": {"param": {"__ref": "5jhJ0sVmiLXY"}, "defaultContents": [], "uuid": "hzYr-N6EOE-j", "parent": {"__ref": "AAV48UJHMJ-2"}, "locked": null, "vsettings": [{"__ref": "Kt6SbTtE5f2d"}], "__type": "TplSlot"}, "HKUPd6zLMLx9": {"variants": [{"__ref": "cm98a4v_47xs"}], "args": [], "attrs": {}, "rs": {"__ref": "I9DVRAHf-80r"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "vizIMCzNU3-t": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "s0gY8o2gv2ri": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "B5PCLuwUUoNH"}, "__type": "ArgType"}, "Kt6SbTtE5f2d": {"variants": [{"__ref": "cm98a4v_47xs"}], "args": [], "attrs": {}, "rs": {"__ref": "wymXokdIVFQB"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I9DVRAHf-80r": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "B5PCLuwUUoNH": {"name": "any", "__type": "AnyType"}, "wymXokdIVFQB": {"values": {}, "mixins": [], "__type": "RuleSet"}, "curkqibc0Nq0": {"uuid": "-hqiMoFjUNuX", "name": "quill-test", "params": [{"__ref": "CEYpSfG-cfGn"}, {"__ref": "tSDE225moDEB"}], "states": [{"__ref": "M7M9er8-pnqB"}], "tplTree": {"__ref": "hyvTxVebiSEG"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "fk85J3YLvQii"}], "variantGroups": [], "pageMeta": {"__ref": "4AQ5OzxCeVFr"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "qwvrJ2-zm5JB": {"component": {"__ref": "curkqibc0Nq0"}, "matrix": {"__ref": "fgU73JcIkMQz"}, "customMatrix": {"__ref": "RQA22unIpG1q"}, "__type": "PageArena"}, "KwQu9JkmCel-": {"uuid": "uLGBRsF8HIdM", "width": 800, "height": 800, "container": {"__ref": "1l0-oc8c44mX"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "fk85J3YLvQii"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": 73, "left": 268, "__type": "ArenaFrame"}, "hyvTxVebiSEG": {"tag": "div", "name": null, "children": [{"__ref": "bFFdDHaq44kb"}, {"__ref": "0swPG9qifssv"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "JcJ2odJGV-dg", "parent": null, "locked": null, "vsettings": [{"__ref": "S4X40TrKYDvO"}], "__type": "TplTag"}, "fk85J3YLvQii": {"uuid": "LMQHLoooNGU7", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4AQ5OzxCeVFr": {"path": "/quill-test", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "fgU73JcIkMQz": {"rows": [{"__ref": "7BLVewAvw2UT"}], "__type": "ArenaFrameGrid"}, "RQA22unIpG1q": {"rows": [{"__ref": "aaxJom-WwEWx"}], "__type": "ArenaFrameGrid"}, "1l0-oc8c44mX": {"name": null, "component": {"__ref": "curkqibc0Nq0"}, "uuid": "Phm0rm-dl1Nh", "parent": null, "locked": null, "vsettings": [{"__ref": "y1VMxFLfQkLp"}], "__type": "TplComponent"}, "S4X40TrKYDvO": {"variants": [{"__ref": "fk85J3YLvQii"}], "args": [], "attrs": {}, "rs": {"__ref": "3C1PRObg4mYD"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7BLVewAvw2UT": {"cols": [{"__ref": "Xb_hl_aNWkot"}, {"__ref": "FQd0NoeZYQvb"}], "rowKey": {"__ref": "fk85J3YLvQii"}, "__type": "ArenaFrameRow"}, "aaxJom-WwEWx": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "y1VMxFLfQkLp": {"variants": [{"__ref": "bB9TU9admzr1"}], "args": [], "attrs": {}, "rs": {"__ref": "i_zfR6LhH6KZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "3C1PRObg4mYD": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "Xb_hl_aNWkot": {"frame": {"__ref": "sLS3yJU-8lIm"}, "cellKey": null, "__type": "ArenaFrameCell"}, "FQd0NoeZYQvb": {"frame": {"__ref": "n1CjIcon-vZd"}, "cellKey": null, "__type": "ArenaFrameCell"}, "i_zfR6LhH6KZ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "sLS3yJU-8lIm": {"uuid": "bzVTGjt53ocD", "width": 1366, "height": 768, "container": {"__ref": "ObP5RVier7MK"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "fk85J3YLvQii"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "n1CjIcon-vZd": {"uuid": "gslVqTG9KbkT", "width": 414, "height": 736, "container": {"__ref": "rsFnK3NrHpRA"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "fk85J3YLvQii"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "ObP5RVier7MK": {"name": null, "component": {"__ref": "curkqibc0Nq0"}, "uuid": "b5H31mf7M26-", "parent": null, "locked": null, "vsettings": [{"__ref": "DgiOny4J0YcV"}], "__type": "TplComponent"}, "rsFnK3NrHpRA": {"name": null, "component": {"__ref": "curkqibc0Nq0"}, "uuid": "lSiPm04z1rL4", "parent": null, "locked": null, "vsettings": [{"__ref": "2IoXqtP5cdUs"}], "__type": "TplComponent"}, "DgiOny4J0YcV": {"variants": [{"__ref": "bB9TU9admzr1"}], "args": [], "attrs": {}, "rs": {"__ref": "DEQiFvcozVc3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2IoXqtP5cdUs": {"variants": [{"__ref": "bB9TU9admzr1"}], "args": [], "attrs": {}, "rs": {"__ref": "fC_SQKoqGbBd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "DEQiFvcozVc3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "fC_SQKoqGbBd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "bFFdDHaq44kb": {"name": "Rich Text Editor", "component": {"__xref": {"uuid": "b911462e-59ce-455d-b8d3-0d84f50a35dd", "iid": "SXPBseNfiyZw"}}, "uuid": "pE2qfsg9qN56", "parent": {"__ref": "hyvTxVebiSEG"}, "locked": null, "vsettings": [{"__ref": "3plh8HuijUbR"}], "__type": "TplComponent"}, "M7M9er8-pnqB": {"param": {"__ref": "CEYpSfG-cfGn"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "tSDE225moDEB"}, "tplNode": {"__ref": "bFFdDHaq44kb"}, "implicitState": {"__xref": {"uuid": "b911462e-59ce-455d-b8d3-0d84f50a35dd", "iid": "i7nio1EgDgQD"}}, "__type": "State"}, "CEYpSfG-cfGn": {"type": {"__ref": "X0DICN69iR63"}, "state": {"__ref": "M7M9er8-pnqB"}, "variable": {"__ref": "eUzztBVRE9LA"}, "uuid": "TUxukTMbLs--", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "tSDE225moDEB": {"type": {"__ref": "AQTZhzE_jXsi"}, "state": {"__ref": "M7M9er8-pnqB"}, "variable": {"__ref": "9KD-ZVMEnqxq"}, "uuid": "o5OyOLoUi1OO", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "3plh8HuijUbR": {"variants": [{"__ref": "fk85J3YLvQii"}], "args": [{"__ref": "27iH6iGWBw3N"}], "attrs": {}, "rs": {"__ref": "xdBZNzVgwxvk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "X0DICN69iR63": {"name": "text", "__type": "Text"}, "eUzztBVRE9LA": {"name": "hostless-react-quill value", "uuid": "w6Lcb2pRJlrk", "__type": "Var"}, "AQTZhzE_jXsi": {"name": "func", "params": [{"__ref": "QznA9k48j9Ma"}], "__type": "FunctionType"}, "9KD-ZVMEnqxq": {"name": "On hostless-react-quill value change", "uuid": "Xb4WvZtUezaZ", "__type": "Var"}, "xdBZNzVgwxvk": {"values": {"width": "stretch", "max-width": "100%", "object-fit": "cover", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "QznA9k48j9Ma": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "e4bGVyD-23Ug"}, "__type": "ArgType"}, "e4bGVyD-23Ug": {"name": "text", "__type": "Text"}, "0swPG9qifssv": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "J9o9H-9lNjls", "parent": {"__ref": "hyvTxVebiSEG"}, "locked": null, "vsettings": [{"__ref": "_92RSQZKYCMH"}], "__type": "TplTag"}, "_92RSQZKYCMH": {"variants": [{"__ref": "fk85J3YLvQii"}], "args": [], "attrs": {"id": {"__ref": "tkbX31iChEAr"}}, "rs": {"__ref": "wOunN_jVvgN4"}, "dataCond": null, "dataRep": null, "text": {"__ref": "wjQavHFGcada"}, "columnsConfig": null, "__type": "VariantSetting"}, "wOunN_jVvgN4": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "wjQavHFGcada": {"expr": {"__ref": "AZy14frNStPA"}, "html": false, "__type": "ExprText"}, "AZy14frNStPA": {"path": ["$state", "richTextEditor", "value"], "fallback": {"__ref": "9z7CfnIsSdHp"}, "__type": "ObjectPath"}, "9z7CfnIsSdHp": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "27iH6iGWBw3N": {"param": {"__xref": {"uuid": "b911462e-59ce-455d-b8d3-0d84f50a35dd", "iid": "AAPjx70LrziJ"}}, "expr": {"__ref": "bkj8NH3I_O9p"}, "__type": "Arg"}, "bkj8NH3I_O9p": {"text": ["Hello World"], "__type": "TemplatedString"}, "tkbX31iChEAr": {"text": ["quill-state"], "__type": "TemplatedString"}}, "deps": ["b911462e-59ce-455d-b8d3-0d84f50a35dd"], "version": "251-add-data-tokens"}]]