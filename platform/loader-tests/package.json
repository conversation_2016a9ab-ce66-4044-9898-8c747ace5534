{"name": "loader-tests", "version": "1.0.0", "main": "index.js", "author": "<PERSON>", "license": "MIT", "private": true, "scripts": {"prepare": "playwright install chromium", "playwright-ui": "playwright test --ui", "test": "yarn test-playwright && yarn test-jest", "test-docker": "yarn docker-build && yarn docker-run", "update-snapshots": "rm -rf cypress/snapshots && yarn test-docker", "docker-build": "docker build --no-cache -t loader-tests .", "docker-run": "docker run -v $PWD/cypress:/app/cypress --add-host host.docker.internal:host-gateway --rm loader-tests", "test-jest": "npx jest --runInBand", "test-playwright": "npx playwright test"}, "devDependencies": {"@types/cypress-image-snapshot": "^3.1.6", "@types/fs-extra": "^9.0.12", "@types/glob": "^7.1.4", "@types/jest": "^29.5.11", "@types/node-fetch": "^2.5.12", "@types/tmp": "^0.2.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^4.3.5"}, "dependencies": {"@playwright/test": "1.38.1", "aws-sdk": "^2.985.0", "cypress": "^12.9.0", "cypress-image-snapshot": "^4.0.1", "fs-extra": "^10.0.0", "get-port": "^5.1.1", "glob": "^7.1.7", "node-fetch": "^2", "scroll-to-bottomjs": "^1.1.0", "tmp": "^0.2.1"}}