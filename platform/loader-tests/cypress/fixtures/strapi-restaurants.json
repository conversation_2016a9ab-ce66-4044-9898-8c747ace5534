{"data": [{"id": 5, "attributes": {"nullValuesCanBeHandled": null, "name": "Café Coffee Day", "description": "Café Coffee Day (CCD) is an Indian multinational chain of coffeehouses headquartered in Bengaluru, Karnataka. It is a subsidiary of Coffee Day Enterprises Limited. Internationally, CCDs were present in Austria, Czech Republic, Malaysia, Nepal and Egypt before shutting down all operations outside India.[4]", "createdAt": "2022-04-19T15:13:46.616Z", "updatedAt": "2022-04-26T02:17:02.520Z", "publishedAt": "2022-04-19T15:13:47.626Z", "categories": {"data": [{"id": 4, "attributes": {"name": "Beverages", "createdAt": "2022-04-19T15:09:36.853Z", "updatedAt": "2022-04-19T15:09:39.111Z", "publishedAt": "2022-04-19T15:09:39.108Z"}}]}, "photo": {"data": {"id": 9, "attributes": {"name": "Cafe_Coffee_Day_logo.png", "alternativeText": "Cafe_Coffee_Day_logo.png", "caption": "Cafe_Coffee_Day_logo.png", "width": 60, "height": 86, "formats": null, "hash": "Cafe_Coffee_Day_logo_338419f75a", "ext": ".png", "mime": "image/png", "size": 1.22, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/Cafe_Coffee_Day_logo_338419f75a.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "Cafe_Coffee_Day_logo_338419f75a", "resource_type": "image"}, "createdAt": "2022-04-26T02:15:43.612Z", "updatedAt": "2022-04-26T02:15:43.612Z"}}}}}, {"id": 6, "attributes": {"name": "<PERSON><PERSON>'s", "description": "<PERSON><PERSON>'s Grill & Bar is an American casual dining restaurant chain.[3] The company was founded by <PERSON> in Texas in 1975 and is currently owned and operated by Brinker International.", "createdAt": "2022-04-19T15:14:06.743Z", "updatedAt": "2022-06-10T04:53:13.680Z", "publishedAt": "2022-04-19T15:14:07.913Z", "categories": {"data": []}, "photo": {"data": {"id": 10, "attributes": {"name": "Chili's_Logo.svg.png", "alternativeText": "Chili's_Logo.svg.png", "caption": "Chili's_Logo.svg.png", "width": 250, "height": 126, "formats": {"thumbnail": {"ext": ".png", "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/thumbnail_Chili_s_Logo_svg_9b74d95e58.png", "hash": "thumbnail_<PERSON><PERSON>_s_Logo_svg_9b74d95e58", "mime": "image/png", "name": "thumbnail_<PERSON><PERSON>'s_Logo.svg.png", "path": null, "size": 16.51, "width": 245, "height": 123, "provider_metadata": {"public_id": "thumbnail_<PERSON><PERSON>_s_Logo_svg_9b74d95e58", "resource_type": "image"}}}, "hash": "<PERSON><PERSON>_s_Logo_svg_9b74d95e58", "ext": ".png", "mime": "image/png", "size": 1.98, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/Chili_s_Logo_svg_9b74d95e58.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "<PERSON><PERSON>_s_Logo_svg_9b74d95e58", "resource_type": "image"}, "createdAt": "2022-04-26T02:15:43.907Z", "updatedAt": "2022-04-26T02:15:43.907Z"}}}}}, {"id": 7, "attributes": {"name": "Chipotle Mexican Grill", "description": "Chipotle Mexican Grill, Inc. (/tʃɪˈpoʊtleɪ/, chih-POHT-lay),[7] often known simply as Chipotle, is an American chain of fast casual restaurants in the United States, United Kingdom,[8] Canada,[9][10] Germany,[11] and France,[12] specializing in tacos and Mission burritos that are made to order in front of the customer.[13][14] Its name derives from chipotle, the Nahuatl name for a smoked and dried jalapeño chili pepper.[15][16]", "createdAt": "2022-04-19T15:14:37.621Z", "updatedAt": "2022-06-10T04:53:38.379Z", "publishedAt": "2022-04-19T15:14:38.684Z", "categories": {"data": []}, "photo": {"data": {"id": 12, "attributes": {"name": "Chipotle_Mexican_Grill_logo.svg.png", "alternativeText": "Chipotle_Mexican_Grill_logo.svg.png", "caption": "Chipotle_Mexican_Grill_logo.svg.png", "width": 220, "height": 220, "formats": {"thumbnail": {"ext": ".png", "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/thumbnail_Chipotle_Mexican_Grill_logo_svg_53d34599eb.png", "hash": "thumbnail_<PERSON><PERSON><PERSON>_Mexican_Grill_logo_svg_53d34599eb", "mime": "image/png", "name": "thumbnail_<PERSON><PERSON><PERSON>_Mexican_Grill_logo.svg.png", "path": null, "size": 25.47, "width": 156, "height": 156, "provider_metadata": {"public_id": "thumbnail_<PERSON><PERSON><PERSON>_Mexican_Grill_logo_svg_53d34599eb", "resource_type": "image"}}}, "hash": "Chipotle_Mexican_Grill_logo_svg_53d34599eb", "ext": ".png", "mime": "image/png", "size": 6.51, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/Chipotle_Mexican_Grill_logo_svg_53d34599eb.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "Chipotle_Mexican_Grill_logo_svg_53d34599eb", "resource_type": "image"}, "createdAt": "2022-04-26T02:15:43.978Z", "updatedAt": "2022-04-26T02:15:43.978Z"}}}}}, {"id": 1, "attributes": {"name": "Big Smoke Burger", "description": "Big Smoke Burger is an international restaurant chain based in Canada. The Big Smoke Burger was founded by <PERSON> in November 2007,[1] originally under the name Craft Burger.[7] It rebranded to Big Smoke Burger in 2011 after <PERSON> was unable to secure a trademark for the original name.[7] The new name was chosen as \"Big Smoke\" has been a noted nickname for the city of Toronto, which is where the company has its headquarters.", "createdAt": "2022-04-19T15:10:59.242Z", "updatedAt": "2022-06-02T18:11:31.584Z", "publishedAt": "2022-04-19T15:11:00.745Z", "categories": {"data": [{"id": 2, "attributes": {"name": "Hamburgers", "createdAt": "2022-04-19T15:09:10.313Z", "updatedAt": "2022-04-19T15:09:11.962Z", "publishedAt": "2022-04-19T15:09:11.958Z"}}]}, "photo": {"data": {"id": 15, "attributes": {"name": "Big_Smoke_Burger_logo.svg.png", "alternativeText": "Big_Smoke_Burger_logo.svg.png", "caption": "Big_Smoke_Burger_logo.svg.png", "width": 180, "height": 208, "formats": {"thumbnail": {"ext": ".png", "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939375/thumbnail_<PERSON>_Smoke_Burger_logo_svg_e3ca76d953.png", "hash": "thumbnail_<PERSON>_<PERSON>_Burger_logo_svg_e3ca76d953", "mime": "image/png", "name": "thumbnail_<PERSON>_Smoke_Burger_logo.svg.png", "path": null, "size": 16.98, "width": 135, "height": 156, "provider_metadata": {"public_id": "thumbnail_<PERSON>_<PERSON>_Burger_logo_svg_e3ca76d953", "resource_type": "image"}}}, "hash": "<PERSON>_<PERSON>_Burger_logo_svg_e3ca76d953", "ext": ".png", "mime": "image/png", "size": 3.69, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939374/<PERSON>_Smoke_Burger_logo_svg_e3ca76d953.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "<PERSON>_<PERSON>_Burger_logo_svg_e3ca76d953", "resource_type": "image"}, "createdAt": "2022-04-26T02:16:15.609Z", "updatedAt": "2022-04-26T02:16:15.609Z"}}}}}, {"id": 4, "attributes": {"name": "Burger King", "description": "Burger King (BK) is an American multinational chain of hamburger fast food restaurants. Headquartered in Miami-Dade County, Florida, the company was founded in 1953 as Insta-Burger King, a Jacksonville, Florida–based restaurant chain. After Insta-Burger King ran into financial difficulties in 1954, its two Miami-based franchisees <PERSON> and <PERSON> purchased the company and renamed it \"Burger King\".[4] Over the next half-century, the company changed hands four times, with its third set of owners, a partnership of TPG Capital, Bain Capital, and Goldman Sachs Capital Partners, taking it public in 2002. ", "createdAt": "2022-04-19T15:13:14.000Z", "updatedAt": "2022-05-20T16:18:23.190Z", "publishedAt": "2022-04-19T15:13:15.026Z", "categories": {"data": [{"id": 2, "attributes": {"name": "Hamburgers", "createdAt": "2022-04-19T15:09:10.313Z", "updatedAt": "2022-04-19T15:09:11.962Z", "publishedAt": "2022-04-19T15:09:11.958Z"}}]}, "photo": {"data": {"id": 13, "attributes": {"name": "Burger_King_2020.svg.png", "alternativeText": "Burger_King_2020.svg.png", "caption": "Burger_King_2020.svg.png", "width": 200, "height": 218, "formats": {"thumbnail": {"ext": ".png", "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/thumbnail_<PERSON>_King_2020_svg_ac8ab9c5f1.png", "hash": "thumbnail_<PERSON>_King_2020_svg_ac8ab9c5f1", "mime": "image/png", "name": "thumbnail_<PERSON>_King_2020.svg.png", "path": null, "size": 18.26, "width": 143, "height": 156, "provider_metadata": {"public_id": "thumbnail_<PERSON>_King_2020_svg_ac8ab9c5f1", "resource_type": "image"}}}, "hash": "<PERSON>_King_2020_svg_ac8ab9c5f1", "ext": ".png", "mime": "image/png", "size": 3.84, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/<PERSON>_King_2020_svg_ac8ab9c5f1.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "<PERSON>_King_2020_svg_ac8ab9c5f1", "resource_type": "image"}, "createdAt": "2022-04-26T02:15:43.997Z", "updatedAt": "2022-04-26T02:15:43.997Z"}}}}}, {"id": 2, "attributes": {"name": "Bonchon Chicken", "description": "Bonchon Chicken (Korean: 본촌치킨; Hanja: 本村치킨) is a South Korean-based international fried chicken restaurant franchise.[1][2] According to the company, Bonchon is a Korean word meaning \"My Hometown\". ", "createdAt": "2022-04-19T15:11:59.282Z", "updatedAt": "2022-04-26T02:16:34.300Z", "publishedAt": "2022-04-19T15:12:01.256Z", "categories": {"data": [{"id": 3, "attributes": {"name": "Chicken", "createdAt": "2022-04-19T15:09:19.683Z", "updatedAt": "2022-04-19T15:09:23.501Z", "publishedAt": "2022-04-19T15:09:23.498Z"}}]}, "photo": {"data": {"id": 14, "attributes": {"name": "Bonchon_Logo.png", "alternativeText": "Bonchon_Logo.png", "caption": "Bonchon_Logo.png", "width": 186, "height": 215, "formats": {"thumbnail": {"ext": ".png", "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/thumbnail_<PERSON>chon_Logo_7f7f16bce2.png", "hash": "thumbnail_<PERSON><PERSON><PERSON>_Logo_7f7f16bce2", "mime": "image/png", "name": "thumbnail_Bonchon_Logo.png", "path": null, "size": 26.4, "width": 135, "height": 156, "provider_metadata": {"public_id": "thumbnail_<PERSON><PERSON><PERSON>_Logo_7f7f16bce2", "resource_type": "image"}}}, "hash": "Bonchon_Logo_7f7f16bce2", "ext": ".png", "mime": "image/png", "size": 9.08, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/Bonchon_Logo_7f7f16bce2.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "Bonchon_Logo_7f7f16bce2", "resource_type": "image"}, "createdAt": "2022-04-26T02:15:44.006Z", "updatedAt": "2022-04-26T02:15:44.006Z"}}}}}, {"id": 3, "attributes": {"name": "Buffalo Wild Wings", "description": "Buffalo Wild Wings (originally Buffalo Wild Wings & Weck,[4] hence the numeronym BW3) is an American casual dining restaurant and sports bar franchise in the United States, Canada, India, Mexico, Oman, Panama, Philippines, Saudi Arabia, United Arab Emirates, and Vietnam which specializes in Buffalo wings and sauces.", "createdAt": "2022-04-19T15:12:31.832Z", "updatedAt": "2022-04-26T02:16:43.983Z", "publishedAt": "2022-04-19T15:12:32.873Z", "categories": {"data": [{"id": 3, "attributes": {"name": "Chicken", "createdAt": "2022-04-19T15:09:19.683Z", "updatedAt": "2022-04-19T15:09:23.501Z", "publishedAt": "2022-04-19T15:09:23.498Z"}}]}, "photo": {"data": {"id": 11, "attributes": {"name": "Buffalo_Wild_Wings_(logo,_vertical).svg.png", "alternativeText": "Buffalo_Wild_Wings_(logo,_vertical).svg.png", "caption": "Buffalo_Wild_Wings_(logo,_vertical).svg.png", "width": 100, "height": 178, "formats": {"thumbnail": {"ext": ".png", "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/thumbnail_Buffalo_Wild_Wings_logo_vertical_svg_cc56dc61aa.png", "hash": "thumbnail_Buffalo_Wild_Wings_logo_vertical_svg_cc56dc61aa", "mime": "image/png", "name": "thumbnail_Buffalo_Wild_Wings_(logo,_vertical).svg.png", "path": null, "size": 18.49, "width": 88, "height": 156, "provider_metadata": {"public_id": "thumbnail_Buffalo_Wild_Wings_logo_vertical_svg_cc56dc61aa", "resource_type": "image"}}}, "hash": "Buffalo_Wild_Wings_logo_vertical_svg_cc56dc61aa", "ext": ".png", "mime": "image/png", "size": 3.55, "url": "https://res.cloudinary.com/tubone-plasmic/image/upload/v1650939343/Buffalo_Wild_Wings_logo_vertical_svg_cc56dc61aa.png", "previewUrl": null, "provider": "cloudinary", "provider_metadata": {"public_id": "Buffalo_Wild_Wings_logo_vertical_svg_cc56dc61aa", "resource_type": "image"}, "createdAt": "2022-04-26T02:15:43.924Z", "updatedAt": "2022-04-26T02:15:43.924Z"}}}}}], "meta": {"pagination": {"page": 1, "pageSize": 25, "pageCount": 1, "total": 7}}}