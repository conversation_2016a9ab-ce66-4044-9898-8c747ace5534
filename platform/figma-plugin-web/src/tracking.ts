const reservedProps = [
  /**
   * Segment reserved properties
   * https://segment.com/docs/connections/spec/track/
   */
  "revenue",
  "currency",
  "value",
  /**
   * Mixpanel reserved properties
   * https://help.mixpanel.com/hc/en-us/articles/360001355266-Event-Properties
   */
  "distinct_id",
  "ip",
  "token",
  "time",
  "length",
  "campaign_id",
  "city",
  "region",
  "country",
  "bucket",
  "message_id",
].map((k) => k.toLowerCase());

// Just show warnings, don't block the track
export function trackEvent(eventName: string, eventData?: any) {
  if (!!eventData) {
    const reserved = Object.keys(eventData)
      .map((k) => k.toLowerCase())
      .filter((k) => reservedProps.includes(k));
    if (reserved.length > 0) {
      console.warn(
        `track(${eventName}, ${eventData}) might behave unexpectedly because the following are reserved properties: ${reserved}`
      );
    }
  }
  analytics.track(eventName, eventData);
}
