import * as Sentry from "@sentry/browser";
import { expose, windowEndpoint } from "comlink";
import pickBy from "lodash/pickBy";
import React from "react";
import ReactDOM from "react-dom";
import App from "./App";
import "./index.css";
import * as serviceWorker from "./serviceWorker";
import { trackEvent } from "./tracking";

// Note that a bunch of code here is copied from the main `wab` project.

const sentryOrgId = "plasmicapp";
const sentryProjId = "1840236";

export function omitNils<V>(x: { [k: string]: V | null | undefined }): {
  [k: string]: V;
} {
  return pickBy(x, (x): x is V => x != null);
}

export function ensure<T>(x: T | null | undefined): T {
  if (x === null || x === undefined) {
    debugger;
    throw new Error(`Value must not be undefined or null.`);
  } else {
    return x;
  }
}

export const tuple = <T extends any[]>(...args: T): T => args;

/**
 * NOTE this returns undefined, unlike the null from swallow()!
 */
export async function swallowAsync<T>(f: Promise<T>): Promise<T | undefined>;
export async function swallowAsync<T>(
  etypes: Function[],
  f: Promise<T>
): Promise<T | undefined>;
export async function swallowAsync<T>(
  a: Promise<T> | Function[],
  b?: Promise<T>
): Promise<T | undefined> {
  const [promise, etypes] = b
    ? tuple(b, a as Function[])
    : tuple(a as Promise<T>, null);
  try {
    return await promise;
  } catch (e) {
    if (etypes == null || [...etypes].some((etype) => e instanceof etype)) {
      return undefined;
    }
    throw e;
  }
}

export interface ApiEntityBase {
  id: string;
  // TODO These are Date in the DB but string via API.
  //  Ideally we should deserialize these back into Dates in the API client.
  createdAt: Date | string;
  updatedAt: Date | string;
  deletedAt: Date | string | null;
  createdById: string | null;
  updatedById: string | null;
  deletedById: string | null;
}

export interface ApiUser extends ApiEntityBase {
  email: string;
  firstName: string | null;
  lastName: string | null;
  avatarUrl: string | null;
  needsIntroSplash: boolean;
  extraData: string | null;
}

export interface SelfResponse {
  user: ApiUser;
}

export interface AddClipResponse {
  id: string;
}

export function fullName(user: ApiUser) {
  return user.firstName || user.lastName
    ? `${user.firstName} ${user.lastName}`
    : user.email;
}

function copyText(string: string) {
  const input = document.createElement("input");
  input.setAttribute("value", string);
  document.body.appendChild(input);
  input.select();
  const result = document.execCommand("copy");
  document.body.removeChild(input);
  return result;
}

/**
 * Client for the Plasmic REST API.
 */
class Api {
  private async put(path: string, data: {}) {
    const headers: { [k: string]: string } = {
      "Content-Type": "application/json",
    };
    if (this._csrf) {
      headers["X-CSRF-Token"] = this._csrf;
    }
    const response = await fetch("/api/v1" + path, {
      method: "PUT",
      headers,
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error();
    }
    return response.json();
  }
  private async get(path: string) {
    const response = await fetch("/api/v1" + path);
    if (!response.ok) {
      throw new Error();
    }
    return response.json();
  }
  async getSelfInfo(): Promise<void> {
    const response = await swallowAsync(this.get("/auth/self"));
    if (response) {
      const res: SelfResponse = await response;
      this.setUser(res.user);
    }
  }
  private _csrf?: string;
  async refreshCsrfToken() {
    const { csrf } = await this.get("/auth/csrf");
    this._csrf = csrf;
  }
  private setUser(user: ApiUser) {
    const { id, email, firstName, lastName } = user;
    const traits = omitNils({
      email,
      firstName,
      lastName,
      createdAt: user.createdAt as string,
      fullName: fullName(user),
      domain: email.split("@")[1],
    });
    analytics.identify(id, traits);
    Sentry.configureScope((scope) => {
      scope.setUser({ id, ...traits });
    });
  }
  async addClip(clipId: string, content: string): Promise<{}> {
    return this.put(`/clip/${encodeURIComponent(clipId)}`, {
      content,
    });
  }
}

/**
 * Service implementing the API exposed to the Figma plugin iframe (which should be this app's parent window).
 */
class ChildApi {
  constructor(private api: Api) {}

  /**
   * Copying data directly to clipboard often fails in browser, because there's a time limit on how long after the
   * click event we are granted clipboard write access. So instead, we immediately generate a UUID and write it to
   * the clipboard (in the Figma ui iframe), then upload the data to Plasmic's app server (in this iframe).
   */
  async setClipboardData(clipId: string, content: string) {
    if (content === "null") {
      Sentry.captureMessage("Figma export failed to serialize");
      return;
    }

    trackEvent("Figma export", {
      size: content.length,
    });

    // Upload the data (even if the clipboard copy failed), just so we can have this info for any debug purposes.
    await this.api.addClip(clipId, content);
  }
  async trackEvent(eventName: string, data: {}) {
    trackEvent(eventName, data);
  }
}

export async function main() {
  Sentry.init({
    dsn: `https://<EMAIL>/${sentryProjId}`,
  });

  const api = new Api();

  // For some reason, same-site cookie policy fails in the Figma Desktop app. But we don't need this since anyone is
  // allowed to make these POST requests.
  // await api.refreshCsrfToken();

  // We only call this for its side effect of initializing the user for tracking.
  //
  // If we're running inside Figma desktop, we won't have Plasmic app session cookies, and thus no user is identified.
  //
  // Even for when we're running in the browser, this is mostly not needed. Usually the user gets identified at some
  // point in the main Plasmic app, and then Segment/Mixpanel/etc.'s own cookies/localStorage will carry this over
  // to this iframe as well (since it runs on the same origin at studio.plasmic.app).  So this is mostly best
  // practice according to Segment docs:
  // https://segment.com/docs/connections/spec/best-practices-identify/#when-and-how-often-to-call-identify
  const selfInfoPromise = api.getSelfInfo();

  // Serve the parent window.
  const childApi = new ChildApi(api);
  expose(childApi, windowEndpoint(window.parent));

  ReactDOM.render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
    document.getElementById("root")
  );
}

main();

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
