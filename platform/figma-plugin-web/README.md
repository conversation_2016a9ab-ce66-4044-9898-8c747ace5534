Testing locally
===============

Figma requires iframes to be HTTPS.

If you don't care about testing with real Plasmic session cookies, then you can just start the dev server on https
 using the simple mkcert tool (https://dev.to/rhymes/really-easy-way-to-use-https-on-localhost-341m).

    # This only needs to be run once ever.
    sudo port install mkcert
    mkcert -install

    # Run this instead of `yarn start`. Listens on port 3006.
    bash tools/dev-server.bash

And then point your local Figma plugin code at https://localhost:3006/figma-plugin-app (by editing the nested iframe
 URL in the code).

Testing on prod
===============

But if you want the real session cookies from Plasmic (to test our analytics), the only way I test this today is
copying things to prod.

In the following, replace yang with your own name, so we have different dirs. First:

    mkdir -p plasmic-figma-plugin-yang

Edit nginx config to clone the figma-plugin-app location as figma-plugin-app-yang.

Then build and deploy:

    yarn build
    bash tools/rsync.bash studio.plasmic.app:plasmic-figma-plugin-yang/build/

Finally edit the URL in the Figma plugin code, then try the plugin.

Unfortunately this does not give you a debuggable build.

A better solution (to be written up in the future) is to have nginx proxy to your dev server via an ssh reverse tunnel.

Deploying to prod
=================

    yarn build
    bash tools/rsync.bash studio.plasmic.app:plasmic-figma-plugin/build/

---

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).
