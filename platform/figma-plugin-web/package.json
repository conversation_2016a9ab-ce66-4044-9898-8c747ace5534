{"name": "figma-plugin-web", "version": "0.1.0", "private": true, "dependencies": {"@sentry/browser": "^5.19.2", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "@types/jest": "^24.0.0", "@types/node": "^12.0.0", "@types/react": "^16.9.0", "@types/react-dom": "^16.9.0", "@types/segment-analytics": "^0.0.32", "comlink": "^4.3.0", "idb-keyval": "^3.2.0", "lodash": "^4.17.19", "react": "^16.13.1", "react-dom": "^16.13.1", "react-scripts": "3.4.1", "typescript": "~3.7.2", "uuid": "^8.3.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "homepage": "https://studio.plasmic.app/figma-plugin-app", "proxy": "http://localhost:3004", "devDependencies": {"@types/lodash": "^4.14.158", "@types/uuid": "^8.0.0", "prettier": "^2.0.5"}}