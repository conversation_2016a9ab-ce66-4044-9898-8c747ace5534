<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="robots" content="noindex" />
    <script>
      const maybeParams = !!window.__PlasmicStudioArgs
        ? new URLSearchParams(window.__PlasmicStudioArgs.replace(/^#/, "?"))
        : undefined;
      const origin = maybeParams
        ? maybeParams.get("origin")
        : window.location.origin;
    </script>
    <script>
      if (typeof window !== "undefined") {
        if (window.parent !== window) {
          try {
            window.__REACT_DEVTOOLS_GLOBAL_HOOK__ =
              window.parent.__REACT_DEVTOOLS_GLOBAL_HOOK__;
          } catch (err) {}
        }
        if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
          const renderers = new Map();
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__ = {
            supportsFiber: true,
            renderers,
            inject: (renderer) => {
              renderers.set(renderers.size + 1, renderer);
            },
            onCommitFiberRoot: function () {},
            onCommitFiberUnmount: function () {},
          };
        }
        if (!window.__REACT_DEVTOOLS_GLOBAL_HOOK__.__PlasmicPreambleVersion) {
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__.__PlasmicPreambleVersion = "1";
        }
      }
    </script>
    <script>
      const isProd =
        "isProd" in window
          ? window.isProd
          : window.origin === "https://studio.plasmic.app" ||
            window.origin === "https://plasmic.dev" ||
            window.origin === "https://host.plasmic.dev" ||
            window.origin === "https://staging.plasmic.app";
      window.isProd = isProd;
    </script>

    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#000000" />
    <style>
      @font-face {
        font-family: plasmicTestFont;
        src: url(data:application/x-font-otf;base64,AAEAAAAOAIAAAwBgRFNJRwAAAAEAABOkAAAACE9TLzJY0VJbAAABaAAAAGBjbWFwf4zoRAAAAtAAAAGSY3Z0IAAhAnkAAARsAAAABGdhc3AAAAAQAAATnAAAAAhnbHlmOxSp9AAABPgAAAt4aGVhZBRbFBsAAADsAAAANmhoZWEEHwE9AAABJAAAACRobXR4K7cL0wAAAcgAAAEIbG9jYV0AWjQAAARwAAAAhm1heHAAhgAUAAABSAAAACBuYW1lgHSs0QAAEHAAAAJ5cG9zdM3+d8kAABLsAAAArnByZXBoBoyFAAAEZAAAAAcAAQAAAAEAAE5sdmZfDzz1AAsD6AAAAADZ+OhOAAAAANn46E4AIQAAASoCmgAAAAgAAgAAAAAAAAABAAACmgAAAFoA+gAAAAABKgABAAAAAAAAAAAAAAAAAAAAQgABAAAAQgARAAIAAAAAAAIAAAABAAEAAABAAAAAAAAAAAQAowGQAAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAUDAAAAAAAAAAAAAwAAAAAAAAAAAAAAAENMR1IAQAAAAKADIP84AFoCmgAAAAAAAQAAAAABbAFmAAAAIAABAWwAIQD6AAABTQAAAPoAAAD6AAAAkgAxAKoAMgCZADMAogAyALkAMgB7ADIAoAAyAJ8AMwCcADIArQAyAKcAMgCkADIAdwAyAHgAMwCrADIAoAAyAKwAMQCmADIApAAyAJYAMgCwADIAqQAyAKkAMgCrADIAeAAwAK4AMgCuADIArgAyAKcAMgCgADIAnAAyAJwAMgCgADIAfAAyAKsAMgCIADIAnAAxAH0ALwCfADIApwAyAKAAMgCTADIAkgAyAKUAMgClADIAogAyAJoAMgCqADIArQAyAJ0AMgCmADIAqQAyAKsAMgCeADIAmwAyAKkAMgCoADIAmwAyAKAAMgB4ADEA+gAAAAAAAwAAAAMAAAAcAAEAAAAAAIwAAwABAAAAHAAEAHAAAAAYABAAAwAIAAAADQAiACcALAAuADsAPwBaAHoAoP//AAAAAAANACAAJwAsAC4AOgA/AEEAYQCg//8AAf/2/+T/4P/c/9v/0P/N/8z/xv+hAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEGAAABAAAAAAAAAAECAAAAAwAAAAAAAAAAAAAAAAAAAAEAAAQFBgAAAAAHAAAAAAgACQAAAAAAAAAAAAAACgsAAAAMAA0ODxAREhMUFRYXGBkaGxwdHh8gISIjJCUmAAAAAAAAJygpKissLS4vMDEyMzQ1Njc4OTo7PD0+P0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC4Af+FsASNAAAhAnkAAAAUABQAFAAUABQALABCAGAAegCUAKoAwADaAPIBDAEiATwBTAFeAXQBjgGqAcQB3AH2AhACJgI8AloCbAKIAqICuALUAvADCAMgAzIDRgNgA3YDjgOiA7gD0APkA/4EFAQuBEgEYgR8BJQEsATMBOgFAAUUBS4FSAVgBXoFlAWqBbwFvAAAAAIAIQAAASoCmgADAAcAADMRIREnMxEjIQEJ6MfHApr9ZiECWAAAAAABADEBkABgAb8ACwAAEzYzMhYUBiInJjU0NgQMChAOEgcIAbYJDhQNBwYKBQAAAAABADIBkwB4AdoACwAAEzYzMhYVFCMuATU0PAoPDhUjEBMBzgwUECMBEg4NAAEAMwG3AGgB8QAQAAATNjMyFxYVFAYjIicmJyY1NDoHCxMIAREKAwIHCQUB6QgaAgMKEQEBBgQOEQAAAAEAMgGvAHAB8QAOAAATNjMyFxYVFAcGIyImNTRCCgcNBwkGCQ4NFAHsBQkLEQ4GCREOFQABADIBygCHAh8ADgAAEzYzMhcWFRQGIyInJjU0SAcLFg0KFhUSCw0CGwQNChMVFgkMFB0AAQAyAZkASQGxAAkAABI0NhcWFxYGIyIyBAcJAgEEBwgBng4FAQELBwQAAAABADIBOABuAXQACwAAEzQ2MzIWFRQGIyImMhMNBhYXCQoSAVUMExcHCBYTAAEAMwE2AGwBbwANAAATNjMyFhQHBiMiJyY1NDoGEA0PBwQPEwcFAWcIDxgLBwkGDQ4AAAABADIBTwBqAYsACwAAEjYzMhYVFAYjIicmMg4LDBMRDAkKCAF9DhAMDhIICAAAAAABADIBWAB8AaMADgAAEzYzMhYXFBYVFAYjIjU0QgkLDhUCARUQJQGdBhIPAgUBDxMkGAAAAQAyAW8AdQG1AAoAABI2MzIWFRQGIiY1MhIPEBITGhYBoxISEQ8UEhIAAAABADIBZgByAagADQAAEzYzMhcWFRQGIyImNTQ6CA8TCAYTDwwSAZ8JCwkLDhUVDgwAAAAAAQAyASAARQEzAAcAABM0MzIVFCMiMgkKCgkBKgkJCgABADMBNQBHAUoACAAAEwYjIjU0MzIWQgMECAkHBAE4AwsKDgABADIBBAB5AU8ACwAAEjYzMhcWFRQjIiY1MhIRDwoLJQ8TATcYCQoSJhYPAAEAMgEhAG4BXAAMAAATNjMyFxYUBwYiJyY0OQgQDwcHCAgcCAgBVAgHBhwKCAcIHAAAAAABADEBSgB6AZAADgAAEzYzMhcWFAcGIyInJjU0OQoUEQkJCQoREggLAYMNCgkgCQoLEAsJAAAAAAEAMgEpAHQBbgAOAAATNjMyFhUUBwYjIicmNTRCBwgOFQcIExAHCQFqBBYODAoLCQoQFgABADIBKgByAWsADAAAEzYzMhYVFAcGIyImNDsKDA8SCgwMDREBYQoSDA0KDBQaAAABADIBPABkAW8ADQAAEzYyFhUUBwYjIicmNTRACggSBAQSDQYFAWkGEgoHBwkHBwcPAAAAAQAyAPAAfgE8AA0AABM2MzIWFRQGIyInJjU0PgoPEBcUEAgJFwExCxcREBQDCBoQAAAAAAEAMgESAHcBWAAKAAATNjMyFCMiJyY1NDoIESQkEggHAVAIRgsIERAAAAAAAQAyAQcAdwFKAAsAABI2MzIWFRQGIyImNTISEQ4UExAPEwEyGBIOEBMSDQABADIBIAB5AWoAEAAAEzYzMhcWFRQGBwYjIicmNTRABg4RCgoGCwwHGAkCAWUFBwYUEA4GBRkECBUAAAABADABOwBIAVQABgAAEzYzMhYGJjMCBgcGCg4BTQcPCgsAAAABADIBLgB8AX0ADwAAEzYzMhcWFRQGIyImJyY1NEYKBQYOExYLChoDAgF4BQcJGgwZDgcECh8AAAEAMgEsAHwBdQAOAAATNjMyFxYVFAYjIicmNTQ7ChESCgoWDxMLBwFoDQ0MCREWEQoKCwABADIBKgB8AXQACwAAEzYzMhYVFAYjIiY0PQwODRgUEBEVAWgMGA8OFRYeAAEAMgEfAHUBYgAPAAATNjMyFxYVFAcGIyInJjU0PQcPCwoNAwkUGAgDAVsHBQcZCAYQFAcHDwAAAQAyARMAbgFOAA4AABM2MzIXFhUUBwYiJyY1NDkGDxAICAgIHAgIAUcHBwYPDQoIBwgPDgAAAAABADIBCgBqAUYADAAAEzYzMhYVFAcGIicmNDwMBgwQCwoSCAkBPggUDA0IBwgJHAABADIBAgBqATwADAAAEjYzMhcWFRQHBiMiJjIQCw8HBw8GBgwRASwQCQcMFQYDEgABADIA6QBuASUABwAANjQ2MhYUBiIyEhgSEhj7GBISGBIAAAABADIBJwBKAUAACQAAEyY1NDMyFRQjIjUDCw0LBQEsBgQKDQwAAAAAAQAyAQkAeQFQAA0AABM2MzIXFhUUBiMiJjU0OwoQEQoJDhIRFgFGCgoJEQ8UEhIQAAAAAAEAMgETAFYBOwAKAAATNhcWFRQrASY1NDkMCwYTBA0BMgkLBgcQAg8IAAAAAQAxAPIAagEpAAwAABM2MzIWFRQGIyImNTQ5BgsMFBMKDBABIgcQCwkTEgwKAAAAAQAvAQwASwEkAAgAABM2MzIVFAYnJjMDBw4RBQYBHgYLBwYFBgAAAAEAMgEJAG0BRgALAAATNjMyFhUUIyImNTQ6CQ0MER4MEQE8ChEOHhMMCgAAAQAyASoAdQFqAAwAABM2MzIWFRQHBiImNTQ8CQ0OFQoJHhIBYggUDQwKCRIPDgAAAQAyASUAbgFmAAkAABI0NjMyFRQGIyIyEQ0eFAsMATceESATDgAAAAEAMgEFAGEBMwANAAASNjMyFxYVFAYjIicmNTIMDg8DAwwKBwYMAScMBwgGCw4DBg8AAAABADIBAwBgATYACgAAEzQzMhcWFRQHBiYyFAcJCgkJHAEcGgYHCwwHCAwAAAEAMgEkAHMBZgANAAASNjMyFhUUBwYjIicmNTISDwwUCAcQEwgHAVQSDhIRCAkLCgwAAAABADIBLABzAW8ADQAAEzYzMhYUBgcGIyImNTQ8Cg4MEwcKBggNFQFjDBMcDAUDEw4KAAAAAQAyAS8AcAFrAA4AABM2MzIXFhUUBwYjIiY1NDsKDA0KCAgIDw0SAWQHCAYPDQoIDBIRAAEAMgE9AGgBcQANAAATNjMyFhUUBiMiJyY1NEMDBwwPEQsOBwUBcAENCwwQCwgIEwAAAAABADIBPAB4AYMADAAAEzYzMhcWFAcGIyI1NDkIEBILCgoJEiEBeAsNDBYMDCMPAAABADIBLAB7AXQADwAAEjYzMhYXFBYVFAYjIicmNTIYDgwTAwETDwQIGwFcGA8MAQQBDxgCCRcAAAEAMgFLAGsBhgAOAAASNjMyFxYVFAcGIyInJjUyEBAOBQYKCwgKCAoBdhAGBw0QCQgHBhAAAAAAAQAyAS8AdAF2AA4AABM2MzIXFhUUBwYiJyY1NEIMCQsKCA8OEgkKAW0JDAoKEAwLCQoNEgAAAAABADIBPAB3AYEADAAAEjYzMhcUFhUUBiMiNTIYDBoGARQQIQFvEh4BBQEOEiEAAAABADIBKgB5AXQACQAAEjQ2MzIWFAYjIjIYDQwWFgwNAUIaGBgaGAAAAQAyAS4AbAFmAA0AABM0JjU0NjMyFhQGIyImMwERCwwSEQwNCwFHAQMBCw8SFhALAAAAAAEAMgFAAGkBeQANAAASNjMyFhUUBwYjIicmNTISCQsRAwoQCAoIAW0MEAsGBhIHBg8AAAABADIBRAB3AYsACwAAEzYzMhYVFAYnJjU0PQsNChgYDx4BgAsSDw4YAwYbDgAAAAABADIBPgB1AYIADgAAEzYzMhYXFRQGIyInJjU0OwsNDBMDFA4KCQ4BdwsQDgUNFAYLEQwAAQAyAU8AaQGHAA4AABM2MzIXFhUUBiMiJyY1NDcGDBEIBxILCAoIAYEGCwoICRIICBEMAAEAMgElAG4BYQAKAAATFAYjIiY0NzYyFm4UCwwRBwcaFAFDCxMSGAgKEQAAAQAxAT4ARgFUAAYAABMmNhcWFCIyAQQHChIBSQcEAQEUAAAAAAAQAMYAAQAAAAAAAQALAAAAAQAAAAAAAgAHAAsAAQAAAAAAAwAuABIAAQAAAAAABAATAEAAAQAAAAAABQAPAFMAAQAAAAAABgATAGIAAQAAAAAACgAcAHUAAwABBAkAAQAWAJEAAwABBAkAAgAOAKcAAwABBAkAAwBcALUAAwABBAkABAAmAREAAwABBAkABQAeATcAAwABBAkABgAmAVUAAwABBAkACgA4AXsAAwABBAkAEAAWAJEAAwABBAkAEQAOAKdNaW5pbWFsZm9udFJlZ3VsYXJDYWxsaWdyYXBociA6IE1pbmltYWxmb250IFJlZ3VsYXIgOiAxOC0xMS0yMDE5TWluaW1hbGZvbnQgUmVndWxhclZlcnNpb24gMDAxLjAwMU1pbmltYWxmb250LVJlZ3VsYXJDcmVhdGVkIHdpdGggQ2FsbGlncmFwaHIuY29tAE0AaQBuAGkAbQBhAGwAZgBvAG4AdABSAGUAZwB1AGwAYQByAEMAYQBsAGwAaQBnAHIAYQBwAGgAcgAgADoAIABNAGkAbgBpAG0AYQBsAGYAbwBuAHQAIABSAGUAZwB1AGwAYQByACAAOgAgADEAOAAtADEAMQAtADIAMAAxADkATQBpAG4AaQBtAGEAbABmAG8AbgB0ACAAUgBlAGcAdQBsAGEAcgBWAGUAcgBzAGkAbwBuACAAMAAwADEALgAwADAAMQBNAGkAbgBpAG0AYQBsAGYAbwBuAHQALQBSAGUAZwB1AGwAYQByAEMAcgBlAGEAdABlAGQAIAB3AGkAdABoACAAQwBhAGwAbABpAGcAcgBhAHAAaAByAC4AYwBvAG0AAAAAAgAAAAAAAP+DADIAAAAAAAAAAAAAAAAAAAAAAAAAAABCAAAAAQACAQIAAwAEAAUACgAPABEAHQAeACIAJAAlACYAJwAoACkAKgArACwALQAuAC8AMAAxADIAMwA0ADUANgA3ADgAOQA6ADsAPAA9AEQARQBGAEcASABJAEoASwBMAE0ATgBPAFAAUQBSAFMAVABVAFYAVwBYAFkAWgBbAFwAXQEDAkNSBG5ic3AAAAABAAH//wAPAAAAAQAAAAA=);
      }
      .fontTester {
        font-family: plasmicTestFont;
        display: block;
        position: absolute;
        left: 0px;
        top: -1000px;
      }

      .StudioPlaceholder {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        grid-template:
          "topBar topBar topBar topBar" 47px
          "leftToolbar leftPanel canvasArea rightPanel" auto / 56px 302px auto 303px;
        grid-gap: 1px;
        background: #ddd;
        transition: all 250ms;
        pointer-events: none;
      }

      .StudioPlaceholder.fadeOut {
        opacity: 0;
        visibility: hidden;
      }

      .StudioPlaceholder.visible {
        display: grid;
      }

      .StudioPlaceholder > * {
        background: white;
      }

      .placeholder_topBar {
        grid-area: topBar;
        padding-left: 10px;
        display: flex;
        align-items: center;
      }
      .placeholder_icon {
        width: 40px;
        height: 40px;
        color: #c8c7c1; /* sand8 color */
      }
      .placeholder_leftToolbar {
        grid-area: leftToolbar;
      }
      .placeholder_leftPanel {
        grid-area: leftPanel;
      }
      .placeholder_canvasArea {
        grid-area: canvasArea;
        background: #f9f9f8;
      }
      .placeholder_rightPanel {
        grid-area: rightPanel;
      }

      @keyframes growUntilFull {
        0% {
          transform: translateX(-100%);
        }
        75% {
          transform: translateX(0);
        }
        100% {
          transform: translateX(100%);
        }
      }

      @keyframes rotateColors {
        100% {
          background: hsl(0, 80%, 70%);
        }
        80% {
          background: hsl(51, 80%, 70%);
        }
        60% {
          background: hsl(102, 80%, 70%);
        }
        40% {
          background: hsl(153, 80%, 70%);
        }
        20% {
          background: hsl(204, 80%, 70%);
        }
        0% {
          background: hsl(255, 80%, 70%);
        }
      }

      .placeholder_loading {
        position: fixed;
        top: 47px;
        left: 0;
        right: 0;
        height: 3px;
        animation: growUntilFull 10s infinite, rotateColors 10s infinite;
        animation-timing-function: ease-in-out;
      }

      .placeholder_loading.placeholder_loading--fast {
        animation-duration: 3s;
      }
    </style>
    <link id="shortcutIcon" rel="shortcut icon" />
    <link
      href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&family=Inter:wght@300;400;500;600;700;900&display=swap"
      rel="stylesheet"
      crossorigin="anonymous"
    />
    <link id="normalizeCss" rel="stylesheet" type="text/css" crossorigin="anonymous" />
    <!-- Inject URLs based on origin -->
    <script>
      document.getElementById("shortcutIcon").href = `${origin}/favicon.ico`;
      document.getElementById(
        "normalizeCss"
      ).href = `${origin}/static/css/normalize.css`;
    </script>

    <title>Plasmic</title>
    <!-- For style tokens and mixins -->
    <style id="globalCssVars"></style>

    <!-- For monaco editor
      see: "Using with webpack" https://github.com/superRaytin/react-monaco-editor
    -->
    <base href="/" />
  </head>
  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>
    <div id="root"></div>
    <div class="app-container"></div>
    <!-- This is to style the downshift-managed Ant dropdown specially. -->
    <div class="xselect"></div>
    <!-- This is used to test if a font is available or not -->
    <div class="fontTester" style="display: none">Plasmic</div>
    <!-- This is used to upload the file for "import project" menu -->
    <input
      class="hidden-file-selector"
      style="display: none"
      type="file"
      accept=".json,.txt"
    />
    <a class="hidden-file-download" href="" style="display: none"
      >hidden link</a
    >
    <div class="StudioPlaceholder">
      <div class="placeholder_topBar">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="placeholder_icon"
          fill="none"
          viewBox="0 0 32 32"
          role="img"
        >
          <path
            d="M3.2 22C3.2 14.93 8.93 9.2 16 9.2S28.8 14.93 28.8 22H32c0-8.837-7.163-16-16-16S0 13.163 0 22h3.2z"
            fill="currentColor"
          ></path>

          <path
            d="M24 22a8 8 0 10-16 0H4.8c0-6.185 5.015-11.2 11.2-11.2S27.2 15.815 27.2 22H24z"
            fill="currentColor"
          ></path>

          <path
            d="M12.8 22a3.2 3.2 0 016.4 0h3.2a6.4 6.4 0 10-12.8 0h3.2z"
            fill="currentColor"
          ></path>
        </svg>
      </div>
      <div class="placeholder_leftToolbar"></div>
      <div class="placeholder_leftPanel"></div>
      <div class="placeholder_canvasArea"></div>
      <div class="placeholder_rightPanel"></div>
      <div class="placeholder_loading"></div>
    </div>
    <script>
      function maybeShowStudioPlaceholder() {
        if (/^\/projects\/[a-z0-9]+/.test(window.location.pathname)) {
          document.querySelector(".StudioPlaceholder").classList.add("visible");
        }
      }

      maybeShowStudioPlaceholder();
    </script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
