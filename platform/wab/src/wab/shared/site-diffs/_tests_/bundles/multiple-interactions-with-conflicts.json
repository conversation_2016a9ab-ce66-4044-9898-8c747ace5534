{"branches": [{"id": "h6vE2f1bx9MYpYFYi7XtiF", "name": "branch"}], "pkgVersions": [{"id": "3762f263-83a1-414e-a790-1c7245589c22", "data": {"root": "dJSk9ik2yvlg", "map": {"vXAWd8bPgKi0": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "y5NGKQw9F5o9": {"name": "Default Typography", "rs": {"__ref": "vXAWd8bPgKi0"}, "preview": null, "uuid": "3R4cuBGEDlM8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nvOwAxYxyI0F": {"values": {}, "mixins": [], "__type": "RuleSet"}, "X2reH9BZqOlB": {"rs": {"__ref": "nvOwAxYxyI0F"}, "__type": "ThemeLayoutSettings"}, "Hs_UqoJr-eyX": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "4awPc-MnpcKq": {"name": "Default \"h1\"", "rs": {"__ref": "Hs_UqoJr-eyX"}, "preview": null, "uuid": "EzefskVUfGaP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AJWk87qHq2Av": {"selector": "h1", "style": {"__ref": "4awPc-MnpcKq"}, "__type": "ThemeStyle"}, "drsq-qEAFZzA": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "vksxzTJEl8iu": {"name": "Default \"h2\"", "rs": {"__ref": "drsq-qEAFZzA"}, "preview": null, "uuid": "XrwR3KgR8z3v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PB3jyOvyvEQt": {"selector": "h2", "style": {"__ref": "vksxzTJEl8iu"}, "__type": "ThemeStyle"}, "TlVZrcyBffD6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "AI3On-Vfl0H9": {"name": "Default \"h3\"", "rs": {"__ref": "TlVZrcyBffD6"}, "preview": null, "uuid": "iQEa6AngIiUw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5eiliG1YEC7Q": {"selector": "h3", "style": {"__ref": "AI3On-Vfl0H9"}, "__type": "ThemeStyle"}, "4idbLAZjyArM": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "MCOVCXCUqdKZ": {"name": "Default \"h4\"", "rs": {"__ref": "4idbLAZjyArM"}, "preview": null, "uuid": "mPtte3CSj-Yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "enGDfzqLPjPv": {"selector": "h4", "style": {"__ref": "MCOVCXCUqdKZ"}, "__type": "ThemeStyle"}, "hr1bVhUyEGWn": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "pqqa6tUzR55a": {"name": "Default \"h5\"", "rs": {"__ref": "hr1bVhUyEGWn"}, "preview": null, "uuid": "7LcwFXhQk5a8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "azPZH50q6sF0": {"selector": "h5", "style": {"__ref": "pqqa6tUzR55a"}, "__type": "ThemeStyle"}, "EnFzptIht708": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "zri_laG0CHZC": {"name": "Default \"h6\"", "rs": {"__ref": "EnFzptIht708"}, "preview": null, "uuid": "a3Bao3RaPbhy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "XsYUymE2Rsg0": {"selector": "h6", "style": {"__ref": "zri_laG0CHZC"}, "__type": "ThemeStyle"}, "XEwGhsDAIYPC": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "LPzZwdaE2RFl": {"name": "Default \"a\"", "rs": {"__ref": "XEwGhsDAIYPC"}, "preview": null, "uuid": "ckz3c5T8Ez3P", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "pkAGqzMkw9LW": {"selector": "a", "style": {"__ref": "LPzZwdaE2RFl"}, "__type": "ThemeStyle"}, "gaE-R9Mw5FIM": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "P9sFQ2dwEmde": {"name": "Default \"a:hover\"", "rs": {"__ref": "gaE-R9Mw5FIM"}, "preview": null, "uuid": "Qr-i4K41wtVk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "w07l_gP3TkP3": {"selector": "a:hover", "style": {"__ref": "P9sFQ2dwEmde"}, "__type": "ThemeStyle"}, "mssx7nQsGXRE": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "b8w6Mt8mhqRH": {"name": "Default \"blockquote\"", "rs": {"__ref": "mssx7nQsGXRE"}, "preview": null, "uuid": "EQdsKEndloVw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZCW7KXcUOsrS": {"selector": "blockquote", "style": {"__ref": "b8w6Mt8mhqRH"}, "__type": "ThemeStyle"}, "u7m1CZgZGmPr": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "AStpDeyW8KFj": {"name": "Default \"code\"", "rs": {"__ref": "u7m1CZgZGmPr"}, "preview": null, "uuid": "sJeNqebPIR-k", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "y193ORwYtouk": {"selector": "code", "style": {"__ref": "AStpDeyW8KFj"}, "__type": "ThemeStyle"}, "ng0q1hm74gHY": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "zxBTK9n7OqcY": {"name": "Default \"pre\"", "rs": {"__ref": "ng0q1hm74gHY"}, "preview": null, "uuid": "OWGZX741fU3O", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BzTWjwkKM2RQ": {"selector": "pre", "style": {"__ref": "zxBTK9n7OqcY"}, "__type": "ThemeStyle"}, "BlPSy77Lk4V5": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "6LLUqMRMdhRa": {"name": "Default \"ol\"", "rs": {"__ref": "BlPSy77Lk4V5"}, "preview": null, "uuid": "ketdRU7YjHBf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vSCgmy9GM9cA": {"selector": "ol", "style": {"__ref": "6LLUqMRMdhRa"}, "__type": "ThemeStyle"}, "BRn6jMwiJMJs": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "NcNX4L8At4oe": {"name": "Default \"ul\"", "rs": {"__ref": "BRn6jMwiJMJs"}, "preview": null, "uuid": "Mu8yrk2PWZqZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "z7RrOVKfALsx": {"selector": "ul", "style": {"__ref": "NcNX4L8At4oe"}, "__type": "ThemeStyle"}, "Hu5NDxBoYOgd": {"defaultStyle": {"__ref": "y5NGKQw9F5o9"}, "styles": [{"__ref": "AJWk87qHq2Av"}, {"__ref": "PB3jyOvyvEQt"}, {"__ref": "5eiliG1YEC7Q"}, {"__ref": "enGDfzqLPjPv"}, {"__ref": "azPZH50q6sF0"}, {"__ref": "XsYUymE2Rsg0"}, {"__ref": "pkAGqzMkw9LW"}, {"__ref": "w07l_gP3TkP3"}, {"__ref": "ZCW7KXcUOsrS"}, {"__ref": "y193ORwYtouk"}, {"__ref": "BzTWjwkKM2RQ"}, {"__ref": "vSCgmy9GM9cA"}, {"__ref": "z7RrOVKfALsx"}], "layout": {"__ref": "X2reH9BZqOlB"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "xDY7LiPYcl6f": {"name": "text", "__type": "Text"}, "hOX_Qid5bt93": {"name": "Screen", "uuid": "NOs-k8uS2CO-", "__type": "Var"}, "RgEPMW-YzGWe": {"type": {"__ref": "xDY7LiPYcl6f"}, "variable": {"__ref": "hOX_Qid5bt93"}, "uuid": "Bx4lKPuI_VH4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "AlrByhDbUDGr": {"type": "global-screen", "param": {"__ref": "RgEPMW-YzGWe"}, "uuid": "UG8KZt4KsYde", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "0yWkHpaCZJOs": {"uuid": "fr2o5Ooddu6G", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zljjFmQgmtIw": {"components": [{"__ref": "g7Ii99ThVWlY"}, {"__ref": "3xCfOSrV1RS3"}, {"__ref": "7Ljh68sM0JVA"}], "arenas": [], "pageArenas": [{"__ref": "oRCTlFsf32CM"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "AlrByhDbUDGr"}], "userManagedFonts": [], "globalVariant": {"__ref": "0yWkHpaCZJOs"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "Hu5NDxBoYOgd"}], "activeTheme": {"__ref": "Hu5NDxBoYOgd"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "AlrByhDbUDGr"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "g7Ii99ThVWlY": {"uuid": "ulXkJIho0yIH", "name": "hostless-plasmic-head", "params": [{"__ref": "n1uVfgkuyImr"}, {"__ref": "FrrPxqMps6Gu"}, {"__ref": "cmCM6brf5Re2"}, {"__ref": "0KWyWZj6oOGb"}], "states": [], "tplTree": {"__ref": "lUBuUI52mSp-"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "uKR-7wp06LVn"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "gi4wtnpWCtuh"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "3xCfOSrV1RS3": {"uuid": "mEPQOcHXQZ1T", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "ykne7coBH4SB"}, {"__ref": "HH1Q8GOQ2HtG"}, {"__ref": "R-E3F5lia7Va"}, {"__ref": "vbnNaMXUDJ9u"}, {"__ref": "bdqtOLdAZ5RS"}], "states": [], "tplTree": {"__ref": "vry5UbAkDNjD"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "cACyJhO8C7U1"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "hlUrcwpUQkrJ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "n1uVfgkuyImr": {"type": {"__ref": "uVJ55-AszkGF"}, "variable": {"__ref": "750EStEfP0Ru"}, "uuid": "RzNARPSJoMTc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "FrrPxqMps6Gu": {"type": {"__ref": "LWUbXAeSIyjm"}, "variable": {"__ref": "KLMfPKeGKhYA"}, "uuid": "hPIpbBZLcwjd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "cmCM6brf5Re2": {"type": {"__ref": "GYpWZCnk5lpq"}, "variable": {"__ref": "wJbQfITGsT7C"}, "uuid": "bQjybmykl6i5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "0KWyWZj6oOGb": {"type": {"__ref": "UEclEogHsdDa"}, "variable": {"__ref": "NscUMP8QkqvC"}, "uuid": "LAvugjsw-W01", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "lUBuUI52mSp-": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "APGe9p--6<PERSON>y", "parent": null, "locked": null, "vsettings": [{"__ref": "Z1tzgwuMJaOM"}], "__type": "TplTag"}, "uKR-7wp06LVn": {"uuid": "7nkRFUKf28qh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gi4wtnpWCtuh": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "ykne7coBH4SB": {"type": {"__ref": "bs8KwBeBZpQ7"}, "variable": {"__ref": "R68INM_k6flp"}, "uuid": "8bMYkfjKU4fG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "HH1Q8GOQ2HtG": {"type": {"__ref": "D3hPKy6u9se_"}, "variable": {"__ref": "f8ht-9V4R8wa"}, "uuid": "LvqktbF-8fOs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R-E3F5lia7Va": {"type": {"__ref": "ASzA85kQRDhN"}, "tplSlot": {"__ref": "1N77DotxMSZt"}, "variable": {"__ref": "eCAvy287bxaR"}, "uuid": "e8gBTyWuDGCj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "vbnNaMXUDJ9u": {"type": {"__ref": "KIsTxGyhupAQ"}, "variable": {"__ref": "_7OWLc1w_Rdk"}, "uuid": "Jyrjcn5ehL9S", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "bdqtOLdAZ5RS": {"type": {"__ref": "uSzS2Y-Mtt2b"}, "variable": {"__ref": "khuleB1uIswB"}, "uuid": "7S-tLiMhOv9P", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "vry5UbAkDNjD": {"tag": "div", "name": null, "children": [{"__ref": "1N77DotxMSZt"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T4saJBfIHHPJ", "parent": null, "locked": null, "vsettings": [{"__ref": "9VwJiPcKOnwg"}], "__type": "TplTag"}, "cACyJhO8C7U1": {"uuid": "v3nu6dI1utno", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "hlUrcwpUQkrJ": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "uVJ55-AszkGF": {"name": "text", "__type": "Text"}, "750EStEfP0Ru": {"name": "title", "uuid": "J5Wi5nFko-Sc", "__type": "Var"}, "LWUbXAeSIyjm": {"name": "text", "__type": "Text"}, "KLMfPKeGKhYA": {"name": "description", "uuid": "-QiW1YvpUQmQ", "__type": "Var"}, "GYpWZCnk5lpq": {"name": "img", "__type": "Img"}, "wJbQfITGsT7C": {"name": "image", "uuid": "_Avda8LqX4DH", "__type": "Var"}, "UEclEogHsdDa": {"name": "text", "__type": "Text"}, "NscUMP8QkqvC": {"name": "canonical", "uuid": "Y3K_wrbFyAsN", "__type": "Var"}, "Z1tzgwuMJaOM": {"variants": [{"__ref": "uKR-7wp06LVn"}], "args": [], "attrs": {}, "rs": {"__ref": "7OaDYd_WUJyk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bs8KwBeBZpQ7": {"name": "any", "__type": "AnyType"}, "R68INM_k6flp": {"name": "dataOp", "uuid": "d3DrXP07JvXZ", "__type": "Var"}, "D3hPKy6u9se_": {"name": "text", "__type": "Text"}, "f8ht-9V4R8wa": {"name": "name", "uuid": "oQ0Jn64DVczH", "__type": "Var"}, "ASzA85kQRDhN": {"name": "renderFunc", "params": [{"__ref": "2DBwyhvZ7KqD"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "eCAvy287bxaR": {"name": "children", "uuid": "w83wyNHRlHKg", "__type": "Var"}, "KIsTxGyhupAQ": {"name": "num", "__type": "<PERSON><PERSON>"}, "_7OWLc1w_Rdk": {"name": "pageSize", "uuid": "9g_REvGUyg4G", "__type": "Var"}, "uSzS2Y-Mtt2b": {"name": "num", "__type": "<PERSON><PERSON>"}, "khuleB1uIswB": {"name": "pageIndex", "uuid": "7g72F55KZoEh", "__type": "Var"}, "1N77DotxMSZt": {"param": {"__ref": "R-E3F5lia7Va"}, "defaultContents": [], "uuid": "AqM0zePZIska", "parent": {"__ref": "vry5UbAkDNjD"}, "locked": null, "vsettings": [{"__ref": "ySAw1UNTW9BK"}], "__type": "TplSlot"}, "9VwJiPcKOnwg": {"variants": [{"__ref": "cACyJhO8C7U1"}], "args": [], "attrs": {}, "rs": {"__ref": "98ahuxsq-OwR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7OaDYd_WUJyk": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "2DBwyhvZ7KqD": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "_GI1UsF70C6b"}, "__type": "ArgType"}, "ySAw1UNTW9BK": {"variants": [{"__ref": "cACyJhO8C7U1"}], "args": [], "attrs": {}, "rs": {"__ref": "g9_69llTvJOc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "98ahuxsq-OwR": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "_GI1UsF70C6b": {"name": "any", "__type": "AnyType"}, "g9_69llTvJOc": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7Ljh68sM0JVA": {"uuid": "p2wrA0DvNTeq", "name": "InteractionsWithConflicts", "params": [{"__ref": "fIRYFfyf0ast"}, {"__ref": "KyVJseUgVHpZ"}, {"__ref": "zhjktiGWzoUG"}, {"__ref": "WbrFcWmcoUB_"}], "states": [{"__ref": "XY80qVQGoiRa"}, {"__ref": "qO9MbtCTqEv_"}], "tplTree": {"__ref": "XsKZIdI2AtF5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "p8GuTLtEem0_"}], "variantGroups": [{"__ref": "9qt1ZQASVpH6"}], "pageMeta": {"__ref": "POIrFjImwbTJ"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "oRCTlFsf32CM": {"component": {"__ref": "7Ljh68sM0JVA"}, "matrix": {"__ref": "OY9mnrv24PJx"}, "customMatrix": {"__ref": "2FNbk3rq5J0j"}, "__type": "PageArena"}, "XsKZIdI2AtF5": {"tag": "div", "name": null, "children": [{"__ref": "EZWaccNxdnkV"}, {"__ref": "K6gFAxw5ZKuR"}, {"__ref": "HPdyaySpXdwJ"}, {"__ref": "N5ZMx_Kc2uUR"}, {"__ref": "pKhaSswZAftK"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "w6CZIL1XMhD3", "parent": null, "locked": null, "vsettings": [{"__ref": "7OzbyS28ybNb"}, {"__ref": "3IjHOkHzJXqJ"}], "__type": "TplTag"}, "p8GuTLtEem0_": {"uuid": "253hBI0BbQwC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "POIrFjImwbTJ": {"path": "/multiple-interactions-conflict", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "OY9mnrv24PJx": {"rows": [{"__ref": "lF6QbL5vE0jO"}, {"__ref": "0wO-T8WYOf5A"}], "__type": "ArenaFrameGrid"}, "2FNbk3rq5J0j": {"rows": [{"__ref": "UnhMOBsTJ957"}], "__type": "ArenaFrameGrid"}, "7OzbyS28ybNb": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "7MbdgIdlX4DT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lF6QbL5vE0jO": {"cols": [{"__ref": "EPzwdG9YVmGj"}, {"__ref": "Pm4aZyBdlFtF"}], "rowKey": {"__ref": "p8GuTLtEem0_"}, "__type": "ArenaFrameRow"}, "UnhMOBsTJ957": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "7MbdgIdlX4DT": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "EPzwdG9YVmGj": {"frame": {"__ref": "S1YltGZ49B1L"}, "cellKey": null, "__type": "ArenaFrameCell"}, "Pm4aZyBdlFtF": {"frame": {"__ref": "nKS6IqxPh_yP"}, "cellKey": null, "__type": "ArenaFrameCell"}, "S1YltGZ49B1L": {"uuid": "AL2HQbAYnyXR", "width": 1366, "height": 768, "container": {"__ref": "6rf_x85VN45q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "p8GuTLtEem0_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "nKS6IqxPh_yP": {"uuid": "Gm3IoGULp_Ic", "width": 414, "height": 736, "container": {"__ref": "Zl4zmiPFgI6C"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "p8GuTLtEem0_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6rf_x85VN45q": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "ZwQtVEBcEDVT", "parent": null, "locked": null, "vsettings": [{"__ref": "IkRLOr9Ulba-"}], "__type": "TplComponent"}, "Zl4zmiPFgI6C": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "J29pwS5udLW_", "parent": null, "locked": null, "vsettings": [{"__ref": "_os-itLRBpon"}], "__type": "TplComponent"}, "IkRLOr9Ulba-": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "yhJIUmtNSumd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_os-itLRBpon": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "MQIoDemHMd8w"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yhJIUmtNSumd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MQIoDemHMd8w": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EZWaccNxdnkV": {"tag": "div", "name": "changeState", "children": [{"__ref": "pnczuF3s9uTF"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "w5y_uwR0Mg9B", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "dDcYYLMDZ6yx"}], "__type": "TplTag"}, "dDcYYLMDZ6yx": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "XmYk2FS0d6Bw"}}, "rs": {"__ref": "gILvTGFZ-8y3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gILvTGFZ-8y3": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XmYk2FS0d6Bw": {"interactions": [{"__ref": "iJ5zTTpe4EIQ"}], "__type": "EventHandler"}, "iJ5zTTpe4EIQ": {"interactionName": "Update text", "actionName": "updateVariable", "args": [{"__ref": "hQK4RGptiCCr"}, {"__ref": "Ch8xRu-kEbYD"}, {"__ref": "5NgRPCiS5azb"}], "condExpr": null, "conditionalMode": "always", "uuid": "kAdeEWCmtJdL", "parent": {"__ref": "XmYk2FS0d6Bw"}, "__type": "Interaction"}, "XY80qVQGoiRa": {"param": {"__ref": "fIRYFfyf0ast"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "KyVJseUgVHpZ"}, "tplNode": null, "implicitState": null, "__type": "State"}, "fIRYFfyf0ast": {"type": {"__ref": "TNJKFgkoosiB"}, "state": {"__ref": "XY80qVQGoiRa"}, "variable": {"__ref": "ep0eLlUt5WMl"}, "uuid": "N8MpIbwTR9GH", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "naIx6KwBo1yM"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "KyVJseUgVHpZ": {"type": {"__ref": "oA_9u6g2COck"}, "state": {"__ref": "XY80qVQGoiRa"}, "variable": {"__ref": "F0jyajA6CKSQ"}, "uuid": "1Nx-w-iTs9eP", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "TNJKFgkoosiB": {"name": "text", "__type": "Text"}, "ep0eLlUt5WMl": {"name": "text", "uuid": "mM_b_ZDr-3yY", "__type": "Var"}, "naIx6KwBo1yM": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "oA_9u6g2COck": {"name": "func", "params": [{"__ref": "HFuOIXs1bYMP"}], "__type": "FunctionType"}, "F0jyajA6CKSQ": {"name": "On text Change", "uuid": "RLApZ9ghOhbb", "__type": "Var"}, "HFuOIXs1bYMP": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "gDbsvNW_RP2N"}, "__type": "ArgType"}, "gDbsvNW_RP2N": {"name": "text", "__type": "Text"}, "pnczuF3s9uTF": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "_ZPGUC-J4ztv", "parent": {"__ref": "EZWaccNxdnkV"}, "locked": null, "vsettings": [{"__ref": "i1ExyMCyGgFu"}], "__type": "TplTag"}, "i1ExyMCyGgFu": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "lIOgxH5xwmVL"}, "dataCond": null, "dataRep": null, "text": {"__ref": "u9nu2ogdP5E9"}, "columnsConfig": null, "__type": "VariantSetting"}, "lIOgxH5xwmVL": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "DXP-44w-9xge": {"path": ["$state", "text"], "fallback": null, "__type": "ObjectPath"}, "3Dezytq1Vw0d": {"code": "0", "fallback": null, "__type": "CustomCode"}, "hQK4RGptiCCr": {"name": "variable", "expr": {"__ref": "DXP-44w-9xge"}, "__type": "NameArg"}, "Ch8xRu-kEbYD": {"name": "operation", "expr": {"__ref": "3Dezytq1Vw0d"}, "__type": "NameArg"}, "5NgRPCiS5azb": {"name": "value", "expr": {"__ref": "8FMnZAdvJO0g"}, "__type": "NameArg"}, "8FMnZAdvJO0g": {"code": "(\"First version main\")", "fallback": null, "__type": "CustomCode"}, "K6gFAxw5ZKuR": {"tag": "div", "name": "updateVariant", "children": [{"__ref": "wD2enrO76jdP"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_9gNnw7STq8U", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "gwq4iXAAg0e-"}], "__type": "TplTag"}, "wD2enrO76jdP": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "0uMDnNoPGFHC", "parent": {"__ref": "K6gFAxw5ZKuR"}, "locked": null, "vsettings": [{"__ref": "h9PTpMSVpj-k"}], "__type": "TplTag"}, "gwq4iXAAg0e-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "DmeygiRO4J0X"}}, "rs": {"__ref": "43b4n-sARa5E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "h9PTpMSVpj-k": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "itf8CUzKB1Ok"}, "dataCond": null, "dataRep": null, "text": {"__ref": "RvSmFzLvgQxI"}, "columnsConfig": null, "__type": "VariantSetting"}, "DmeygiRO4J0X": {"interactions": [{"__ref": "D08q6jm_Wy1U"}], "__type": "EventHandler"}, "43b4n-sARa5E": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "itf8CUzKB1Ok": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "D08q6jm_Wy1U": {"interactionName": "Update highlight", "actionName": "updateVariant", "args": [{"__ref": "kJwYcWDQ-Mpf"}, {"__ref": "PAZqV421LAah"}, {"__ref": "Wlu1B4SD3P9R"}], "condExpr": null, "conditionalMode": "always", "uuid": "XO1epSq8uFEw", "parent": {"__ref": "DmeygiRO4J0X"}, "__type": "Interaction"}, "qO9MbtCTqEv_": {"variantGroup": {"__ref": "9qt1ZQASVpH6"}, "param": {"__ref": "zhjktiGWzoUG"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "WbrFcWmcoUB_"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "9qt1ZQASVpH6": {"type": "component", "param": {"__ref": "zhjktiGWzoUG"}, "linkedState": {"__ref": "qO9MbtCTqEv_"}, "uuid": "ula71m0Ti7ut", "variants": [{"__ref": "uuEPWZXblHLL"}], "multi": false, "__type": "ComponentVariantGroup"}, "zhjktiGWzoUG": {"type": {"__ref": "kKtsdrFffrTt"}, "state": {"__ref": "qO9MbtCTqEv_"}, "variable": {"__ref": "1v6Qm-CtgrXY"}, "uuid": "U4CjUpH4rgpx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "WbrFcWmcoUB_": {"type": {"__ref": "nhuK7OSYQCmA"}, "state": {"__ref": "qO9MbtCTqEv_"}, "variable": {"__ref": "om7SVM2GPPro"}, "uuid": "AlZBow1bK1sU", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "0wO-T8WYOf5A": {"cols": [{"__ref": "Z-r7u-1kMaWk"}, {"__ref": "l1PvdcEyAklQ"}], "rowKey": {"__ref": "uuEPWZXblHLL"}, "__type": "ArenaFrameRow"}, "3IjHOkHzJXqJ": {"variants": [{"__ref": "uuEPWZXblHLL"}], "args": [], "attrs": {}, "rs": {"__ref": "4L9lTzU2a54T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uuEPWZXblHLL": {"uuid": "B93OYtxxAPGB", "name": "highlight", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "9qt1ZQASVpH6"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kKtsdrFffrTt": {"name": "text", "__type": "Text"}, "1v6Qm-CtgrXY": {"name": "highlight", "uuid": "uTQp2T7tkyxk", "__type": "Var"}, "nhuK7OSYQCmA": {"name": "func", "params": [{"__ref": "t5a-rifdBdqo"}], "__type": "FunctionType"}, "om7SVM2GPPro": {"name": "On highlight change", "uuid": "q_w6sN3mhu5L", "__type": "Var"}, "Z-r7u-1kMaWk": {"frame": {"__ref": "GSJScN6fre41"}, "cellKey": null, "__type": "ArenaFrameCell"}, "l1PvdcEyAklQ": {"frame": {"__ref": "gkaYgMyddH6x"}, "cellKey": null, "__type": "ArenaFrameCell"}, "4L9lTzU2a54T": {"values": {}, "mixins": [], "__type": "RuleSet"}, "t5a-rifdBdqo": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "HZ3A69IJ221a"}, "__type": "ArgType"}, "GSJScN6fre41": {"uuid": "t8XSwviG9fwP", "width": 1366, "height": 768, "container": {"__ref": "a8F9KmpsuUpq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uuEPWZXblHLL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gkaYgMyddH6x": {"uuid": "HpY2LzfFZhh7", "width": 414, "height": 736, "container": {"__ref": "RQtIZcKUrUWU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uuEPWZXblHLL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "HZ3A69IJ221a": {"name": "any", "__type": "AnyType"}, "a8F9KmpsuUpq": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "lLfmpSQaoNCC", "parent": null, "locked": null, "vsettings": [{"__ref": "R9OSEhajmjrm"}], "__type": "TplComponent"}, "RQtIZcKUrUWU": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "Iw5ipkZW0SzB", "parent": null, "locked": null, "vsettings": [{"__ref": "r-ecdOJe_dYd"}], "__type": "TplComponent"}, "R9OSEhajmjrm": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "x2Xqn1qkWhnY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r-ecdOJe_dYd": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "xIv75oCOps9_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x2Xqn1qkWhnY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xIv75oCOps9_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RvSmFzLvgQxI": {"markers": [], "text": "Update variant", "__type": "RawText"}, "kJwYcWDQ-Mpf": {"name": "vgroup", "expr": {"__ref": "Q77Xg1tClHwq"}, "__type": "NameArg"}, "PAZqV421LAah": {"name": "operation", "expr": {"__ref": "epunppDqA2cb"}, "__type": "NameArg"}, "Wlu1B4SD3P9R": {"name": "value", "expr": {"__ref": "82-FPj8lsSPT"}, "__type": "NameArg"}, "Q77Xg1tClHwq": {"variable": {"__ref": "1v6Qm-CtgrXY"}, "__type": "VarRef"}, "epunppDqA2cb": {"code": "2", "fallback": null, "__type": "CustomCode"}, "82-FPj8lsSPT": {"variants": [{"__ref": "uuEPWZXblHLL"}], "__type": "VariantsRef"}, "HPdyaySpXdwJ": {"tag": "div", "name": "goToPage", "children": [{"__ref": "7E-JUmYZf6Nq"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2hs7kMnlIZZ2", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "TddoAfyNgdHZ"}], "__type": "TplTag"}, "7E-JUmYZf6Nq": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "z8OMf2LlwGco", "parent": {"__ref": "HPdyaySpXdwJ"}, "locked": null, "vsettings": [{"__ref": "FPQxgF9HkzB2"}], "__type": "TplTag"}, "TddoAfyNgdHZ": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "DgNXbTM3JNS5"}}, "rs": {"__ref": "5jSvl8G-UZuA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FPQxgF9HkzB2": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "F-lXmIAl3wZS"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ngjsfbb7cT6U"}, "columnsConfig": null, "__type": "VariantSetting"}, "DgNXbTM3JNS5": {"interactions": [{"__ref": "vicauwZenhIL"}], "__type": "EventHandler"}, "5jSvl8G-UZuA": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "F-lXmIAl3wZS": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "vicauwZenhIL": {"interactionName": "Go to /other-page", "actionName": "navigation", "args": [{"__ref": "DHNjdQ4SVuSy"}], "condExpr": null, "conditionalMode": "always", "uuid": "Yr61esKGQBGB", "parent": {"__ref": "DgNXbTM3JNS5"}, "__type": "Interaction"}, "DHNjdQ4SVuSy": {"name": "destination", "expr": {"__ref": "L5jGZlEOR9_Z"}, "__type": "NameArg"}, "L5jGZlEOR9_Z": {"code": "\"/other-page\"", "fallback": null, "__type": "CustomCode"}, "ngjsfbb7cT6U": {"markers": [], "text": "Go to page", "__type": "RawText"}, "N5ZMx_Kc2uUR": {"tag": "div", "name": "runCode", "children": [{"__ref": "jTEtMAnxDQdl"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "giD5qkzgKg3e", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "xuEQJTBFSG2-"}], "__type": "TplTag"}, "jTEtMAnxDQdl": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kN553mK6AkLE", "parent": {"__ref": "N5ZMx_Kc2uUR"}, "locked": null, "vsettings": [{"__ref": "MzvFAUOUZozE"}], "__type": "TplTag"}, "xuEQJTBFSG2-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "8Nx3vNfefYWX"}}, "rs": {"__ref": "dJukb5mAScYw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MzvFAUOUZozE": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "7qzw98Ilvm2K"}, "dataCond": null, "dataRep": null, "text": {"__ref": "GqlaaVHpEmcs"}, "columnsConfig": null, "__type": "VariantSetting"}, "8Nx3vNfefYWX": {"interactions": [{"__ref": "Ih1kyVOvymwW"}], "__type": "EventHandler"}, "dJukb5mAScYw": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "7qzw98Ilvm2K": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "Ih1kyVOvymwW": {"interactionName": "Run code", "actionName": "customFunction", "args": [{"__ref": "nnEzK2IbOX4L"}], "condExpr": null, "conditionalMode": "always", "uuid": "SYkc9rKbodI8", "parent": {"__ref": "8Nx3vNfefYWX"}, "__type": "Interaction"}, "nnEzK2IbOX4L": {"name": "customFunction", "expr": {"__ref": "7VhF6Zalm67i"}, "__type": "NameArg"}, "7VhF6Zalm67i": {"argNames": [], "bodyExpr": {"__ref": "GRO2Q-XmUY2Q"}, "__type": "FunctionExpr"}, "GRO2Q-XmUY2Q": {"code": "(console.log(\"Running in main first publish\");)", "fallback": null, "__type": "CustomCode"}, "GqlaaVHpEmcs": {"markers": [], "text": "Run code", "__type": "RawText"}, "pKhaSswZAftK": {"tag": "div", "name": "combinedActions", "children": [{"__ref": "Q8PkCF1LZhwh"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "rWSXmB3UAgZ1", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "qn0rgdwUMMk-"}], "__type": "TplTag"}, "Q8PkCF1LZhwh": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "fVVJBvnZ-kuW", "parent": {"__ref": "pKhaSswZAftK"}, "locked": null, "vsettings": [{"__ref": "-66CZ_OV-BvR"}], "__type": "TplTag"}, "qn0rgdwUMMk-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "GQQ-iKl75wpm"}}, "rs": {"__ref": "ikm4uVHVt3UN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-66CZ_OV-BvR": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "FNZZTtQeSfpx"}, "dataCond": null, "dataRep": null, "text": {"__ref": "MatqRE1k1Wrz"}, "columnsConfig": null, "__type": "VariantSetting"}, "GQQ-iKl75wpm": {"interactions": [{"__ref": "MPIlG2Mc3il1"}, {"__ref": "8bSPzkFvZG6u"}], "__type": "EventHandler"}, "ikm4uVHVt3UN": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "FNZZTtQeSfpx": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "MPIlG2Mc3il1": {"interactionName": "Update text variable", "actionName": "updateVariable", "args": [{"__ref": "wg3iw1CiCGYp"}, {"__ref": "tH-nZZ-locf9"}, {"__ref": "rZFdf8xnGRNQ"}], "condExpr": null, "conditionalMode": "always", "uuid": "rLtmlbx4Hsi8", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "MatqRE1k1Wrz": {"markers": [], "text": "Combined of actions", "__type": "RawText"}, "u9nu2ogdP5E9": {"markers": [], "text": "Change state", "__type": "RawText"}, "6TQNcnbCKfvz": {"path": ["$state", "text"], "fallback": null, "__type": "ObjectPath"}, "nbw7c-JfemQX": {"code": "0", "fallback": null, "__type": "CustomCode"}, "wg3iw1CiCGYp": {"name": "variable", "expr": {"__ref": "6TQNcnbCKfvz"}, "__type": "NameArg"}, "tH-nZZ-locf9": {"name": "operation", "expr": {"__ref": "nbw7c-JfemQX"}, "__type": "NameArg"}, "rZFdf8xnGRNQ": {"name": "value", "expr": {"__ref": "_al9qs-zYrTN"}, "__type": "NameArg"}, "_al9qs-zYrTN": {"code": "(\"Change value in first publish in combined actions\")", "fallback": null, "__type": "CustomCode"}, "8bSPzkFvZG6u": {"interactionName": "Update variant", "actionName": "updateVariant", "args": [{"__ref": "5-wITgViDNVc"}, {"__ref": "aqmQobQT9b_6"}, {"__ref": "F2kfP0BjbmxP"}], "condExpr": null, "conditionalMode": "always", "uuid": "akGHdrj_zSw7", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "5-wITgViDNVc": {"name": "vgroup", "expr": {"__ref": "snt8j58CEX4u"}, "__type": "NameArg"}, "aqmQobQT9b_6": {"name": "operation", "expr": {"__ref": "aCr5nWA-gKfT"}, "__type": "NameArg"}, "F2kfP0BjbmxP": {"name": "value", "expr": {"__ref": "e7xsxpveElTw"}, "__type": "NameArg"}, "snt8j58CEX4u": {"variable": {"__ref": "1v6Qm-CtgrXY"}, "__type": "VarRef"}, "aCr5nWA-gKfT": {"code": "2", "fallback": null, "__type": "CustomCode"}, "e7xsxpveElTw": {"variants": [{"__ref": "uuEPWZXblHLL"}], "__type": "VariantsRef"}, "dJSk9ik2yvlg": {"uuid": "kzo4SSa3Wkkz", "pkgId": "404da7b6-8001-4a90-b40a-5838626ee6bf", "projectId": "7Z38ZNJhfCuQ4JaULZaWDi", "version": "0.0.1", "name": "Multiple Interactions with conflicts", "site": {"__ref": "zljjFmQgmtIw"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}, "projectId": "7Z38ZNJhfCuQ4JaULZaWDi", "version": "0.0.1", "branchId": "main"}], "project": {"id": "7Z38ZNJhfCuQ4JaULZaWDi", "name": "Multiple Interactions with conflicts", "commitGraph": {"parents": {"3762f263-83a1-414e-a790-1c7245589c22": []}, "branches": {"main": "3762f263-83a1-414e-a790-1c7245589c22", "h6vE2f1bx9MYpYFYi7XtiF": "3762f263-83a1-414e-a790-1c7245589c22"}}}, "revisions": [{"branchId": "h6vE2f1bx9MYpYFYi7XtiF", "data": {"root": "zljjFmQgmtIw", "map": {"vXAWd8bPgKi0": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "y5NGKQw9F5o9": {"name": "Default Typography", "rs": {"__ref": "vXAWd8bPgKi0"}, "preview": null, "uuid": "3R4cuBGEDlM8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nvOwAxYxyI0F": {"values": {}, "mixins": [], "__type": "RuleSet"}, "X2reH9BZqOlB": {"rs": {"__ref": "nvOwAxYxyI0F"}, "__type": "ThemeLayoutSettings"}, "Hs_UqoJr-eyX": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "4awPc-MnpcKq": {"name": "Default \"h1\"", "rs": {"__ref": "Hs_UqoJr-eyX"}, "preview": null, "uuid": "EzefskVUfGaP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AJWk87qHq2Av": {"selector": "h1", "style": {"__ref": "4awPc-MnpcKq"}, "__type": "ThemeStyle"}, "drsq-qEAFZzA": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "vksxzTJEl8iu": {"name": "Default \"h2\"", "rs": {"__ref": "drsq-qEAFZzA"}, "preview": null, "uuid": "XrwR3KgR8z3v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PB3jyOvyvEQt": {"selector": "h2", "style": {"__ref": "vksxzTJEl8iu"}, "__type": "ThemeStyle"}, "TlVZrcyBffD6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "AI3On-Vfl0H9": {"name": "Default \"h3\"", "rs": {"__ref": "TlVZrcyBffD6"}, "preview": null, "uuid": "iQEa6AngIiUw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5eiliG1YEC7Q": {"selector": "h3", "style": {"__ref": "AI3On-Vfl0H9"}, "__type": "ThemeStyle"}, "4idbLAZjyArM": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "MCOVCXCUqdKZ": {"name": "Default \"h4\"", "rs": {"__ref": "4idbLAZjyArM"}, "preview": null, "uuid": "mPtte3CSj-Yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "enGDfzqLPjPv": {"selector": "h4", "style": {"__ref": "MCOVCXCUqdKZ"}, "__type": "ThemeStyle"}, "hr1bVhUyEGWn": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "pqqa6tUzR55a": {"name": "Default \"h5\"", "rs": {"__ref": "hr1bVhUyEGWn"}, "preview": null, "uuid": "7LcwFXhQk5a8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "azPZH50q6sF0": {"selector": "h5", "style": {"__ref": "pqqa6tUzR55a"}, "__type": "ThemeStyle"}, "EnFzptIht708": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "zri_laG0CHZC": {"name": "Default \"h6\"", "rs": {"__ref": "EnFzptIht708"}, "preview": null, "uuid": "a3Bao3RaPbhy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "XsYUymE2Rsg0": {"selector": "h6", "style": {"__ref": "zri_laG0CHZC"}, "__type": "ThemeStyle"}, "XEwGhsDAIYPC": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "LPzZwdaE2RFl": {"name": "Default \"a\"", "rs": {"__ref": "XEwGhsDAIYPC"}, "preview": null, "uuid": "ckz3c5T8Ez3P", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "pkAGqzMkw9LW": {"selector": "a", "style": {"__ref": "LPzZwdaE2RFl"}, "__type": "ThemeStyle"}, "gaE-R9Mw5FIM": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "P9sFQ2dwEmde": {"name": "Default \"a:hover\"", "rs": {"__ref": "gaE-R9Mw5FIM"}, "preview": null, "uuid": "Qr-i4K41wtVk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "w07l_gP3TkP3": {"selector": "a:hover", "style": {"__ref": "P9sFQ2dwEmde"}, "__type": "ThemeStyle"}, "mssx7nQsGXRE": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "b8w6Mt8mhqRH": {"name": "Default \"blockquote\"", "rs": {"__ref": "mssx7nQsGXRE"}, "preview": null, "uuid": "EQdsKEndloVw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZCW7KXcUOsrS": {"selector": "blockquote", "style": {"__ref": "b8w6Mt8mhqRH"}, "__type": "ThemeStyle"}, "u7m1CZgZGmPr": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "AStpDeyW8KFj": {"name": "Default \"code\"", "rs": {"__ref": "u7m1CZgZGmPr"}, "preview": null, "uuid": "sJeNqebPIR-k", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "y193ORwYtouk": {"selector": "code", "style": {"__ref": "AStpDeyW8KFj"}, "__type": "ThemeStyle"}, "ng0q1hm74gHY": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "zxBTK9n7OqcY": {"name": "Default \"pre\"", "rs": {"__ref": "ng0q1hm74gHY"}, "preview": null, "uuid": "OWGZX741fU3O", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BzTWjwkKM2RQ": {"selector": "pre", "style": {"__ref": "zxBTK9n7OqcY"}, "__type": "ThemeStyle"}, "BlPSy77Lk4V5": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "6LLUqMRMdhRa": {"name": "Default \"ol\"", "rs": {"__ref": "BlPSy77Lk4V5"}, "preview": null, "uuid": "ketdRU7YjHBf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vSCgmy9GM9cA": {"selector": "ol", "style": {"__ref": "6LLUqMRMdhRa"}, "__type": "ThemeStyle"}, "BRn6jMwiJMJs": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "NcNX4L8At4oe": {"name": "Default \"ul\"", "rs": {"__ref": "BRn6jMwiJMJs"}, "preview": null, "uuid": "Mu8yrk2PWZqZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "z7RrOVKfALsx": {"selector": "ul", "style": {"__ref": "NcNX4L8At4oe"}, "__type": "ThemeStyle"}, "Hu5NDxBoYOgd": {"defaultStyle": {"__ref": "y5NGKQw9F5o9"}, "styles": [{"__ref": "AJWk87qHq2Av"}, {"__ref": "PB3jyOvyvEQt"}, {"__ref": "5eiliG1YEC7Q"}, {"__ref": "enGDfzqLPjPv"}, {"__ref": "azPZH50q6sF0"}, {"__ref": "XsYUymE2Rsg0"}, {"__ref": "pkAGqzMkw9LW"}, {"__ref": "w07l_gP3TkP3"}, {"__ref": "ZCW7KXcUOsrS"}, {"__ref": "y193ORwYtouk"}, {"__ref": "BzTWjwkKM2RQ"}, {"__ref": "vSCgmy9GM9cA"}, {"__ref": "z7RrOVKfALsx"}], "layout": {"__ref": "X2reH9BZqOlB"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "xDY7LiPYcl6f": {"name": "text", "__type": "Text"}, "hOX_Qid5bt93": {"name": "Screen", "uuid": "NOs-k8uS2CO-", "__type": "Var"}, "RgEPMW-YzGWe": {"type": {"__ref": "xDY7LiPYcl6f"}, "variable": {"__ref": "hOX_Qid5bt93"}, "uuid": "Bx4lKPuI_VH4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "AlrByhDbUDGr": {"type": "global-screen", "param": {"__ref": "RgEPMW-YzGWe"}, "uuid": "UG8KZt4KsYde", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "0yWkHpaCZJOs": {"uuid": "fr2o5Ooddu6G", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zljjFmQgmtIw": {"components": [{"__ref": "g7Ii99ThVWlY"}, {"__ref": "3xCfOSrV1RS3"}, {"__ref": "7Ljh68sM0JVA"}], "arenas": [], "pageArenas": [{"__ref": "oRCTlFsf32CM"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "AlrByhDbUDGr"}], "userManagedFonts": [], "globalVariant": {"__ref": "0yWkHpaCZJOs"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "Hu5NDxBoYOgd"}], "activeTheme": {"__ref": "Hu5NDxBoYOgd"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "AlrByhDbUDGr"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "g7Ii99ThVWlY": {"uuid": "ulXkJIho0yIH", "name": "hostless-plasmic-head", "params": [{"__ref": "n1uVfgkuyImr"}, {"__ref": "FrrPxqMps6Gu"}, {"__ref": "cmCM6brf5Re2"}, {"__ref": "0KWyWZj6oOGb"}], "states": [], "tplTree": {"__ref": "lUBuUI52mSp-"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "uKR-7wp06LVn"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "gi4wtnpWCtuh"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "3xCfOSrV1RS3": {"uuid": "mEPQOcHXQZ1T", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "ykne7coBH4SB"}, {"__ref": "HH1Q8GOQ2HtG"}, {"__ref": "R-E3F5lia7Va"}, {"__ref": "vbnNaMXUDJ9u"}, {"__ref": "bdqtOLdAZ5RS"}], "states": [], "tplTree": {"__ref": "vry5UbAkDNjD"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "cACyJhO8C7U1"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "hlUrcwpUQkrJ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "n1uVfgkuyImr": {"type": {"__ref": "uVJ55-AszkGF"}, "variable": {"__ref": "750EStEfP0Ru"}, "uuid": "RzNARPSJoMTc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "FrrPxqMps6Gu": {"type": {"__ref": "LWUbXAeSIyjm"}, "variable": {"__ref": "KLMfPKeGKhYA"}, "uuid": "hPIpbBZLcwjd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "cmCM6brf5Re2": {"type": {"__ref": "GYpWZCnk5lpq"}, "variable": {"__ref": "wJbQfITGsT7C"}, "uuid": "bQjybmykl6i5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "0KWyWZj6oOGb": {"type": {"__ref": "UEclEogHsdDa"}, "variable": {"__ref": "NscUMP8QkqvC"}, "uuid": "LAvugjsw-W01", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "lUBuUI52mSp-": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "APGe9p--6<PERSON>y", "parent": null, "locked": null, "vsettings": [{"__ref": "Z1tzgwuMJaOM"}], "__type": "TplTag"}, "uKR-7wp06LVn": {"uuid": "7nkRFUKf28qh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gi4wtnpWCtuh": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "ykne7coBH4SB": {"type": {"__ref": "bs8KwBeBZpQ7"}, "variable": {"__ref": "R68INM_k6flp"}, "uuid": "8bMYkfjKU4fG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "HH1Q8GOQ2HtG": {"type": {"__ref": "D3hPKy6u9se_"}, "variable": {"__ref": "f8ht-9V4R8wa"}, "uuid": "LvqktbF-8fOs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R-E3F5lia7Va": {"type": {"__ref": "ASzA85kQRDhN"}, "tplSlot": {"__ref": "1N77DotxMSZt"}, "variable": {"__ref": "eCAvy287bxaR"}, "uuid": "e8gBTyWuDGCj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "vbnNaMXUDJ9u": {"type": {"__ref": "KIsTxGyhupAQ"}, "variable": {"__ref": "_7OWLc1w_Rdk"}, "uuid": "Jyrjcn5ehL9S", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "bdqtOLdAZ5RS": {"type": {"__ref": "uSzS2Y-Mtt2b"}, "variable": {"__ref": "khuleB1uIswB"}, "uuid": "7S-tLiMhOv9P", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "vry5UbAkDNjD": {"tag": "div", "name": null, "children": [{"__ref": "1N77DotxMSZt"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T4saJBfIHHPJ", "parent": null, "locked": null, "vsettings": [{"__ref": "9VwJiPcKOnwg"}], "__type": "TplTag"}, "cACyJhO8C7U1": {"uuid": "v3nu6dI1utno", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "hlUrcwpUQkrJ": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "uVJ55-AszkGF": {"name": "text", "__type": "Text"}, "750EStEfP0Ru": {"name": "title", "uuid": "J5Wi5nFko-Sc", "__type": "Var"}, "LWUbXAeSIyjm": {"name": "text", "__type": "Text"}, "KLMfPKeGKhYA": {"name": "description", "uuid": "-QiW1YvpUQmQ", "__type": "Var"}, "GYpWZCnk5lpq": {"name": "img", "__type": "Img"}, "wJbQfITGsT7C": {"name": "image", "uuid": "_Avda8LqX4DH", "__type": "Var"}, "UEclEogHsdDa": {"name": "text", "__type": "Text"}, "NscUMP8QkqvC": {"name": "canonical", "uuid": "Y3K_wrbFyAsN", "__type": "Var"}, "Z1tzgwuMJaOM": {"variants": [{"__ref": "uKR-7wp06LVn"}], "args": [], "attrs": {}, "rs": {"__ref": "7OaDYd_WUJyk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bs8KwBeBZpQ7": {"name": "any", "__type": "AnyType"}, "R68INM_k6flp": {"name": "dataOp", "uuid": "d3DrXP07JvXZ", "__type": "Var"}, "D3hPKy6u9se_": {"name": "text", "__type": "Text"}, "f8ht-9V4R8wa": {"name": "name", "uuid": "oQ0Jn64DVczH", "__type": "Var"}, "ASzA85kQRDhN": {"name": "renderFunc", "params": [{"__ref": "2DBwyhvZ7KqD"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "eCAvy287bxaR": {"name": "children", "uuid": "w83wyNHRlHKg", "__type": "Var"}, "KIsTxGyhupAQ": {"name": "num", "__type": "<PERSON><PERSON>"}, "_7OWLc1w_Rdk": {"name": "pageSize", "uuid": "9g_REvGUyg4G", "__type": "Var"}, "uSzS2Y-Mtt2b": {"name": "num", "__type": "<PERSON><PERSON>"}, "khuleB1uIswB": {"name": "pageIndex", "uuid": "7g72F55KZoEh", "__type": "Var"}, "1N77DotxMSZt": {"param": {"__ref": "R-E3F5lia7Va"}, "defaultContents": [], "uuid": "AqM0zePZIska", "parent": {"__ref": "vry5UbAkDNjD"}, "locked": null, "vsettings": [{"__ref": "ySAw1UNTW9BK"}], "__type": "TplSlot"}, "9VwJiPcKOnwg": {"variants": [{"__ref": "cACyJhO8C7U1"}], "args": [], "attrs": {}, "rs": {"__ref": "98ahuxsq-OwR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7OaDYd_WUJyk": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "2DBwyhvZ7KqD": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "_GI1UsF70C6b"}, "__type": "ArgType"}, "ySAw1UNTW9BK": {"variants": [{"__ref": "cACyJhO8C7U1"}], "args": [], "attrs": {}, "rs": {"__ref": "g9_69llTvJOc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "98ahuxsq-OwR": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "_GI1UsF70C6b": {"name": "any", "__type": "AnyType"}, "g9_69llTvJOc": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7Ljh68sM0JVA": {"uuid": "p2wrA0DvNTeq", "name": "InteractionsWithConflicts", "params": [{"__ref": "fIRYFfyf0ast"}, {"__ref": "KyVJseUgVHpZ"}, {"__ref": "zhjktiGWzoUG"}, {"__ref": "WbrFcWmcoUB_"}, {"__ref": "GGd0KIleGQxT"}, {"__ref": "ErwFJ-LYfVcS"}], "states": [{"__ref": "XY80qVQGoiRa"}, {"__ref": "qO9MbtCTqEv_"}, {"__ref": "WW_FfPsdQWR5"}], "tplTree": {"__ref": "XsKZIdI2AtF5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "p8GuTLtEem0_"}], "variantGroups": [{"__ref": "9qt1ZQASVpH6"}, {"__ref": "4doogn2mt2Rd"}], "pageMeta": {"__ref": "POIrFjImwbTJ"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "oRCTlFsf32CM": {"component": {"__ref": "7Ljh68sM0JVA"}, "matrix": {"__ref": "OY9mnrv24PJx"}, "customMatrix": {"__ref": "2FNbk3rq5J0j"}, "__type": "PageArena"}, "XsKZIdI2AtF5": {"tag": "div", "name": null, "children": [{"__ref": "EZWaccNxdnkV"}, {"__ref": "K6gFAxw5ZKuR"}, {"__ref": "HPdyaySpXdwJ"}, {"__ref": "N5ZMx_Kc2uUR"}, {"__ref": "pKhaSswZAftK"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "w6CZIL1XMhD3", "parent": null, "locked": null, "vsettings": [{"__ref": "7OzbyS28ybNb"}, {"__ref": "3IjHOkHzJXqJ"}, {"__ref": "IYLvaUGyzhbg"}], "__type": "TplTag"}, "p8GuTLtEem0_": {"uuid": "253hBI0BbQwC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "POIrFjImwbTJ": {"path": "/multiple-interactions-conflict", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "OY9mnrv24PJx": {"rows": [{"__ref": "lF6QbL5vE0jO"}, {"__ref": "0wO-T8WYOf5A"}, {"__ref": "uM1KbAVPxiTN"}], "__type": "ArenaFrameGrid"}, "2FNbk3rq5J0j": {"rows": [{"__ref": "UnhMOBsTJ957"}], "__type": "ArenaFrameGrid"}, "7OzbyS28ybNb": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "7MbdgIdlX4DT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lF6QbL5vE0jO": {"cols": [{"__ref": "EPzwdG9YVmGj"}, {"__ref": "Pm4aZyBdlFtF"}], "rowKey": {"__ref": "p8GuTLtEem0_"}, "__type": "ArenaFrameRow"}, "UnhMOBsTJ957": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "7MbdgIdlX4DT": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "EPzwdG9YVmGj": {"frame": {"__ref": "S1YltGZ49B1L"}, "cellKey": null, "__type": "ArenaFrameCell"}, "Pm4aZyBdlFtF": {"frame": {"__ref": "nKS6IqxPh_yP"}, "cellKey": null, "__type": "ArenaFrameCell"}, "S1YltGZ49B1L": {"uuid": "AL2HQbAYnyXR", "width": 1366, "height": 768, "container": {"__ref": "6rf_x85VN45q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "p8GuTLtEem0_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "nKS6IqxPh_yP": {"uuid": "Gm3IoGULp_Ic", "width": 414, "height": 736, "container": {"__ref": "Zl4zmiPFgI6C"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "p8GuTLtEem0_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6rf_x85VN45q": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "ZwQtVEBcEDVT", "parent": null, "locked": null, "vsettings": [{"__ref": "IkRLOr9Ulba-"}], "__type": "TplComponent"}, "Zl4zmiPFgI6C": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "J29pwS5udLW_", "parent": null, "locked": null, "vsettings": [{"__ref": "_os-itLRBpon"}], "__type": "TplComponent"}, "IkRLOr9Ulba-": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "yhJIUmtNSumd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_os-itLRBpon": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "MQIoDemHMd8w"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yhJIUmtNSumd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MQIoDemHMd8w": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EZWaccNxdnkV": {"tag": "div", "name": "changeState", "children": [{"__ref": "pnczuF3s9uTF"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "w5y_uwR0Mg9B", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "dDcYYLMDZ6yx"}], "__type": "TplTag"}, "dDcYYLMDZ6yx": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "XmYk2FS0d6Bw"}}, "rs": {"__ref": "gILvTGFZ-8y3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gILvTGFZ-8y3": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XmYk2FS0d6Bw": {"interactions": [{"__ref": "iJ5zTTpe4EIQ"}], "__type": "EventHandler"}, "iJ5zTTpe4EIQ": {"interactionName": "Update text", "actionName": "updateVariable", "args": [{"__ref": "lsjNNrYBum_x"}, {"__ref": "6wprEb8XS435"}, {"__ref": "rZy22Nu33-LY"}], "condExpr": null, "conditionalMode": "always", "uuid": "kAdeEWCmtJdL", "parent": {"__ref": "XmYk2FS0d6Bw"}, "__type": "Interaction"}, "XY80qVQGoiRa": {"param": {"__ref": "fIRYFfyf0ast"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "KyVJseUgVHpZ"}, "tplNode": null, "implicitState": null, "__type": "State"}, "fIRYFfyf0ast": {"type": {"__ref": "TNJKFgkoosiB"}, "state": {"__ref": "XY80qVQGoiRa"}, "variable": {"__ref": "ep0eLlUt5WMl"}, "uuid": "N8MpIbwTR9GH", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "naIx6KwBo1yM"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "KyVJseUgVHpZ": {"type": {"__ref": "oA_9u6g2COck"}, "state": {"__ref": "XY80qVQGoiRa"}, "variable": {"__ref": "F0jyajA6CKSQ"}, "uuid": "1Nx-w-iTs9eP", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "TNJKFgkoosiB": {"name": "text", "__type": "Text"}, "ep0eLlUt5WMl": {"name": "text", "uuid": "mM_b_ZDr-3yY", "__type": "Var"}, "naIx6KwBo1yM": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "oA_9u6g2COck": {"name": "func", "params": [{"__ref": "HFuOIXs1bYMP"}], "__type": "FunctionType"}, "F0jyajA6CKSQ": {"name": "On text Change", "uuid": "RLApZ9ghOhbb", "__type": "Var"}, "HFuOIXs1bYMP": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "gDbsvNW_RP2N"}, "__type": "ArgType"}, "gDbsvNW_RP2N": {"name": "text", "__type": "Text"}, "pnczuF3s9uTF": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "_ZPGUC-J4ztv", "parent": {"__ref": "EZWaccNxdnkV"}, "locked": null, "vsettings": [{"__ref": "i1ExyMCyGgFu"}], "__type": "TplTag"}, "i1ExyMCyGgFu": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "lIOgxH5xwmVL"}, "dataCond": null, "dataRep": null, "text": {"__ref": "u9nu2ogdP5E9"}, "columnsConfig": null, "__type": "VariantSetting"}, "lIOgxH5xwmVL": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "DXP-44w-9xge": {"path": ["$state", "text"], "fallback": null, "__type": "ObjectPath"}, "3Dezytq1Vw0d": {"code": "0", "fallback": null, "__type": "CustomCode"}, "K6gFAxw5ZKuR": {"tag": "div", "name": "updateVariant", "children": [{"__ref": "wD2enrO76jdP"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_9gNnw7STq8U", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "gwq4iXAAg0e-"}], "__type": "TplTag"}, "wD2enrO76jdP": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "0uMDnNoPGFHC", "parent": {"__ref": "K6gFAxw5ZKuR"}, "locked": null, "vsettings": [{"__ref": "h9PTpMSVpj-k"}], "__type": "TplTag"}, "gwq4iXAAg0e-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "DmeygiRO4J0X"}}, "rs": {"__ref": "43b4n-sARa5E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "h9PTpMSVpj-k": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "itf8CUzKB1Ok"}, "dataCond": null, "dataRep": null, "text": {"__ref": "RvSmFzLvgQxI"}, "columnsConfig": null, "__type": "VariantSetting"}, "DmeygiRO4J0X": {"interactions": [{"__ref": "D08q6jm_Wy1U"}], "__type": "EventHandler"}, "43b4n-sARa5E": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "itf8CUzKB1Ok": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "D08q6jm_Wy1U": {"interactionName": "Update highlightBranching", "actionName": "updateVariant", "args": [{"__ref": "wExnTIKn0LIw"}, {"__ref": "AMrGLMOCY_W7"}], "condExpr": null, "conditionalMode": "always", "uuid": "XO1epSq8uFEw", "parent": {"__ref": "DmeygiRO4J0X"}, "__type": "Interaction"}, "qO9MbtCTqEv_": {"variantGroup": {"__ref": "9qt1ZQASVpH6"}, "param": {"__ref": "zhjktiGWzoUG"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "WbrFcWmcoUB_"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "9qt1ZQASVpH6": {"type": "component", "param": {"__ref": "zhjktiGWzoUG"}, "linkedState": {"__ref": "qO9MbtCTqEv_"}, "uuid": "ula71m0Ti7ut", "variants": [{"__ref": "uuEPWZXblHLL"}], "multi": false, "__type": "ComponentVariantGroup"}, "zhjktiGWzoUG": {"type": {"__ref": "kKtsdrFffrTt"}, "state": {"__ref": "qO9MbtCTqEv_"}, "variable": {"__ref": "1v6Qm-CtgrXY"}, "uuid": "U4CjUpH4rgpx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "WbrFcWmcoUB_": {"type": {"__ref": "nhuK7OSYQCmA"}, "state": {"__ref": "qO9MbtCTqEv_"}, "variable": {"__ref": "om7SVM2GPPro"}, "uuid": "AlZBow1bK1sU", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "0wO-T8WYOf5A": {"cols": [{"__ref": "Z-r7u-1kMaWk"}, {"__ref": "l1PvdcEyAklQ"}], "rowKey": {"__ref": "uuEPWZXblHLL"}, "__type": "ArenaFrameRow"}, "3IjHOkHzJXqJ": {"variants": [{"__ref": "uuEPWZXblHLL"}], "args": [], "attrs": {}, "rs": {"__ref": "4L9lTzU2a54T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uuEPWZXblHLL": {"uuid": "B93OYtxxAPGB", "name": "highlight", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "9qt1ZQASVpH6"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kKtsdrFffrTt": {"name": "text", "__type": "Text"}, "1v6Qm-CtgrXY": {"name": "highlight", "uuid": "uTQp2T7tkyxk", "__type": "Var"}, "nhuK7OSYQCmA": {"name": "func", "params": [{"__ref": "t5a-rifdBdqo"}], "__type": "FunctionType"}, "om7SVM2GPPro": {"name": "On highlight change", "uuid": "q_w6sN3mhu5L", "__type": "Var"}, "Z-r7u-1kMaWk": {"frame": {"__ref": "GSJScN6fre41"}, "cellKey": null, "__type": "ArenaFrameCell"}, "l1PvdcEyAklQ": {"frame": {"__ref": "gkaYgMyddH6x"}, "cellKey": null, "__type": "ArenaFrameCell"}, "4L9lTzU2a54T": {"values": {}, "mixins": [], "__type": "RuleSet"}, "t5a-rifdBdqo": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "HZ3A69IJ221a"}, "__type": "ArgType"}, "GSJScN6fre41": {"uuid": "t8XSwviG9fwP", "width": 1366, "height": 768, "container": {"__ref": "a8F9KmpsuUpq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uuEPWZXblHLL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gkaYgMyddH6x": {"uuid": "HpY2LzfFZhh7", "width": 414, "height": 736, "container": {"__ref": "RQtIZcKUrUWU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uuEPWZXblHLL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "HZ3A69IJ221a": {"name": "any", "__type": "AnyType"}, "a8F9KmpsuUpq": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "lLfmpSQaoNCC", "parent": null, "locked": null, "vsettings": [{"__ref": "R9OSEhajmjrm"}], "__type": "TplComponent"}, "RQtIZcKUrUWU": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "Iw5ipkZW0SzB", "parent": null, "locked": null, "vsettings": [{"__ref": "r-ecdOJe_dYd"}], "__type": "TplComponent"}, "R9OSEhajmjrm": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "x2Xqn1qkWhnY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r-ecdOJe_dYd": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "xIv75oCOps9_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x2Xqn1qkWhnY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xIv75oCOps9_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RvSmFzLvgQxI": {"markers": [], "text": "Update variant", "__type": "RawText"}, "epunppDqA2cb": {"code": "2", "fallback": null, "__type": "CustomCode"}, "HPdyaySpXdwJ": {"tag": "div", "name": "goToPage", "children": [{"__ref": "7E-JUmYZf6Nq"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2hs7kMnlIZZ2", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "TddoAfyNgdHZ"}], "__type": "TplTag"}, "7E-JUmYZf6Nq": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "z8OMf2LlwGco", "parent": {"__ref": "HPdyaySpXdwJ"}, "locked": null, "vsettings": [{"__ref": "FPQxgF9HkzB2"}], "__type": "TplTag"}, "TddoAfyNgdHZ": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "DgNXbTM3JNS5"}}, "rs": {"__ref": "5jSvl8G-UZuA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FPQxgF9HkzB2": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "F-lXmIAl3wZS"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ngjsfbb7cT6U"}, "columnsConfig": null, "__type": "VariantSetting"}, "DgNXbTM3JNS5": {"interactions": [{"__ref": "vicauwZenhIL"}], "__type": "EventHandler"}, "5jSvl8G-UZuA": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "F-lXmIAl3wZS": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "vicauwZenhIL": {"interactionName": "Go to /branch/other-page", "actionName": "navigation", "args": [{"__ref": "EwwnLIFbCps9"}], "condExpr": null, "conditionalMode": "always", "uuid": "Yr61esKGQBGB", "parent": {"__ref": "DgNXbTM3JNS5"}, "__type": "Interaction"}, "ngjsfbb7cT6U": {"markers": [], "text": "Go to page", "__type": "RawText"}, "N5ZMx_Kc2uUR": {"tag": "div", "name": "runCode", "children": [{"__ref": "jTEtMAnxDQdl"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "giD5qkzgKg3e", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "xuEQJTBFSG2-"}], "__type": "TplTag"}, "jTEtMAnxDQdl": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kN553mK6AkLE", "parent": {"__ref": "N5ZMx_Kc2uUR"}, "locked": null, "vsettings": [{"__ref": "MzvFAUOUZozE"}], "__type": "TplTag"}, "xuEQJTBFSG2-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "8Nx3vNfefYWX"}}, "rs": {"__ref": "dJukb5mAScYw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MzvFAUOUZozE": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "7qzw98Ilvm2K"}, "dataCond": null, "dataRep": null, "text": {"__ref": "GqlaaVHpEmcs"}, "columnsConfig": null, "__type": "VariantSetting"}, "8Nx3vNfefYWX": {"interactions": [{"__ref": "Ih1kyVOvymwW"}], "__type": "EventHandler"}, "dJukb5mAScYw": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "7qzw98Ilvm2K": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "Ih1kyVOvymwW": {"interactionName": "Run code", "actionName": "customFunction", "args": [{"__ref": "HwfS0WF6k47Q"}], "condExpr": null, "conditionalMode": "always", "uuid": "SYkc9rKbodI8", "parent": {"__ref": "8Nx3vNfefYWX"}, "__type": "Interaction"}, "GqlaaVHpEmcs": {"markers": [], "text": "Run code", "__type": "RawText"}, "pKhaSswZAftK": {"tag": "div", "name": "combinedActions", "children": [{"__ref": "Q8PkCF1LZhwh"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "rWSXmB3UAgZ1", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "qn0rgdwUMMk-"}], "__type": "TplTag"}, "Q8PkCF1LZhwh": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "fVVJBvnZ-kuW", "parent": {"__ref": "pKhaSswZAftK"}, "locked": null, "vsettings": [{"__ref": "-66CZ_OV-BvR"}], "__type": "TplTag"}, "qn0rgdwUMMk-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "GQQ-iKl75wpm"}}, "rs": {"__ref": "ikm4uVHVt3UN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-66CZ_OV-BvR": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "FNZZTtQeSfpx"}, "dataCond": null, "dataRep": null, "text": {"__ref": "MatqRE1k1Wrz"}, "columnsConfig": null, "__type": "VariantSetting"}, "GQQ-iKl75wpm": {"interactions": [{"__ref": "MPIlG2Mc3il1"}, {"__ref": "XIMrbMXcamq2"}], "__type": "EventHandler"}, "ikm4uVHVt3UN": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "FNZZTtQeSfpx": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "MPIlG2Mc3il1": {"interactionName": "Update text variable", "actionName": "updateVariable", "args": [{"__ref": "wg3iw1CiCGYp"}, {"__ref": "tH-nZZ-locf9"}, {"__ref": "rZFdf8xnGRNQ"}], "condExpr": null, "conditionalMode": "always", "uuid": "rLtmlbx4Hsi8", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "MatqRE1k1Wrz": {"markers": [], "text": "Combined of actions", "__type": "RawText"}, "u9nu2ogdP5E9": {"markers": [], "text": "Change state", "__type": "RawText"}, "6TQNcnbCKfvz": {"path": ["$state", "text"], "fallback": null, "__type": "ObjectPath"}, "nbw7c-JfemQX": {"code": "0", "fallback": null, "__type": "CustomCode"}, "wg3iw1CiCGYp": {"name": "variable", "expr": {"__ref": "6TQNcnbCKfvz"}, "__type": "NameArg"}, "tH-nZZ-locf9": {"name": "operation", "expr": {"__ref": "nbw7c-JfemQX"}, "__type": "NameArg"}, "rZFdf8xnGRNQ": {"name": "value", "expr": {"__ref": "_al9qs-zYrTN"}, "__type": "NameArg"}, "_al9qs-zYrTN": {"code": "(\"Change value in first publish in combined actions\")", "fallback": null, "__type": "CustomCode"}, "lsjNNrYBum_x": {"name": "variable", "expr": {"__ref": "DXP-44w-9xge"}, "__type": "NameArg"}, "6wprEb8XS435": {"name": "operation", "expr": {"__ref": "3Dezytq1Vw0d"}, "__type": "NameArg"}, "rZy22Nu33-LY": {"name": "value", "expr": {"__ref": "QmzEgdHig7qD"}, "__type": "NameArg"}, "QmzEgdHig7qD": {"code": "(\"First version branch\")", "fallback": null, "__type": "CustomCode"}, "WW_FfPsdQWR5": {"variantGroup": {"__ref": "4doogn2mt2Rd"}, "param": {"__ref": "GGd0KIleGQxT"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "ErwFJ-LYfVcS"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "4doogn2mt2Rd": {"type": "component", "param": {"__ref": "GGd0KIleGQxT"}, "linkedState": {"__ref": "WW_FfPsdQWR5"}, "uuid": "fsZLBCKiKCSk", "variants": [{"__ref": "yW4V4--uILY0"}], "multi": false, "__type": "ComponentVariantGroup"}, "GGd0KIleGQxT": {"type": {"__ref": "FfX57-82_F3E"}, "state": {"__ref": "WW_FfPsdQWR5"}, "variable": {"__ref": "mw-chQ7gEfY6"}, "uuid": "JvlL1eKeaQok", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "ErwFJ-LYfVcS": {"type": {"__ref": "lENGcwALt1PC"}, "state": {"__ref": "WW_FfPsdQWR5"}, "variable": {"__ref": "UVNKY3TY34He"}, "uuid": "UYV_dO-6tZfz", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "uM1KbAVPxiTN": {"cols": [{"__ref": "JmfcY0BHGg_C"}, {"__ref": "XWNOEfN-8nHA"}], "rowKey": {"__ref": "yW4V4--uILY0"}, "__type": "ArenaFrameRow"}, "IYLvaUGyzhbg": {"variants": [{"__ref": "yW4V4--uILY0"}], "args": [], "attrs": {}, "rs": {"__ref": "RMy9q8Sl52ph"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yW4V4--uILY0": {"uuid": "bG37MUb_ttyA", "name": "highlightBranching", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "4doogn2mt2Rd"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "FfX57-82_F3E": {"name": "text", "__type": "Text"}, "mw-chQ7gEfY6": {"name": "highlightBranching", "uuid": "Ir8UHzendQ6p", "__type": "Var"}, "lENGcwALt1PC": {"name": "func", "params": [{"__ref": "BGnPWnwZVtg7"}], "__type": "FunctionType"}, "UVNKY3TY34He": {"name": "On highlightBranching change", "uuid": "hjSZnY9KB2r3", "__type": "Var"}, "JmfcY0BHGg_C": {"frame": {"__ref": "uKL1hktGzDBQ"}, "cellKey": null, "__type": "ArenaFrameCell"}, "XWNOEfN-8nHA": {"frame": {"__ref": "q0Kt4VjQum_U"}, "cellKey": null, "__type": "ArenaFrameCell"}, "RMy9q8Sl52ph": {"values": {}, "mixins": [], "__type": "RuleSet"}, "BGnPWnwZVtg7": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "zwliyzf4Dsez"}, "__type": "ArgType"}, "uKL1hktGzDBQ": {"uuid": "lJhOZElKIewP", "width": 1366, "height": 768, "container": {"__ref": "31epdy1CpwBE"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "yW4V4--uILY0"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "q0Kt4VjQum_U": {"uuid": "L5n_3ORo26xH", "width": 414, "height": 736, "container": {"__ref": "JRRAiG7RjPV_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "yW4V4--uILY0"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "zwliyzf4Dsez": {"name": "any", "__type": "AnyType"}, "31epdy1CpwBE": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "8R11-kCIKZ0P", "parent": null, "locked": null, "vsettings": [{"__ref": "zFF8BLr7yivL"}], "__type": "TplComponent"}, "JRRAiG7RjPV_": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "g3Frx2Hs8H6G", "parent": null, "locked": null, "vsettings": [{"__ref": "s7QmfTpWQBvj"}], "__type": "TplComponent"}, "zFF8BLr7yivL": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "I10Iu9q_rX3V"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "s7QmfTpWQBvj": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "FNtKeIrXgGC2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I10Iu9q_rX3V": {"values": {}, "mixins": [], "__type": "RuleSet"}, "FNtKeIrXgGC2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "wExnTIKn0LIw": {"name": "vgroup", "expr": {"__ref": "Z72xmSqHBjqh"}, "__type": "NameArg"}, "AMrGLMOCY_W7": {"name": "operation", "expr": {"__ref": "epunppDqA2cb"}, "__type": "NameArg"}, "Z72xmSqHBjqh": {"variable": {"__ref": "mw-chQ7gEfY6"}, "__type": "VarRef"}, "EwwnLIFbCps9": {"name": "destination", "expr": {"__ref": "Yl13qzlwr7xh"}, "__type": "NameArg"}, "Yl13qzlwr7xh": {"code": "\"/branch/other-page\"", "fallback": null, "__type": "CustomCode"}, "HwfS0WF6k47Q": {"name": "customFunction", "expr": {"__ref": "BZhIIAkujT7d"}, "__type": "NameArg"}, "BZhIIAkujT7d": {"argNames": [], "bodyExpr": {"__ref": "RuTHc8tjoS94"}, "__type": "FunctionExpr"}, "RuTHc8tjoS94": {"code": "(console.log(\"Running in branch first publish\");)", "fallback": null, "__type": "CustomCode"}, "XIMrbMXcamq2": {"interactionName": "Run code", "actionName": "customFunction", "args": [{"__ref": "1jNcvIGMCyZK"}], "condExpr": null, "conditionalMode": "always", "uuid": "7F3-RvV5LVzv", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "1jNcvIGMCyZK": {"name": "customFunction", "expr": {"__ref": "4sPdavseLQZH"}, "__type": "NameArg"}, "4sPdavseLQZH": {"argNames": [], "bodyExpr": {"__ref": "iNkFEFdT7Yj3"}, "__type": "FunctionExpr"}, "iNkFEFdT7Yj3": {"code": "(\"Another run code\")", "fallback": null, "__type": "CustomCode"}}, "deps": [], "version": "251-add-data-tokens"}}, {"branchId": "main", "data": {"root": "zljjFmQgmtIw", "map": {"vXAWd8bPgKi0": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "y5NGKQw9F5o9": {"name": "Default Typography", "rs": {"__ref": "vXAWd8bPgKi0"}, "preview": null, "uuid": "3R4cuBGEDlM8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nvOwAxYxyI0F": {"values": {}, "mixins": [], "__type": "RuleSet"}, "X2reH9BZqOlB": {"rs": {"__ref": "nvOwAxYxyI0F"}, "__type": "ThemeLayoutSettings"}, "Hs_UqoJr-eyX": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "4awPc-MnpcKq": {"name": "Default \"h1\"", "rs": {"__ref": "Hs_UqoJr-eyX"}, "preview": null, "uuid": "EzefskVUfGaP", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "AJWk87qHq2Av": {"selector": "h1", "style": {"__ref": "4awPc-MnpcKq"}, "__type": "ThemeStyle"}, "drsq-qEAFZzA": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "vksxzTJEl8iu": {"name": "Default \"h2\"", "rs": {"__ref": "drsq-qEAFZzA"}, "preview": null, "uuid": "XrwR3KgR8z3v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PB3jyOvyvEQt": {"selector": "h2", "style": {"__ref": "vksxzTJEl8iu"}, "__type": "ThemeStyle"}, "TlVZrcyBffD6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "AI3On-Vfl0H9": {"name": "Default \"h3\"", "rs": {"__ref": "TlVZrcyBffD6"}, "preview": null, "uuid": "iQEa6AngIiUw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "5eiliG1YEC7Q": {"selector": "h3", "style": {"__ref": "AI3On-Vfl0H9"}, "__type": "ThemeStyle"}, "4idbLAZjyArM": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "MCOVCXCUqdKZ": {"name": "Default \"h4\"", "rs": {"__ref": "4idbLAZjyArM"}, "preview": null, "uuid": "mPtte3CSj-Yq", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "enGDfzqLPjPv": {"selector": "h4", "style": {"__ref": "MCOVCXCUqdKZ"}, "__type": "ThemeStyle"}, "hr1bVhUyEGWn": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "pqqa6tUzR55a": {"name": "Default \"h5\"", "rs": {"__ref": "hr1bVhUyEGWn"}, "preview": null, "uuid": "7LcwFXhQk5a8", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "azPZH50q6sF0": {"selector": "h5", "style": {"__ref": "pqqa6tUzR55a"}, "__type": "ThemeStyle"}, "EnFzptIht708": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "zri_laG0CHZC": {"name": "Default \"h6\"", "rs": {"__ref": "EnFzptIht708"}, "preview": null, "uuid": "a3Bao3RaPbhy", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "XsYUymE2Rsg0": {"selector": "h6", "style": {"__ref": "zri_laG0CHZC"}, "__type": "ThemeStyle"}, "XEwGhsDAIYPC": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "LPzZwdaE2RFl": {"name": "Default \"a\"", "rs": {"__ref": "XEwGhsDAIYPC"}, "preview": null, "uuid": "ckz3c5T8Ez3P", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "pkAGqzMkw9LW": {"selector": "a", "style": {"__ref": "LPzZwdaE2RFl"}, "__type": "ThemeStyle"}, "gaE-R9Mw5FIM": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "P9sFQ2dwEmde": {"name": "Default \"a:hover\"", "rs": {"__ref": "gaE-R9Mw5FIM"}, "preview": null, "uuid": "Qr-i4K41wtVk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "w07l_gP3TkP3": {"selector": "a:hover", "style": {"__ref": "P9sFQ2dwEmde"}, "__type": "ThemeStyle"}, "mssx7nQsGXRE": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "b8w6Mt8mhqRH": {"name": "Default \"blockquote\"", "rs": {"__ref": "mssx7nQsGXRE"}, "preview": null, "uuid": "EQdsKEndloVw", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ZCW7KXcUOsrS": {"selector": "blockquote", "style": {"__ref": "b8w6Mt8mhqRH"}, "__type": "ThemeStyle"}, "u7m1CZgZGmPr": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "AStpDeyW8KFj": {"name": "Default \"code\"", "rs": {"__ref": "u7m1CZgZGmPr"}, "preview": null, "uuid": "sJeNqebPIR-k", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "y193ORwYtouk": {"selector": "code", "style": {"__ref": "AStpDeyW8KFj"}, "__type": "ThemeStyle"}, "ng0q1hm74gHY": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "zxBTK9n7OqcY": {"name": "Default \"pre\"", "rs": {"__ref": "ng0q1hm74gHY"}, "preview": null, "uuid": "OWGZX741fU3O", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BzTWjwkKM2RQ": {"selector": "pre", "style": {"__ref": "zxBTK9n7OqcY"}, "__type": "ThemeStyle"}, "BlPSy77Lk4V5": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "6LLUqMRMdhRa": {"name": "Default \"ol\"", "rs": {"__ref": "BlPSy77Lk4V5"}, "preview": null, "uuid": "ketdRU7YjHBf", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vSCgmy9GM9cA": {"selector": "ol", "style": {"__ref": "6LLUqMRMdhRa"}, "__type": "ThemeStyle"}, "BRn6jMwiJMJs": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "NcNX4L8At4oe": {"name": "Default \"ul\"", "rs": {"__ref": "BRn6jMwiJMJs"}, "preview": null, "uuid": "Mu8yrk2PWZqZ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "z7RrOVKfALsx": {"selector": "ul", "style": {"__ref": "NcNX4L8At4oe"}, "__type": "ThemeStyle"}, "Hu5NDxBoYOgd": {"defaultStyle": {"__ref": "y5NGKQw9F5o9"}, "styles": [{"__ref": "AJWk87qHq2Av"}, {"__ref": "PB3jyOvyvEQt"}, {"__ref": "5eiliG1YEC7Q"}, {"__ref": "enGDfzqLPjPv"}, {"__ref": "azPZH50q6sF0"}, {"__ref": "XsYUymE2Rsg0"}, {"__ref": "pkAGqzMkw9LW"}, {"__ref": "w07l_gP3TkP3"}, {"__ref": "ZCW7KXcUOsrS"}, {"__ref": "y193ORwYtouk"}, {"__ref": "BzTWjwkKM2RQ"}, {"__ref": "vSCgmy9GM9cA"}, {"__ref": "z7RrOVKfALsx"}], "layout": {"__ref": "X2reH9BZqOlB"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "xDY7LiPYcl6f": {"name": "text", "__type": "Text"}, "hOX_Qid5bt93": {"name": "Screen", "uuid": "NOs-k8uS2CO-", "__type": "Var"}, "RgEPMW-YzGWe": {"type": {"__ref": "xDY7LiPYcl6f"}, "variable": {"__ref": "hOX_Qid5bt93"}, "uuid": "Bx4lKPuI_VH4", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "AlrByhDbUDGr": {"type": "global-screen", "param": {"__ref": "RgEPMW-YzGWe"}, "uuid": "UG8KZt4KsYde", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "0yWkHpaCZJOs": {"uuid": "fr2o5Ooddu6G", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "zljjFmQgmtIw": {"components": [{"__ref": "g7Ii99ThVWlY"}, {"__ref": "3xCfOSrV1RS3"}, {"__ref": "7Ljh68sM0JVA"}], "arenas": [], "pageArenas": [{"__ref": "oRCTlFsf32CM"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "AlrByhDbUDGr"}], "userManagedFonts": [], "globalVariant": {"__ref": "0yWkHpaCZJOs"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "Hu5NDxBoYOgd"}], "activeTheme": {"__ref": "Hu5NDxBoYOgd"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "AlrByhDbUDGr"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "g7Ii99ThVWlY": {"uuid": "ulXkJIho0yIH", "name": "hostless-plasmic-head", "params": [{"__ref": "n1uVfgkuyImr"}, {"__ref": "FrrPxqMps6Gu"}, {"__ref": "cmCM6brf5Re2"}, {"__ref": "0KWyWZj6oOGb"}], "states": [], "tplTree": {"__ref": "lUBuUI52mSp-"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "uKR-7wp06LVn"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "gi4wtnpWCtuh"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "3xCfOSrV1RS3": {"uuid": "mEPQOcHXQZ1T", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "ykne7coBH4SB"}, {"__ref": "HH1Q8GOQ2HtG"}, {"__ref": "R-E3F5lia7Va"}, {"__ref": "vbnNaMXUDJ9u"}, {"__ref": "bdqtOLdAZ5RS"}], "states": [], "tplTree": {"__ref": "vry5UbAkDNjD"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "cACyJhO8C7U1"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "hlUrcwpUQkrJ"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "n1uVfgkuyImr": {"type": {"__ref": "uVJ55-AszkGF"}, "variable": {"__ref": "750EStEfP0Ru"}, "uuid": "RzNARPSJoMTc", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "FrrPxqMps6Gu": {"type": {"__ref": "LWUbXAeSIyjm"}, "variable": {"__ref": "KLMfPKeGKhYA"}, "uuid": "hPIpbBZLcwjd", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "cmCM6brf5Re2": {"type": {"__ref": "GYpWZCnk5lpq"}, "variable": {"__ref": "wJbQfITGsT7C"}, "uuid": "bQjybmykl6i5", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "0KWyWZj6oOGb": {"type": {"__ref": "UEclEogHsdDa"}, "variable": {"__ref": "NscUMP8QkqvC"}, "uuid": "LAvugjsw-W01", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "lUBuUI52mSp-": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "APGe9p--6<PERSON>y", "parent": null, "locked": null, "vsettings": [{"__ref": "Z1tzgwuMJaOM"}], "__type": "TplTag"}, "uKR-7wp06LVn": {"uuid": "7nkRFUKf28qh", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "gi4wtnpWCtuh": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "ykne7coBH4SB": {"type": {"__ref": "bs8KwBeBZpQ7"}, "variable": {"__ref": "R68INM_k6flp"}, "uuid": "8bMYkfjKU4fG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "HH1Q8GOQ2HtG": {"type": {"__ref": "D3hPKy6u9se_"}, "variable": {"__ref": "f8ht-9V4R8wa"}, "uuid": "LvqktbF-8fOs", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R-E3F5lia7Va": {"type": {"__ref": "ASzA85kQRDhN"}, "tplSlot": {"__ref": "1N77DotxMSZt"}, "variable": {"__ref": "eCAvy287bxaR"}, "uuid": "e8gBTyWuDGCj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "vbnNaMXUDJ9u": {"type": {"__ref": "KIsTxGyhupAQ"}, "variable": {"__ref": "_7OWLc1w_Rdk"}, "uuid": "Jyrjcn5ehL9S", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "bdqtOLdAZ5RS": {"type": {"__ref": "uSzS2Y-Mtt2b"}, "variable": {"__ref": "khuleB1uIswB"}, "uuid": "7S-tLiMhOv9P", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "vry5UbAkDNjD": {"tag": "div", "name": null, "children": [{"__ref": "1N77DotxMSZt"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "T4saJBfIHHPJ", "parent": null, "locked": null, "vsettings": [{"__ref": "9VwJiPcKOnwg"}], "__type": "TplTag"}, "cACyJhO8C7U1": {"uuid": "v3nu6dI1utno", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "hlUrcwpUQkrJ": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "refActions": [], "__type": "CodeComponentMeta"}, "uVJ55-AszkGF": {"name": "text", "__type": "Text"}, "750EStEfP0Ru": {"name": "title", "uuid": "J5Wi5nFko-Sc", "__type": "Var"}, "LWUbXAeSIyjm": {"name": "text", "__type": "Text"}, "KLMfPKeGKhYA": {"name": "description", "uuid": "-QiW1YvpUQmQ", "__type": "Var"}, "GYpWZCnk5lpq": {"name": "img", "__type": "Img"}, "wJbQfITGsT7C": {"name": "image", "uuid": "_Avda8LqX4DH", "__type": "Var"}, "UEclEogHsdDa": {"name": "text", "__type": "Text"}, "NscUMP8QkqvC": {"name": "canonical", "uuid": "Y3K_wrbFyAsN", "__type": "Var"}, "Z1tzgwuMJaOM": {"variants": [{"__ref": "uKR-7wp06LVn"}], "args": [], "attrs": {}, "rs": {"__ref": "7OaDYd_WUJyk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "bs8KwBeBZpQ7": {"name": "any", "__type": "AnyType"}, "R68INM_k6flp": {"name": "dataOp", "uuid": "d3DrXP07JvXZ", "__type": "Var"}, "D3hPKy6u9se_": {"name": "text", "__type": "Text"}, "f8ht-9V4R8wa": {"name": "name", "uuid": "oQ0Jn64DVczH", "__type": "Var"}, "ASzA85kQRDhN": {"name": "renderFunc", "params": [{"__ref": "2DBwyhvZ7KqD"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "eCAvy287bxaR": {"name": "children", "uuid": "w83wyNHRlHKg", "__type": "Var"}, "KIsTxGyhupAQ": {"name": "num", "__type": "<PERSON><PERSON>"}, "_7OWLc1w_Rdk": {"name": "pageSize", "uuid": "9g_REvGUyg4G", "__type": "Var"}, "uSzS2Y-Mtt2b": {"name": "num", "__type": "<PERSON><PERSON>"}, "khuleB1uIswB": {"name": "pageIndex", "uuid": "7g72F55KZoEh", "__type": "Var"}, "1N77DotxMSZt": {"param": {"__ref": "R-E3F5lia7Va"}, "defaultContents": [], "uuid": "AqM0zePZIska", "parent": {"__ref": "vry5UbAkDNjD"}, "locked": null, "vsettings": [{"__ref": "ySAw1UNTW9BK"}], "__type": "TplSlot"}, "9VwJiPcKOnwg": {"variants": [{"__ref": "cACyJhO8C7U1"}], "args": [], "attrs": {}, "rs": {"__ref": "98ahuxsq-OwR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7OaDYd_WUJyk": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "2DBwyhvZ7KqD": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "_GI1UsF70C6b"}, "__type": "ArgType"}, "ySAw1UNTW9BK": {"variants": [{"__ref": "cACyJhO8C7U1"}], "args": [], "attrs": {}, "rs": {"__ref": "g9_69llTvJOc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "98ahuxsq-OwR": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "_GI1UsF70C6b": {"name": "any", "__type": "AnyType"}, "g9_69llTvJOc": {"values": {}, "mixins": [], "__type": "RuleSet"}, "7Ljh68sM0JVA": {"uuid": "p2wrA0DvNTeq", "name": "InteractionsWithConflicts", "params": [{"__ref": "fIRYFfyf0ast"}, {"__ref": "KyVJseUgVHpZ"}, {"__ref": "zhjktiGWzoUG"}, {"__ref": "WbrFcWmcoUB_"}, {"__ref": "HVObM_6BxDKD"}, {"__ref": "T_rEkfEoLCBA"}], "states": [{"__ref": "XY80qVQGoiRa"}, {"__ref": "qO9MbtCTqEv_"}, {"__ref": "trL7LQ3U9C4k"}], "tplTree": {"__ref": "XsKZIdI2AtF5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "p8GuTLtEem0_"}], "variantGroups": [{"__ref": "9qt1ZQASVpH6"}, {"__ref": "ochrQP4kzeVX"}], "pageMeta": {"__ref": "POIrFjImwbTJ"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "oRCTlFsf32CM": {"component": {"__ref": "7Ljh68sM0JVA"}, "matrix": {"__ref": "OY9mnrv24PJx"}, "customMatrix": {"__ref": "2FNbk3rq5J0j"}, "__type": "PageArena"}, "XsKZIdI2AtF5": {"tag": "div", "name": null, "children": [{"__ref": "EZWaccNxdnkV"}, {"__ref": "K6gFAxw5ZKuR"}, {"__ref": "HPdyaySpXdwJ"}, {"__ref": "N5ZMx_Kc2uUR"}, {"__ref": "pKhaSswZAftK"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "w6CZIL1XMhD3", "parent": null, "locked": null, "vsettings": [{"__ref": "7OzbyS28ybNb"}, {"__ref": "3IjHOkHzJXqJ"}, {"__ref": "openXFFi5JX4"}], "__type": "TplTag"}, "p8GuTLtEem0_": {"uuid": "253hBI0BbQwC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "POIrFjImwbTJ": {"path": "/multiple-interactions-conflict", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "OY9mnrv24PJx": {"rows": [{"__ref": "lF6QbL5vE0jO"}, {"__ref": "0wO-T8WYOf5A"}, {"__ref": "oz7DebjOznJk"}], "__type": "ArenaFrameGrid"}, "2FNbk3rq5J0j": {"rows": [{"__ref": "UnhMOBsTJ957"}], "__type": "ArenaFrameGrid"}, "7OzbyS28ybNb": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "7MbdgIdlX4DT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lF6QbL5vE0jO": {"cols": [{"__ref": "EPzwdG9YVmGj"}, {"__ref": "Pm4aZyBdlFtF"}], "rowKey": {"__ref": "p8GuTLtEem0_"}, "__type": "ArenaFrameRow"}, "UnhMOBsTJ957": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "7MbdgIdlX4DT": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "EPzwdG9YVmGj": {"frame": {"__ref": "S1YltGZ49B1L"}, "cellKey": null, "__type": "ArenaFrameCell"}, "Pm4aZyBdlFtF": {"frame": {"__ref": "nKS6IqxPh_yP"}, "cellKey": null, "__type": "ArenaFrameCell"}, "S1YltGZ49B1L": {"uuid": "AL2HQbAYnyXR", "width": 1366, "height": 768, "container": {"__ref": "6rf_x85VN45q"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "p8GuTLtEem0_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "nKS6IqxPh_yP": {"uuid": "Gm3IoGULp_Ic", "width": 414, "height": 736, "container": {"__ref": "Zl4zmiPFgI6C"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "p8GuTLtEem0_"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6rf_x85VN45q": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "ZwQtVEBcEDVT", "parent": null, "locked": null, "vsettings": [{"__ref": "IkRLOr9Ulba-"}], "__type": "TplComponent"}, "Zl4zmiPFgI6C": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "J29pwS5udLW_", "parent": null, "locked": null, "vsettings": [{"__ref": "_os-itLRBpon"}], "__type": "TplComponent"}, "IkRLOr9Ulba-": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "yhJIUmtNSumd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_os-itLRBpon": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "MQIoDemHMd8w"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yhJIUmtNSumd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MQIoDemHMd8w": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EZWaccNxdnkV": {"tag": "div", "name": "changeState", "children": [{"__ref": "pnczuF3s9uTF"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "w5y_uwR0Mg9B", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "dDcYYLMDZ6yx"}], "__type": "TplTag"}, "dDcYYLMDZ6yx": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "XmYk2FS0d6Bw"}}, "rs": {"__ref": "gILvTGFZ-8y3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gILvTGFZ-8y3": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XmYk2FS0d6Bw": {"interactions": [{"__ref": "iJ5zTTpe4EIQ"}], "__type": "EventHandler"}, "iJ5zTTpe4EIQ": {"interactionName": "Update text", "actionName": "updateVariable", "args": [{"__ref": "xom2sboBD9Sl"}, {"__ref": "Mq5131EaEx_z"}, {"__ref": "tFgyA2pGeuMW"}], "condExpr": null, "conditionalMode": "always", "uuid": "kAdeEWCmtJdL", "parent": {"__ref": "XmYk2FS0d6Bw"}, "__type": "Interaction"}, "XY80qVQGoiRa": {"param": {"__ref": "fIRYFfyf0ast"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "KyVJseUgVHpZ"}, "tplNode": null, "implicitState": null, "__type": "State"}, "fIRYFfyf0ast": {"type": {"__ref": "TNJKFgkoosiB"}, "state": {"__ref": "XY80qVQGoiRa"}, "variable": {"__ref": "ep0eLlUt5WMl"}, "uuid": "N8MpIbwTR9GH", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "naIx6KwBo1yM"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "KyVJseUgVHpZ": {"type": {"__ref": "oA_9u6g2COck"}, "state": {"__ref": "XY80qVQGoiRa"}, "variable": {"__ref": "F0jyajA6CKSQ"}, "uuid": "1Nx-w-iTs9eP", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "TNJKFgkoosiB": {"name": "text", "__type": "Text"}, "ep0eLlUt5WMl": {"name": "text", "uuid": "mM_b_ZDr-3yY", "__type": "Var"}, "naIx6KwBo1yM": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "oA_9u6g2COck": {"name": "func", "params": [{"__ref": "HFuOIXs1bYMP"}], "__type": "FunctionType"}, "F0jyajA6CKSQ": {"name": "On text Change", "uuid": "RLApZ9ghOhbb", "__type": "Var"}, "HFuOIXs1bYMP": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "gDbsvNW_RP2N"}, "__type": "ArgType"}, "gDbsvNW_RP2N": {"name": "text", "__type": "Text"}, "pnczuF3s9uTF": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "_ZPGUC-J4ztv", "parent": {"__ref": "EZWaccNxdnkV"}, "locked": null, "vsettings": [{"__ref": "i1ExyMCyGgFu"}], "__type": "TplTag"}, "i1ExyMCyGgFu": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "lIOgxH5xwmVL"}, "dataCond": null, "dataRep": null, "text": {"__ref": "u9nu2ogdP5E9"}, "columnsConfig": null, "__type": "VariantSetting"}, "lIOgxH5xwmVL": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "DXP-44w-9xge": {"path": ["$state", "text"], "fallback": null, "__type": "ObjectPath"}, "3Dezytq1Vw0d": {"code": "0", "fallback": null, "__type": "CustomCode"}, "K6gFAxw5ZKuR": {"tag": "div", "name": "updateVariant", "children": [{"__ref": "wD2enrO76jdP"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "_9gNnw7STq8U", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "gwq4iXAAg0e-"}], "__type": "TplTag"}, "wD2enrO76jdP": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "0uMDnNoPGFHC", "parent": {"__ref": "K6gFAxw5ZKuR"}, "locked": null, "vsettings": [{"__ref": "h9PTpMSVpj-k"}], "__type": "TplTag"}, "gwq4iXAAg0e-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "DmeygiRO4J0X"}}, "rs": {"__ref": "43b4n-sARa5E"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "h9PTpMSVpj-k": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "itf8CUzKB1Ok"}, "dataCond": null, "dataRep": null, "text": {"__ref": "RvSmFzLvgQxI"}, "columnsConfig": null, "__type": "VariantSetting"}, "DmeygiRO4J0X": {"interactions": [{"__ref": "D08q6jm_Wy1U"}], "__type": "EventHandler"}, "43b4n-sARa5E": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "itf8CUzKB1Ok": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "D08q6jm_Wy1U": {"interactionName": "Update highlight<PERSON><PERSON>", "actionName": "updateVariant", "args": [{"__ref": "_CAIp-Vax_us"}, {"__ref": "Sn7qfTUESmCD"}], "condExpr": null, "conditionalMode": "always", "uuid": "XO1epSq8uFEw", "parent": {"__ref": "DmeygiRO4J0X"}, "__type": "Interaction"}, "qO9MbtCTqEv_": {"variantGroup": {"__ref": "9qt1ZQASVpH6"}, "param": {"__ref": "zhjktiGWzoUG"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "WbrFcWmcoUB_"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "9qt1ZQASVpH6": {"type": "component", "param": {"__ref": "zhjktiGWzoUG"}, "linkedState": {"__ref": "qO9MbtCTqEv_"}, "uuid": "ula71m0Ti7ut", "variants": [{"__ref": "uuEPWZXblHLL"}], "multi": false, "__type": "ComponentVariantGroup"}, "zhjktiGWzoUG": {"type": {"__ref": "kKtsdrFffrTt"}, "state": {"__ref": "qO9MbtCTqEv_"}, "variable": {"__ref": "1v6Qm-CtgrXY"}, "uuid": "U4CjUpH4rgpx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "WbrFcWmcoUB_": {"type": {"__ref": "nhuK7OSYQCmA"}, "state": {"__ref": "qO9MbtCTqEv_"}, "variable": {"__ref": "om7SVM2GPPro"}, "uuid": "AlZBow1bK1sU", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "0wO-T8WYOf5A": {"cols": [{"__ref": "Z-r7u-1kMaWk"}, {"__ref": "l1PvdcEyAklQ"}], "rowKey": {"__ref": "uuEPWZXblHLL"}, "__type": "ArenaFrameRow"}, "3IjHOkHzJXqJ": {"variants": [{"__ref": "uuEPWZXblHLL"}], "args": [], "attrs": {}, "rs": {"__ref": "4L9lTzU2a54T"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uuEPWZXblHLL": {"uuid": "B93OYtxxAPGB", "name": "highlight", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "9qt1ZQASVpH6"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kKtsdrFffrTt": {"name": "text", "__type": "Text"}, "1v6Qm-CtgrXY": {"name": "highlight", "uuid": "uTQp2T7tkyxk", "__type": "Var"}, "nhuK7OSYQCmA": {"name": "func", "params": [{"__ref": "t5a-rifdBdqo"}], "__type": "FunctionType"}, "om7SVM2GPPro": {"name": "On highlight change", "uuid": "q_w6sN3mhu5L", "__type": "Var"}, "Z-r7u-1kMaWk": {"frame": {"__ref": "GSJScN6fre41"}, "cellKey": null, "__type": "ArenaFrameCell"}, "l1PvdcEyAklQ": {"frame": {"__ref": "gkaYgMyddH6x"}, "cellKey": null, "__type": "ArenaFrameCell"}, "4L9lTzU2a54T": {"values": {}, "mixins": [], "__type": "RuleSet"}, "t5a-rifdBdqo": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "HZ3A69IJ221a"}, "__type": "ArgType"}, "GSJScN6fre41": {"uuid": "t8XSwviG9fwP", "width": 1366, "height": 768, "container": {"__ref": "a8F9KmpsuUpq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uuEPWZXblHLL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "gkaYgMyddH6x": {"uuid": "HpY2LzfFZhh7", "width": 414, "height": 736, "container": {"__ref": "RQtIZcKUrUWU"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "uuEPWZXblHLL"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "HZ3A69IJ221a": {"name": "any", "__type": "AnyType"}, "a8F9KmpsuUpq": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "lLfmpSQaoNCC", "parent": null, "locked": null, "vsettings": [{"__ref": "R9OSEhajmjrm"}], "__type": "TplComponent"}, "RQtIZcKUrUWU": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "Iw5ipkZW0SzB", "parent": null, "locked": null, "vsettings": [{"__ref": "r-ecdOJe_dYd"}], "__type": "TplComponent"}, "R9OSEhajmjrm": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "x2Xqn1qkWhnY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "r-ecdOJe_dYd": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "xIv75oCOps9_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x2Xqn1qkWhnY": {"values": {}, "mixins": [], "__type": "RuleSet"}, "xIv75oCOps9_": {"values": {}, "mixins": [], "__type": "RuleSet"}, "RvSmFzLvgQxI": {"markers": [], "text": "Update variant", "__type": "RawText"}, "epunppDqA2cb": {"code": "2", "fallback": null, "__type": "CustomCode"}, "HPdyaySpXdwJ": {"tag": "div", "name": "goToPage", "children": [{"__ref": "7E-JUmYZf6Nq"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2hs7kMnlIZZ2", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "TddoAfyNgdHZ"}], "__type": "TplTag"}, "7E-JUmYZf6Nq": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "z8OMf2LlwGco", "parent": {"__ref": "HPdyaySpXdwJ"}, "locked": null, "vsettings": [{"__ref": "FPQxgF9HkzB2"}], "__type": "TplTag"}, "TddoAfyNgdHZ": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "DgNXbTM3JNS5"}}, "rs": {"__ref": "5jSvl8G-UZuA"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "FPQxgF9HkzB2": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "F-lXmIAl3wZS"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ngjsfbb7cT6U"}, "columnsConfig": null, "__type": "VariantSetting"}, "DgNXbTM3JNS5": {"interactions": [{"__ref": "vicauwZenhIL"}], "__type": "EventHandler"}, "5jSvl8G-UZuA": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "F-lXmIAl3wZS": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "vicauwZenhIL": {"interactionName": "Go to /main/other-page", "actionName": "navigation", "args": [{"__ref": "f0SMpBGRGuLg"}], "condExpr": null, "conditionalMode": "always", "uuid": "Yr61esKGQBGB", "parent": {"__ref": "DgNXbTM3JNS5"}, "__type": "Interaction"}, "ngjsfbb7cT6U": {"markers": [], "text": "Go to page", "__type": "RawText"}, "N5ZMx_Kc2uUR": {"tag": "div", "name": "runCode", "children": [{"__ref": "jTEtMAnxDQdl"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "giD5qkzgKg3e", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "xuEQJTBFSG2-"}], "__type": "TplTag"}, "jTEtMAnxDQdl": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kN553mK6AkLE", "parent": {"__ref": "N5ZMx_Kc2uUR"}, "locked": null, "vsettings": [{"__ref": "MzvFAUOUZozE"}], "__type": "TplTag"}, "xuEQJTBFSG2-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "8Nx3vNfefYWX"}}, "rs": {"__ref": "dJukb5mAScYw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "MzvFAUOUZozE": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "7qzw98Ilvm2K"}, "dataCond": null, "dataRep": null, "text": {"__ref": "GqlaaVHpEmcs"}, "columnsConfig": null, "__type": "VariantSetting"}, "8Nx3vNfefYWX": {"interactions": [{"__ref": "Ih1kyVOvymwW"}], "__type": "EventHandler"}, "dJukb5mAScYw": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "7qzw98Ilvm2K": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "Ih1kyVOvymwW": {"interactionName": "Run code", "actionName": "customFunction", "args": [{"__ref": "9Ufc6Mu71aEE"}], "condExpr": null, "conditionalMode": "always", "uuid": "SYkc9rKbodI8", "parent": {"__ref": "8Nx3vNfefYWX"}, "__type": "Interaction"}, "GqlaaVHpEmcs": {"markers": [], "text": "Run code", "__type": "RawText"}, "pKhaSswZAftK": {"tag": "div", "name": "combinedActions", "children": [{"__ref": "Q8PkCF1LZhwh"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "rWSXmB3UAgZ1", "parent": {"__ref": "XsKZIdI2AtF5"}, "locked": null, "vsettings": [{"__ref": "qn0rgdwUMMk-"}], "__type": "TplTag"}, "Q8PkCF1LZhwh": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "fVVJBvnZ-kuW", "parent": {"__ref": "pKhaSswZAftK"}, "locked": null, "vsettings": [{"__ref": "-66CZ_OV-BvR"}], "__type": "TplTag"}, "qn0rgdwUMMk-": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {"onClick": {"__ref": "GQQ-iKl75wpm"}}, "rs": {"__ref": "ikm4uVHVt3UN"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-66CZ_OV-BvR": {"variants": [{"__ref": "p8GuTLtEem0_"}], "args": [], "attrs": {}, "rs": {"__ref": "FNZZTtQeSfpx"}, "dataCond": null, "dataRep": null, "text": {"__ref": "MatqRE1k1Wrz"}, "columnsConfig": null, "__type": "VariantSetting"}, "GQQ-iKl75wpm": {"interactions": [{"__ref": "MPIlG2Mc3il1"}, {"__ref": "8bSPzkFvZG6u"}, {"__ref": "yMstG2CFCI-v"}], "__type": "EventHandler"}, "ikm4uVHVt3UN": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "FNZZTtQeSfpx": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "MPIlG2Mc3il1": {"interactionName": "Update text variable", "actionName": "updateVariable", "args": [{"__ref": "wg3iw1CiCGYp"}, {"__ref": "tH-nZZ-locf9"}, {"__ref": "rZFdf8xnGRNQ"}], "condExpr": null, "conditionalMode": "always", "uuid": "rLtmlbx4Hsi8", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "MatqRE1k1Wrz": {"markers": [], "text": "Combined of actions", "__type": "RawText"}, "u9nu2ogdP5E9": {"markers": [], "text": "Change state", "__type": "RawText"}, "6TQNcnbCKfvz": {"path": ["$state", "text"], "fallback": null, "__type": "ObjectPath"}, "nbw7c-JfemQX": {"code": "0", "fallback": null, "__type": "CustomCode"}, "wg3iw1CiCGYp": {"name": "variable", "expr": {"__ref": "6TQNcnbCKfvz"}, "__type": "NameArg"}, "tH-nZZ-locf9": {"name": "operation", "expr": {"__ref": "nbw7c-JfemQX"}, "__type": "NameArg"}, "rZFdf8xnGRNQ": {"name": "value", "expr": {"__ref": "_al9qs-zYrTN"}, "__type": "NameArg"}, "_al9qs-zYrTN": {"code": "(\"Change value in first publish in combined actions\")", "fallback": null, "__type": "CustomCode"}, "8bSPzkFvZG6u": {"interactionName": "Update variant", "actionName": "updateVariant", "args": [{"__ref": "5-wITgViDNVc"}, {"__ref": "aqmQobQT9b_6"}, {"__ref": "F2kfP0BjbmxP"}], "condExpr": null, "conditionalMode": "always", "uuid": "akGHdrj_zSw7", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "5-wITgViDNVc": {"name": "vgroup", "expr": {"__ref": "snt8j58CEX4u"}, "__type": "NameArg"}, "aqmQobQT9b_6": {"name": "operation", "expr": {"__ref": "aCr5nWA-gKfT"}, "__type": "NameArg"}, "F2kfP0BjbmxP": {"name": "value", "expr": {"__ref": "e7xsxpveElTw"}, "__type": "NameArg"}, "snt8j58CEX4u": {"variable": {"__ref": "1v6Qm-CtgrXY"}, "__type": "VarRef"}, "aCr5nWA-gKfT": {"code": "2", "fallback": null, "__type": "CustomCode"}, "e7xsxpveElTw": {"variants": [{"__ref": "uuEPWZXblHLL"}], "__type": "VariantsRef"}, "xom2sboBD9Sl": {"name": "variable", "expr": {"__ref": "DXP-44w-9xge"}, "__type": "NameArg"}, "Mq5131EaEx_z": {"name": "operation", "expr": {"__ref": "3Dezytq1Vw0d"}, "__type": "NameArg"}, "tFgyA2pGeuMW": {"name": "value", "expr": {"__ref": "o5KGd4ZK1TF2"}, "__type": "NameArg"}, "o5KGd4ZK1TF2": {"code": "(\"Second version main\")", "fallback": null, "__type": "CustomCode"}, "trL7LQ3U9C4k": {"variantGroup": {"__ref": "ochrQP4kzeVX"}, "param": {"__ref": "HVObM_6BxDKD"}, "accessType": "private", "variableType": "variant", "onChangeParam": {"__ref": "T_rEkfEoLCBA"}, "tplNode": null, "implicitState": null, "__type": "VariantGroupState"}, "ochrQP4kzeVX": {"type": "component", "param": {"__ref": "HVObM_6BxDKD"}, "linkedState": {"__ref": "trL7LQ3U9C4k"}, "uuid": "Aam9k6z_mmLC", "variants": [{"__ref": "kX938O8ZTTll"}], "multi": false, "__type": "ComponentVariantGroup"}, "HVObM_6BxDKD": {"type": {"__ref": "n3kyKfDu0j73"}, "state": {"__ref": "trL7LQ3U9C4k"}, "variable": {"__ref": "DR2AaaQys_pU"}, "uuid": "M4I_Jluuf8Ua", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "T_rEkfEoLCBA": {"type": {"__ref": "fdWfVuHOAbXY"}, "state": {"__ref": "trL7LQ3U9C4k"}, "variable": {"__ref": "wyFfHzRe8ccq"}, "uuid": "9TAyYqQK68O9", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "oz7DebjOznJk": {"cols": [{"__ref": "GpiFQN96sOup"}, {"__ref": "96V6mhggK6Tm"}], "rowKey": {"__ref": "kX938O8ZTTll"}, "__type": "ArenaFrameRow"}, "openXFFi5JX4": {"variants": [{"__ref": "kX938O8ZTTll"}], "args": [], "attrs": {}, "rs": {"__ref": "3RFjwpYmA39y"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kX938O8ZTTll": {"uuid": "3y_0JSXPa01l", "name": "<PERSON><PERSON><PERSON>", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "ochrQP4kzeVX"}, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "n3kyKfDu0j73": {"name": "text", "__type": "Text"}, "DR2AaaQys_pU": {"name": "<PERSON><PERSON><PERSON>", "uuid": "ruDREI1E9fEF", "__type": "Var"}, "fdWfVuHOAbXY": {"name": "func", "params": [{"__ref": "nr4UmztS7HlF"}], "__type": "FunctionType"}, "wyFfHzRe8ccq": {"name": "On highlightMain change", "uuid": "rckfmRfXoHHN", "__type": "Var"}, "GpiFQN96sOup": {"frame": {"__ref": "ke-BJ3920C5x"}, "cellKey": null, "__type": "ArenaFrameCell"}, "96V6mhggK6Tm": {"frame": {"__ref": "zsFqGWBrVsFK"}, "cellKey": null, "__type": "ArenaFrameCell"}, "3RFjwpYmA39y": {"values": {}, "mixins": [], "__type": "RuleSet"}, "nr4UmztS7HlF": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "pzHNXoskSSte"}, "__type": "ArgType"}, "ke-BJ3920C5x": {"uuid": "Tz5kPecLqMpg", "width": 1366, "height": 768, "container": {"__ref": "FMUqY-TQ7KnT"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kX938O8ZTTll"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "zsFqGWBrVsFK": {"uuid": "WDQMhzM1Ep2E", "width": 414, "height": 736, "container": {"__ref": "NFTYnMBcu8NW"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "kX938O8ZTTll"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "pzHNXoskSSte": {"name": "any", "__type": "AnyType"}, "FMUqY-TQ7KnT": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "QBDVU-D4QZN9", "parent": null, "locked": null, "vsettings": [{"__ref": "xgZs7sLLzy0v"}], "__type": "TplComponent"}, "NFTYnMBcu8NW": {"name": null, "component": {"__ref": "7Ljh68sM0JVA"}, "uuid": "HHH8at53NTlw", "parent": null, "locked": null, "vsettings": [{"__ref": "eZOU0rWnCNsL"}], "__type": "TplComponent"}, "xgZs7sLLzy0v": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "truuFnaksicb"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eZOU0rWnCNsL": {"variants": [{"__ref": "0yWkHpaCZJOs"}], "args": [], "attrs": {}, "rs": {"__ref": "EMVq-YMLGAN3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "truuFnaksicb": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EMVq-YMLGAN3": {"values": {}, "mixins": [], "__type": "RuleSet"}, "_CAIp-Vax_us": {"name": "vgroup", "expr": {"__ref": "nBJW6yX1ei62"}, "__type": "NameArg"}, "Sn7qfTUESmCD": {"name": "operation", "expr": {"__ref": "epunppDqA2cb"}, "__type": "NameArg"}, "nBJW6yX1ei62": {"variable": {"__ref": "DR2AaaQys_pU"}, "__type": "VarRef"}, "f0SMpBGRGuLg": {"name": "destination", "expr": {"__ref": "enJdpC9a6qd3"}, "__type": "NameArg"}, "enJdpC9a6qd3": {"code": "\"/main/other-page\"", "fallback": null, "__type": "CustomCode"}, "9Ufc6Mu71aEE": {"name": "customFunction", "expr": {"__ref": "sDUcRZiWkTGt"}, "__type": "NameArg"}, "sDUcRZiWkTGt": {"argNames": [], "bodyExpr": {"__ref": "3o9gRnkCwprX"}, "__type": "FunctionExpr"}, "3o9gRnkCwprX": {"code": "(console.log(\"Running in main second publish\");)", "fallback": null, "__type": "CustomCode"}, "yMstG2CFCI-v": {"interactionName": "Run code", "actionName": "customFunction", "args": [{"__ref": "DUIIKS6H1Dm2"}], "condExpr": null, "conditionalMode": "always", "uuid": "lRaax4nr7SVk", "parent": {"__ref": "GQQ-iKl75wpm"}, "__type": "Interaction"}, "DUIIKS6H1Dm2": {"name": "customFunction", "expr": {"__ref": "U995HPgw7zJ4"}, "__type": "NameArg"}, "U995HPgw7zJ4": {"argNames": [], "bodyExpr": {"__ref": "hFlbS5WNN96s"}, "__type": "FunctionExpr"}, "hFlbS5WNN96s": {"code": "(console.log(\"Some log from main second version\");)", "fallback": null, "__type": "CustomCode"}}, "deps": [], "version": "251-add-data-tokens"}}]}