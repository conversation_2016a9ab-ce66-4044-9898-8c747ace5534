{"branches": [{"id": "kHeTwwUnVPJvKSEn57R4qs", "name": "test"}], "pkgVersions": [{"id": "3a9cad97-364f-4e97-abd1-bfab52e503da", "data": {"root": "64682001", "map": {"36067001": {"components": [{"__ref": "53903001"}, {"__ref": "53903002"}, {"__ref": "53903049"}, {"__ref": "53903082"}, {"__ref": "53903233"}, {"__ref": "53903310"}, {"__ref": "53903337"}], "arenas": [{"__ref": "36067002"}], "pageArenas": [], "componentArenas": [{"__ref": "53903083"}, {"__ref": "53903234"}, {"__ref": "53903311"}, {"__ref": "53903338"}], "globalVariantGroups": [{"__ref": "36067003"}], "userManagedFonts": [], "globalVariant": {"__ref": "36067007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "36067008"}], "activeTheme": {"__ref": "36067008"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "36067003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "36067002": {"name": "Custom arena 1", "children": [{"__ref": "53903050"}], "__type": "Arena"}, "36067003": {"type": "global-screen", "param": {"__ref": "36067004"}, "uuid": "iGBSJMZicCl", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "36067004": {"type": {"__ref": "36067006"}, "variable": {"__ref": "36067005"}, "uuid": "RwpYTpxAnFj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "36067005": {"name": "Screen", "uuid": "mFSn7PINq-G", "__type": "Var"}, "36067006": {"name": "text", "__type": "Text"}, "36067007": {"uuid": "5hGDUozB98K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36067008": {"defaultStyle": {"__ref": "36067009"}, "styles": [{"__ref": "36067024"}, {"__ref": "36067033"}, {"__ref": "36067042"}, {"__ref": "36067051"}, {"__ref": "36067060"}, {"__ref": "36067069"}, {"__ref": "36067077"}, {"__ref": "36067081"}, {"__ref": "36067085"}, {"__ref": "36067093"}, {"__ref": "36067118"}, {"__ref": "36067143"}, {"__ref": "36067154"}], "layout": {"__ref": "36067165"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "36067009": {"name": "Default Typography", "rs": {"__ref": "36067010"}, "preview": null, "uuid": "xPo4OMm9Fd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "36067024": {"selector": "h1", "style": {"__ref": "36067025"}, "__type": "ThemeStyle"}, "36067025": {"name": "Default \"h1\"", "rs": {"__ref": "36067026"}, "preview": null, "uuid": "bRf0zG4QA3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "36067033": {"selector": "h2", "style": {"__ref": "36067034"}, "__type": "ThemeStyle"}, "36067034": {"name": "Default \"h2\"", "rs": {"__ref": "36067035"}, "preview": null, "uuid": "dB8pmOn_N9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "36067042": {"selector": "h3", "style": {"__ref": "36067043"}, "__type": "ThemeStyle"}, "36067043": {"name": "Default \"h3\"", "rs": {"__ref": "36067044"}, "preview": null, "uuid": "IQbf_sDnUD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "36067051": {"selector": "h4", "style": {"__ref": "36067052"}, "__type": "ThemeStyle"}, "36067052": {"name": "Default \"h4\"", "rs": {"__ref": "36067053"}, "preview": null, "uuid": "cXkEtX1CdH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "36067060": {"selector": "h5", "style": {"__ref": "36067061"}, "__type": "ThemeStyle"}, "36067061": {"name": "Default \"h5\"", "rs": {"__ref": "36067062"}, "preview": null, "uuid": "B4ZP50j4nt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "36067069": {"selector": "h6", "style": {"__ref": "36067070"}, "__type": "ThemeStyle"}, "36067070": {"name": "Default \"h6\"", "rs": {"__ref": "36067071"}, "preview": null, "uuid": "4eN_P-oe5C", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "36067077": {"selector": "a", "style": {"__ref": "36067078"}, "__type": "ThemeStyle"}, "36067078": {"name": "Default \"a\"", "rs": {"__ref": "36067079"}, "preview": null, "uuid": "xmoUjn8krF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "36067081": {"selector": "a:hover", "style": {"__ref": "36067082"}, "__type": "ThemeStyle"}, "36067082": {"name": "Default \"a:hover\"", "rs": {"__ref": "36067083"}, "preview": null, "uuid": "6yCMQ6jm2f", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "36067085": {"selector": "blockquote", "style": {"__ref": "36067086"}, "__type": "ThemeStyle"}, "36067086": {"name": "Default \"blockquote\"", "rs": {"__ref": "36067087"}, "preview": null, "uuid": "EuQqvgccj3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "36067093": {"selector": "code", "style": {"__ref": "36067094"}, "__type": "ThemeStyle"}, "36067094": {"name": "Default \"code\"", "rs": {"__ref": "36067095"}, "preview": null, "uuid": "PVkq6JtA_W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "36067118": {"selector": "pre", "style": {"__ref": "36067119"}, "__type": "ThemeStyle"}, "36067119": {"name": "Default \"pre\"", "rs": {"__ref": "36067120"}, "preview": null, "uuid": "oP2rh7pkyob", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "36067143": {"selector": "ol", "style": {"__ref": "36067144"}, "__type": "ThemeStyle"}, "36067144": {"name": "Default \"ol\"", "rs": {"__ref": "36067145"}, "preview": null, "uuid": "FMA9pQU_msC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "36067154": {"selector": "ul", "style": {"__ref": "36067155"}, "__type": "ThemeStyle"}, "36067155": {"name": "Default \"ul\"", "rs": {"__ref": "36067156"}, "preview": null, "uuid": "Vpe_gR8_I4B", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "36067165": {"rs": {"__ref": "36067166"}, "__type": "ThemeLayoutSettings"}, "36067166": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903001": {"uuid": "qWmq-h9ODh", "name": "hostless-plasmic-head", "params": [{"__ref": "53903003"}, {"__ref": "53903004"}, {"__ref": "53903005"}, {"__ref": "53903006"}], "states": [], "tplTree": {"__ref": "53903007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "53903009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903002": {"uuid": "IMw9swUYCu", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "53903010"}, {"__ref": "53903011"}, {"__ref": "53903012"}, {"__ref": "53903013"}, {"__ref": "53903014"}], "states": [], "tplTree": {"__ref": "53903015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "53903017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903003": {"type": {"__ref": "53903019"}, "variable": {"__ref": "53903018"}, "uuid": "8qq-0hSrJf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903004": {"type": {"__ref": "53903021"}, "variable": {"__ref": "53903020"}, "uuid": "w76TiK4TAU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903005": {"type": {"__ref": "53903023"}, "variable": {"__ref": "53903022"}, "uuid": "rxWx-2Ubs1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903006": {"type": {"__ref": "53903025"}, "variable": {"__ref": "53903024"}, "uuid": "084egcc_CT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7PT6i-WeGn", "parent": null, "locked": null, "vsettings": [{"__ref": "53903026"}], "__type": "TplTag"}, "53903008": {"uuid": "-Zmygg1XG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "53903010": {"type": {"__ref": "53903028"}, "variable": {"__ref": "53903027"}, "uuid": "EaHCNjCUAB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903011": {"type": {"__ref": "53903030"}, "variable": {"__ref": "53903029"}, "uuid": "-yHUBqlCptE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903012": {"type": {"__ref": "53903032"}, "tplSlot": {"__ref": "53903037"}, "variable": {"__ref": "53903031"}, "uuid": "ozr-iK4Pv_L", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903013": {"type": {"__ref": "53903034"}, "variable": {"__ref": "53903033"}, "uuid": "DIWnirbqmDb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903014": {"type": {"__ref": "53903036"}, "variable": {"__ref": "53903035"}, "uuid": "ihK5IoUFQ6v", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903015": {"tag": "div", "name": null, "children": [{"__ref": "53903037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5nVc7ktXnn", "parent": null, "locked": null, "vsettings": [{"__ref": "53903038"}], "__type": "TplTag"}, "53903016": {"uuid": "OT3oJCqKEm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "53903018": {"name": "title", "uuid": "5w9GaJmpCQ", "__type": "Var"}, "53903019": {"name": "text", "__type": "Text"}, "53903020": {"name": "description", "uuid": "yOVFpGUlez", "__type": "Var"}, "53903021": {"name": "text", "__type": "Text"}, "53903022": {"name": "image", "uuid": "kv3ah8yHlN", "__type": "Var"}, "53903023": {"name": "img", "__type": "Img"}, "53903024": {"name": "canonical", "uuid": "OWieep-MIe", "__type": "Var"}, "53903025": {"name": "text", "__type": "Text"}, "53903026": {"variants": [{"__ref": "53903008"}], "args": [], "attrs": {}, "rs": {"__ref": "53903039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903027": {"name": "dataOp", "uuid": "2rInNx1JP5", "__type": "Var"}, "53903028": {"name": "any", "__type": "AnyType"}, "53903029": {"name": "name", "uuid": "_mwn6YlzdtO", "__type": "Var"}, "53903030": {"name": "text", "__type": "Text"}, "53903031": {"name": "children", "uuid": "ZmrIiicdqCH", "__type": "Var"}, "53903032": {"name": "renderFunc", "params": [{"__ref": "53903040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "53903033": {"name": "pageSize", "uuid": "xXH0ZudPRcm", "__type": "Var"}, "53903034": {"name": "num", "__type": "<PERSON><PERSON>"}, "53903035": {"name": "pageIndex", "uuid": "fXxdD2tli_Y", "__type": "Var"}, "53903036": {"name": "num", "__type": "<PERSON><PERSON>"}, "53903037": {"param": {"__ref": "53903012"}, "defaultContents": [], "uuid": "XX9ReAPqdTq", "parent": {"__ref": "53903015"}, "locked": null, "vsettings": [{"__ref": "53903041"}], "__type": "TplSlot"}, "53903038": {"variants": [{"__ref": "53903016"}], "args": [], "attrs": {}, "rs": {"__ref": "53903042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "53903040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "53903045"}, "__type": "ArgType"}, "53903041": {"variants": [{"__ref": "53903016"}], "args": [], "attrs": {}, "rs": {"__ref": "53903046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "53903045": {"name": "any", "__type": "AnyType"}, "53903046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903049": {"uuid": "ctkwFVWWSn", "name": "", "params": [], "states": [], "tplTree": {"__ref": "53903051"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903052"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903050": {"uuid": "nwztLgMhtp", "width": 834, "height": 1194, "container": {"__ref": "53903053"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "mainFrame", "top": 31, "left": 826, "__type": "ArenaFrame"}, "53903051": {"tag": "div", "name": null, "children": [{"__ref": "53903081"}, {"__ref": "53903191"}, {"__ref": "53903268"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "jlUocrUXx", "parent": null, "locked": null, "vsettings": [{"__ref": "53903054"}], "__type": "TplTag"}, "53903052": {"uuid": "JtmW5TCXcJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903053": {"name": null, "component": {"__ref": "53903049"}, "uuid": "WhhDN143T-", "parent": null, "locked": null, "vsettings": [{"__ref": "53903055"}], "__type": "TplComponent"}, "53903054": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903056"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903055": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903057"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903056": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "53903057": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903081": {"name": null, "component": {"__ref": "53903082"}, "uuid": "lOhwymcpxc", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903084"}], "__type": "TplComponent"}, "53903082": {"uuid": "1D9Qxb0AO7", "name": "TestSection", "params": [{"__ref": "53903125"}, {"__ref": "53903133"}, {"__ref": "53903161"}], "states": [], "tplTree": {"__ref": "53903085"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903086"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903083": {"component": {"__ref": "53903082"}, "matrix": {"__ref": "53903087"}, "customMatrix": {"__ref": "53903088"}, "__type": "ComponentArena"}, "53903084": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903127"}, {"__ref": "53903135"}, {"__ref": "53903163"}], "attrs": {}, "rs": {"__ref": "53903089"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903085": {"tag": "div", "name": null, "children": [{"__ref": "53903151"}, {"__ref": "53903162"}, {"__ref": "53903117"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "TLhCbsO7M", "parent": null, "locked": null, "vsettings": [{"__ref": "53903090"}], "__type": "TplTag"}, "53903086": {"uuid": "u1F6FSSSK_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903087": {"rows": [{"__ref": "53903091"}], "__type": "ArenaFrameGrid"}, "53903088": {"rows": [{"__ref": "53903092"}], "__type": "ArenaFrameGrid"}, "53903089": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "53903090": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903096"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903091": {"cols": [{"__ref": "53903097"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903092": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903096": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative", "row-gap": "12px"}, "mixins": [], "__type": "RuleSet"}, "53903097": {"frame": {"__ref": "53903103"}, "cellKey": {"__ref": "53903086"}, "__type": "ArenaFrameCell"}, "53903103": {"uuid": "NjyMJmZRY2", "width": 340, "height": 340, "container": {"__ref": "53903104"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903086"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903104": {"name": null, "component": {"__ref": "53903082"}, "uuid": "mnxAs3crU5", "parent": null, "locked": null, "vsettings": [{"__ref": "53903105"}], "__type": "TplComponent"}, "53903105": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903106"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903106": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903117": {"tag": "div", "name": null, "children": [{"__ref": "53903126"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "XwElxYMrB", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903118"}], "__type": "TplTag"}, "53903118": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903119"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903119": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903125": {"type": {"__ref": "53903129"}, "tplSlot": {"__ref": "53903126"}, "variable": {"__ref": "53903128"}, "uuid": "NivoufBeJ-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903126": {"param": {"__ref": "53903125"}, "defaultContents": [], "uuid": "7GUW_P2FHH", "parent": {"__ref": "53903117"}, "locked": null, "vsettings": [{"__ref": "53903130"}], "__type": "TplSlot"}, "53903127": {"param": {"__ref": "53903125"}, "expr": {"__ref": "53903182"}, "__type": "Arg"}, "53903128": {"name": "children", "uuid": "2-oSj8MB1", "__type": "Var"}, "53903129": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903130": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903132"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903132": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903133": {"type": {"__ref": "53903137"}, "tplSlot": {"__ref": "53903134"}, "variable": {"__ref": "53903136"}, "uuid": "ifm0hmIzEb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903134": {"param": {"__ref": "53903133"}, "defaultContents": [{"__ref": "53903138"}], "uuid": "u_-9vmIoGV", "parent": {"__ref": "53903151"}, "locked": null, "vsettings": [{"__ref": "53903139"}], "__type": "TplSlot"}, "53903135": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903150"}, "__type": "Arg"}, "53903136": {"name": "title", "uuid": "ZXFwxIDAp", "__type": "Var"}, "53903137": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903138": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "zSsK_1HEC3", "parent": {"__ref": "53903134"}, "locked": null, "vsettings": [{"__ref": "53903141"}], "__type": "TplTag"}, "53903139": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903142"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903141": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903144"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903145"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903142": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903143": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "L_1AFOMJew", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903146"}], "__type": "TplTag"}, "53903144": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903145": {"markers": [], "text": "Enter some text", "__type": "RawText"}, "53903146": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903147"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903179"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903147": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903150": {"tpl": [{"__ref": "53903143"}], "__type": "RenderExpr"}, "53903151": {"tag": "h4", "name": null, "children": [{"__ref": "53903134"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "YbB548fVl", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903152"}], "__type": "TplTag"}, "53903152": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903153"}, "dataCond": {"__ref": "53903154"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903153": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "53903154": {"code": "true", "fallback": null, "__type": "CustomCode"}, "53903161": {"type": {"__ref": "53903165"}, "tplSlot": {"__ref": "53903162"}, "variable": {"__ref": "53903164"}, "uuid": "reztUKnNT_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903162": {"param": {"__ref": "53903161"}, "defaultContents": [{"__ref": "53903166"}], "uuid": "FpJwn5yyRy", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903167"}], "__type": "TplSlot"}, "53903163": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903181"}, "__type": "Arg"}, "53903164": {"name": "description", "uuid": "wRK72UFUx", "__type": "Var"}, "53903165": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903166": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cIbwDiDRAA", "parent": {"__ref": "53903162"}, "locked": null, "vsettings": [{"__ref": "53903169"}], "__type": "TplTag"}, "53903167": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903170"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903169": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903172"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903173"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903170": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903171": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "YEyox_O7By", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903174"}], "__type": "TplTag"}, "53903172": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903173": {"markers": [], "text": "Enter some text", "__type": "RawText"}, "53903174": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903175"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903180"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903175": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903179": {"markers": [], "text": "1 - Delete node in one branch, move to new TplTag in the other", "__type": "RawText"}, "53903180": {"markers": [], "text": "One branch deletes \"section1tpl\", the other moves it to a new tpl tag \"section1newTpl\"", "__type": "RawText"}, "53903181": {"tpl": [{"__ref": "53903171"}], "__type": "RenderExpr"}, "53903182": {"tpl": [{"__ref": "53903183"}], "__type": "RenderExpr"}, "53903183": {"tag": "div", "name": "section1tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "cBVW4bOxC", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903184"}], "__type": "TplTag"}, "53903184": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903185"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903185": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903191": {"name": null, "component": {"__ref": "53903082"}, "uuid": "35jcOMX41", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903192"}], "__type": "TplComponent"}, "53903192": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903193"}, {"__ref": "53903194"}, {"__ref": "53903195"}], "attrs": {}, "rs": {"__ref": "53903196"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903193": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903211"}, "__type": "Arg"}, "53903194": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903213"}, "__type": "Arg"}, "53903195": {"param": {"__ref": "53903125"}, "expr": {"__ref": "53903267"}, "__type": "Arg"}, "53903196": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903202": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ak91hkyVRs", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903204"}], "__type": "TplTag"}, "53903203": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xbdhdeS4mC", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903205"}], "__type": "TplTag"}, "53903204": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903206"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903210"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903205": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903208"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903212"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903206": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903208": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903210": {"markers": [], "text": "2 - Delete node in one branch, move to new TplComp in the other", "__type": "RawText"}, "53903211": {"tpl": [{"__ref": "53903202"}], "__type": "RenderExpr"}, "53903212": {"markers": [], "text": "One branch deletes \"section2tpl\", the other moves it to a new tpl component \"section2comp\"", "__type": "RawText"}, "53903213": {"tpl": [{"__ref": "53903203"}], "__type": "RenderExpr"}, "53903215": {"tag": "div", "name": "section2tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "FJhcKIwp4", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903216"}], "__type": "TplTag"}, "53903216": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903217"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903217": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903233": {"uuid": "Kb8NzqUy2M", "name": "Section2Comp", "params": [{"__ref": "53903259"}], "states": [], "tplTree": {"__ref": "53903236"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903237"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903234": {"component": {"__ref": "53903233"}, "matrix": {"__ref": "53903238"}, "customMatrix": {"__ref": "53903239"}, "__type": "ComponentArena"}, "53903236": {"tag": "div", "name": null, "children": [{"__ref": "53903260"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UIb7mybKb", "parent": null, "locked": null, "vsettings": [{"__ref": "53903241"}], "__type": "TplTag"}, "53903237": {"uuid": "osT_Wb5IXO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903238": {"rows": [{"__ref": "53903242"}], "__type": "ArenaFrameGrid"}, "53903239": {"rows": [{"__ref": "53903243"}], "__type": "ArenaFrameGrid"}, "53903241": {"variants": [{"__ref": "53903237"}], "args": [], "attrs": {}, "rs": {"__ref": "53903245"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903242": {"cols": [{"__ref": "53903246"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903243": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903245": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903246": {"frame": {"__ref": "53903255"}, "cellKey": {"__ref": "53903237"}, "__type": "ArenaFrameCell"}, "53903255": {"uuid": "Z7bv26rtLX", "width": 340, "height": 340, "container": {"__ref": "53903256"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903237"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903256": {"name": null, "component": {"__ref": "53903233"}, "uuid": "VR9QBAk-P-", "parent": null, "locked": null, "vsettings": [{"__ref": "53903257"}], "__type": "TplComponent"}, "53903257": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903258": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903259": {"type": {"__ref": "53903263"}, "tplSlot": {"__ref": "53903260"}, "variable": {"__ref": "53903262"}, "uuid": "JM4HCS94W1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903260": {"param": {"__ref": "53903259"}, "defaultContents": [], "uuid": "kZ7xuHl9XX", "parent": {"__ref": "53903236"}, "locked": null, "vsettings": [{"__ref": "53903264"}], "__type": "TplSlot"}, "53903262": {"name": "children", "uuid": "1P3_HQEak", "__type": "Var"}, "53903263": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903264": {"variants": [{"__ref": "53903237"}], "args": [], "attrs": {}, "rs": {"__ref": "53903266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903266": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903267": {"tpl": [{"__ref": "53903215"}], "__type": "RenderExpr"}, "53903268": {"name": null, "component": {"__ref": "53903082"}, "uuid": "AYOfshtE2", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903269"}], "__type": "TplComponent"}, "53903269": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903270"}, {"__ref": "53903271"}, {"__ref": "53903272"}], "attrs": {}, "rs": {"__ref": "53903273"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903270": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903288"}, "__type": "Arg"}, "53903271": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903290"}, "__type": "Arg"}, "53903272": {"param": {"__ref": "53903125"}, "expr": {"__ref": "53903380"}, "__type": "Arg"}, "53903273": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903279": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "JmUpu3UY7-", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903281"}], "__type": "TplTag"}, "53903280": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "4x3dc1koY3", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903282"}], "__type": "TplTag"}, "53903281": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903283"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903287"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903282": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903285"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903289"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903283": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903285": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903287": {"markers": [], "text": "3 - Regression test - dangling tpl comp referencing deleted param", "__type": "RawText"}, "53903288": {"tpl": [{"__ref": "53903279"}], "__type": "RenderExpr"}, "53903289": {"markers": [], "text": "One branch deletes \"Section3Comp1\" and deletes the slot of \"Section3Comp2\". The other creates a new tpl component of \"Section3Comp1\", whose children contains a new instance of \"Section3Comp2\" with a slot arg referencing the deleted param.", "__type": "RawText"}, "53903290": {"tpl": [{"__ref": "53903280"}], "__type": "RenderExpr"}, "53903310": {"uuid": "i83l6q9eHi", "name": "Section3Comp1", "params": [{"__ref": "53903363"}], "states": [], "tplTree": {"__ref": "53903313"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903314"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903311": {"component": {"__ref": "53903310"}, "matrix": {"__ref": "53903315"}, "customMatrix": {"__ref": "53903316"}, "__type": "ComponentArena"}, "53903313": {"tag": "div", "name": null, "children": [{"__ref": "53903364"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lxfJRAlIK", "parent": null, "locked": null, "vsettings": [{"__ref": "53903318"}], "__type": "TplTag"}, "53903314": {"uuid": "FNg3H4U6Tb", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903315": {"rows": [{"__ref": "53903319"}], "__type": "ArenaFrameGrid"}, "53903316": {"rows": [{"__ref": "53903320"}], "__type": "ArenaFrameGrid"}, "53903318": {"variants": [{"__ref": "53903314"}], "args": [], "attrs": {}, "rs": {"__ref": "53903322"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903319": {"cols": [{"__ref": "53903323"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903320": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903322": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903323": {"frame": {"__ref": "53903332"}, "cellKey": {"__ref": "53903314"}, "__type": "ArenaFrameCell"}, "53903332": {"uuid": "M5VXWk219U", "width": 340, "height": 340, "container": {"__ref": "53903333"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903314"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903333": {"name": null, "component": {"__ref": "53903310"}, "uuid": "yd4pAC5vDg", "parent": null, "locked": null, "vsettings": [{"__ref": "53903334"}], "__type": "TplComponent"}, "53903334": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903335"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903335": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903337": {"uuid": "tS05uFhSW", "name": "Section3Comp2", "params": [{"__ref": "53903371"}], "states": [], "tplTree": {"__ref": "53903340"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903341"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903338": {"component": {"__ref": "53903337"}, "matrix": {"__ref": "53903342"}, "customMatrix": {"__ref": "53903343"}, "__type": "ComponentArena"}, "53903340": {"tag": "div", "name": null, "children": [{"__ref": "53903372"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "arTUpxk5R", "parent": null, "locked": null, "vsettings": [{"__ref": "53903345"}], "__type": "TplTag"}, "53903341": {"uuid": "mvifJnTI9M", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903342": {"rows": [{"__ref": "53903346"}], "__type": "ArenaFrameGrid"}, "53903343": {"rows": [{"__ref": "53903347"}], "__type": "ArenaFrameGrid"}, "53903345": {"variants": [{"__ref": "53903341"}], "args": [], "attrs": {}, "rs": {"__ref": "53903349"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903346": {"cols": [{"__ref": "53903350"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903347": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903349": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903350": {"frame": {"__ref": "53903359"}, "cellKey": {"__ref": "53903341"}, "__type": "ArenaFrameCell"}, "53903359": {"uuid": "iiERKW0rWO", "width": 340, "height": 340, "container": {"__ref": "53903360"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903341"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903360": {"name": null, "component": {"__ref": "53903337"}, "uuid": "kXmyWI4b0c", "parent": null, "locked": null, "vsettings": [{"__ref": "53903361"}], "__type": "TplComponent"}, "53903361": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903362"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903362": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903363": {"type": {"__ref": "53903367"}, "tplSlot": {"__ref": "53903364"}, "variable": {"__ref": "53903366"}, "uuid": "YBiIX2-QxI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903364": {"param": {"__ref": "53903363"}, "defaultContents": [], "uuid": "oCMZrejyV9", "parent": {"__ref": "53903313"}, "locked": null, "vsettings": [{"__ref": "53903368"}], "__type": "TplSlot"}, "53903366": {"name": "children", "uuid": "jkCLyDVS3", "__type": "Var"}, "53903367": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903368": {"variants": [{"__ref": "53903314"}], "args": [], "attrs": {}, "rs": {"__ref": "53903370"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903370": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903371": {"type": {"__ref": "53903375"}, "tplSlot": {"__ref": "53903372"}, "variable": {"__ref": "53903374"}, "uuid": "9izKkFQyu0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903372": {"param": {"__ref": "53903371"}, "defaultContents": [], "uuid": "OCrNQHcKiY", "parent": {"__ref": "53903340"}, "locked": null, "vsettings": [{"__ref": "53903376"}], "__type": "TplSlot"}, "53903374": {"name": "children", "uuid": "i5ADNAhdj", "__type": "Var"}, "53903375": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903376": {"variants": [{"__ref": "53903341"}], "args": [], "attrs": {}, "rs": {"__ref": "53903378"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903378": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903380": {"tpl": [], "__type": "RenderExpr"}, "64682001": {"uuid": "82BUhhow1", "pkgId": "c6abbb42-1b95-4c5d-ab4e-60b57d85d1ae", "projectId": "2YAtuqUruz4SscKrr5g25x", "version": "0.0.1", "name": "test_edge_cases_merge_2", "site": {"__ref": "36067001"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}, "projectId": "2YAtuqUruz4SscKrr5g25x", "version": "0.0.1", "branchId": "main"}], "project": {"id": "2YAtuqUruz4SscKrr5g25x", "name": "test_edge_cases_merge_2", "commitGraph": {"parents": {"3a9cad97-364f-4e97-abd1-bfab52e503da": []}, "branches": {"main": "3a9cad97-364f-4e97-abd1-bfab52e503da", "kHeTwwUnVPJvKSEn57R4qs": "3a9cad97-364f-4e97-abd1-bfab52e503da"}}}, "revisions": [{"branchId": "kHeTwwUnVPJvKSEn57R4qs", "data": {"root": "36067001", "map": {"30076001": {"tpl": [], "__type": "RenderExpr"}, "30076002": {"tpl": [], "__type": "RenderExpr"}, "30076011": {"tpl": [], "__type": "RenderExpr"}, "36067001": {"components": [{"__ref": "53903001"}, {"__ref": "53903002"}, {"__ref": "53903049"}, {"__ref": "53903082"}, {"__ref": "53903233"}, {"__ref": "53903337"}], "arenas": [{"__ref": "36067002"}], "pageArenas": [], "componentArenas": [{"__ref": "53903083"}, {"__ref": "53903234"}, {"__ref": "53903338"}], "globalVariantGroups": [{"__ref": "36067003"}], "userManagedFonts": [], "globalVariant": {"__ref": "36067007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "36067008"}], "activeTheme": {"__ref": "36067008"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "36067003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "36067002": {"name": "Custom arena 1", "children": [{"__ref": "53903050"}], "__type": "Arena"}, "36067003": {"type": "global-screen", "param": {"__ref": "36067004"}, "uuid": "iGBSJMZicCl", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "36067004": {"type": {"__ref": "36067006"}, "variable": {"__ref": "36067005"}, "uuid": "RwpYTpxAnFj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "36067005": {"name": "Screen", "uuid": "mFSn7PINq-G", "__type": "Var"}, "36067006": {"name": "text", "__type": "Text"}, "36067007": {"uuid": "5hGDUozB98K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36067008": {"defaultStyle": {"__ref": "36067009"}, "styles": [{"__ref": "36067024"}, {"__ref": "36067033"}, {"__ref": "36067042"}, {"__ref": "36067051"}, {"__ref": "36067060"}, {"__ref": "36067069"}, {"__ref": "36067077"}, {"__ref": "36067081"}, {"__ref": "36067085"}, {"__ref": "36067093"}, {"__ref": "36067118"}, {"__ref": "36067143"}, {"__ref": "36067154"}], "layout": {"__ref": "36067165"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "36067009": {"name": "Default Typography", "rs": {"__ref": "36067010"}, "preview": null, "uuid": "xPo4OMm9Fd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "36067024": {"selector": "h1", "style": {"__ref": "36067025"}, "__type": "ThemeStyle"}, "36067025": {"name": "Default \"h1\"", "rs": {"__ref": "36067026"}, "preview": null, "uuid": "bRf0zG4QA3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "36067033": {"selector": "h2", "style": {"__ref": "36067034"}, "__type": "ThemeStyle"}, "36067034": {"name": "Default \"h2\"", "rs": {"__ref": "36067035"}, "preview": null, "uuid": "dB8pmOn_N9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "36067042": {"selector": "h3", "style": {"__ref": "36067043"}, "__type": "ThemeStyle"}, "36067043": {"name": "Default \"h3\"", "rs": {"__ref": "36067044"}, "preview": null, "uuid": "IQbf_sDnUD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "36067051": {"selector": "h4", "style": {"__ref": "36067052"}, "__type": "ThemeStyle"}, "36067052": {"name": "Default \"h4\"", "rs": {"__ref": "36067053"}, "preview": null, "uuid": "cXkEtX1CdH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "36067060": {"selector": "h5", "style": {"__ref": "36067061"}, "__type": "ThemeStyle"}, "36067061": {"name": "Default \"h5\"", "rs": {"__ref": "36067062"}, "preview": null, "uuid": "B4ZP50j4nt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "36067069": {"selector": "h6", "style": {"__ref": "36067070"}, "__type": "ThemeStyle"}, "36067070": {"name": "Default \"h6\"", "rs": {"__ref": "36067071"}, "preview": null, "uuid": "4eN_P-oe5C", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "36067077": {"selector": "a", "style": {"__ref": "36067078"}, "__type": "ThemeStyle"}, "36067078": {"name": "Default \"a\"", "rs": {"__ref": "36067079"}, "preview": null, "uuid": "xmoUjn8krF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "36067081": {"selector": "a:hover", "style": {"__ref": "36067082"}, "__type": "ThemeStyle"}, "36067082": {"name": "Default \"a:hover\"", "rs": {"__ref": "36067083"}, "preview": null, "uuid": "6yCMQ6jm2f", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "36067085": {"selector": "blockquote", "style": {"__ref": "36067086"}, "__type": "ThemeStyle"}, "36067086": {"name": "Default \"blockquote\"", "rs": {"__ref": "36067087"}, "preview": null, "uuid": "EuQqvgccj3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "36067093": {"selector": "code", "style": {"__ref": "36067094"}, "__type": "ThemeStyle"}, "36067094": {"name": "Default \"code\"", "rs": {"__ref": "36067095"}, "preview": null, "uuid": "PVkq6JtA_W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "36067118": {"selector": "pre", "style": {"__ref": "36067119"}, "__type": "ThemeStyle"}, "36067119": {"name": "Default \"pre\"", "rs": {"__ref": "36067120"}, "preview": null, "uuid": "oP2rh7pkyob", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "36067143": {"selector": "ol", "style": {"__ref": "36067144"}, "__type": "ThemeStyle"}, "36067144": {"name": "Default \"ol\"", "rs": {"__ref": "36067145"}, "preview": null, "uuid": "FMA9pQU_msC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "36067154": {"selector": "ul", "style": {"__ref": "36067155"}, "__type": "ThemeStyle"}, "36067155": {"name": "Default \"ul\"", "rs": {"__ref": "36067156"}, "preview": null, "uuid": "Vpe_gR8_I4B", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "36067165": {"rs": {"__ref": "36067166"}, "__type": "ThemeLayoutSettings"}, "36067166": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903001": {"uuid": "qWmq-h9ODh", "name": "hostless-plasmic-head", "params": [{"__ref": "53903003"}, {"__ref": "53903004"}, {"__ref": "53903005"}, {"__ref": "53903006"}], "states": [], "tplTree": {"__ref": "53903007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "53903009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903002": {"uuid": "IMw9swUYCu", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "53903010"}, {"__ref": "53903011"}, {"__ref": "53903012"}, {"__ref": "53903013"}, {"__ref": "53903014"}], "states": [], "tplTree": {"__ref": "53903015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "53903017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903003": {"type": {"__ref": "53903019"}, "variable": {"__ref": "53903018"}, "uuid": "8qq-0hSrJf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903004": {"type": {"__ref": "53903021"}, "variable": {"__ref": "53903020"}, "uuid": "w76TiK4TAU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903005": {"type": {"__ref": "53903023"}, "variable": {"__ref": "53903022"}, "uuid": "rxWx-2Ubs1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903006": {"type": {"__ref": "53903025"}, "variable": {"__ref": "53903024"}, "uuid": "084egcc_CT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7PT6i-WeGn", "parent": null, "locked": null, "vsettings": [{"__ref": "53903026"}], "__type": "TplTag"}, "53903008": {"uuid": "-Zmygg1XG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "53903010": {"type": {"__ref": "53903028"}, "variable": {"__ref": "53903027"}, "uuid": "EaHCNjCUAB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903011": {"type": {"__ref": "53903030"}, "variable": {"__ref": "53903029"}, "uuid": "-yHUBqlCptE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903012": {"type": {"__ref": "53903032"}, "tplSlot": {"__ref": "53903037"}, "variable": {"__ref": "53903031"}, "uuid": "ozr-iK4Pv_L", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903013": {"type": {"__ref": "53903034"}, "variable": {"__ref": "53903033"}, "uuid": "DIWnirbqmDb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903014": {"type": {"__ref": "53903036"}, "variable": {"__ref": "53903035"}, "uuid": "ihK5IoUFQ6v", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903015": {"tag": "div", "name": null, "children": [{"__ref": "53903037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5nVc7ktXnn", "parent": null, "locked": null, "vsettings": [{"__ref": "53903038"}], "__type": "TplTag"}, "53903016": {"uuid": "OT3oJCqKEm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "53903018": {"name": "title", "uuid": "5w9GaJmpCQ", "__type": "Var"}, "53903019": {"name": "text", "__type": "Text"}, "53903020": {"name": "description", "uuid": "yOVFpGUlez", "__type": "Var"}, "53903021": {"name": "text", "__type": "Text"}, "53903022": {"name": "image", "uuid": "kv3ah8yHlN", "__type": "Var"}, "53903023": {"name": "img", "__type": "Img"}, "53903024": {"name": "canonical", "uuid": "OWieep-MIe", "__type": "Var"}, "53903025": {"name": "text", "__type": "Text"}, "53903026": {"variants": [{"__ref": "53903008"}], "args": [], "attrs": {}, "rs": {"__ref": "53903039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903027": {"name": "dataOp", "uuid": "2rInNx1JP5", "__type": "Var"}, "53903028": {"name": "any", "__type": "AnyType"}, "53903029": {"name": "name", "uuid": "_mwn6YlzdtO", "__type": "Var"}, "53903030": {"name": "text", "__type": "Text"}, "53903031": {"name": "children", "uuid": "ZmrIiicdqCH", "__type": "Var"}, "53903032": {"name": "renderFunc", "params": [{"__ref": "53903040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "53903033": {"name": "pageSize", "uuid": "xXH0ZudPRcm", "__type": "Var"}, "53903034": {"name": "num", "__type": "<PERSON><PERSON>"}, "53903035": {"name": "pageIndex", "uuid": "fXxdD2tli_Y", "__type": "Var"}, "53903036": {"name": "num", "__type": "<PERSON><PERSON>"}, "53903037": {"param": {"__ref": "53903012"}, "defaultContents": [], "uuid": "XX9ReAPqdTq", "parent": {"__ref": "53903015"}, "locked": null, "vsettings": [{"__ref": "53903041"}], "__type": "TplSlot"}, "53903038": {"variants": [{"__ref": "53903016"}], "args": [], "attrs": {}, "rs": {"__ref": "53903042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "53903040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "53903045"}, "__type": "ArgType"}, "53903041": {"variants": [{"__ref": "53903016"}], "args": [], "attrs": {}, "rs": {"__ref": "53903046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "53903045": {"name": "any", "__type": "AnyType"}, "53903046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903049": {"uuid": "ctkwFVWWSn", "name": "", "params": [], "states": [], "tplTree": {"__ref": "53903051"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903052"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903050": {"uuid": "nwztLgMhtp", "width": 834, "height": 1194, "container": {"__ref": "53903053"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "mainFrame", "top": 31, "left": 826, "__type": "ArenaFrame"}, "53903051": {"tag": "div", "name": null, "children": [{"__ref": "53903081"}, {"__ref": "53903191"}, {"__ref": "53903268"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "jlUocrUXx", "parent": null, "locked": null, "vsettings": [{"__ref": "53903054"}], "__type": "TplTag"}, "53903052": {"uuid": "JtmW5TCXcJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903053": {"name": null, "component": {"__ref": "53903049"}, "uuid": "WhhDN143T-", "parent": null, "locked": null, "vsettings": [{"__ref": "53903055"}], "__type": "TplComponent"}, "53903054": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903056"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903055": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903057"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903056": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "53903057": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903081": {"name": null, "component": {"__ref": "53903082"}, "uuid": "lOhwymcpxc", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903084"}], "__type": "TplComponent"}, "53903082": {"uuid": "1D9Qxb0AO7", "name": "TestSection", "params": [{"__ref": "53903125"}, {"__ref": "53903133"}, {"__ref": "53903161"}], "states": [], "tplTree": {"__ref": "53903085"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903086"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903083": {"component": {"__ref": "53903082"}, "matrix": {"__ref": "53903087"}, "customMatrix": {"__ref": "53903088"}, "__type": "ComponentArena"}, "53903084": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903127"}, {"__ref": "53903135"}, {"__ref": "53903163"}], "attrs": {}, "rs": {"__ref": "53903089"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903085": {"tag": "div", "name": null, "children": [{"__ref": "53903151"}, {"__ref": "53903162"}, {"__ref": "53903117"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "TLhCbsO7M", "parent": null, "locked": null, "vsettings": [{"__ref": "53903090"}], "__type": "TplTag"}, "53903086": {"uuid": "u1F6FSSSK_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903087": {"rows": [{"__ref": "53903091"}], "__type": "ArenaFrameGrid"}, "53903088": {"rows": [{"__ref": "53903092"}], "__type": "ArenaFrameGrid"}, "53903089": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "53903090": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903096"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903091": {"cols": [{"__ref": "53903097"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903092": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903096": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative", "row-gap": "12px"}, "mixins": [], "__type": "RuleSet"}, "53903097": {"frame": {"__ref": "53903103"}, "cellKey": {"__ref": "53903086"}, "__type": "ArenaFrameCell"}, "53903103": {"uuid": "NjyMJmZRY2", "width": 340, "height": 340, "container": {"__ref": "53903104"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903086"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903104": {"name": null, "component": {"__ref": "53903082"}, "uuid": "mnxAs3crU5", "parent": null, "locked": null, "vsettings": [{"__ref": "53903105"}], "__type": "TplComponent"}, "53903105": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903106"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903106": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903117": {"tag": "div", "name": null, "children": [{"__ref": "53903126"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "XwElxYMrB", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903118"}], "__type": "TplTag"}, "53903118": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903119"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903119": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903125": {"type": {"__ref": "53903129"}, "tplSlot": {"__ref": "53903126"}, "variable": {"__ref": "53903128"}, "uuid": "NivoufBeJ-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903126": {"param": {"__ref": "53903125"}, "defaultContents": [], "uuid": "7GUW_P2FHH", "parent": {"__ref": "53903117"}, "locked": null, "vsettings": [{"__ref": "53903130"}], "__type": "TplSlot"}, "53903127": {"param": {"__ref": "53903125"}, "expr": {"__ref": "30076001"}, "__type": "Arg"}, "53903128": {"name": "children", "uuid": "2-oSj8MB1", "__type": "Var"}, "53903129": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903130": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903132"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903132": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903133": {"type": {"__ref": "53903137"}, "tplSlot": {"__ref": "53903134"}, "variable": {"__ref": "53903136"}, "uuid": "ifm0hmIzEb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903134": {"param": {"__ref": "53903133"}, "defaultContents": [{"__ref": "53903138"}], "uuid": "u_-9vmIoGV", "parent": {"__ref": "53903151"}, "locked": null, "vsettings": [{"__ref": "53903139"}], "__type": "TplSlot"}, "53903135": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903150"}, "__type": "Arg"}, "53903136": {"name": "title", "uuid": "ZXFwxIDAp", "__type": "Var"}, "53903137": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903138": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "zSsK_1HEC3", "parent": {"__ref": "53903134"}, "locked": null, "vsettings": [{"__ref": "53903141"}], "__type": "TplTag"}, "53903139": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903142"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903141": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903144"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903145"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903142": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903143": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "L_1AFOMJew", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903146"}], "__type": "TplTag"}, "53903144": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903145": {"markers": [], "text": "Enter some text", "__type": "RawText"}, "53903146": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903147"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903179"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903147": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903150": {"tpl": [{"__ref": "53903143"}], "__type": "RenderExpr"}, "53903151": {"tag": "h4", "name": null, "children": [{"__ref": "53903134"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "YbB548fVl", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903152"}], "__type": "TplTag"}, "53903152": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903153"}, "dataCond": {"__ref": "53903154"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903153": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "53903154": {"code": "true", "fallback": null, "__type": "CustomCode"}, "53903161": {"type": {"__ref": "53903165"}, "tplSlot": {"__ref": "53903162"}, "variable": {"__ref": "53903164"}, "uuid": "reztUKnNT_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903162": {"param": {"__ref": "53903161"}, "defaultContents": [{"__ref": "53903166"}], "uuid": "FpJwn5yyRy", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903167"}], "__type": "TplSlot"}, "53903163": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903181"}, "__type": "Arg"}, "53903164": {"name": "description", "uuid": "wRK72UFUx", "__type": "Var"}, "53903165": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903166": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cIbwDiDRAA", "parent": {"__ref": "53903162"}, "locked": null, "vsettings": [{"__ref": "53903169"}], "__type": "TplTag"}, "53903167": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903170"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903169": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903172"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903173"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903170": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903171": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "YEyox_O7By", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903174"}], "__type": "TplTag"}, "53903172": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903173": {"markers": [], "text": "Enter some text", "__type": "RawText"}, "53903174": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903175"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903180"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903175": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903179": {"markers": [], "text": "1 - Delete node in one branch, move to new TplTag in the other", "__type": "RawText"}, "53903180": {"markers": [], "text": "One branch deletes \"section1tpl\", the other moves it to a new tpl tag \"section1newTpl\"", "__type": "RawText"}, "53903181": {"tpl": [{"__ref": "53903171"}], "__type": "RenderExpr"}, "53903191": {"name": null, "component": {"__ref": "53903082"}, "uuid": "35jcOMX41", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903192"}], "__type": "TplComponent"}, "53903192": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903193"}, {"__ref": "53903194"}, {"__ref": "53903195"}], "attrs": {}, "rs": {"__ref": "53903196"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903193": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903211"}, "__type": "Arg"}, "53903194": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903213"}, "__type": "Arg"}, "53903195": {"param": {"__ref": "53903125"}, "expr": {"__ref": "30076002"}, "__type": "Arg"}, "53903196": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903202": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ak91hkyVRs", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903204"}], "__type": "TplTag"}, "53903203": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xbdhdeS4mC", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903205"}], "__type": "TplTag"}, "53903204": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903206"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903210"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903205": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903208"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903212"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903206": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903208": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903210": {"markers": [], "text": "2 - Delete node in one branch, move to new TplComp in the other", "__type": "RawText"}, "53903211": {"tpl": [{"__ref": "53903202"}], "__type": "RenderExpr"}, "53903212": {"markers": [], "text": "One branch deletes \"section2tpl\", the other moves it to a new tpl component \"section2comp\"", "__type": "RawText"}, "53903213": {"tpl": [{"__ref": "53903203"}], "__type": "RenderExpr"}, "53903233": {"uuid": "Kb8NzqUy2M", "name": "Section2Comp", "params": [{"__ref": "53903259"}], "states": [], "tplTree": {"__ref": "53903236"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903237"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903234": {"component": {"__ref": "53903233"}, "matrix": {"__ref": "53903238"}, "customMatrix": {"__ref": "53903239"}, "__type": "ComponentArena"}, "53903236": {"tag": "div", "name": null, "children": [{"__ref": "53903260"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UIb7mybKb", "parent": null, "locked": null, "vsettings": [{"__ref": "53903241"}], "__type": "TplTag"}, "53903237": {"uuid": "osT_Wb5IXO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903238": {"rows": [{"__ref": "53903242"}], "__type": "ArenaFrameGrid"}, "53903239": {"rows": [{"__ref": "53903243"}], "__type": "ArenaFrameGrid"}, "53903241": {"variants": [{"__ref": "53903237"}], "args": [], "attrs": {}, "rs": {"__ref": "53903245"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903242": {"cols": [{"__ref": "53903246"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903243": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903245": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903246": {"frame": {"__ref": "53903255"}, "cellKey": {"__ref": "53903237"}, "__type": "ArenaFrameCell"}, "53903255": {"uuid": "Z7bv26rtLX", "width": 340, "height": 340, "container": {"__ref": "53903256"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903237"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903256": {"name": null, "component": {"__ref": "53903233"}, "uuid": "VR9QBAk-P-", "parent": null, "locked": null, "vsettings": [{"__ref": "53903257"}], "__type": "TplComponent"}, "53903257": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903258": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903259": {"type": {"__ref": "53903263"}, "tplSlot": {"__ref": "53903260"}, "variable": {"__ref": "53903262"}, "uuid": "JM4HCS94W1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903260": {"param": {"__ref": "53903259"}, "defaultContents": [], "uuid": "kZ7xuHl9XX", "parent": {"__ref": "53903236"}, "locked": null, "vsettings": [{"__ref": "53903264"}], "__type": "TplSlot"}, "53903262": {"name": "children", "uuid": "1P3_HQEak", "__type": "Var"}, "53903263": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903264": {"variants": [{"__ref": "53903237"}], "args": [], "attrs": {}, "rs": {"__ref": "53903266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903266": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903268": {"name": null, "component": {"__ref": "53903082"}, "uuid": "AYOfshtE2", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903269"}], "__type": "TplComponent"}, "53903269": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903270"}, {"__ref": "53903271"}, {"__ref": "53903272"}], "attrs": {}, "rs": {"__ref": "53903273"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903270": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903288"}, "__type": "Arg"}, "53903271": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903290"}, "__type": "Arg"}, "53903272": {"param": {"__ref": "53903125"}, "expr": {"__ref": "30076011"}, "__type": "Arg"}, "53903273": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903279": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "JmUpu3UY7-", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903281"}], "__type": "TplTag"}, "53903280": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "4x3dc1koY3", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903282"}], "__type": "TplTag"}, "53903281": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903283"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903287"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903282": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903285"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903289"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903283": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903285": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903287": {"markers": [], "text": "3 - Regression test - dangling tpl comp referencing deleted param", "__type": "RawText"}, "53903288": {"tpl": [{"__ref": "53903279"}], "__type": "RenderExpr"}, "53903289": {"markers": [], "text": "One branch deletes \"Section3Comp1\" and deletes the slot of \"Section3Comp2\". The other creates a new tpl component of \"Section3Comp1\", whose children contains a new instance of \"Section3Comp2\" with a slot arg referencing the deleted param.", "__type": "RawText"}, "53903290": {"tpl": [{"__ref": "53903280"}], "__type": "RenderExpr"}, "53903337": {"uuid": "tS05uFhSW", "name": "Section3Comp2", "params": [], "states": [], "tplTree": {"__ref": "53903340"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903341"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903338": {"component": {"__ref": "53903337"}, "matrix": {"__ref": "53903342"}, "customMatrix": {"__ref": "53903343"}, "__type": "ComponentArena"}, "53903340": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "arTUpxk5R", "parent": null, "locked": null, "vsettings": [{"__ref": "53903345"}], "__type": "TplTag"}, "53903341": {"uuid": "mvifJnTI9M", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903342": {"rows": [{"__ref": "53903346"}], "__type": "ArenaFrameGrid"}, "53903343": {"rows": [{"__ref": "53903347"}], "__type": "ArenaFrameGrid"}, "53903345": {"variants": [{"__ref": "53903341"}], "args": [], "attrs": {}, "rs": {"__ref": "53903349"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903346": {"cols": [{"__ref": "53903350"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903347": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903349": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903350": {"frame": {"__ref": "53903359"}, "cellKey": {"__ref": "53903341"}, "__type": "ArenaFrameCell"}, "53903359": {"uuid": "iiERKW0rWO", "width": 340, "height": 340, "container": {"__ref": "53903360"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903341"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903360": {"name": null, "component": {"__ref": "53903337"}, "uuid": "kXmyWI4b0c", "parent": null, "locked": null, "vsettings": [{"__ref": "53903361"}], "__type": "TplComponent"}, "53903361": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903362"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903362": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "251-add-data-tokens"}}, {"branchId": "main", "data": {"root": "36067001", "map": {"36067001": {"components": [{"__ref": "53903001"}, {"__ref": "53903002"}, {"__ref": "53903049"}, {"__ref": "53903082"}, {"__ref": "53903233"}, {"__ref": "53903310"}, {"__ref": "53903337"}], "arenas": [{"__ref": "36067002"}], "pageArenas": [], "componentArenas": [{"__ref": "53903083"}, {"__ref": "53903234"}, {"__ref": "53903311"}, {"__ref": "53903338"}], "globalVariantGroups": [{"__ref": "36067003"}], "userManagedFonts": [], "globalVariant": {"__ref": "36067007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "36067008"}], "activeTheme": {"__ref": "36067008"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "36067003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "36067002": {"name": "Custom arena 1", "children": [{"__ref": "53903050"}], "__type": "Arena"}, "36067003": {"type": "global-screen", "param": {"__ref": "36067004"}, "uuid": "iGBSJMZicCl", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "36067004": {"type": {"__ref": "36067006"}, "variable": {"__ref": "36067005"}, "uuid": "RwpYTpxAnFj", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "36067005": {"name": "Screen", "uuid": "mFSn7PINq-G", "__type": "Var"}, "36067006": {"name": "text", "__type": "Text"}, "36067007": {"uuid": "5hGDUozB98K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "36067008": {"defaultStyle": {"__ref": "36067009"}, "styles": [{"__ref": "36067024"}, {"__ref": "36067033"}, {"__ref": "36067042"}, {"__ref": "36067051"}, {"__ref": "36067060"}, {"__ref": "36067069"}, {"__ref": "36067077"}, {"__ref": "36067081"}, {"__ref": "36067085"}, {"__ref": "36067093"}, {"__ref": "36067118"}, {"__ref": "36067143"}, {"__ref": "36067154"}], "layout": {"__ref": "36067165"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "36067009": {"name": "Default Typography", "rs": {"__ref": "36067010"}, "preview": null, "uuid": "xPo4OMm9Fd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "36067024": {"selector": "h1", "style": {"__ref": "36067025"}, "__type": "ThemeStyle"}, "36067025": {"name": "Default \"h1\"", "rs": {"__ref": "36067026"}, "preview": null, "uuid": "bRf0zG4QA3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "36067033": {"selector": "h2", "style": {"__ref": "36067034"}, "__type": "ThemeStyle"}, "36067034": {"name": "Default \"h2\"", "rs": {"__ref": "36067035"}, "preview": null, "uuid": "dB8pmOn_N9", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "36067042": {"selector": "h3", "style": {"__ref": "36067043"}, "__type": "ThemeStyle"}, "36067043": {"name": "Default \"h3\"", "rs": {"__ref": "36067044"}, "preview": null, "uuid": "IQbf_sDnUD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "36067051": {"selector": "h4", "style": {"__ref": "36067052"}, "__type": "ThemeStyle"}, "36067052": {"name": "Default \"h4\"", "rs": {"__ref": "36067053"}, "preview": null, "uuid": "cXkEtX1CdH", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "36067060": {"selector": "h5", "style": {"__ref": "36067061"}, "__type": "ThemeStyle"}, "36067061": {"name": "Default \"h5\"", "rs": {"__ref": "36067062"}, "preview": null, "uuid": "B4ZP50j4nt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "36067069": {"selector": "h6", "style": {"__ref": "36067070"}, "__type": "ThemeStyle"}, "36067070": {"name": "Default \"h6\"", "rs": {"__ref": "36067071"}, "preview": null, "uuid": "4eN_P-oe5C", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "36067077": {"selector": "a", "style": {"__ref": "36067078"}, "__type": "ThemeStyle"}, "36067078": {"name": "Default \"a\"", "rs": {"__ref": "36067079"}, "preview": null, "uuid": "xmoUjn8krF", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "36067081": {"selector": "a:hover", "style": {"__ref": "36067082"}, "__type": "ThemeStyle"}, "36067082": {"name": "Default \"a:hover\"", "rs": {"__ref": "36067083"}, "preview": null, "uuid": "6yCMQ6jm2f", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "36067085": {"selector": "blockquote", "style": {"__ref": "36067086"}, "__type": "ThemeStyle"}, "36067086": {"name": "Default \"blockquote\"", "rs": {"__ref": "36067087"}, "preview": null, "uuid": "EuQqvgccj3", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "36067093": {"selector": "code", "style": {"__ref": "36067094"}, "__type": "ThemeStyle"}, "36067094": {"name": "Default \"code\"", "rs": {"__ref": "36067095"}, "preview": null, "uuid": "PVkq6JtA_W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "36067118": {"selector": "pre", "style": {"__ref": "36067119"}, "__type": "ThemeStyle"}, "36067119": {"name": "Default \"pre\"", "rs": {"__ref": "36067120"}, "preview": null, "uuid": "oP2rh7pkyob", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "36067143": {"selector": "ol", "style": {"__ref": "36067144"}, "__type": "ThemeStyle"}, "36067144": {"name": "Default \"ol\"", "rs": {"__ref": "36067145"}, "preview": null, "uuid": "FMA9pQU_msC", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "36067154": {"selector": "ul", "style": {"__ref": "36067155"}, "__type": "ThemeStyle"}, "36067155": {"name": "Default \"ul\"", "rs": {"__ref": "36067156"}, "preview": null, "uuid": "Vpe_gR8_I4B", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "36067156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "36067165": {"rs": {"__ref": "36067166"}, "__type": "ThemeLayoutSettings"}, "36067166": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903001": {"uuid": "qWmq-h9ODh", "name": "hostless-plasmic-head", "params": [{"__ref": "53903003"}, {"__ref": "53903004"}, {"__ref": "53903005"}, {"__ref": "53903006"}], "states": [], "tplTree": {"__ref": "53903007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "53903009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903002": {"uuid": "IMw9swUYCu", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "53903010"}, {"__ref": "53903011"}, {"__ref": "53903012"}, {"__ref": "53903013"}, {"__ref": "53903014"}], "states": [], "tplTree": {"__ref": "53903015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "53903017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903003": {"type": {"__ref": "53903019"}, "variable": {"__ref": "53903018"}, "uuid": "8qq-0hSrJf", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903004": {"type": {"__ref": "53903021"}, "variable": {"__ref": "53903020"}, "uuid": "w76TiK4TAU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903005": {"type": {"__ref": "53903023"}, "variable": {"__ref": "53903022"}, "uuid": "rxWx-2Ubs1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903006": {"type": {"__ref": "53903025"}, "variable": {"__ref": "53903024"}, "uuid": "084egcc_CT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "7PT6i-WeGn", "parent": null, "locked": null, "vsettings": [{"__ref": "53903026"}], "__type": "TplTag"}, "53903008": {"uuid": "-Zmygg1XG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "53903010": {"type": {"__ref": "53903028"}, "variable": {"__ref": "53903027"}, "uuid": "EaHCNjCUAB", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903011": {"type": {"__ref": "53903030"}, "variable": {"__ref": "53903029"}, "uuid": "-yHUBqlCptE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903012": {"type": {"__ref": "53903032"}, "tplSlot": {"__ref": "53903037"}, "variable": {"__ref": "53903031"}, "uuid": "ozr-iK4Pv_L", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903013": {"type": {"__ref": "53903034"}, "variable": {"__ref": "53903033"}, "uuid": "DIWnirbqmDb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903014": {"type": {"__ref": "53903036"}, "variable": {"__ref": "53903035"}, "uuid": "ihK5IoUFQ6v", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "53903015": {"tag": "div", "name": null, "children": [{"__ref": "53903037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5nVc7ktXnn", "parent": null, "locked": null, "vsettings": [{"__ref": "53903038"}], "__type": "TplTag"}, "53903016": {"uuid": "OT3oJCqKEm", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "53903018": {"name": "title", "uuid": "5w9GaJmpCQ", "__type": "Var"}, "53903019": {"name": "text", "__type": "Text"}, "53903020": {"name": "description", "uuid": "yOVFpGUlez", "__type": "Var"}, "53903021": {"name": "text", "__type": "Text"}, "53903022": {"name": "image", "uuid": "kv3ah8yHlN", "__type": "Var"}, "53903023": {"name": "img", "__type": "Img"}, "53903024": {"name": "canonical", "uuid": "OWieep-MIe", "__type": "Var"}, "53903025": {"name": "text", "__type": "Text"}, "53903026": {"variants": [{"__ref": "53903008"}], "args": [], "attrs": {}, "rs": {"__ref": "53903039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903027": {"name": "dataOp", "uuid": "2rInNx1JP5", "__type": "Var"}, "53903028": {"name": "any", "__type": "AnyType"}, "53903029": {"name": "name", "uuid": "_mwn6YlzdtO", "__type": "Var"}, "53903030": {"name": "text", "__type": "Text"}, "53903031": {"name": "children", "uuid": "ZmrIiicdqCH", "__type": "Var"}, "53903032": {"name": "renderFunc", "params": [{"__ref": "53903040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "53903033": {"name": "pageSize", "uuid": "xXH0ZudPRcm", "__type": "Var"}, "53903034": {"name": "num", "__type": "<PERSON><PERSON>"}, "53903035": {"name": "pageIndex", "uuid": "fXxdD2tli_Y", "__type": "Var"}, "53903036": {"name": "num", "__type": "<PERSON><PERSON>"}, "53903037": {"param": {"__ref": "53903012"}, "defaultContents": [], "uuid": "XX9ReAPqdTq", "parent": {"__ref": "53903015"}, "locked": null, "vsettings": [{"__ref": "53903041"}], "__type": "TplSlot"}, "53903038": {"variants": [{"__ref": "53903016"}], "args": [], "attrs": {}, "rs": {"__ref": "53903042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "53903040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "53903045"}, "__type": "ArgType"}, "53903041": {"variants": [{"__ref": "53903016"}], "args": [], "attrs": {}, "rs": {"__ref": "53903046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "53903045": {"name": "any", "__type": "AnyType"}, "53903046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903049": {"uuid": "ctkwFVWWSn", "name": "", "params": [], "states": [], "tplTree": {"__ref": "53903051"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903052"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903050": {"uuid": "nwztLgMhtp", "width": 834, "height": 1194, "container": {"__ref": "53903053"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903052"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "mainFrame", "top": 31, "left": 826, "__type": "ArenaFrame"}, "53903051": {"tag": "div", "name": null, "children": [{"__ref": "53903081"}, {"__ref": "53903191"}, {"__ref": "53903268"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "jlUocrUXx", "parent": null, "locked": null, "vsettings": [{"__ref": "53903054"}], "__type": "TplTag"}, "53903052": {"uuid": "JtmW5TCXcJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903053": {"name": null, "component": {"__ref": "53903049"}, "uuid": "WhhDN143T-", "parent": null, "locked": null, "vsettings": [{"__ref": "53903055"}], "__type": "TplComponent"}, "53903054": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903056"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903055": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903057"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903056": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "row-gap": "16px"}, "mixins": [], "__type": "RuleSet"}, "53903057": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903081": {"name": null, "component": {"__ref": "53903082"}, "uuid": "lOhwymcpxc", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903084"}], "__type": "TplComponent"}, "53903082": {"uuid": "1D9Qxb0AO7", "name": "TestSection", "params": [{"__ref": "53903125"}, {"__ref": "53903133"}, {"__ref": "53903161"}], "states": [], "tplTree": {"__ref": "53903085"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903086"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903083": {"component": {"__ref": "53903082"}, "matrix": {"__ref": "53903087"}, "customMatrix": {"__ref": "53903088"}, "__type": "ComponentArena"}, "53903084": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903127"}, {"__ref": "53903135"}, {"__ref": "53903163"}], "attrs": {}, "rs": {"__ref": "53903089"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903085": {"tag": "div", "name": null, "children": [{"__ref": "53903151"}, {"__ref": "53903162"}, {"__ref": "53903117"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "TLhCbsO7M", "parent": null, "locked": null, "vsettings": [{"__ref": "53903090"}], "__type": "TplTag"}, "53903086": {"uuid": "u1F6FSSSK_", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903087": {"rows": [{"__ref": "53903091"}], "__type": "ArenaFrameGrid"}, "53903088": {"rows": [{"__ref": "53903092"}], "__type": "ArenaFrameGrid"}, "53903089": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "53903090": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903096"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903091": {"cols": [{"__ref": "53903097"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903092": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903096": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative", "row-gap": "12px"}, "mixins": [], "__type": "RuleSet"}, "53903097": {"frame": {"__ref": "53903103"}, "cellKey": {"__ref": "53903086"}, "__type": "ArenaFrameCell"}, "53903103": {"uuid": "NjyMJmZRY2", "width": 340, "height": 340, "container": {"__ref": "53903104"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903086"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903104": {"name": null, "component": {"__ref": "53903082"}, "uuid": "mnxAs3crU5", "parent": null, "locked": null, "vsettings": [{"__ref": "53903105"}], "__type": "TplComponent"}, "53903105": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903106"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903106": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903117": {"tag": "div", "name": null, "children": [{"__ref": "53903126"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "XwElxYMrB", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903118"}], "__type": "TplTag"}, "53903118": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903119"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903119": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903125": {"type": {"__ref": "53903129"}, "tplSlot": {"__ref": "53903126"}, "variable": {"__ref": "53903128"}, "uuid": "NivoufBeJ-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903126": {"param": {"__ref": "53903125"}, "defaultContents": [], "uuid": "7GUW_P2FHH", "parent": {"__ref": "53903117"}, "locked": null, "vsettings": [{"__ref": "53903130"}], "__type": "TplSlot"}, "53903127": {"param": {"__ref": "53903125"}, "expr": {"__ref": "53903390"}, "__type": "Arg"}, "53903128": {"name": "children", "uuid": "2-oSj8MB1", "__type": "Var"}, "53903129": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903130": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903132"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903132": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903133": {"type": {"__ref": "53903137"}, "tplSlot": {"__ref": "53903134"}, "variable": {"__ref": "53903136"}, "uuid": "ifm0hmIzEb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903134": {"param": {"__ref": "53903133"}, "defaultContents": [{"__ref": "53903138"}], "uuid": "u_-9vmIoGV", "parent": {"__ref": "53903151"}, "locked": null, "vsettings": [{"__ref": "53903139"}], "__type": "TplSlot"}, "53903135": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903150"}, "__type": "Arg"}, "53903136": {"name": "title", "uuid": "ZXFwxIDAp", "__type": "Var"}, "53903137": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903138": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "zSsK_1HEC3", "parent": {"__ref": "53903134"}, "locked": null, "vsettings": [{"__ref": "53903141"}], "__type": "TplTag"}, "53903139": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903142"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903141": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903144"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903145"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903142": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903143": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "L_1AFOMJew", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903146"}], "__type": "TplTag"}, "53903144": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903145": {"markers": [], "text": "Enter some text", "__type": "RawText"}, "53903146": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903147"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903179"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903147": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903150": {"tpl": [{"__ref": "53903143"}], "__type": "RenderExpr"}, "53903151": {"tag": "h4", "name": null, "children": [{"__ref": "53903134"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "YbB548fVl", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903152"}], "__type": "TplTag"}, "53903152": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903153"}, "dataCond": {"__ref": "53903154"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903153": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "53903154": {"code": "true", "fallback": null, "__type": "CustomCode"}, "53903161": {"type": {"__ref": "53903165"}, "tplSlot": {"__ref": "53903162"}, "variable": {"__ref": "53903164"}, "uuid": "reztUKnNT_", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903162": {"param": {"__ref": "53903161"}, "defaultContents": [{"__ref": "53903166"}], "uuid": "FpJwn5yyRy", "parent": {"__ref": "53903085"}, "locked": null, "vsettings": [{"__ref": "53903167"}], "__type": "TplSlot"}, "53903163": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903181"}, "__type": "Arg"}, "53903164": {"name": "description", "uuid": "wRK72UFUx", "__type": "Var"}, "53903165": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903166": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cIbwDiDRAA", "parent": {"__ref": "53903162"}, "locked": null, "vsettings": [{"__ref": "53903169"}], "__type": "TplTag"}, "53903167": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903170"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903169": {"variants": [{"__ref": "53903086"}], "args": [], "attrs": {}, "rs": {"__ref": "53903172"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903173"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903170": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903171": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "YEyox_O7By", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903174"}], "__type": "TplTag"}, "53903172": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903173": {"markers": [], "text": "Enter some text", "__type": "RawText"}, "53903174": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903175"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903180"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903175": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903179": {"markers": [], "text": "1 - Delete node in one branch, move to new TplTag in the other", "__type": "RawText"}, "53903180": {"markers": [], "text": "One branch deletes \"section1tpl\", the other moves it to a new tpl tag \"section1newTpl\"", "__type": "RawText"}, "53903181": {"tpl": [{"__ref": "53903171"}], "__type": "RenderExpr"}, "53903183": {"tag": "div", "name": "section1tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "cBVW4bOxC", "parent": {"__ref": "53903382"}, "locked": null, "vsettings": [{"__ref": "53903184"}], "__type": "TplTag"}, "53903184": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903185"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903185": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903191": {"name": null, "component": {"__ref": "53903082"}, "uuid": "35jcOMX41", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903192"}], "__type": "TplComponent"}, "53903192": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903193"}, {"__ref": "53903194"}, {"__ref": "53903195"}], "attrs": {}, "rs": {"__ref": "53903196"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903193": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903211"}, "__type": "Arg"}, "53903194": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903213"}, "__type": "Arg"}, "53903195": {"param": {"__ref": "53903125"}, "expr": {"__ref": "53903399"}, "__type": "Arg"}, "53903196": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903202": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ak91hkyVRs", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903204"}], "__type": "TplTag"}, "53903203": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xbdhdeS4mC", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903205"}], "__type": "TplTag"}, "53903204": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903206"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903210"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903205": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903208"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903212"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903206": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903208": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903210": {"markers": [], "text": "2 - Delete node in one branch, move to new TplComp in the other", "__type": "RawText"}, "53903211": {"tpl": [{"__ref": "53903202"}], "__type": "RenderExpr"}, "53903212": {"markers": [], "text": "One branch deletes \"section2tpl\", the other moves it to a new tpl component \"section2comp\"", "__type": "RawText"}, "53903213": {"tpl": [{"__ref": "53903203"}], "__type": "RenderExpr"}, "53903215": {"tag": "div", "name": "section2tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "FJhcKIwp4", "parent": {"__ref": "53903392"}, "locked": null, "vsettings": [{"__ref": "53903216"}], "__type": "TplTag"}, "53903216": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903217"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903217": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903233": {"uuid": "Kb8NzqUy2M", "name": "Section2Comp", "params": [{"__ref": "53903259"}], "states": [], "tplTree": {"__ref": "53903236"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903237"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903234": {"component": {"__ref": "53903233"}, "matrix": {"__ref": "53903238"}, "customMatrix": {"__ref": "53903239"}, "__type": "ComponentArena"}, "53903236": {"tag": "div", "name": null, "children": [{"__ref": "53903260"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "UIb7mybKb", "parent": null, "locked": null, "vsettings": [{"__ref": "53903241"}], "__type": "TplTag"}, "53903237": {"uuid": "osT_Wb5IXO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903238": {"rows": [{"__ref": "53903242"}], "__type": "ArenaFrameGrid"}, "53903239": {"rows": [{"__ref": "53903243"}], "__type": "ArenaFrameGrid"}, "53903241": {"variants": [{"__ref": "53903237"}], "args": [], "attrs": {}, "rs": {"__ref": "53903245"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903242": {"cols": [{"__ref": "53903246"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903243": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903245": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903246": {"frame": {"__ref": "53903255"}, "cellKey": {"__ref": "53903237"}, "__type": "ArenaFrameCell"}, "53903255": {"uuid": "Z7bv26rtLX", "width": 340, "height": 340, "container": {"__ref": "53903256"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903237"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903256": {"name": null, "component": {"__ref": "53903233"}, "uuid": "VR9QBAk-P-", "parent": null, "locked": null, "vsettings": [{"__ref": "53903257"}], "__type": "TplComponent"}, "53903257": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903258"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903258": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903259": {"type": {"__ref": "53903263"}, "tplSlot": {"__ref": "53903260"}, "variable": {"__ref": "53903262"}, "uuid": "JM4HCS94W1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903260": {"param": {"__ref": "53903259"}, "defaultContents": [], "uuid": "kZ7xuHl9XX", "parent": {"__ref": "53903236"}, "locked": null, "vsettings": [{"__ref": "53903264"}], "__type": "TplSlot"}, "53903262": {"name": "children", "uuid": "1P3_HQEak", "__type": "Var"}, "53903263": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903264": {"variants": [{"__ref": "53903237"}], "args": [], "attrs": {}, "rs": {"__ref": "53903266"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903266": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903268": {"name": null, "component": {"__ref": "53903082"}, "uuid": "AYOfshtE2", "parent": {"__ref": "53903051"}, "locked": null, "vsettings": [{"__ref": "53903269"}], "__type": "TplComponent"}, "53903269": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903270"}, {"__ref": "53903271"}, {"__ref": "53903272"}], "attrs": {}, "rs": {"__ref": "53903273"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903270": {"param": {"__ref": "53903133"}, "expr": {"__ref": "53903288"}, "__type": "Arg"}, "53903271": {"param": {"__ref": "53903161"}, "expr": {"__ref": "53903290"}, "__type": "Arg"}, "53903272": {"param": {"__ref": "53903125"}, "expr": {"__ref": "53903401"}, "__type": "Arg"}, "53903273": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903279": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "JmUpu3UY7-", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903281"}], "__type": "TplTag"}, "53903280": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "4x3dc1koY3", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903282"}], "__type": "TplTag"}, "53903281": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903283"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903287"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903282": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903285"}, "dataCond": null, "dataRep": null, "text": {"__ref": "53903289"}, "columnsConfig": null, "__type": "VariantSetting"}, "53903283": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903285": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903287": {"markers": [], "text": "3 - Regression test - dangling tpl comp referencing deleted param", "__type": "RawText"}, "53903288": {"tpl": [{"__ref": "53903279"}], "__type": "RenderExpr"}, "53903289": {"markers": [], "text": "One branch deletes \"Section3Comp1\" and deletes the slot of \"Section3Comp2\". The other creates a new tpl component of \"Section3Comp1\", whose children contains a new instance of \"Section3Comp2\" with a slot arg referencing the deleted param.", "__type": "RawText"}, "53903290": {"tpl": [{"__ref": "53903280"}], "__type": "RenderExpr"}, "53903310": {"uuid": "i83l6q9eHi", "name": "Section3Comp1", "params": [{"__ref": "53903363"}], "states": [], "tplTree": {"__ref": "53903313"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903314"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903311": {"component": {"__ref": "53903310"}, "matrix": {"__ref": "53903315"}, "customMatrix": {"__ref": "53903316"}, "__type": "ComponentArena"}, "53903313": {"tag": "div", "name": null, "children": [{"__ref": "53903364"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lxfJRAlIK", "parent": null, "locked": null, "vsettings": [{"__ref": "53903318"}], "__type": "TplTag"}, "53903314": {"uuid": "FNg3H4U6Tb", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903315": {"rows": [{"__ref": "53903319"}], "__type": "ArenaFrameGrid"}, "53903316": {"rows": [{"__ref": "53903320"}], "__type": "ArenaFrameGrid"}, "53903318": {"variants": [{"__ref": "53903314"}], "args": [], "attrs": {}, "rs": {"__ref": "53903322"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903319": {"cols": [{"__ref": "53903323"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903320": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903322": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903323": {"frame": {"__ref": "53903332"}, "cellKey": {"__ref": "53903314"}, "__type": "ArenaFrameCell"}, "53903332": {"uuid": "M5VXWk219U", "width": 340, "height": 340, "container": {"__ref": "53903333"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903314"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903333": {"name": null, "component": {"__ref": "53903310"}, "uuid": "yd4pAC5vDg", "parent": null, "locked": null, "vsettings": [{"__ref": "53903334"}], "__type": "TplComponent"}, "53903334": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903335"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903335": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903337": {"uuid": "tS05uFhSW", "name": "Section3Comp2", "params": [{"__ref": "53903371"}], "states": [], "tplTree": {"__ref": "53903340"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "53903341"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "53903338": {"component": {"__ref": "53903337"}, "matrix": {"__ref": "53903342"}, "customMatrix": {"__ref": "53903343"}, "__type": "ComponentArena"}, "53903340": {"tag": "div", "name": null, "children": [{"__ref": "53903372"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "arTUpxk5R", "parent": null, "locked": null, "vsettings": [{"__ref": "53903345"}], "__type": "TplTag"}, "53903341": {"uuid": "mvifJnTI9M", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "53903342": {"rows": [{"__ref": "53903346"}], "__type": "ArenaFrameGrid"}, "53903343": {"rows": [{"__ref": "53903347"}], "__type": "ArenaFrameGrid"}, "53903345": {"variants": [{"__ref": "53903341"}], "args": [], "attrs": {}, "rs": {"__ref": "53903349"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903346": {"cols": [{"__ref": "53903350"}], "rowKey": null, "__type": "ArenaFrameRow"}, "53903347": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "53903349": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903350": {"frame": {"__ref": "53903359"}, "cellKey": {"__ref": "53903341"}, "__type": "ArenaFrameCell"}, "53903359": {"uuid": "iiERKW0rWO", "width": 340, "height": 340, "container": {"__ref": "53903360"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "53903341"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "53903360": {"name": null, "component": {"__ref": "53903337"}, "uuid": "kXmyWI4b0c", "parent": null, "locked": null, "vsettings": [{"__ref": "53903361"}], "__type": "TplComponent"}, "53903361": {"variants": [{"__ref": "36067007"}], "args": [], "attrs": {}, "rs": {"__ref": "53903362"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903362": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903363": {"type": {"__ref": "53903367"}, "tplSlot": {"__ref": "53903364"}, "variable": {"__ref": "53903366"}, "uuid": "YBiIX2-QxI", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903364": {"param": {"__ref": "53903363"}, "defaultContents": [], "uuid": "oCMZrejyV9", "parent": {"__ref": "53903313"}, "locked": null, "vsettings": [{"__ref": "53903368"}], "__type": "TplSlot"}, "53903366": {"name": "children", "uuid": "jkCLyDVS3", "__type": "Var"}, "53903367": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903368": {"variants": [{"__ref": "53903314"}], "args": [], "attrs": {}, "rs": {"__ref": "53903370"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903370": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903371": {"type": {"__ref": "53903375"}, "tplSlot": {"__ref": "53903372"}, "variable": {"__ref": "53903374"}, "uuid": "9izKkFQyu0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "53903372": {"param": {"__ref": "53903371"}, "defaultContents": [], "uuid": "OCrNQHcKiY", "parent": {"__ref": "53903340"}, "locked": null, "vsettings": [{"__ref": "53903376"}], "__type": "TplSlot"}, "53903374": {"name": "children", "uuid": "i5ADNAhdj", "__type": "Var"}, "53903375": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "53903376": {"variants": [{"__ref": "53903341"}], "args": [], "attrs": {}, "rs": {"__ref": "53903378"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903378": {"values": {}, "mixins": [], "__type": "RuleSet"}, "53903382": {"tag": "div", "name": "section1newTpl", "children": [{"__ref": "53903183"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "kNbZlWQ5I", "parent": {"__ref": "53903081"}, "locked": null, "vsettings": [{"__ref": "53903383"}], "__type": "TplTag"}, "53903383": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903384"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903384": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "53903390": {"tpl": [{"__ref": "53903382"}], "__type": "RenderExpr"}, "53903392": {"name": "section2comp", "component": {"__ref": "53903233"}, "uuid": "zvon55hm6", "parent": {"__ref": "53903191"}, "locked": null, "vsettings": [{"__ref": "53903393"}], "__type": "TplComponent"}, "53903393": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903394"}], "attrs": {}, "rs": {"__ref": "53903395"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903394": {"param": {"__ref": "53903259"}, "expr": {"__ref": "53903400"}, "__type": "Arg"}, "53903395": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903399": {"tpl": [{"__ref": "53903392"}], "__type": "RenderExpr"}, "53903400": {"tpl": [{"__ref": "53903215"}], "__type": "RenderExpr"}, "53903401": {"tpl": [{"__ref": "53903402"}], "__type": "RenderExpr"}, "53903402": {"name": "section3comp1", "component": {"__ref": "53903310"}, "uuid": "ixLjvKz48", "parent": {"__ref": "53903268"}, "locked": null, "vsettings": [{"__ref": "53903403"}], "__type": "TplComponent"}, "53903403": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903404"}], "attrs": {}, "rs": {"__ref": "53903405"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903404": {"param": {"__ref": "53903363"}, "expr": {"__ref": "53903409"}, "__type": "Arg"}, "53903405": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903409": {"tpl": [{"__ref": "53903410"}], "__type": "RenderExpr"}, "53903410": {"name": "section3comp2", "component": {"__ref": "53903337"}, "uuid": "cnReXMlRU", "parent": {"__ref": "53903402"}, "locked": null, "vsettings": [{"__ref": "53903411"}], "__type": "TplComponent"}, "53903411": {"variants": [{"__ref": "53903052"}], "args": [{"__ref": "53903412"}], "attrs": {}, "rs": {"__ref": "53903413"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903412": {"param": {"__ref": "53903371"}, "expr": {"__ref": "53903417"}, "__type": "Arg"}, "53903413": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "53903417": {"tpl": [{"__ref": "53903418"}], "__type": "RenderExpr"}, "53903418": {"tag": "div", "name": "section3child", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Yn4enzMcq", "parent": {"__ref": "53903410"}, "locked": null, "vsettings": [{"__ref": "53903419"}], "__type": "TplTag"}, "53903419": {"variants": [{"__ref": "53903052"}], "args": [], "attrs": {}, "rs": {"__ref": "53903420"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "53903420": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "251-add-data-tokens"}}]}