{"branches": [{"id": "3oNJqKojkvKU3PqHPY6eW3", "name": "broken"}], "pkgVersions": [{"id": "b3b27a2a-949d-437d-b367-84db7d868942", "data": {"root": "7K9yTsH7Hsam", "map": {"o_IpZWdrJ6k4": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "v30pZLvaRgcP": {"name": "Default Typography", "rs": {"__ref": "o_IpZWdrJ6k4"}, "preview": null, "uuid": "drIjq5irThZG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HhGS4RaymTUl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "oXJoYwuxFf21": {"rs": {"__ref": "HhGS4RaymTUl"}, "__type": "ThemeLayoutSettings"}, "Nez8EnmNyKQo": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "cs00yFbtCPu5": {"name": "Default \"h1\"", "rs": {"__ref": "Nez8EnmNyKQo"}, "preview": null, "uuid": "lCbOBeT_cgp7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "cIDG_55N1Fpw": {"selector": "h1", "style": {"__ref": "cs00yFbtCPu5"}, "__type": "ThemeStyle"}, "cvXzmIa9412y": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "YzvgRe1Y6adD": {"name": "Default \"h2\"", "rs": {"__ref": "cvXzmIa9412y"}, "preview": null, "uuid": "YB1JosPGCrnX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hecebwT2Rzc_": {"selector": "h2", "style": {"__ref": "YzvgRe1Y6adD"}, "__type": "ThemeStyle"}, "bf9s88nNEuFS": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "V1TYUdNAQJJn": {"name": "Default \"h3\"", "rs": {"__ref": "bf9s88nNEuFS"}, "preview": null, "uuid": "t02qtico63Nm", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9CJBkppIqt9P": {"selector": "h3", "style": {"__ref": "V1TYUdNAQJJn"}, "__type": "ThemeStyle"}, "PcIWCzoFL2In": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "fYIeAK6A5Cxw": {"name": "Default \"h4\"", "rs": {"__ref": "PcIWCzoFL2In"}, "preview": null, "uuid": "8QKgjAXfxV2k", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Jk70cI71FsEj": {"selector": "h4", "style": {"__ref": "fYIeAK6A5Cxw"}, "__type": "ThemeStyle"}, "Gkeg-9lMo5KY": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "Tdq8-nchuKDu": {"name": "Default \"h5\"", "rs": {"__ref": "Gkeg-9lMo5KY"}, "preview": null, "uuid": "Of_nPFozBg1M", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "6pWDYtx3quce": {"selector": "h5", "style": {"__ref": "Tdq8-nchuKDu"}, "__type": "ThemeStyle"}, "w-K116V_-af7": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "JfZZZUkVkprh": {"name": "Default \"h6\"", "rs": {"__ref": "w-K116V_-af7"}, "preview": null, "uuid": "jpw28QSTXvnQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "I2es3aTGk8Uh": {"selector": "h6", "style": {"__ref": "JfZZZUkVkprh"}, "__type": "ThemeStyle"}, "HYwCGzuTSsaE": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "NEprx9duIvW2": {"name": "Default \"a\"", "rs": {"__ref": "HYwCGzuTSsaE"}, "preview": null, "uuid": "It975DeOKr34", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "riQIC31_qsaR": {"selector": "a", "style": {"__ref": "NEprx9duIvW2"}, "__type": "ThemeStyle"}, "oJ2ZAC_noA0d": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "IfbAxEw8VR9-": {"name": "Default \"a:hover\"", "rs": {"__ref": "oJ2ZAC_noA0d"}, "preview": null, "uuid": "aiekhxB5k0-X", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BtAYTWU3zTNl": {"selector": "a:hover", "style": {"__ref": "IfbAxEw8VR9-"}, "__type": "ThemeStyle"}, "ep6pRa5lQ1zA": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "_uZezshwWRfd": {"name": "Default \"blockquote\"", "rs": {"__ref": "ep6pRa5lQ1zA"}, "preview": null, "uuid": "gI5cpIvf52Ik", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "3H1f4GtJ0zHM": {"selector": "blockquote", "style": {"__ref": "_uZezshwWRfd"}, "__type": "ThemeStyle"}, "nsjpK_Q6_yb3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "QQRT3CNracla": {"name": "Default \"code\"", "rs": {"__ref": "nsjpK_Q6_yb3"}, "preview": null, "uuid": "e17ky251554D", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xcGVbMO6XG-0": {"selector": "code", "style": {"__ref": "QQRT3CNracla"}, "__type": "ThemeStyle"}, "gB9OML3HfCTe": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "Sjy-1UdSCsyR": {"name": "Default \"pre\"", "rs": {"__ref": "gB9OML3HfCTe"}, "preview": null, "uuid": "sWKYKrMTC5er", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Dn27uuD5IXVR": {"selector": "pre", "style": {"__ref": "Sjy-1UdSCsyR"}, "__type": "ThemeStyle"}, "OJQ8F49dq03Y": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "sZUfickrUJSF": {"name": "Default \"ol\"", "rs": {"__ref": "OJQ8F49dq03Y"}, "preview": null, "uuid": "FlFebUX3SzfG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rm4kxThiGTsN": {"selector": "ol", "style": {"__ref": "sZUfickrUJSF"}, "__type": "ThemeStyle"}, "_QWQ9Z9Jimqz": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "NErB2sBVMWIc": {"name": "Default \"ul\"", "rs": {"__ref": "_QWQ9Z9Jimqz"}, "preview": null, "uuid": "lzILSOFEanHd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9Vbuuq0Z7jUJ": {"selector": "ul", "style": {"__ref": "NErB2sBVMWIc"}, "__type": "ThemeStyle"}, "-kktNl7gIjBb": {"defaultStyle": {"__ref": "v30pZLvaRgcP"}, "styles": [{"__ref": "cIDG_55N1Fpw"}, {"__ref": "hecebwT2Rzc_"}, {"__ref": "9CJBkppIqt9P"}, {"__ref": "Jk70cI71FsEj"}, {"__ref": "6pWDYtx3quce"}, {"__ref": "I2es3aTGk8Uh"}, {"__ref": "riQIC31_qsaR"}, {"__ref": "BtAYTWU3zTNl"}, {"__ref": "3H1f4GtJ0zHM"}, {"__ref": "xcGVbMO6XG-0"}, {"__ref": "Dn27uuD5IXVR"}, {"__ref": "Rm4kxThiGTsN"}, {"__ref": "9Vbuuq0Z7jUJ"}], "layout": {"__ref": "oXJoYwuxFf21"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "YZmfWqoDbiIb": {"name": "text", "__type": "Text"}, "8JVukDb_Vcdm": {"name": "Screen", "uuid": "Z6c_OohR62vI", "__type": "Var"}, "_Zt9NWxM9LvU": {"type": {"__ref": "YZmfWqoDbiIb"}, "variable": {"__ref": "8JVukDb_Vcdm"}, "uuid": "EUoy5WmiFMfO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "4jevmYDqUWr2": {"type": "global-screen", "param": {"__ref": "_Zt9NWxM9LvU"}, "uuid": "-lKhz_5_01KN", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "DgqVJbxT6rzS": {"uuid": "4BAUmxR3ilwJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "YFGCw9A6o6px": {"components": [{"__ref": "frGaU1n73odM"}, {"__ref": "TO5_tsJ2rxq7"}, {"__ref": "8yK7aI7AMEgx"}, {"__ref": "8YRyvzzFFKxR"}, {"__ref": "GT3W9zV1CrDq"}], "arenas": [], "pageArenas": [{"__ref": "tzzp3JKJ2vki"}], "componentArenas": [{"__ref": "1kRWeS0cq1ZP"}, {"__ref": "tIsSKbgI4WLQ"}], "globalVariantGroups": [{"__ref": "4jevmYDqUWr2"}], "userManagedFonts": [], "globalVariant": {"__ref": "DgqVJbxT6rzS"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "-kktNl7gIjBb"}], "activeTheme": {"__ref": "-kktNl7gIjBb"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "4jevmYDqUWr2"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "frGaU1n73odM": {"uuid": "AmEg4oPHP3ZT", "name": "hostless-plasmic-head", "params": [{"__ref": "IGtKvsQdnWeM"}, {"__ref": "sYMuYU-7rQKC"}, {"__ref": "SGRUBKLX_ybx"}, {"__ref": "Cc8BGduU1OxN"}], "states": [], "tplTree": {"__ref": "c5-pgL0u4NH5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "9Lb3HyVpGwQL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "2yorzVi9XxpM"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "TO5_tsJ2rxq7": {"uuid": "XNcPoW5tB7Uk", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "nhPtM8j6X84D"}, {"__ref": "WaWADqAe8ETN"}, {"__ref": "R4qX-icILd9s"}, {"__ref": "CXgBdHc5jMFy"}, {"__ref": "FR-nlHcZDRWw"}], "states": [], "tplTree": {"__ref": "Jv1gKCY0MijD"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "2pFTJ6tbNYdN"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "mr4n06O3k33t"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "IGtKvsQdnWeM": {"type": {"__ref": "3oKp5S94WjHQ"}, "variable": {"__ref": "zuftQdXnLzvb"}, "uuid": "vMz1jo0LZEOq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "sYMuYU-7rQKC": {"type": {"__ref": "nWHpU0kAloNH"}, "variable": {"__ref": "LD6NAdRwYl7d"}, "uuid": "G4yCjh5sYi1g", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "SGRUBKLX_ybx": {"type": {"__ref": "SfMdPZRPyqxl"}, "variable": {"__ref": "2US99w6qCjR6"}, "uuid": "uMpqkfEtE8AT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Cc8BGduU1OxN": {"type": {"__ref": "YsPRhUzHSent"}, "variable": {"__ref": "WTLC5JXXFSCD"}, "uuid": "3elwXnX2alUO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "c5-pgL0u4NH5": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dNfY4GLaQ8Uj", "parent": null, "locked": null, "vsettings": [{"__ref": "d8NSIHe1tyCu"}], "__type": "TplTag"}, "9Lb3HyVpGwQL": {"uuid": "QnDlFrLlsIh2", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "2yorzVi9XxpM": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "nhPtM8j6X84D": {"type": {"__ref": "hx3LJT0zz7Q7"}, "variable": {"__ref": "IdF_AYjxwnJK"}, "uuid": "Hz0rd0UarMqv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "WaWADqAe8ETN": {"type": {"__ref": "KiHeXEXr5NrW"}, "variable": {"__ref": "oZKex35IYl5C"}, "uuid": "eJP03N9A61ed", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R4qX-icILd9s": {"type": {"__ref": "i0jXPMd40HJm"}, "tplSlot": {"__ref": "Z5i0h8K8YZSL"}, "variable": {"__ref": "SL3aLPXg10oD"}, "uuid": "NFHZNSW835dp", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "CXgBdHc5jMFy": {"type": {"__ref": "OSlps3j6tPxv"}, "variable": {"__ref": "KaksGZTcPKBg"}, "uuid": "41IvtzHFV1j7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "FR-nlHcZDRWw": {"type": {"__ref": "ArN6tRaeloOJ"}, "variable": {"__ref": "ghR1T7-Xp6L2"}, "uuid": "99VxW-dlG3h8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Jv1gKCY0MijD": {"tag": "div", "name": null, "children": [{"__ref": "Z5i0h8K8YZSL"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "F4S63__8qoIn", "parent": null, "locked": null, "vsettings": [{"__ref": "gx2iSSprNbAn"}], "__type": "TplTag"}, "2pFTJ6tbNYdN": {"uuid": "6KPKWPE5uIsl", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mr4n06O3k33t": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "3oKp5S94WjHQ": {"name": "text", "__type": "Text"}, "zuftQdXnLzvb": {"name": "title", "uuid": "KeNEpGh3o88D", "__type": "Var"}, "nWHpU0kAloNH": {"name": "text", "__type": "Text"}, "LD6NAdRwYl7d": {"name": "description", "uuid": "UjDQg12bgoQg", "__type": "Var"}, "SfMdPZRPyqxl": {"name": "img", "__type": "Img"}, "2US99w6qCjR6": {"name": "image", "uuid": "Ne86h0qmrZhP", "__type": "Var"}, "YsPRhUzHSent": {"name": "text", "__type": "Text"}, "WTLC5JXXFSCD": {"name": "canonical", "uuid": "tzrOgbGPq8Gp", "__type": "Var"}, "d8NSIHe1tyCu": {"variants": [{"__ref": "9Lb3HyVpGwQL"}], "args": [], "attrs": {}, "rs": {"__ref": "LTEvtItK2SqM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hx3LJT0zz7Q7": {"name": "any", "__type": "AnyType"}, "IdF_AYjxwnJK": {"name": "dataOp", "uuid": "y3apU-90PtWG", "__type": "Var"}, "KiHeXEXr5NrW": {"name": "text", "__type": "Text"}, "oZKex35IYl5C": {"name": "name", "uuid": "4l7Q35kdHF_A", "__type": "Var"}, "i0jXPMd40HJm": {"name": "renderFunc", "params": [{"__ref": "2cL_JOOyK8_n"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "SL3aLPXg10oD": {"name": "children", "uuid": "9e0WuC4vJyQI", "__type": "Var"}, "OSlps3j6tPxv": {"name": "num", "__type": "<PERSON><PERSON>"}, "KaksGZTcPKBg": {"name": "pageSize", "uuid": "UZAGFV1l0Tw1", "__type": "Var"}, "ArN6tRaeloOJ": {"name": "num", "__type": "<PERSON><PERSON>"}, "ghR1T7-Xp6L2": {"name": "pageIndex", "uuid": "OHRjh9OHK-Fg", "__type": "Var"}, "Z5i0h8K8YZSL": {"param": {"__ref": "R4qX-icILd9s"}, "defaultContents": [], "uuid": "E4FCSP32ws-M", "parent": {"__ref": "Jv1gKCY0MijD"}, "locked": null, "vsettings": [{"__ref": "TVytyyaI44Xw"}], "__type": "TplSlot"}, "gx2iSSprNbAn": {"variants": [{"__ref": "2pFTJ6tbNYdN"}], "args": [], "attrs": {}, "rs": {"__ref": "sqFtVNXWloLL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LTEvtItK2SqM": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "2cL_JOOyK8_n": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "e09ZknpvnyZq"}, "__type": "ArgType"}, "TVytyyaI44Xw": {"variants": [{"__ref": "2pFTJ6tbNYdN"}], "args": [], "attrs": {}, "rs": {"__ref": "nGQc0xJsGkxU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sqFtVNXWloLL": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "e09ZknpvnyZq": {"name": "any", "__type": "AnyType"}, "nGQc0xJsGkxU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8yK7aI7AMEgx": {"uuid": "Hy3y0l7-rqSZ", "name": "BaseComp", "params": [{"__ref": "XuVVhhjq3TBN"}, {"__ref": "vjN8Z7XD0BX1"}], "states": [], "tplTree": {"__ref": "blnL5929hQms"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "YP8HAIZQKDcr"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "1kRWeS0cq1ZP": {"component": {"__ref": "8yK7aI7AMEgx"}, "matrix": {"__ref": "JsZ06M6jIh1y"}, "customMatrix": {"__ref": "YSNoRP1Ryasr"}, "__type": "ComponentArena"}, "blnL5929hQms": {"tag": "div", "name": null, "children": [{"__ref": "CEk3y6ne2mTI"}, {"__ref": "0llWdoxRfpp7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QUAhaPx6IjNH", "parent": null, "locked": null, "vsettings": [{"__ref": "aM39t13jq1Wk"}], "__type": "TplTag"}, "YP8HAIZQKDcr": {"uuid": "IBn0Uu6RFaZ1", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "JsZ06M6jIh1y": {"rows": [{"__ref": "LvK7pGqBPqya"}], "__type": "ArenaFrameGrid"}, "YSNoRP1Ryasr": {"rows": [{"__ref": "G02xCrdHNT_d"}], "__type": "ArenaFrameGrid"}, "aM39t13jq1Wk": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "fzxv64fjozmT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LvK7pGqBPqya": {"cols": [{"__ref": "PwU1IMLK7pkb"}], "rowKey": null, "__type": "ArenaFrameRow"}, "G02xCrdHNT_d": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "fzxv64fjozmT": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "PwU1IMLK7pkb": {"frame": {"__ref": "TIpAIdC-8g1e"}, "cellKey": {"__ref": "YP8HAIZQKDcr"}, "__type": "ArenaFrameCell"}, "TIpAIdC-8g1e": {"uuid": "50GgfZGp261j", "width": 1180, "height": 540, "container": {"__ref": "2UrkhmAvsJgo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YP8HAIZQKDcr"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "2UrkhmAvsJgo": {"name": null, "component": {"__ref": "8yK7aI7AMEgx"}, "uuid": "dU-1mzjNBkgi", "parent": null, "locked": null, "vsettings": [{"__ref": "BgR4irmdwAhQ"}], "__type": "TplComponent"}, "BgR4irmdwAhQ": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "lyuEsguC_Jbk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lyuEsguC_Jbk": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CEk3y6ne2mTI": {"tag": "div", "name": null, "children": [{"__ref": "99B9-0qJ17Va"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "M2M64D6t_RZk", "parent": {"__ref": "blnL5929hQms"}, "locked": null, "vsettings": [{"__ref": "WDQqdE_-ZlI8"}], "__type": "TplTag"}, "0llWdoxRfpp7": {"tag": "div", "name": null, "children": [{"__ref": "VddedS8IaDDi"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OlDskMw5LLN4", "parent": {"__ref": "blnL5929hQms"}, "locked": null, "vsettings": [{"__ref": "nwHAegIdpHa0"}], "__type": "TplTag"}, "WDQqdE_-ZlI8": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "RPqnFvpCR-xc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nwHAegIdpHa0": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "tpc1rVSwRNLL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RPqnFvpCR-xc": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "tpc1rVSwRNLL": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XuVVhhjq3TBN": {"type": {"__ref": "fGh9c39am4Cx"}, "tplSlot": {"__ref": "99B9-0qJ17Va"}, "variable": {"__ref": "0LtOdqRPFniM"}, "uuid": "zFRLNPsJUax6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "99B9-0qJ17Va": {"param": {"__ref": "XuVVhhjq3TBN"}, "defaultContents": [], "uuid": "v1Fp_PkRgVt9", "parent": {"__ref": "CEk3y6ne2mTI"}, "locked": null, "vsettings": [{"__ref": "rFpEDuEUZagL"}], "__type": "TplSlot"}, "fGh9c39am4Cx": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "0LtOdqRPFniM": {"name": "trigger", "uuid": "K8YYAstJB_jW", "__type": "Var"}, "rFpEDuEUZagL": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "kCViYxxrbJZP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kCViYxxrbJZP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vjN8Z7XD0BX1": {"type": {"__ref": "TLxgyqNRzMQX"}, "tplSlot": {"__ref": "VddedS8IaDDi"}, "variable": {"__ref": "W5lu5VaUALYx"}, "uuid": "U4lvukbs3bEa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "VddedS8IaDDi": {"param": {"__ref": "vjN8Z7XD0BX1"}, "defaultContents": [], "uuid": "FFTgqJRvbMiZ", "parent": {"__ref": "0llWdoxRfpp7"}, "locked": null, "vsettings": [{"__ref": "npJgfTfFOUP2"}], "__type": "TplSlot"}, "TLxgyqNRzMQX": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "W5lu5VaUALYx": {"name": "content", "uuid": "a8C7n9ySps0a", "__type": "Var"}, "npJgfTfFOUP2": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "9baYqa8Kv6KL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9baYqa8Kv6KL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8YRyvzzFFKxR": {"uuid": "AJaJei6VwctN", "name": "/", "params": [], "states": [], "tplTree": {"__ref": "uTpcXLHDZJiG"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "nPphB5wxAula"}], "variantGroups": [], "pageMeta": {"__ref": "ca-PyuCl3C2q"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tzzp3JKJ2vki": {"component": {"__ref": "8YRyvzzFFKxR"}, "matrix": {"__ref": "do69jMnRk3Wo"}, "customMatrix": {"__ref": "qFv8jYnutLjk"}, "__type": "PageArena"}, "uTpcXLHDZJiG": {"tag": "div", "name": null, "children": [{"__ref": "mZfVmb-T6LbR"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Ha6ZrWCjlCR1", "parent": null, "locked": null, "vsettings": [{"__ref": "D6yvh_LuSJgU"}], "__type": "TplTag"}, "nPphB5wxAula": {"uuid": "9f6Xy0t4V2iT", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ca-PyuCl3C2q": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "do69jMnRk3Wo": {"rows": [{"__ref": "VuTaid6GRcWp"}], "__type": "ArenaFrameGrid"}, "qFv8jYnutLjk": {"rows": [{"__ref": "49xbJwXchD8T"}], "__type": "ArenaFrameGrid"}, "D6yvh_LuSJgU": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "3xkB8fPa-fp1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VuTaid6GRcWp": {"cols": [{"__ref": "mAxbWNwazpXs"}, {"__ref": "_XPbpPcpbtf9"}], "rowKey": {"__ref": "nPphB5wxAula"}, "__type": "ArenaFrameRow"}, "49xbJwXchD8T": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3xkB8fPa-fp1": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "mAxbWNwazpXs": {"frame": {"__ref": "r8QFAD7eH4xW"}, "cellKey": null, "__type": "ArenaFrameCell"}, "_XPbpPcpbtf9": {"frame": {"__ref": "k7FmXyLzbvBf"}, "cellKey": null, "__type": "ArenaFrameCell"}, "r8QFAD7eH4xW": {"uuid": "j1RP_P0cHNhR", "width": 1366, "height": 768, "container": {"__ref": "aX8A4r8LuLFp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "nPphB5wxAula"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "k7FmXyLzbvBf": {"uuid": "NyMxDrHnvLmV", "width": 414, "height": 736, "container": {"__ref": "ad-uthT6XRBV"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "nPphB5wxAula"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "aX8A4r8LuLFp": {"name": null, "component": {"__ref": "8YRyvzzFFKxR"}, "uuid": "J72L20C8mIsK", "parent": null, "locked": null, "vsettings": [{"__ref": "8stJe9TmtADp"}], "__type": "TplComponent"}, "ad-uthT6XRBV": {"name": null, "component": {"__ref": "8YRyvzzFFKxR"}, "uuid": "TqIDypkUM_7G", "parent": null, "locked": null, "vsettings": [{"__ref": "Azb_sheiFIAJ"}], "__type": "TplComponent"}, "8stJe9TmtADp": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "BkCaQnMxfTk6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Azb_sheiFIAJ": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "i4W3uhQ9LrBM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BkCaQnMxfTk6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "i4W3uhQ9LrBM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mZfVmb-T6LbR": {"name": "wrappedInstance", "component": {"__ref": "GT3W9zV1CrDq"}, "uuid": "PgRH7DBNcYkR", "parent": {"__ref": "uTpcXLHDZJiG"}, "locked": null, "vsettings": [{"__ref": "d6yI_jQueyow"}], "__type": "TplComponent"}, "GT3W9zV1CrDq": {"uuid": "F5kyZccwXBSw", "name": "WrappedComp", "params": [{"__ref": "1_GL9iUL45ox"}, {"__ref": "OrHdsTK0ddZj"}], "states": [], "tplTree": {"__ref": "eLUBu1P7CwY0"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "2-QadRNq1uQu"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tIsSKbgI4WLQ": {"component": {"__ref": "GT3W9zV1CrDq"}, "matrix": {"__ref": "WVQSVUnHnuaB"}, "customMatrix": {"__ref": "o-bOtBSBss5e"}, "__type": "ComponentArena"}, "d6yI_jQueyow": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [{"__ref": "GVCuIvexkyhh"}, {"__ref": "S5LlkePoGHXk"}], "attrs": {}, "rs": {"__ref": "cQCKW-qBpa4J"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eLUBu1P7CwY0": {"name": null, "component": {"__ref": "8yK7aI7AMEgx"}, "uuid": "NvOQZPRXsgzs", "parent": null, "locked": null, "vsettings": [{"__ref": "5XsILk61tB6E"}], "__type": "TplComponent"}, "2-QadRNq1uQu": {"uuid": "-vFe4iqiIkfH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WVQSVUnHnuaB": {"rows": [{"__ref": "GqB6kVLVSRFr"}], "__type": "ArenaFrameGrid"}, "o-bOtBSBss5e": {"rows": [{"__ref": "zDDkT4rGd_ch"}], "__type": "ArenaFrameGrid"}, "cQCKW-qBpa4J": {"values": {"width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "5XsILk61tB6E": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [{"__ref": "VSnIKomFcj_8"}, {"__ref": "jSFmSa3AeEHa"}], "attrs": {}, "rs": {"__ref": "6F6x--AlaiPX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GqB6kVLVSRFr": {"cols": [{"__ref": "M8pkUC60aF5E"}], "rowKey": null, "__type": "ArenaFrameRow"}, "zDDkT4rGd_ch": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "VSnIKomFcj_8": {"param": {"__ref": "XuVVhhjq3TBN"}, "expr": {"__ref": "NlWcjqiY2umR"}, "__type": "Arg"}, "jSFmSa3AeEHa": {"param": {"__ref": "vjN8Z7XD0BX1"}, "expr": {"__ref": "zIUo8gGbjpFS"}, "__type": "Arg"}, "6F6x--AlaiPX": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "M8pkUC60aF5E": {"frame": {"__ref": "nZbRoVWCPhfG"}, "cellKey": {"__ref": "2-QadRNq1uQu"}, "__type": "ArenaFrameCell"}, "nZbRoVWCPhfG": {"uuid": "2PQvAVryqcso", "width": 1180, "height": 540, "container": {"__ref": "D3f8pw1ntqYL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "2-QadRNq1uQu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "D3f8pw1ntqYL": {"name": null, "component": {"__ref": "GT3W9zV1CrDq"}, "uuid": "ptjD9uS33BoU", "parent": null, "locked": null, "vsettings": [{"__ref": "hP7JsCZmgS1V"}], "__type": "TplComponent"}, "hP7JsCZmgS1V": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "I8stP5IDToih"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I8stP5IDToih": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1_GL9iUL45ox": {"type": {"__ref": "aNDulDYx6n9o"}, "tplSlot": {"__ref": "cCazRAneq9Kf"}, "variable": {"__ref": "42nL_dxVwgHD"}, "uuid": "TK3WIcfB1iKR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "NlWcjqiY2umR": {"tpl": [{"__ref": "cCazRAneq9Kf"}], "__type": "RenderExpr"}, "GVCuIvexkyhh": {"param": {"__ref": "1_GL9iUL45ox"}, "expr": {"__ref": "WRkwPrKJryG6"}, "__type": "Arg"}, "aNDulDYx6n9o": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "42nL_dxVwgHD": {"name": "target", "uuid": "iy5PEJl-waVp", "__type": "Var"}, "cCazRAneq9Kf": {"param": {"__ref": "1_GL9iUL45ox"}, "defaultContents": [{"__ref": "wnn7Ijz0p3_J"}], "uuid": "9mpeeur7vurA", "parent": {"__ref": "eLUBu1P7CwY0"}, "locked": null, "vsettings": [{"__ref": "Q9msYmCR7_vv"}], "__type": "TplSlot"}, "wnn7Ijz0p3_J": {"tag": "div", "name": null, "children": [{"__ref": "s2u83pPJGhae"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Su2f1Kq616JP", "parent": {"__ref": "cCazRAneq9Kf"}, "locked": null, "vsettings": [{"__ref": "Wi9MEuxng0Bo"}], "__type": "TplTag"}, "Q9msYmCR7_vv": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "F_97zqkLbz7c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Wi9MEuxng0Bo": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "jFYmkUNtGpuw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F_97zqkLbz7c": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jFYmkUNtGpuw": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "s2u83pPJGhae": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kvnuVCx1x7zz", "parent": {"__ref": "wnn7Ijz0p3_J"}, "locked": null, "vsettings": [{"__ref": "-tb4znOU9PRD"}], "__type": "TplTag"}, "-tb4znOU9PRD": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "B4Tw-jP9genZ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "XUEWYtRMf8bQ"}, "columnsConfig": null, "__type": "VariantSetting"}, "B4Tw-jP9genZ": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "XUEWYtRMf8bQ": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "WRkwPrKJryG6": {"tpl": [{"__ref": "bkzH8P0ki-VK"}], "__type": "VirtualRenderExpr"}, "bkzH8P0ki-VK": {"tag": "div", "name": null, "children": [{"__ref": "IiAqzydZsOg7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dDrOFnDxYuRC", "parent": {"__ref": "mZfVmb-T6LbR"}, "locked": null, "vsettings": [{"__ref": "hXxzpuRuYoBl"}], "__type": "TplTag"}, "IiAqzydZsOg7": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "mK7CqSt2zv62", "parent": {"__ref": "bkzH8P0ki-VK"}, "locked": null, "vsettings": [{"__ref": "CsCm70E0-IN6"}], "__type": "TplTag"}, "hXxzpuRuYoBl": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "iUV5cDtnaI-d"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CsCm70E0-IN6": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "0ERSVAAhv2sR"}, "dataCond": null, "dataRep": null, "text": {"__ref": "rny_A2gBFmC1"}, "columnsConfig": null, "__type": "VariantSetting"}, "iUV5cDtnaI-d": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "0ERSVAAhv2sR": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "rny_A2gBFmC1": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "OrHdsTK0ddZj": {"type": {"__ref": "KZZntV0orPyr"}, "tplSlot": {"__ref": "OCRWGdxAnAdk"}, "variable": {"__ref": "07hJ1gOVOhSo"}, "uuid": "2w9DLe1HlZ4J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "zIUo8gGbjpFS": {"tpl": [{"__ref": "OCRWGdxAnAdk"}], "__type": "RenderExpr"}, "S5LlkePoGHXk": {"param": {"__ref": "OrHdsTK0ddZj"}, "expr": {"__ref": "rfpCkhQTqDCT"}, "__type": "Arg"}, "KZZntV0orPyr": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "07hJ1gOVOhSo": {"name": "content", "uuid": "p-TWXlAPGCyq", "__type": "Var"}, "OCRWGdxAnAdk": {"param": {"__ref": "OrHdsTK0ddZj"}, "defaultContents": [{"__ref": "6jTrDzMOLO7n"}], "uuid": "oc81PUKKZ_OM", "parent": {"__ref": "eLUBu1P7CwY0"}, "locked": null, "vsettings": [{"__ref": "WZIiJvKcckSG"}], "__type": "TplSlot"}, "6jTrDzMOLO7n": {"tag": "div", "name": null, "children": [{"__ref": "XuVgY0b5cHDf"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "app3iK1FqcbW", "parent": {"__ref": "OCRWGdxAnAdk"}, "locked": null, "vsettings": [{"__ref": "HfzplSX3s43l"}], "__type": "TplTag"}, "WZIiJvKcckSG": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "-ma7vk0HDrpg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XuVgY0b5cHDf": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ARuY0QrvaMN3", "parent": {"__ref": "6jTrDzMOLO7n"}, "locked": null, "vsettings": [{"__ref": "O8HnhDOUp_dJ"}], "__type": "TplTag"}, "HfzplSX3s43l": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "udCXAFZX0mEy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-ma7vk0HDrpg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "O8HnhDOUp_dJ": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "YPGZg_dUZWT1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "PTWWKhPcVTC0"}, "columnsConfig": null, "__type": "VariantSetting"}, "udCXAFZX0mEy": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "YPGZg_dUZWT1": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "PTWWKhPcVTC0": {"markers": [], "text": "Content", "__type": "RawText"}, "rfpCkhQTqDCT": {"tpl": [{"__ref": "kFLBh_E_dinN"}], "__type": "VirtualRenderExpr"}, "kFLBh_E_dinN": {"tag": "div", "name": null, "children": [{"__ref": "JPUNN3B8piqz"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "B0OwaJFKUAgY", "parent": {"__ref": "mZfVmb-T6LbR"}, "locked": null, "vsettings": [{"__ref": "T-5FOaqaom5n"}], "__type": "TplTag"}, "JPUNN3B8piqz": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "846bne2YM3ho", "parent": {"__ref": "kFLBh_E_dinN"}, "locked": null, "vsettings": [{"__ref": "x1jakPooGbl2"}], "__type": "TplTag"}, "T-5FOaqaom5n": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "EbzY6OEZQyO4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x1jakPooGbl2": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "7629xlXPqF7i"}, "dataCond": null, "dataRep": null, "text": {"__ref": "jTItfzYxC2mj"}, "columnsConfig": null, "__type": "VariantSetting"}, "EbzY6OEZQyO4": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "7629xlXPqF7i": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "jTItfzYxC2mj": {"markers": [], "text": "Content", "__type": "RawText"}, "7K9yTsH7Hsam": {"uuid": "dKsqwgqYGMK7", "pkgId": "bbb16df6-b831-4f83-ab97-dc16461a26ef", "projectId": "draijZF6gtxjcZgKwMFy9R", "version": "1.0.0", "name": "bug  virtual slots", "site": {"__ref": "YFGCw9A6o6px"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}, "projectId": "draijZF6gtxjcZgKwMFy9R", "version": "1.0.0", "branchId": "main"}], "project": {"id": "draijZF6gtxjcZgKwMFy9R", "name": "bug  virtual slots", "commitGraph": {"parents": {"4410b993-696c-4249-8652-6009c1520429": [], "73cf9284-819e-4295-8a86-4157ea0d5cd4": ["4410b993-696c-4249-8652-6009c1520429"], "991d6c2f-3dcf-4c51-996a-b4d802eb80d5": ["73cf9284-819e-4295-8a86-4157ea0d5cd4", "4410b993-696c-4249-8652-6009c1520429"], "b3b27a2a-949d-437d-b367-84db7d868942": ["4410b993-696c-4249-8652-6009c1520429"]}, "branches": {"main": "b3b27a2a-949d-437d-b367-84db7d868942", "3oNJqKojkvKU3PqHPY6eW3": "b3b27a2a-949d-437d-b367-84db7d868942", "jih3f85m5hxRQZF66Vvhr3": "4410b993-696c-4249-8652-6009c1520429", "nUBmbzqeUyLYr1AALR3Fvj": "991d6c2f-3dcf-4c51-996a-b4d802eb80d5"}}}, "revisions": [{"branchId": "3oNJqKojkvKU3PqHPY6eW3", "data": {"root": "YFGCw9A6o6px", "map": {"o_IpZWdrJ6k4": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "v30pZLvaRgcP": {"name": "Default Typography", "rs": {"__ref": "o_IpZWdrJ6k4"}, "preview": null, "uuid": "drIjq5irThZG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HhGS4RaymTUl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "oXJoYwuxFf21": {"rs": {"__ref": "HhGS4RaymTUl"}, "__type": "ThemeLayoutSettings"}, "Nez8EnmNyKQo": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "cs00yFbtCPu5": {"name": "Default \"h1\"", "rs": {"__ref": "Nez8EnmNyKQo"}, "preview": null, "uuid": "lCbOBeT_cgp7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "cIDG_55N1Fpw": {"selector": "h1", "style": {"__ref": "cs00yFbtCPu5"}, "__type": "ThemeStyle"}, "cvXzmIa9412y": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "YzvgRe1Y6adD": {"name": "Default \"h2\"", "rs": {"__ref": "cvXzmIa9412y"}, "preview": null, "uuid": "YB1JosPGCrnX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hecebwT2Rzc_": {"selector": "h2", "style": {"__ref": "YzvgRe1Y6adD"}, "__type": "ThemeStyle"}, "bf9s88nNEuFS": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "V1TYUdNAQJJn": {"name": "Default \"h3\"", "rs": {"__ref": "bf9s88nNEuFS"}, "preview": null, "uuid": "t02qtico63Nm", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9CJBkppIqt9P": {"selector": "h3", "style": {"__ref": "V1TYUdNAQJJn"}, "__type": "ThemeStyle"}, "PcIWCzoFL2In": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "fYIeAK6A5Cxw": {"name": "Default \"h4\"", "rs": {"__ref": "PcIWCzoFL2In"}, "preview": null, "uuid": "8QKgjAXfxV2k", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Jk70cI71FsEj": {"selector": "h4", "style": {"__ref": "fYIeAK6A5Cxw"}, "__type": "ThemeStyle"}, "Gkeg-9lMo5KY": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "Tdq8-nchuKDu": {"name": "Default \"h5\"", "rs": {"__ref": "Gkeg-9lMo5KY"}, "preview": null, "uuid": "Of_nPFozBg1M", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "6pWDYtx3quce": {"selector": "h5", "style": {"__ref": "Tdq8-nchuKDu"}, "__type": "ThemeStyle"}, "w-K116V_-af7": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "JfZZZUkVkprh": {"name": "Default \"h6\"", "rs": {"__ref": "w-K116V_-af7"}, "preview": null, "uuid": "jpw28QSTXvnQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "I2es3aTGk8Uh": {"selector": "h6", "style": {"__ref": "JfZZZUkVkprh"}, "__type": "ThemeStyle"}, "HYwCGzuTSsaE": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "NEprx9duIvW2": {"name": "Default \"a\"", "rs": {"__ref": "HYwCGzuTSsaE"}, "preview": null, "uuid": "It975DeOKr34", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "riQIC31_qsaR": {"selector": "a", "style": {"__ref": "NEprx9duIvW2"}, "__type": "ThemeStyle"}, "oJ2ZAC_noA0d": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "IfbAxEw8VR9-": {"name": "Default \"a:hover\"", "rs": {"__ref": "oJ2ZAC_noA0d"}, "preview": null, "uuid": "aiekhxB5k0-X", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BtAYTWU3zTNl": {"selector": "a:hover", "style": {"__ref": "IfbAxEw8VR9-"}, "__type": "ThemeStyle"}, "ep6pRa5lQ1zA": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "_uZezshwWRfd": {"name": "Default \"blockquote\"", "rs": {"__ref": "ep6pRa5lQ1zA"}, "preview": null, "uuid": "gI5cpIvf52Ik", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "3H1f4GtJ0zHM": {"selector": "blockquote", "style": {"__ref": "_uZezshwWRfd"}, "__type": "ThemeStyle"}, "nsjpK_Q6_yb3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "QQRT3CNracla": {"name": "Default \"code\"", "rs": {"__ref": "nsjpK_Q6_yb3"}, "preview": null, "uuid": "e17ky251554D", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xcGVbMO6XG-0": {"selector": "code", "style": {"__ref": "QQRT3CNracla"}, "__type": "ThemeStyle"}, "gB9OML3HfCTe": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "Sjy-1UdSCsyR": {"name": "Default \"pre\"", "rs": {"__ref": "gB9OML3HfCTe"}, "preview": null, "uuid": "sWKYKrMTC5er", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Dn27uuD5IXVR": {"selector": "pre", "style": {"__ref": "Sjy-1UdSCsyR"}, "__type": "ThemeStyle"}, "OJQ8F49dq03Y": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "sZUfickrUJSF": {"name": "Default \"ol\"", "rs": {"__ref": "OJQ8F49dq03Y"}, "preview": null, "uuid": "FlFebUX3SzfG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rm4kxThiGTsN": {"selector": "ol", "style": {"__ref": "sZUfickrUJSF"}, "__type": "ThemeStyle"}, "_QWQ9Z9Jimqz": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "NErB2sBVMWIc": {"name": "Default \"ul\"", "rs": {"__ref": "_QWQ9Z9Jimqz"}, "preview": null, "uuid": "lzILSOFEanHd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9Vbuuq0Z7jUJ": {"selector": "ul", "style": {"__ref": "NErB2sBVMWIc"}, "__type": "ThemeStyle"}, "-kktNl7gIjBb": {"defaultStyle": {"__ref": "v30pZLvaRgcP"}, "styles": [{"__ref": "cIDG_55N1Fpw"}, {"__ref": "hecebwT2Rzc_"}, {"__ref": "9CJBkppIqt9P"}, {"__ref": "Jk70cI71FsEj"}, {"__ref": "6pWDYtx3quce"}, {"__ref": "I2es3aTGk8Uh"}, {"__ref": "riQIC31_qsaR"}, {"__ref": "BtAYTWU3zTNl"}, {"__ref": "3H1f4GtJ0zHM"}, {"__ref": "xcGVbMO6XG-0"}, {"__ref": "Dn27uuD5IXVR"}, {"__ref": "Rm4kxThiGTsN"}, {"__ref": "9Vbuuq0Z7jUJ"}], "layout": {"__ref": "oXJoYwuxFf21"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "YZmfWqoDbiIb": {"name": "text", "__type": "Text"}, "8JVukDb_Vcdm": {"name": "Screen", "uuid": "Z6c_OohR62vI", "__type": "Var"}, "_Zt9NWxM9LvU": {"type": {"__ref": "YZmfWqoDbiIb"}, "variable": {"__ref": "8JVukDb_Vcdm"}, "uuid": "EUoy5WmiFMfO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "4jevmYDqUWr2": {"type": "global-screen", "param": {"__ref": "_Zt9NWxM9LvU"}, "uuid": "-lKhz_5_01KN", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "DgqVJbxT6rzS": {"uuid": "4BAUmxR3ilwJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "YFGCw9A6o6px": {"components": [{"__ref": "frGaU1n73odM"}, {"__ref": "TO5_tsJ2rxq7"}, {"__ref": "8yK7aI7AMEgx"}, {"__ref": "8YRyvzzFFKxR"}, {"__ref": "GT3W9zV1CrDq"}], "arenas": [], "pageArenas": [{"__ref": "tzzp3JKJ2vki"}], "componentArenas": [{"__ref": "1kRWeS0cq1ZP"}, {"__ref": "tIsSKbgI4WLQ"}], "globalVariantGroups": [{"__ref": "4jevmYDqUWr2"}], "userManagedFonts": [], "globalVariant": {"__ref": "DgqVJbxT6rzS"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "-kktNl7gIjBb"}], "activeTheme": {"__ref": "-kktNl7gIjBb"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "4jevmYDqUWr2"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "frGaU1n73odM": {"uuid": "AmEg4oPHP3ZT", "name": "hostless-plasmic-head", "params": [{"__ref": "IGtKvsQdnWeM"}, {"__ref": "sYMuYU-7rQKC"}, {"__ref": "SGRUBKLX_ybx"}, {"__ref": "Cc8BGduU1OxN"}], "states": [], "tplTree": {"__ref": "c5-pgL0u4NH5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "9Lb3HyVpGwQL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "2yorzVi9XxpM"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "TO5_tsJ2rxq7": {"uuid": "XNcPoW5tB7Uk", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "nhPtM8j6X84D"}, {"__ref": "WaWADqAe8ETN"}, {"__ref": "R4qX-icILd9s"}, {"__ref": "CXgBdHc5jMFy"}, {"__ref": "FR-nlHcZDRWw"}], "states": [], "tplTree": {"__ref": "Jv1gKCY0MijD"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "2pFTJ6tbNYdN"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "mr4n06O3k33t"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "IGtKvsQdnWeM": {"type": {"__ref": "3oKp5S94WjHQ"}, "variable": {"__ref": "zuftQdXnLzvb"}, "uuid": "vMz1jo0LZEOq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "sYMuYU-7rQKC": {"type": {"__ref": "nWHpU0kAloNH"}, "variable": {"__ref": "LD6NAdRwYl7d"}, "uuid": "G4yCjh5sYi1g", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "SGRUBKLX_ybx": {"type": {"__ref": "SfMdPZRPyqxl"}, "variable": {"__ref": "2US99w6qCjR6"}, "uuid": "uMpqkfEtE8AT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Cc8BGduU1OxN": {"type": {"__ref": "YsPRhUzHSent"}, "variable": {"__ref": "WTLC5JXXFSCD"}, "uuid": "3elwXnX2alUO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "c5-pgL0u4NH5": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dNfY4GLaQ8Uj", "parent": null, "locked": null, "vsettings": [{"__ref": "d8NSIHe1tyCu"}], "__type": "TplTag"}, "9Lb3HyVpGwQL": {"uuid": "QnDlFrLlsIh2", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "2yorzVi9XxpM": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "nhPtM8j6X84D": {"type": {"__ref": "hx3LJT0zz7Q7"}, "variable": {"__ref": "IdF_AYjxwnJK"}, "uuid": "Hz0rd0UarMqv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "WaWADqAe8ETN": {"type": {"__ref": "KiHeXEXr5NrW"}, "variable": {"__ref": "oZKex35IYl5C"}, "uuid": "eJP03N9A61ed", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R4qX-icILd9s": {"type": {"__ref": "i0jXPMd40HJm"}, "tplSlot": {"__ref": "Z5i0h8K8YZSL"}, "variable": {"__ref": "SL3aLPXg10oD"}, "uuid": "NFHZNSW835dp", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "CXgBdHc5jMFy": {"type": {"__ref": "OSlps3j6tPxv"}, "variable": {"__ref": "KaksGZTcPKBg"}, "uuid": "41IvtzHFV1j7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "FR-nlHcZDRWw": {"type": {"__ref": "ArN6tRaeloOJ"}, "variable": {"__ref": "ghR1T7-Xp6L2"}, "uuid": "99VxW-dlG3h8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Jv1gKCY0MijD": {"tag": "div", "name": null, "children": [{"__ref": "Z5i0h8K8YZSL"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "F4S63__8qoIn", "parent": null, "locked": null, "vsettings": [{"__ref": "gx2iSSprNbAn"}], "__type": "TplTag"}, "2pFTJ6tbNYdN": {"uuid": "6KPKWPE5uIsl", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mr4n06O3k33t": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "3oKp5S94WjHQ": {"name": "text", "__type": "Text"}, "zuftQdXnLzvb": {"name": "title", "uuid": "KeNEpGh3o88D", "__type": "Var"}, "nWHpU0kAloNH": {"name": "text", "__type": "Text"}, "LD6NAdRwYl7d": {"name": "description", "uuid": "UjDQg12bgoQg", "__type": "Var"}, "SfMdPZRPyqxl": {"name": "img", "__type": "Img"}, "2US99w6qCjR6": {"name": "image", "uuid": "Ne86h0qmrZhP", "__type": "Var"}, "YsPRhUzHSent": {"name": "text", "__type": "Text"}, "WTLC5JXXFSCD": {"name": "canonical", "uuid": "tzrOgbGPq8Gp", "__type": "Var"}, "d8NSIHe1tyCu": {"variants": [{"__ref": "9Lb3HyVpGwQL"}], "args": [], "attrs": {}, "rs": {"__ref": "LTEvtItK2SqM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hx3LJT0zz7Q7": {"name": "any", "__type": "AnyType"}, "IdF_AYjxwnJK": {"name": "dataOp", "uuid": "y3apU-90PtWG", "__type": "Var"}, "KiHeXEXr5NrW": {"name": "text", "__type": "Text"}, "oZKex35IYl5C": {"name": "name", "uuid": "4l7Q35kdHF_A", "__type": "Var"}, "i0jXPMd40HJm": {"name": "renderFunc", "params": [{"__ref": "2cL_JOOyK8_n"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "SL3aLPXg10oD": {"name": "children", "uuid": "9e0WuC4vJyQI", "__type": "Var"}, "OSlps3j6tPxv": {"name": "num", "__type": "<PERSON><PERSON>"}, "KaksGZTcPKBg": {"name": "pageSize", "uuid": "UZAGFV1l0Tw1", "__type": "Var"}, "ArN6tRaeloOJ": {"name": "num", "__type": "<PERSON><PERSON>"}, "ghR1T7-Xp6L2": {"name": "pageIndex", "uuid": "OHRjh9OHK-Fg", "__type": "Var"}, "Z5i0h8K8YZSL": {"param": {"__ref": "R4qX-icILd9s"}, "defaultContents": [], "uuid": "E4FCSP32ws-M", "parent": {"__ref": "Jv1gKCY0MijD"}, "locked": null, "vsettings": [{"__ref": "TVytyyaI44Xw"}], "__type": "TplSlot"}, "gx2iSSprNbAn": {"variants": [{"__ref": "2pFTJ6tbNYdN"}], "args": [], "attrs": {}, "rs": {"__ref": "sqFtVNXWloLL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LTEvtItK2SqM": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "2cL_JOOyK8_n": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "e09ZknpvnyZq"}, "__type": "ArgType"}, "TVytyyaI44Xw": {"variants": [{"__ref": "2pFTJ6tbNYdN"}], "args": [], "attrs": {}, "rs": {"__ref": "nGQc0xJsGkxU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sqFtVNXWloLL": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "e09ZknpvnyZq": {"name": "any", "__type": "AnyType"}, "nGQc0xJsGkxU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8yK7aI7AMEgx": {"uuid": "Hy3y0l7-rqSZ", "name": "BaseComp", "params": [{"__ref": "XuVVhhjq3TBN"}, {"__ref": "vjN8Z7XD0BX1"}], "states": [], "tplTree": {"__ref": "blnL5929hQms"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "YP8HAIZQKDcr"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "1kRWeS0cq1ZP": {"component": {"__ref": "8yK7aI7AMEgx"}, "matrix": {"__ref": "JsZ06M6jIh1y"}, "customMatrix": {"__ref": "YSNoRP1Ryasr"}, "__type": "ComponentArena"}, "blnL5929hQms": {"tag": "div", "name": null, "children": [{"__ref": "CEk3y6ne2mTI"}, {"__ref": "0llWdoxRfpp7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QUAhaPx6IjNH", "parent": null, "locked": null, "vsettings": [{"__ref": "aM39t13jq1Wk"}], "__type": "TplTag"}, "YP8HAIZQKDcr": {"uuid": "IBn0Uu6RFaZ1", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "JsZ06M6jIh1y": {"rows": [{"__ref": "LvK7pGqBPqya"}], "__type": "ArenaFrameGrid"}, "YSNoRP1Ryasr": {"rows": [{"__ref": "G02xCrdHNT_d"}], "__type": "ArenaFrameGrid"}, "aM39t13jq1Wk": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "fzxv64fjozmT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LvK7pGqBPqya": {"cols": [{"__ref": "PwU1IMLK7pkb"}], "rowKey": null, "__type": "ArenaFrameRow"}, "G02xCrdHNT_d": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "fzxv64fjozmT": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "PwU1IMLK7pkb": {"frame": {"__ref": "TIpAIdC-8g1e"}, "cellKey": {"__ref": "YP8HAIZQKDcr"}, "__type": "ArenaFrameCell"}, "TIpAIdC-8g1e": {"uuid": "50GgfZGp261j", "width": 1180, "height": 540, "container": {"__ref": "2UrkhmAvsJgo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YP8HAIZQKDcr"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "2UrkhmAvsJgo": {"name": null, "component": {"__ref": "8yK7aI7AMEgx"}, "uuid": "dU-1mzjNBkgi", "parent": null, "locked": null, "vsettings": [{"__ref": "BgR4irmdwAhQ"}], "__type": "TplComponent"}, "BgR4irmdwAhQ": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "lyuEsguC_Jbk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lyuEsguC_Jbk": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CEk3y6ne2mTI": {"tag": "div", "name": null, "children": [{"__ref": "99B9-0qJ17Va"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "M2M64D6t_RZk", "parent": {"__ref": "blnL5929hQms"}, "locked": null, "vsettings": [{"__ref": "WDQqdE_-ZlI8"}], "__type": "TplTag"}, "0llWdoxRfpp7": {"tag": "div", "name": null, "children": [{"__ref": "VddedS8IaDDi"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OlDskMw5LLN4", "parent": {"__ref": "blnL5929hQms"}, "locked": null, "vsettings": [{"__ref": "nwHAegIdpHa0"}], "__type": "TplTag"}, "WDQqdE_-ZlI8": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "RPqnFvpCR-xc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nwHAegIdpHa0": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "tpc1rVSwRNLL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RPqnFvpCR-xc": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "tpc1rVSwRNLL": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XuVVhhjq3TBN": {"type": {"__ref": "fGh9c39am4Cx"}, "tplSlot": {"__ref": "99B9-0qJ17Va"}, "variable": {"__ref": "0LtOdqRPFniM"}, "uuid": "zFRLNPsJUax6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "99B9-0qJ17Va": {"param": {"__ref": "XuVVhhjq3TBN"}, "defaultContents": [], "uuid": "v1Fp_PkRgVt9", "parent": {"__ref": "CEk3y6ne2mTI"}, "locked": null, "vsettings": [{"__ref": "rFpEDuEUZagL"}], "__type": "TplSlot"}, "fGh9c39am4Cx": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "0LtOdqRPFniM": {"name": "trigger", "uuid": "K8YYAstJB_jW", "__type": "Var"}, "rFpEDuEUZagL": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "kCViYxxrbJZP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kCViYxxrbJZP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vjN8Z7XD0BX1": {"type": {"__ref": "TLxgyqNRzMQX"}, "tplSlot": {"__ref": "VddedS8IaDDi"}, "variable": {"__ref": "W5lu5VaUALYx"}, "uuid": "U4lvukbs3bEa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "VddedS8IaDDi": {"param": {"__ref": "vjN8Z7XD0BX1"}, "defaultContents": [], "uuid": "FFTgqJRvbMiZ", "parent": {"__ref": "0llWdoxRfpp7"}, "locked": null, "vsettings": [{"__ref": "npJgfTfFOUP2"}], "__type": "TplSlot"}, "TLxgyqNRzMQX": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "W5lu5VaUALYx": {"name": "content", "uuid": "a8C7n9ySps0a", "__type": "Var"}, "npJgfTfFOUP2": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "9baYqa8Kv6KL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9baYqa8Kv6KL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8YRyvzzFFKxR": {"uuid": "AJaJei6VwctN", "name": "/", "params": [], "states": [], "tplTree": {"__ref": "uTpcXLHDZJiG"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "nPphB5wxAula"}], "variantGroups": [], "pageMeta": {"__ref": "ca-PyuCl3C2q"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tzzp3JKJ2vki": {"component": {"__ref": "8YRyvzzFFKxR"}, "matrix": {"__ref": "do69jMnRk3Wo"}, "customMatrix": {"__ref": "qFv8jYnutLjk"}, "__type": "PageArena"}, "uTpcXLHDZJiG": {"tag": "div", "name": null, "children": [{"__ref": "mZfVmb-T6LbR"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Ha6ZrWCjlCR1", "parent": null, "locked": null, "vsettings": [{"__ref": "D6yvh_LuSJgU"}], "__type": "TplTag"}, "nPphB5wxAula": {"uuid": "9f6Xy0t4V2iT", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ca-PyuCl3C2q": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "do69jMnRk3Wo": {"rows": [{"__ref": "VuTaid6GRcWp"}], "__type": "ArenaFrameGrid"}, "qFv8jYnutLjk": {"rows": [{"__ref": "49xbJwXchD8T"}], "__type": "ArenaFrameGrid"}, "D6yvh_LuSJgU": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "3xkB8fPa-fp1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VuTaid6GRcWp": {"cols": [{"__ref": "mAxbWNwazpXs"}, {"__ref": "_XPbpPcpbtf9"}], "rowKey": {"__ref": "nPphB5wxAula"}, "__type": "ArenaFrameRow"}, "49xbJwXchD8T": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3xkB8fPa-fp1": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "mAxbWNwazpXs": {"frame": {"__ref": "r8QFAD7eH4xW"}, "cellKey": null, "__type": "ArenaFrameCell"}, "_XPbpPcpbtf9": {"frame": {"__ref": "k7FmXyLzbvBf"}, "cellKey": null, "__type": "ArenaFrameCell"}, "r8QFAD7eH4xW": {"uuid": "j1RP_P0cHNhR", "width": 1366, "height": 768, "container": {"__ref": "aX8A4r8LuLFp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "nPphB5wxAula"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "k7FmXyLzbvBf": {"uuid": "NyMxDrHnvLmV", "width": 414, "height": 736, "container": {"__ref": "ad-uthT6XRBV"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "nPphB5wxAula"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "aX8A4r8LuLFp": {"name": null, "component": {"__ref": "8YRyvzzFFKxR"}, "uuid": "J72L20C8mIsK", "parent": null, "locked": null, "vsettings": [{"__ref": "8stJe9TmtADp"}], "__type": "TplComponent"}, "ad-uthT6XRBV": {"name": null, "component": {"__ref": "8YRyvzzFFKxR"}, "uuid": "TqIDypkUM_7G", "parent": null, "locked": null, "vsettings": [{"__ref": "Azb_sheiFIAJ"}], "__type": "TplComponent"}, "8stJe9TmtADp": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "BkCaQnMxfTk6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Azb_sheiFIAJ": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "i4W3uhQ9LrBM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BkCaQnMxfTk6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "i4W3uhQ9LrBM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mZfVmb-T6LbR": {"name": "wrappedInstance", "component": {"__ref": "GT3W9zV1CrDq"}, "uuid": "PgRH7DBNcYkR", "parent": {"__ref": "uTpcXLHDZJiG"}, "locked": null, "vsettings": [{"__ref": "d6yI_jQueyow"}], "__type": "TplComponent"}, "GT3W9zV1CrDq": {"uuid": "F5kyZccwXBSw", "name": "WrappedComp", "params": [{"__ref": "1_GL9iUL45ox"}, {"__ref": "OrHdsTK0ddZj"}], "states": [], "tplTree": {"__ref": "eLUBu1P7CwY0"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "2-QadRNq1uQu"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tIsSKbgI4WLQ": {"component": {"__ref": "GT3W9zV1CrDq"}, "matrix": {"__ref": "WVQSVUnHnuaB"}, "customMatrix": {"__ref": "o-bOtBSBss5e"}, "__type": "ComponentArena"}, "d6yI_jQueyow": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [{"__ref": "GVCuIvexkyhh"}, {"__ref": "S5LlkePoGHXk"}], "attrs": {}, "rs": {"__ref": "cQCKW-qBpa4J"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eLUBu1P7CwY0": {"name": null, "component": {"__ref": "8yK7aI7AMEgx"}, "uuid": "NvOQZPRXsgzs", "parent": null, "locked": null, "vsettings": [{"__ref": "5XsILk61tB6E"}], "__type": "TplComponent"}, "2-QadRNq1uQu": {"uuid": "-vFe4iqiIkfH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WVQSVUnHnuaB": {"rows": [{"__ref": "GqB6kVLVSRFr"}], "__type": "ArenaFrameGrid"}, "o-bOtBSBss5e": {"rows": [{"__ref": "zDDkT4rGd_ch"}], "__type": "ArenaFrameGrid"}, "cQCKW-qBpa4J": {"values": {"width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "5XsILk61tB6E": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [{"__ref": "VSnIKomFcj_8"}, {"__ref": "jSFmSa3AeEHa"}], "attrs": {}, "rs": {"__ref": "6F6x--AlaiPX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GqB6kVLVSRFr": {"cols": [{"__ref": "M8pkUC60aF5E"}], "rowKey": null, "__type": "ArenaFrameRow"}, "zDDkT4rGd_ch": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "VSnIKomFcj_8": {"param": {"__ref": "XuVVhhjq3TBN"}, "expr": {"__ref": "NlWcjqiY2umR"}, "__type": "Arg"}, "jSFmSa3AeEHa": {"param": {"__ref": "vjN8Z7XD0BX1"}, "expr": {"__ref": "zIUo8gGbjpFS"}, "__type": "Arg"}, "6F6x--AlaiPX": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "M8pkUC60aF5E": {"frame": {"__ref": "nZbRoVWCPhfG"}, "cellKey": {"__ref": "2-QadRNq1uQu"}, "__type": "ArenaFrameCell"}, "nZbRoVWCPhfG": {"uuid": "2PQvAVryqcso", "width": 1180, "height": 540, "container": {"__ref": "D3f8pw1ntqYL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "2-QadRNq1uQu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "D3f8pw1ntqYL": {"name": null, "component": {"__ref": "GT3W9zV1CrDq"}, "uuid": "ptjD9uS33BoU", "parent": null, "locked": null, "vsettings": [{"__ref": "hP7JsCZmgS1V"}], "__type": "TplComponent"}, "hP7JsCZmgS1V": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "I8stP5IDToih"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I8stP5IDToih": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1_GL9iUL45ox": {"type": {"__ref": "aNDulDYx6n9o"}, "tplSlot": {"__ref": "cCazRAneq9Kf"}, "variable": {"__ref": "42nL_dxVwgHD"}, "uuid": "TK3WIcfB1iKR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "NlWcjqiY2umR": {"tpl": [{"__ref": "cCazRAneq9Kf"}], "__type": "RenderExpr"}, "GVCuIvexkyhh": {"param": {"__ref": "1_GL9iUL45ox"}, "expr": {"__ref": "WRkwPrKJryG6"}, "__type": "Arg"}, "aNDulDYx6n9o": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "42nL_dxVwgHD": {"name": "target", "uuid": "iy5PEJl-waVp", "__type": "Var"}, "cCazRAneq9Kf": {"param": {"__ref": "1_GL9iUL45ox"}, "defaultContents": [{"__ref": "wnn7Ijz0p3_J"}], "uuid": "9mpeeur7vurA", "parent": {"__ref": "eLUBu1P7CwY0"}, "locked": null, "vsettings": [{"__ref": "Q9msYmCR7_vv"}], "__type": "TplSlot"}, "wnn7Ijz0p3_J": {"tag": "div", "name": null, "children": [{"__ref": "s2u83pPJGhae"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Su2f1Kq616JP", "parent": {"__ref": "cCazRAneq9Kf"}, "locked": null, "vsettings": [{"__ref": "Wi9MEuxng0Bo"}], "__type": "TplTag"}, "Q9msYmCR7_vv": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "F_97zqkLbz7c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Wi9MEuxng0Bo": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "jFYmkUNtGpuw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F_97zqkLbz7c": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jFYmkUNtGpuw": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "s2u83pPJGhae": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kvnuVCx1x7zz", "parent": {"__ref": "wnn7Ijz0p3_J"}, "locked": null, "vsettings": [{"__ref": "-tb4znOU9PRD"}], "__type": "TplTag"}, "-tb4znOU9PRD": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "B4Tw-jP9genZ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "XUEWYtRMf8bQ"}, "columnsConfig": null, "__type": "VariantSetting"}, "B4Tw-jP9genZ": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "XUEWYtRMf8bQ": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "WRkwPrKJryG6": {"tpl": [{"__ref": "bkzH8P0ki-VK"}], "__type": "VirtualRenderExpr"}, "bkzH8P0ki-VK": {"tag": "div", "name": null, "children": [{"__ref": "IiAqzydZsOg7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dDrOFnDxYuRC", "parent": {"__ref": "mZfVmb-T6LbR"}, "locked": null, "vsettings": [{"__ref": "hXxzpuRuYoBl"}], "__type": "TplTag"}, "IiAqzydZsOg7": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "mK7CqSt2zv62", "parent": {"__ref": "bkzH8P0ki-VK"}, "locked": null, "vsettings": [{"__ref": "CsCm70E0-IN6"}], "__type": "TplTag"}, "hXxzpuRuYoBl": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "iUV5cDtnaI-d"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CsCm70E0-IN6": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "0ERSVAAhv2sR"}, "dataCond": null, "dataRep": null, "text": {"__ref": "rny_A2gBFmC1"}, "columnsConfig": null, "__type": "VariantSetting"}, "iUV5cDtnaI-d": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "0ERSVAAhv2sR": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "rny_A2gBFmC1": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "OrHdsTK0ddZj": {"type": {"__ref": "KZZntV0orPyr"}, "tplSlot": {"__ref": "OCRWGdxAnAdk"}, "variable": {"__ref": "07hJ1gOVOhSo"}, "uuid": "2w9DLe1HlZ4J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "zIUo8gGbjpFS": {"tpl": [{"__ref": "OCRWGdxAnAdk"}], "__type": "RenderExpr"}, "S5LlkePoGHXk": {"param": {"__ref": "OrHdsTK0ddZj"}, "expr": {"__ref": "rzqUyAGn_bKO"}, "__type": "Arg"}, "KZZntV0orPyr": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "07hJ1gOVOhSo": {"name": "content", "uuid": "p-TWXlAPGCyq", "__type": "Var"}, "OCRWGdxAnAdk": {"param": {"__ref": "OrHdsTK0ddZj"}, "defaultContents": [{"__ref": "6jTrDzMOLO7n"}], "uuid": "oc81PUKKZ_OM", "parent": {"__ref": "eLUBu1P7CwY0"}, "locked": null, "vsettings": [{"__ref": "WZIiJvKcckSG"}], "__type": "TplSlot"}, "6jTrDzMOLO7n": {"tag": "div", "name": null, "children": [{"__ref": "XuVgY0b5cHDf"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "app3iK1FqcbW", "parent": {"__ref": "OCRWGdxAnAdk"}, "locked": null, "vsettings": [{"__ref": "HfzplSX3s43l"}], "__type": "TplTag"}, "WZIiJvKcckSG": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "-ma7vk0HDrpg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XuVgY0b5cHDf": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ARuY0QrvaMN3", "parent": {"__ref": "6jTrDzMOLO7n"}, "locked": null, "vsettings": [{"__ref": "O8HnhDOUp_dJ"}], "__type": "TplTag"}, "HfzplSX3s43l": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "udCXAFZX0mEy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-ma7vk0HDrpg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "O8HnhDOUp_dJ": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "YPGZg_dUZWT1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "fKAbrhNm68Ye"}, "columnsConfig": null, "__type": "VariantSetting"}, "udCXAFZX0mEy": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "YPGZg_dUZWT1": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "fKAbrhNm68Ye": {"markers": [], "text": "Content (changed)", "__type": "RawText"}, "rzqUyAGn_bKO": {"tpl": [{"__ref": "ydksv8l1_NBn"}], "__type": "VirtualRenderExpr"}, "ydksv8l1_NBn": {"tag": "div", "name": null, "children": [{"__ref": "Jf4fI1cePR_h"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "uaaDmBFVtHu5", "parent": {"__ref": "mZfVmb-T6LbR"}, "locked": null, "vsettings": [{"__ref": "hssxKJGu43QE"}], "__type": "TplTag"}, "Jf4fI1cePR_h": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "aKNfzvJRZyO3", "parent": {"__ref": "ydksv8l1_NBn"}, "locked": null, "vsettings": [{"__ref": "H5yh9OVbiDhs"}], "__type": "TplTag"}, "hssxKJGu43QE": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "a_cECJt19WO2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "H5yh9OVbiDhs": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "JDFsap81Wua6"}, "dataCond": null, "dataRep": null, "text": {"__ref": "QAykhdJ5_ojK"}, "columnsConfig": null, "__type": "VariantSetting"}, "a_cECJt19WO2": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "JDFsap81Wua6": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "QAykhdJ5_ojK": {"markers": [], "text": "Content (changed)", "__type": "RawText"}}, "deps": [], "version": "251-add-data-tokens"}}, {"branchId": "main", "data": {"root": "YFGCw9A6o6px", "map": {"o_IpZWdrJ6k4": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "v30pZLvaRgcP": {"name": "Default Typography", "rs": {"__ref": "o_IpZWdrJ6k4"}, "preview": null, "uuid": "drIjq5irThZG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "HhGS4RaymTUl": {"values": {}, "mixins": [], "__type": "RuleSet"}, "oXJoYwuxFf21": {"rs": {"__ref": "HhGS4RaymTUl"}, "__type": "ThemeLayoutSettings"}, "Nez8EnmNyKQo": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "cs00yFbtCPu5": {"name": "Default \"h1\"", "rs": {"__ref": "Nez8EnmNyKQo"}, "preview": null, "uuid": "lCbOBeT_cgp7", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "cIDG_55N1Fpw": {"selector": "h1", "style": {"__ref": "cs00yFbtCPu5"}, "__type": "ThemeStyle"}, "cvXzmIa9412y": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "YzvgRe1Y6adD": {"name": "Default \"h2\"", "rs": {"__ref": "cvXzmIa9412y"}, "preview": null, "uuid": "YB1JosPGCrnX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hecebwT2Rzc_": {"selector": "h2", "style": {"__ref": "YzvgRe1Y6adD"}, "__type": "ThemeStyle"}, "bf9s88nNEuFS": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "V1TYUdNAQJJn": {"name": "Default \"h3\"", "rs": {"__ref": "bf9s88nNEuFS"}, "preview": null, "uuid": "t02qtico63Nm", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9CJBkppIqt9P": {"selector": "h3", "style": {"__ref": "V1TYUdNAQJJn"}, "__type": "ThemeStyle"}, "PcIWCzoFL2In": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "fYIeAK6A5Cxw": {"name": "Default \"h4\"", "rs": {"__ref": "PcIWCzoFL2In"}, "preview": null, "uuid": "8QKgjAXfxV2k", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Jk70cI71FsEj": {"selector": "h4", "style": {"__ref": "fYIeAK6A5Cxw"}, "__type": "ThemeStyle"}, "Gkeg-9lMo5KY": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "Tdq8-nchuKDu": {"name": "Default \"h5\"", "rs": {"__ref": "Gkeg-9lMo5KY"}, "preview": null, "uuid": "Of_nPFozBg1M", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "6pWDYtx3quce": {"selector": "h5", "style": {"__ref": "Tdq8-nchuKDu"}, "__type": "ThemeStyle"}, "w-K116V_-af7": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "JfZZZUkVkprh": {"name": "Default \"h6\"", "rs": {"__ref": "w-K116V_-af7"}, "preview": null, "uuid": "jpw28QSTXvnQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "I2es3aTGk8Uh": {"selector": "h6", "style": {"__ref": "JfZZZUkVkprh"}, "__type": "ThemeStyle"}, "HYwCGzuTSsaE": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "NEprx9duIvW2": {"name": "Default \"a\"", "rs": {"__ref": "HYwCGzuTSsaE"}, "preview": null, "uuid": "It975DeOKr34", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "riQIC31_qsaR": {"selector": "a", "style": {"__ref": "NEprx9duIvW2"}, "__type": "ThemeStyle"}, "oJ2ZAC_noA0d": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "IfbAxEw8VR9-": {"name": "Default \"a:hover\"", "rs": {"__ref": "oJ2ZAC_noA0d"}, "preview": null, "uuid": "aiekhxB5k0-X", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BtAYTWU3zTNl": {"selector": "a:hover", "style": {"__ref": "IfbAxEw8VR9-"}, "__type": "ThemeStyle"}, "ep6pRa5lQ1zA": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "_uZezshwWRfd": {"name": "Default \"blockquote\"", "rs": {"__ref": "ep6pRa5lQ1zA"}, "preview": null, "uuid": "gI5cpIvf52Ik", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "3H1f4GtJ0zHM": {"selector": "blockquote", "style": {"__ref": "_uZezshwWRfd"}, "__type": "ThemeStyle"}, "nsjpK_Q6_yb3": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "QQRT3CNracla": {"name": "Default \"code\"", "rs": {"__ref": "nsjpK_Q6_yb3"}, "preview": null, "uuid": "e17ky251554D", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "xcGVbMO6XG-0": {"selector": "code", "style": {"__ref": "QQRT3CNracla"}, "__type": "ThemeStyle"}, "gB9OML3HfCTe": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "Sjy-1UdSCsyR": {"name": "Default \"pre\"", "rs": {"__ref": "gB9OML3HfCTe"}, "preview": null, "uuid": "sWKYKrMTC5er", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Dn27uuD5IXVR": {"selector": "pre", "style": {"__ref": "Sjy-1UdSCsyR"}, "__type": "ThemeStyle"}, "OJQ8F49dq03Y": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "sZUfickrUJSF": {"name": "Default \"ol\"", "rs": {"__ref": "OJQ8F49dq03Y"}, "preview": null, "uuid": "FlFebUX3SzfG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rm4kxThiGTsN": {"selector": "ol", "style": {"__ref": "sZUfickrUJSF"}, "__type": "ThemeStyle"}, "_QWQ9Z9Jimqz": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "NErB2sBVMWIc": {"name": "Default \"ul\"", "rs": {"__ref": "_QWQ9Z9Jimqz"}, "preview": null, "uuid": "lzILSOFEanHd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "9Vbuuq0Z7jUJ": {"selector": "ul", "style": {"__ref": "NErB2sBVMWIc"}, "__type": "ThemeStyle"}, "-kktNl7gIjBb": {"defaultStyle": {"__ref": "v30pZLvaRgcP"}, "styles": [{"__ref": "cIDG_55N1Fpw"}, {"__ref": "hecebwT2Rzc_"}, {"__ref": "9CJBkppIqt9P"}, {"__ref": "Jk70cI71FsEj"}, {"__ref": "6pWDYtx3quce"}, {"__ref": "I2es3aTGk8Uh"}, {"__ref": "riQIC31_qsaR"}, {"__ref": "BtAYTWU3zTNl"}, {"__ref": "3H1f4GtJ0zHM"}, {"__ref": "xcGVbMO6XG-0"}, {"__ref": "Dn27uuD5IXVR"}, {"__ref": "Rm4kxThiGTsN"}, {"__ref": "9Vbuuq0Z7jUJ"}], "layout": {"__ref": "oXJoYwuxFf21"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "YZmfWqoDbiIb": {"name": "text", "__type": "Text"}, "8JVukDb_Vcdm": {"name": "Screen", "uuid": "Z6c_OohR62vI", "__type": "Var"}, "_Zt9NWxM9LvU": {"type": {"__ref": "YZmfWqoDbiIb"}, "variable": {"__ref": "8JVukDb_Vcdm"}, "uuid": "EUoy5WmiFMfO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "4jevmYDqUWr2": {"type": "global-screen", "param": {"__ref": "_Zt9NWxM9LvU"}, "uuid": "-lKhz_5_01KN", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "DgqVJbxT6rzS": {"uuid": "4BAUmxR3ilwJ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "YFGCw9A6o6px": {"components": [{"__ref": "frGaU1n73odM"}, {"__ref": "TO5_tsJ2rxq7"}, {"__ref": "8yK7aI7AMEgx"}, {"__ref": "8YRyvzzFFKxR"}, {"__ref": "GT3W9zV1CrDq"}], "arenas": [], "pageArenas": [{"__ref": "tzzp3JKJ2vki"}], "componentArenas": [{"__ref": "1kRWeS0cq1ZP"}, {"__ref": "tIsSKbgI4WLQ"}], "globalVariantGroups": [{"__ref": "4jevmYDqUWr2"}], "userManagedFonts": [], "globalVariant": {"__ref": "DgqVJbxT6rzS"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "-kktNl7gIjBb"}], "activeTheme": {"__ref": "-kktNl7gIjBb"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "4jevmYDqUWr2"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "frGaU1n73odM": {"uuid": "AmEg4oPHP3ZT", "name": "hostless-plasmic-head", "params": [{"__ref": "IGtKvsQdnWeM"}, {"__ref": "sYMuYU-7rQKC"}, {"__ref": "SGRUBKLX_ybx"}, {"__ref": "Cc8BGduU1OxN"}], "states": [], "tplTree": {"__ref": "c5-pgL0u4NH5"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "9Lb3HyVpGwQL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "2yorzVi9XxpM"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "TO5_tsJ2rxq7": {"uuid": "XNcPoW5tB7Uk", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "nhPtM8j6X84D"}, {"__ref": "WaWADqAe8ETN"}, {"__ref": "R4qX-icILd9s"}, {"__ref": "CXgBdHc5jMFy"}, {"__ref": "FR-nlHcZDRWw"}], "states": [], "tplTree": {"__ref": "Jv1gKCY0MijD"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "2pFTJ6tbNYdN"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "mr4n06O3k33t"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "IGtKvsQdnWeM": {"type": {"__ref": "3oKp5S94WjHQ"}, "variable": {"__ref": "zuftQdXnLzvb"}, "uuid": "vMz1jo0LZEOq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "sYMuYU-7rQKC": {"type": {"__ref": "nWHpU0kAloNH"}, "variable": {"__ref": "LD6NAdRwYl7d"}, "uuid": "G4yCjh5sYi1g", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "SGRUBKLX_ybx": {"type": {"__ref": "SfMdPZRPyqxl"}, "variable": {"__ref": "2US99w6qCjR6"}, "uuid": "uMpqkfEtE8AT", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Cc8BGduU1OxN": {"type": {"__ref": "YsPRhUzHSent"}, "variable": {"__ref": "WTLC5JXXFSCD"}, "uuid": "3elwXnX2alUO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "c5-pgL0u4NH5": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dNfY4GLaQ8Uj", "parent": null, "locked": null, "vsettings": [{"__ref": "d8NSIHe1tyCu"}], "__type": "TplTag"}, "9Lb3HyVpGwQL": {"uuid": "QnDlFrLlsIh2", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "2yorzVi9XxpM": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "nhPtM8j6X84D": {"type": {"__ref": "hx3LJT0zz7Q7"}, "variable": {"__ref": "IdF_AYjxwnJK"}, "uuid": "Hz0rd0UarMqv", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "WaWADqAe8ETN": {"type": {"__ref": "KiHeXEXr5NrW"}, "variable": {"__ref": "oZKex35IYl5C"}, "uuid": "eJP03N9A61ed", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R4qX-icILd9s": {"type": {"__ref": "i0jXPMd40HJm"}, "tplSlot": {"__ref": "Z5i0h8K8YZSL"}, "variable": {"__ref": "SL3aLPXg10oD"}, "uuid": "NFHZNSW835dp", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "CXgBdHc5jMFy": {"type": {"__ref": "OSlps3j6tPxv"}, "variable": {"__ref": "KaksGZTcPKBg"}, "uuid": "41IvtzHFV1j7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "FR-nlHcZDRWw": {"type": {"__ref": "ArN6tRaeloOJ"}, "variable": {"__ref": "ghR1T7-Xp6L2"}, "uuid": "99VxW-dlG3h8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Jv1gKCY0MijD": {"tag": "div", "name": null, "children": [{"__ref": "Z5i0h8K8YZSL"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "F4S63__8qoIn", "parent": null, "locked": null, "vsettings": [{"__ref": "gx2iSSprNbAn"}], "__type": "TplTag"}, "2pFTJ6tbNYdN": {"uuid": "6KPKWPE5uIsl", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "mr4n06O3k33t": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "3oKp5S94WjHQ": {"name": "text", "__type": "Text"}, "zuftQdXnLzvb": {"name": "title", "uuid": "KeNEpGh3o88D", "__type": "Var"}, "nWHpU0kAloNH": {"name": "text", "__type": "Text"}, "LD6NAdRwYl7d": {"name": "description", "uuid": "UjDQg12bgoQg", "__type": "Var"}, "SfMdPZRPyqxl": {"name": "img", "__type": "Img"}, "2US99w6qCjR6": {"name": "image", "uuid": "Ne86h0qmrZhP", "__type": "Var"}, "YsPRhUzHSent": {"name": "text", "__type": "Text"}, "WTLC5JXXFSCD": {"name": "canonical", "uuid": "tzrOgbGPq8Gp", "__type": "Var"}, "d8NSIHe1tyCu": {"variants": [{"__ref": "9Lb3HyVpGwQL"}], "args": [], "attrs": {}, "rs": {"__ref": "LTEvtItK2SqM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "hx3LJT0zz7Q7": {"name": "any", "__type": "AnyType"}, "IdF_AYjxwnJK": {"name": "dataOp", "uuid": "y3apU-90PtWG", "__type": "Var"}, "KiHeXEXr5NrW": {"name": "text", "__type": "Text"}, "oZKex35IYl5C": {"name": "name", "uuid": "4l7Q35kdHF_A", "__type": "Var"}, "i0jXPMd40HJm": {"name": "renderFunc", "params": [{"__ref": "2cL_JOOyK8_n"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "SL3aLPXg10oD": {"name": "children", "uuid": "9e0WuC4vJyQI", "__type": "Var"}, "OSlps3j6tPxv": {"name": "num", "__type": "<PERSON><PERSON>"}, "KaksGZTcPKBg": {"name": "pageSize", "uuid": "UZAGFV1l0Tw1", "__type": "Var"}, "ArN6tRaeloOJ": {"name": "num", "__type": "<PERSON><PERSON>"}, "ghR1T7-Xp6L2": {"name": "pageIndex", "uuid": "OHRjh9OHK-Fg", "__type": "Var"}, "Z5i0h8K8YZSL": {"param": {"__ref": "R4qX-icILd9s"}, "defaultContents": [], "uuid": "E4FCSP32ws-M", "parent": {"__ref": "Jv1gKCY0MijD"}, "locked": null, "vsettings": [{"__ref": "TVytyyaI44Xw"}], "__type": "TplSlot"}, "gx2iSSprNbAn": {"variants": [{"__ref": "2pFTJ6tbNYdN"}], "args": [], "attrs": {}, "rs": {"__ref": "sqFtVNXWloLL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LTEvtItK2SqM": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "2cL_JOOyK8_n": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "e09ZknpvnyZq"}, "__type": "ArgType"}, "TVytyyaI44Xw": {"variants": [{"__ref": "2pFTJ6tbNYdN"}], "args": [], "attrs": {}, "rs": {"__ref": "nGQc0xJsGkxU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "sqFtVNXWloLL": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "e09ZknpvnyZq": {"name": "any", "__type": "AnyType"}, "nGQc0xJsGkxU": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8yK7aI7AMEgx": {"uuid": "Hy3y0l7-rqSZ", "name": "BaseComp", "params": [{"__ref": "XuVVhhjq3TBN"}, {"__ref": "vjN8Z7XD0BX1"}], "states": [], "tplTree": {"__ref": "blnL5929hQms"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "YP8HAIZQKDcr"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "1kRWeS0cq1ZP": {"component": {"__ref": "8yK7aI7AMEgx"}, "matrix": {"__ref": "JsZ06M6jIh1y"}, "customMatrix": {"__ref": "YSNoRP1Ryasr"}, "__type": "ComponentArena"}, "blnL5929hQms": {"tag": "div", "name": null, "children": [{"__ref": "CEk3y6ne2mTI"}, {"__ref": "0llWdoxRfpp7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QUAhaPx6IjNH", "parent": null, "locked": null, "vsettings": [{"__ref": "aM39t13jq1Wk"}], "__type": "TplTag"}, "YP8HAIZQKDcr": {"uuid": "IBn0Uu6RFaZ1", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "JsZ06M6jIh1y": {"rows": [{"__ref": "LvK7pGqBPqya"}], "__type": "ArenaFrameGrid"}, "YSNoRP1Ryasr": {"rows": [{"__ref": "G02xCrdHNT_d"}], "__type": "ArenaFrameGrid"}, "aM39t13jq1Wk": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "fzxv64fjozmT"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "LvK7pGqBPqya": {"cols": [{"__ref": "PwU1IMLK7pkb"}], "rowKey": null, "__type": "ArenaFrameRow"}, "G02xCrdHNT_d": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "fzxv64fjozmT": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "PwU1IMLK7pkb": {"frame": {"__ref": "TIpAIdC-8g1e"}, "cellKey": {"__ref": "YP8HAIZQKDcr"}, "__type": "ArenaFrameCell"}, "TIpAIdC-8g1e": {"uuid": "50GgfZGp261j", "width": 1180, "height": 540, "container": {"__ref": "2UrkhmAvsJgo"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "YP8HAIZQKDcr"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "2UrkhmAvsJgo": {"name": null, "component": {"__ref": "8yK7aI7AMEgx"}, "uuid": "dU-1mzjNBkgi", "parent": null, "locked": null, "vsettings": [{"__ref": "BgR4irmdwAhQ"}], "__type": "TplComponent"}, "BgR4irmdwAhQ": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "lyuEsguC_Jbk"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "lyuEsguC_Jbk": {"values": {}, "mixins": [], "__type": "RuleSet"}, "CEk3y6ne2mTI": {"tag": "div", "name": null, "children": [{"__ref": "99B9-0qJ17Va"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "M2M64D6t_RZk", "parent": {"__ref": "blnL5929hQms"}, "locked": null, "vsettings": [{"__ref": "WDQqdE_-ZlI8"}], "__type": "TplTag"}, "0llWdoxRfpp7": {"tag": "div", "name": null, "children": [{"__ref": "VddedS8IaDDi"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OlDskMw5LLN4", "parent": {"__ref": "blnL5929hQms"}, "locked": null, "vsettings": [{"__ref": "nwHAegIdpHa0"}], "__type": "TplTag"}, "WDQqdE_-ZlI8": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "RPqnFvpCR-xc"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "nwHAegIdpHa0": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "tpc1rVSwRNLL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RPqnFvpCR-xc": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "tpc1rVSwRNLL": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "XuVVhhjq3TBN": {"type": {"__ref": "fGh9c39am4Cx"}, "tplSlot": {"__ref": "99B9-0qJ17Va"}, "variable": {"__ref": "0LtOdqRPFniM"}, "uuid": "zFRLNPsJUax6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "99B9-0qJ17Va": {"param": {"__ref": "XuVVhhjq3TBN"}, "defaultContents": [], "uuid": "v1Fp_PkRgVt9", "parent": {"__ref": "CEk3y6ne2mTI"}, "locked": null, "vsettings": [{"__ref": "rFpEDuEUZagL"}], "__type": "TplSlot"}, "fGh9c39am4Cx": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "0LtOdqRPFniM": {"name": "trigger", "uuid": "K8YYAstJB_jW", "__type": "Var"}, "rFpEDuEUZagL": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "kCViYxxrbJZP"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kCViYxxrbJZP": {"values": {}, "mixins": [], "__type": "RuleSet"}, "vjN8Z7XD0BX1": {"type": {"__ref": "TLxgyqNRzMQX"}, "tplSlot": {"__ref": "VddedS8IaDDi"}, "variable": {"__ref": "W5lu5VaUALYx"}, "uuid": "U4lvukbs3bEa", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "VddedS8IaDDi": {"param": {"__ref": "vjN8Z7XD0BX1"}, "defaultContents": [], "uuid": "FFTgqJRvbMiZ", "parent": {"__ref": "0llWdoxRfpp7"}, "locked": null, "vsettings": [{"__ref": "npJgfTfFOUP2"}], "__type": "TplSlot"}, "TLxgyqNRzMQX": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "W5lu5VaUALYx": {"name": "content", "uuid": "a8C7n9ySps0a", "__type": "Var"}, "npJgfTfFOUP2": {"variants": [{"__ref": "YP8HAIZQKDcr"}], "args": [], "attrs": {}, "rs": {"__ref": "9baYqa8Kv6KL"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "9baYqa8Kv6KL": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8YRyvzzFFKxR": {"uuid": "AJaJei6VwctN", "name": "/", "params": [], "states": [], "tplTree": {"__ref": "uTpcXLHDZJiG"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "nPphB5wxAula"}], "variantGroups": [], "pageMeta": {"__ref": "ca-PyuCl3C2q"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tzzp3JKJ2vki": {"component": {"__ref": "8YRyvzzFFKxR"}, "matrix": {"__ref": "do69jMnRk3Wo"}, "customMatrix": {"__ref": "qFv8jYnutLjk"}, "__type": "PageArena"}, "uTpcXLHDZJiG": {"tag": "div", "name": null, "children": [{"__ref": "mZfVmb-T6LbR"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Ha6ZrWCjlCR1", "parent": null, "locked": null, "vsettings": [{"__ref": "D6yvh_LuSJgU"}], "__type": "TplTag"}, "nPphB5wxAula": {"uuid": "9f6Xy0t4V2iT", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "ca-PyuCl3C2q": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "do69jMnRk3Wo": {"rows": [{"__ref": "VuTaid6GRcWp"}], "__type": "ArenaFrameGrid"}, "qFv8jYnutLjk": {"rows": [{"__ref": "49xbJwXchD8T"}], "__type": "ArenaFrameGrid"}, "D6yvh_LuSJgU": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "3xkB8fPa-fp1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VuTaid6GRcWp": {"cols": [{"__ref": "mAxbWNwazpXs"}, {"__ref": "_XPbpPcpbtf9"}], "rowKey": {"__ref": "nPphB5wxAula"}, "__type": "ArenaFrameRow"}, "49xbJwXchD8T": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "3xkB8fPa-fp1": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "mAxbWNwazpXs": {"frame": {"__ref": "r8QFAD7eH4xW"}, "cellKey": null, "__type": "ArenaFrameCell"}, "_XPbpPcpbtf9": {"frame": {"__ref": "k7FmXyLzbvBf"}, "cellKey": null, "__type": "ArenaFrameCell"}, "r8QFAD7eH4xW": {"uuid": "j1RP_P0cHNhR", "width": 1366, "height": 768, "container": {"__ref": "aX8A4r8LuLFp"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "nPphB5wxAula"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "k7FmXyLzbvBf": {"uuid": "NyMxDrHnvLmV", "width": 414, "height": 736, "container": {"__ref": "ad-uthT6XRBV"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "nPphB5wxAula"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "aX8A4r8LuLFp": {"name": null, "component": {"__ref": "8YRyvzzFFKxR"}, "uuid": "J72L20C8mIsK", "parent": null, "locked": null, "vsettings": [{"__ref": "8stJe9TmtADp"}], "__type": "TplComponent"}, "ad-uthT6XRBV": {"name": null, "component": {"__ref": "8YRyvzzFFKxR"}, "uuid": "TqIDypkUM_7G", "parent": null, "locked": null, "vsettings": [{"__ref": "Azb_sheiFIAJ"}], "__type": "TplComponent"}, "8stJe9TmtADp": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "BkCaQnMxfTk6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Azb_sheiFIAJ": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "i4W3uhQ9LrBM"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "BkCaQnMxfTk6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "i4W3uhQ9LrBM": {"values": {}, "mixins": [], "__type": "RuleSet"}, "mZfVmb-T6LbR": {"name": "wrappedInstance", "component": {"__ref": "GT3W9zV1CrDq"}, "uuid": "PgRH7DBNcYkR", "parent": {"__ref": "uTpcXLHDZJiG"}, "locked": null, "vsettings": [{"__ref": "d6yI_jQueyow"}], "__type": "TplComponent"}, "GT3W9zV1CrDq": {"uuid": "F5kyZccwXBSw", "name": "WrappedComp", "params": [{"__ref": "1_GL9iUL45ox"}, {"__ref": "OrHdsTK0ddZj"}], "states": [], "tplTree": {"__ref": "eLUBu1P7CwY0"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "2-QadRNq1uQu"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tIsSKbgI4WLQ": {"component": {"__ref": "GT3W9zV1CrDq"}, "matrix": {"__ref": "WVQSVUnHnuaB"}, "customMatrix": {"__ref": "o-bOtBSBss5e"}, "__type": "ComponentArena"}, "d6yI_jQueyow": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [{"__ref": "GVCuIvexkyhh"}, {"__ref": "S5LlkePoGHXk"}], "attrs": {}, "rs": {"__ref": "cQCKW-qBpa4J"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eLUBu1P7CwY0": {"name": null, "component": {"__ref": "8yK7aI7AMEgx"}, "uuid": "NvOQZPRXsgzs", "parent": null, "locked": null, "vsettings": [{"__ref": "5XsILk61tB6E"}], "__type": "TplComponent"}, "2-QadRNq1uQu": {"uuid": "-vFe4iqiIkfH", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WVQSVUnHnuaB": {"rows": [{"__ref": "GqB6kVLVSRFr"}], "__type": "ArenaFrameGrid"}, "o-bOtBSBss5e": {"rows": [{"__ref": "zDDkT4rGd_ch"}], "__type": "ArenaFrameGrid"}, "cQCKW-qBpa4J": {"values": {"width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "5XsILk61tB6E": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [{"__ref": "VSnIKomFcj_8"}, {"__ref": "jSFmSa3AeEHa"}], "attrs": {}, "rs": {"__ref": "6F6x--AlaiPX"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GqB6kVLVSRFr": {"cols": [{"__ref": "M8pkUC60aF5E"}], "rowKey": null, "__type": "ArenaFrameRow"}, "zDDkT4rGd_ch": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "VSnIKomFcj_8": {"param": {"__ref": "XuVVhhjq3TBN"}, "expr": {"__ref": "NlWcjqiY2umR"}, "__type": "Arg"}, "jSFmSa3AeEHa": {"param": {"__ref": "vjN8Z7XD0BX1"}, "expr": {"__ref": "zIUo8gGbjpFS"}, "__type": "Arg"}, "6F6x--AlaiPX": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "M8pkUC60aF5E": {"frame": {"__ref": "nZbRoVWCPhfG"}, "cellKey": {"__ref": "2-QadRNq1uQu"}, "__type": "ArenaFrameCell"}, "nZbRoVWCPhfG": {"uuid": "2PQvAVryqcso", "width": 1180, "height": 540, "container": {"__ref": "D3f8pw1ntqYL"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "2-QadRNq1uQu"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "D3f8pw1ntqYL": {"name": null, "component": {"__ref": "GT3W9zV1CrDq"}, "uuid": "ptjD9uS33BoU", "parent": null, "locked": null, "vsettings": [{"__ref": "hP7JsCZmgS1V"}], "__type": "TplComponent"}, "hP7JsCZmgS1V": {"variants": [{"__ref": "DgqVJbxT6rzS"}], "args": [], "attrs": {}, "rs": {"__ref": "I8stP5IDToih"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "I8stP5IDToih": {"values": {}, "mixins": [], "__type": "RuleSet"}, "1_GL9iUL45ox": {"type": {"__ref": "aNDulDYx6n9o"}, "tplSlot": {"__ref": "cCazRAneq9Kf"}, "variable": {"__ref": "42nL_dxVwgHD"}, "uuid": "TK3WIcfB1iKR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "NlWcjqiY2umR": {"tpl": [{"__ref": "cCazRAneq9Kf"}], "__type": "RenderExpr"}, "GVCuIvexkyhh": {"param": {"__ref": "1_GL9iUL45ox"}, "expr": {"__ref": "WRkwPrKJryG6"}, "__type": "Arg"}, "aNDulDYx6n9o": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "42nL_dxVwgHD": {"name": "target", "uuid": "iy5PEJl-waVp", "__type": "Var"}, "cCazRAneq9Kf": {"param": {"__ref": "1_GL9iUL45ox"}, "defaultContents": [{"__ref": "wnn7Ijz0p3_J"}], "uuid": "9mpeeur7vurA", "parent": {"__ref": "eLUBu1P7CwY0"}, "locked": null, "vsettings": [{"__ref": "Q9msYmCR7_vv"}], "__type": "TplSlot"}, "wnn7Ijz0p3_J": {"tag": "div", "name": null, "children": [{"__ref": "s2u83pPJGhae"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "Su2f1Kq616JP", "parent": {"__ref": "cCazRAneq9Kf"}, "locked": null, "vsettings": [{"__ref": "Wi9MEuxng0Bo"}], "__type": "TplTag"}, "Q9msYmCR7_vv": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "F_97zqkLbz7c"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Wi9MEuxng0Bo": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "jFYmkUNtGpuw"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "F_97zqkLbz7c": {"values": {}, "mixins": [], "__type": "RuleSet"}, "jFYmkUNtGpuw": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "s2u83pPJGhae": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "kvnuVCx1x7zz", "parent": {"__ref": "wnn7Ijz0p3_J"}, "locked": null, "vsettings": [{"__ref": "-tb4znOU9PRD"}], "__type": "TplTag"}, "-tb4znOU9PRD": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "B4Tw-jP9genZ"}, "dataCond": null, "dataRep": null, "text": {"__ref": "XUEWYtRMf8bQ"}, "columnsConfig": null, "__type": "VariantSetting"}, "B4Tw-jP9genZ": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "XUEWYtRMf8bQ": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "WRkwPrKJryG6": {"tpl": [{"__ref": "bkzH8P0ki-VK"}], "__type": "VirtualRenderExpr"}, "bkzH8P0ki-VK": {"tag": "div", "name": null, "children": [{"__ref": "IiAqzydZsOg7"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dDrOFnDxYuRC", "parent": {"__ref": "mZfVmb-T6LbR"}, "locked": null, "vsettings": [{"__ref": "hXxzpuRuYoBl"}], "__type": "TplTag"}, "IiAqzydZsOg7": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "mK7CqSt2zv62", "parent": {"__ref": "bkzH8P0ki-VK"}, "locked": null, "vsettings": [{"__ref": "CsCm70E0-IN6"}], "__type": "TplTag"}, "hXxzpuRuYoBl": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "iUV5cDtnaI-d"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CsCm70E0-IN6": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "0ERSVAAhv2sR"}, "dataCond": null, "dataRep": null, "text": {"__ref": "rny_A2gBFmC1"}, "columnsConfig": null, "__type": "VariantSetting"}, "iUV5cDtnaI-d": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "0ERSVAAhv2sR": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "rny_A2gBFmC1": {"markers": [], "text": "<PERSON><PERSON>", "__type": "RawText"}, "OrHdsTK0ddZj": {"type": {"__ref": "KZZntV0orPyr"}, "tplSlot": {"__ref": "OCRWGdxAnAdk"}, "variable": {"__ref": "07hJ1gOVOhSo"}, "uuid": "2w9DLe1HlZ4J", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "zIUo8gGbjpFS": {"tpl": [{"__ref": "OCRWGdxAnAdk"}], "__type": "RenderExpr"}, "S5LlkePoGHXk": {"param": {"__ref": "OrHdsTK0ddZj"}, "expr": {"__ref": "rfpCkhQTqDCT"}, "__type": "Arg"}, "KZZntV0orPyr": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "07hJ1gOVOhSo": {"name": "content", "uuid": "p-TWXlAPGCyq", "__type": "Var"}, "OCRWGdxAnAdk": {"param": {"__ref": "OrHdsTK0ddZj"}, "defaultContents": [{"__ref": "6jTrDzMOLO7n"}], "uuid": "oc81PUKKZ_OM", "parent": {"__ref": "eLUBu1P7CwY0"}, "locked": null, "vsettings": [{"__ref": "WZIiJvKcckSG"}], "__type": "TplSlot"}, "6jTrDzMOLO7n": {"tag": "div", "name": null, "children": [{"__ref": "XuVgY0b5cHDf"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "app3iK1FqcbW", "parent": {"__ref": "OCRWGdxAnAdk"}, "locked": null, "vsettings": [{"__ref": "HfzplSX3s43l"}], "__type": "TplTag"}, "WZIiJvKcckSG": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "-ma7vk0HDrpg"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XuVgY0b5cHDf": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ARuY0QrvaMN3", "parent": {"__ref": "6jTrDzMOLO7n"}, "locked": null, "vsettings": [{"__ref": "O8HnhDOUp_dJ"}], "__type": "TplTag"}, "HfzplSX3s43l": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "udCXAFZX0mEy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "-ma7vk0HDrpg": {"values": {}, "mixins": [], "__type": "RuleSet"}, "O8HnhDOUp_dJ": {"variants": [{"__ref": "2-QadRNq1uQu"}], "args": [], "attrs": {}, "rs": {"__ref": "YPGZg_dUZWT1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "PTWWKhPcVTC0"}, "columnsConfig": null, "__type": "VariantSetting"}, "udCXAFZX0mEy": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "YPGZg_dUZWT1": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "PTWWKhPcVTC0": {"markers": [], "text": "Content", "__type": "RawText"}, "rfpCkhQTqDCT": {"tpl": [{"__ref": "kFLBh_E_dinN"}], "__type": "VirtualRenderExpr"}, "kFLBh_E_dinN": {"tag": "div", "name": null, "children": [{"__ref": "JPUNN3B8piqz"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "B0OwaJFKUAgY", "parent": {"__ref": "mZfVmb-T6LbR"}, "locked": null, "vsettings": [{"__ref": "T-5FOaqaom5n"}], "__type": "TplTag"}, "JPUNN3B8piqz": {"tag": "h1", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "846bne2YM3ho", "parent": {"__ref": "kFLBh_E_dinN"}, "locked": null, "vsettings": [{"__ref": "x1jakPooGbl2"}], "__type": "TplTag"}, "T-5FOaqaom5n": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "EbzY6OEZQyO4"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "x1jakPooGbl2": {"variants": [{"__ref": "nPphB5wxAula"}], "args": [], "attrs": {}, "rs": {"__ref": "7629xlXPqF7i"}, "dataCond": null, "dataRep": null, "text": {"__ref": "jTItfzYxC2mj"}, "columnsConfig": null, "__type": "VariantSetting"}, "EbzY6OEZQyO4": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "7629xlXPqF7i": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "jTItfzYxC2mj": {"markers": [], "text": "Content", "__type": "RawText"}}, "deps": [], "version": "251-add-data-tokens"}}]}