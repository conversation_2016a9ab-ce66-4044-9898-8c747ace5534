{"branches": [{"id": "ooJVxuGbGb2UTDwLB54CHA", "name": "test"}], "pkgVersions": [{"id": "c1b7d6be-ba8a-4290-bb6f-580db736bfb2", "data": {"root": "55993001", "map": {"8615001": {"components": [{"__ref": "52044001"}, {"__ref": "52044002"}, {"__ref": "20279001"}, {"__ref": "20279051"}, {"__ref": "20279156"}, {"__ref": "20279387"}, {"__ref": "20279601"}, {"__ref": "20279694"}, {"__ref": "20279863"}, {"__ref": "20280177"}, {"__ref": "20280271"}], "arenas": [{"__ref": "8615002"}], "pageArenas": [], "componentArenas": [{"__ref": "20279052"}, {"__ref": "20279157"}, {"__ref": "20279388"}, {"__ref": "20279602"}, {"__ref": "20279695"}, {"__ref": "20279864"}, {"__ref": "20280178"}], "globalVariantGroups": [{"__ref": "8615003"}], "userManagedFonts": [], "globalVariant": {"__ref": "8615007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "8615008"}], "activeTheme": {"__ref": "8615008"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "8615003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "8615002": {"name": "Custom arena 1", "children": [{"__ref": "20279002"}, {"__ref": "20280272"}], "__type": "Arena"}, "8615003": {"type": "global-screen", "param": {"__ref": "8615004"}, "uuid": "ECHG62LxIap", "variants": [{"__ref": "20280233"}], "multi": true, "__type": "GlobalVariantGroup"}, "8615004": {"type": {"__ref": "8615006"}, "variable": {"__ref": "8615005"}, "uuid": "dWUX2PHHThx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "8615005": {"name": "Screen", "uuid": "oW8p5ZQllQ", "__type": "Var"}, "8615006": {"name": "text", "__type": "Text"}, "8615007": {"uuid": "CiJdOYqwUYG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8615008": {"defaultStyle": {"__ref": "8615009"}, "styles": [{"__ref": "8615024"}, {"__ref": "8615033"}, {"__ref": "8615042"}, {"__ref": "8615051"}, {"__ref": "8615060"}, {"__ref": "8615069"}, {"__ref": "8615077"}, {"__ref": "8615081"}, {"__ref": "8615085"}, {"__ref": "8615093"}, {"__ref": "8615118"}, {"__ref": "8615143"}, {"__ref": "8615154"}], "layout": {"__ref": "8615165"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8615009": {"name": "Default Typography", "rs": {"__ref": "8615010"}, "preview": null, "uuid": "lRD3svOrvl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "8615024": {"selector": "h1", "style": {"__ref": "8615025"}, "__type": "ThemeStyle"}, "8615025": {"name": "Default \"h1\"", "rs": {"__ref": "8615026"}, "preview": null, "uuid": "8wtJRE1kj0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "8615033": {"selector": "h2", "style": {"__ref": "8615034"}, "__type": "ThemeStyle"}, "8615034": {"name": "Default \"h2\"", "rs": {"__ref": "8615035"}, "preview": null, "uuid": "PLBNpIosis", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "8615042": {"selector": "h3", "style": {"__ref": "8615043"}, "__type": "ThemeStyle"}, "8615043": {"name": "Default \"h3\"", "rs": {"__ref": "8615044"}, "preview": null, "uuid": "1ti1p6M7tt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "8615051": {"selector": "h4", "style": {"__ref": "8615052"}, "__type": "ThemeStyle"}, "8615052": {"name": "Default \"h4\"", "rs": {"__ref": "8615053"}, "preview": null, "uuid": "shd9hbKB6g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "8615060": {"selector": "h5", "style": {"__ref": "8615061"}, "__type": "ThemeStyle"}, "8615061": {"name": "Default \"h5\"", "rs": {"__ref": "8615062"}, "preview": null, "uuid": "9gubx8aDyX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8615069": {"selector": "h6", "style": {"__ref": "8615070"}, "__type": "ThemeStyle"}, "8615070": {"name": "Default \"h6\"", "rs": {"__ref": "8615071"}, "preview": null, "uuid": "zYyvcLNLdY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8615077": {"selector": "a", "style": {"__ref": "8615078"}, "__type": "ThemeStyle"}, "8615078": {"name": "Default \"a\"", "rs": {"__ref": "8615079"}, "preview": null, "uuid": "ekX-ag13Ze", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "8615081": {"selector": "a:hover", "style": {"__ref": "8615082"}, "__type": "ThemeStyle"}, "8615082": {"name": "Default \"a:hover\"", "rs": {"__ref": "8615083"}, "preview": null, "uuid": "K9WtVdND3A", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "8615085": {"selector": "blockquote", "style": {"__ref": "8615086"}, "__type": "ThemeStyle"}, "8615086": {"name": "Default \"blockquote\"", "rs": {"__ref": "8615087"}, "preview": null, "uuid": "rnJtOH0-Sx", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "8615093": {"selector": "code", "style": {"__ref": "8615094"}, "__type": "ThemeStyle"}, "8615094": {"name": "Default \"code\"", "rs": {"__ref": "8615095"}, "preview": null, "uuid": "lPJab7QWsT", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "8615118": {"selector": "pre", "style": {"__ref": "8615119"}, "__type": "ThemeStyle"}, "8615119": {"name": "Default \"pre\"", "rs": {"__ref": "8615120"}, "preview": null, "uuid": "EEamYjn6m0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "8615143": {"selector": "ol", "style": {"__ref": "8615144"}, "__type": "ThemeStyle"}, "8615144": {"name": "Default \"ol\"", "rs": {"__ref": "8615145"}, "preview": null, "uuid": "v-GhaIYQVR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "8615154": {"selector": "ul", "style": {"__ref": "8615155"}, "__type": "ThemeStyle"}, "8615155": {"name": "Default \"ul\"", "rs": {"__ref": "8615156"}, "preview": null, "uuid": "B5mrhhC0jn", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "8615165": {"rs": {"__ref": "8615166"}, "__type": "ThemeLayoutSettings"}, "8615166": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279001": {"uuid": "jvObxJk-ch", "name": "", "params": [], "states": [], "tplTree": {"__ref": "20279003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279004"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279002": {"uuid": "zocqdP_S5b", "width": 757, "height": 2517, "container": {"__ref": "20279005"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "mainArtboard", "top": 77, "left": 506, "__type": "ArenaFrame"}, "20279003": {"tag": "div", "name": null, "children": [{"__ref": "20279155"}, {"__ref": "20279313"}, {"__ref": "20279447"}, {"__ref": "20279552"}, {"__ref": "20279661"}, {"__ref": "20279807"}, {"__ref": "20279831"}, {"__ref": "20280142"}, {"__ref": "20280212"}, {"__ref": "20280239"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "MvT3KhzVr", "parent": null, "locked": null, "vsettings": [{"__ref": "20279006"}], "__type": "TplTag"}, "20279004": {"uuid": "FhvkK6xECC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279005": {"name": null, "component": {"__ref": "20279001"}, "uuid": "20-2SDUfRJ", "parent": null, "locked": null, "vsettings": [{"__ref": "20279007"}], "__type": "TplComponent"}, "20279006": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279008"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279007": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279009"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279008": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "row-gap": "18px"}, "mixins": [], "__type": "RuleSet"}, "20279009": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279051": {"uuid": "45FQ_SMj-t", "name": "Section1Comp", "params": [{"__ref": "20279099"}], "states": [], "tplTree": {"__ref": "20279054"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279055"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279052": {"component": {"__ref": "20279051"}, "matrix": {"__ref": "20279056"}, "customMatrix": {"__ref": "20279057"}, "__type": "ComponentArena"}, "20279054": {"tag": "div", "name": null, "children": [{"__ref": "20279080"}, {"__ref": "20279100"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "reED4a-W1", "parent": null, "locked": null, "vsettings": [{"__ref": "20279059"}], "__type": "TplTag"}, "20279055": {"uuid": "o3lC58Jf4A", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279056": {"rows": [{"__ref": "20279060"}], "__type": "ArenaFrameGrid"}, "20279057": {"rows": [{"__ref": "20279061"}], "__type": "ArenaFrameGrid"}, "20279059": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279065"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279060": {"cols": [{"__ref": "20279066"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279061": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279065": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279066": {"frame": {"__ref": "20279072"}, "cellKey": {"__ref": "20279055"}, "__type": "ArenaFrameCell"}, "20279072": {"uuid": "415HmIBU1R", "width": 340, "height": 340, "container": {"__ref": "20279073"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279055"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279073": {"name": null, "component": {"__ref": "20279051"}, "uuid": "kaiFxmXH98", "parent": null, "locked": null, "vsettings": [{"__ref": "20279074"}], "__type": "TplComponent"}, "20279074": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279075"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279075": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279080": {"tag": "div", "name": "tplTag", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OHNdhS6tJ", "parent": {"__ref": "20279054"}, "locked": null, "vsettings": [{"__ref": "20279081"}], "__type": "TplTag"}, "20279081": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279082"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279082": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279099": {"type": {"__ref": "20279103"}, "tplSlot": {"__ref": "20279100"}, "variable": {"__ref": "20279102"}, "uuid": "DZvLD_xOph", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279100": {"param": {"__ref": "20279099"}, "defaultContents": [{"__ref": "20279104"}, {"__ref": "20279115"}], "uuid": "5j_HSOW0iJ", "parent": {"__ref": "20279054"}, "locked": null, "vsettings": [{"__ref": "20279105"}], "__type": "TplSlot"}, "20279102": {"name": "children", "uuid": "hvUvnbv_T", "__type": "Var"}, "20279103": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279104": {"tag": "div", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2ASZgfZBr8", "parent": {"__ref": "20279100"}, "locked": null, "vsettings": [{"__ref": "20279107"}], "__type": "TplTag"}, "20279105": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279108"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279107": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279110"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279111"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279108": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279110": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279111": {"markers": [], "text": "slot contents", "__type": "RawText"}, "20279115": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xeZyq2UEZ", "parent": {"__ref": "20279100"}, "locked": null, "vsettings": [{"__ref": "20279116"}], "__type": "TplTag"}, "20279116": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279120"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279133"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279120": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279133": {"markers": [], "text": "more contents", "__type": "RawText"}, "20279155": {"name": "section1", "component": {"__ref": "20279156"}, "uuid": "9i5aDR6ffM", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279158"}], "__type": "TplComponent"}, "20279156": {"uuid": "QQCZUQ2awS", "name": "TestSection", "params": [{"__ref": "20279237"}, {"__ref": "20279279"}, {"__ref": "20279298"}], "states": [], "tplTree": {"__ref": "20279159"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279160"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279157": {"component": {"__ref": "20279156"}, "matrix": {"__ref": "20279161"}, "customMatrix": {"__ref": "20279162"}, "__type": "ComponentArena"}, "20279158": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279239"}, {"__ref": "20279281"}, {"__ref": "20279299"}], "attrs": {}, "rs": {"__ref": "20279163"}, "dataCond": {"__ref": "20279164"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279159": {"tag": "div", "name": null, "children": [{"__ref": "20279165"}, {"__ref": "20279280"}, {"__ref": "20279238"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KOJVzRh6yc", "parent": null, "locked": null, "vsettings": [{"__ref": "20279168"}], "__type": "TplTag"}, "20279160": {"uuid": "-6Em6VHq4y", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279161": {"rows": [{"__ref": "20279169"}], "__type": "ArenaFrameGrid"}, "20279162": {"rows": [{"__ref": "20279170"}], "__type": "ArenaFrameGrid"}, "20279163": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279164": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279165": {"tag": "h4", "name": null, "children": [{"__ref": "20279295"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "S9ucL-nzE", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279174"}], "__type": "TplTag"}, "20279168": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279177"}, "dataCond": {"__ref": "20279178"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279169": {"cols": [{"__ref": "20279179"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279170": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279174": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279180"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279177": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "position": "relative", "row-gap": "10px"}, "mixins": [], "__type": "RuleSet"}, "20279178": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279179": {"frame": {"__ref": "20279193"}, "cellKey": {"__ref": "20279160"}, "__type": "ArenaFrameCell"}, "20279180": {"values": {"padding-top": "0px", "display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "20279193": {"uuid": "MKRoOY66Bq", "width": 340, "height": 340, "container": {"__ref": "20279200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279160"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279200": {"name": null, "component": {"__ref": "20279156"}, "uuid": "o3TwFlNKyM", "parent": null, "locked": null, "vsettings": [{"__ref": "20279203"}], "__type": "TplComponent"}, "20279203": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279206"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279206": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279237": {"type": {"__ref": "20279241"}, "tplSlot": {"__ref": "20279238"}, "variable": {"__ref": "20279240"}, "uuid": "l5VcK-bWz9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279238": {"param": {"__ref": "20279237"}, "defaultContents": [], "uuid": "nedHRtVvxY", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279243"}], "__type": "TplSlot"}, "20279239": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279375"}, "__type": "Arg"}, "20279240": {"name": "children", "uuid": "qC3M5lsu3", "__type": "Var"}, "20279241": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279243": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279246"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279246": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279247": {"name": null, "component": {"__ref": "20279051"}, "uuid": "7-oLaszagZ", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279250"}], "__type": "TplComponent"}, "20279250": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279255"}], "attrs": {}, "rs": {"__ref": "20279256"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279255": {"param": {"__ref": "20279099"}, "expr": {"__ref": "20279259"}, "__type": "Arg"}, "20279256": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279259": {"tpl": [{"__ref": "20279265"}, {"__ref": "20279266"}], "__type": "VirtualRenderExpr"}, "20279265": {"tag": "div", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jV-6NFthfZ", "parent": {"__ref": "20279247"}, "locked": null, "vsettings": [{"__ref": "20279271"}], "__type": "TplTag"}, "20279266": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cr3dBnS55Y", "parent": {"__ref": "20279247"}, "locked": null, "vsettings": [{"__ref": "20279272"}], "__type": "TplTag"}, "20279271": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279274"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279275"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279272": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279276"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279277"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279274": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279275": {"markers": [], "text": "slot contents", "__type": "RawText"}, "20279276": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279277": {"markers": [], "text": "more contents", "__type": "RawText"}, "20279279": {"type": {"__ref": "20279283"}, "tplSlot": {"__ref": "20279280"}, "variable": {"__ref": "20279282"}, "uuid": "LFQWtR8Jy0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279280": {"param": {"__ref": "20279279"}, "defaultContents": [{"__ref": "20279284"}], "uuid": "55mnzn8_UF", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279285"}], "__type": "TplSlot"}, "20279281": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279286"}, "__type": "Arg"}, "20279282": {"name": "description", "uuid": "2asjOM6OR", "__type": "Var"}, "20279283": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279284": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "W0PvB37urM", "parent": {"__ref": "20279280"}, "locked": null, "vsettings": [{"__ref": "20279287"}], "__type": "TplTag"}, "20279285": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279288"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279286": {"tpl": [{"__ref": "20279289"}], "__type": "VirtualRenderExpr"}, "20279287": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279290"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279291"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279288": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279289": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "irGF1kbQoo", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279292"}], "__type": "TplTag"}, "20279290": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279291": {"markers": [], "text": "Update \"Section1Comp\", move \"oldDefaultContent\" out of \"children\" slot default content's (move it to \"tplTag\").", "__type": "RawText"}, "20279292": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279293"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279294"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279293": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279294": {"markers": [], "text": "Update \"Section1Comp\", move \"oldDefaultContent\" out of \"children\" slot default content's (move it to \"tplTag\").", "__type": "RawText"}, "20279295": {"param": {"__ref": "20279298"}, "defaultContents": [{"__ref": "20279300"}], "uuid": "x2uA2obsAf", "parent": {"__ref": "20279165"}, "locked": null, "vsettings": [{"__ref": "20279301"}], "__type": "TplSlot"}, "20279298": {"type": {"__ref": "20279303"}, "tplSlot": {"__ref": "20279295"}, "variable": {"__ref": "20279302"}, "uuid": "_Iv9B526Fq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279299": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279304"}, "__type": "Arg"}, "20279300": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "GZFjtY5Ish", "parent": {"__ref": "20279295"}, "locked": null, "vsettings": [{"__ref": "20279305"}], "__type": "TplTag"}, "20279301": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279302": {"name": "title", "uuid": "Zptf8vNrR7", "__type": "Var"}, "20279303": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279304": {"tpl": [{"__ref": "20279307"}], "__type": "VirtualRenderExpr"}, "20279305": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279308"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279309"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279306": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279307": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "HmFW_fkaW-", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279310"}], "__type": "TplTag"}, "20279308": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279309": {"markers": [], "text": "1 - Test update parent of TplSlot default contents", "__type": "RawText"}, "20279310": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279311"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279312"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279311": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279312": {"markers": [], "text": "1 - Test update parent of TplSlot default contents", "__type": "RawText"}, "20279313": {"name": "section2", "component": {"__ref": "20279156"}, "uuid": "OJz2ETlm3Y", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279314"}], "__type": "TplComponent"}, "20279314": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279316"}, {"__ref": "20279317"}, {"__ref": "20279376"}], "attrs": {}, "rs": {"__ref": "20279318"}, "dataCond": {"__ref": "20279319"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279316": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279355"}, "__type": "Arg"}, "20279317": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279353"}, "__type": "Arg"}, "20279318": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279319": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279327": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "4BLGLlLMj7", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279330"}], "__type": "TplTag"}, "20279328": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8XWi5MJV-i", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279331"}], "__type": "TplTag"}, "20279330": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279334"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279354"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279331": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279336"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279352"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279334": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279336": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279352": {"markers": [], "text": "2 - Test update parent of slot arg", "__type": "RawText"}, "20279353": {"tpl": [{"__ref": "20279328"}], "__type": "RenderExpr"}, "20279354": {"markers": [], "text": "Update the instance of \"Section2Comp\", move \"oldArg\" out of \"children\" slot (move it to \"section2TplTag\").", "__type": "RawText"}, "20279355": {"tpl": [{"__ref": "20279327"}], "__type": "RenderExpr"}, "20279375": {"tpl": [{"__ref": "20279247"}], "__type": "RenderExpr"}, "20279376": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279438"}, "__type": "Arg"}, "20279387": {"uuid": "vctvB2ZxYd", "name": "Section2Comp", "params": [{"__ref": "20279421"}], "states": [], "tplTree": {"__ref": "20279390"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279391"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279388": {"component": {"__ref": "20279387"}, "matrix": {"__ref": "20279392"}, "customMatrix": {"__ref": "20279393"}, "__type": "ComponentArena"}, "20279389": {"name": null, "component": {"__ref": "20279387"}, "uuid": "GF9CproY5n", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279394"}], "__type": "TplComponent"}, "20279390": {"tag": "div", "name": null, "children": [{"__ref": "20279413"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "noYEyl2aF", "parent": null, "locked": null, "vsettings": [{"__ref": "20279395"}], "__type": "TplTag"}, "20279391": {"uuid": "PjKIzFH40K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279392": {"rows": [{"__ref": "20279396"}], "__type": "ArenaFrameGrid"}, "20279393": {"rows": [{"__ref": "20279397"}], "__type": "ArenaFrameGrid"}, "20279394": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279423"}], "attrs": {}, "rs": {"__ref": "20279398"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279395": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279399"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279396": {"cols": [{"__ref": "20279400"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279397": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279398": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279399": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279400": {"frame": {"__ref": "20279409"}, "cellKey": {"__ref": "20279391"}, "__type": "ArenaFrameCell"}, "20279409": {"uuid": "gBh_vb8GMk", "width": 340, "height": 340, "container": {"__ref": "20279410"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279391"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279410": {"name": null, "component": {"__ref": "20279387"}, "uuid": "QlxSo83WWO", "parent": null, "locked": null, "vsettings": [{"__ref": "20279411"}], "__type": "TplComponent"}, "20279411": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279412"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279412": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279413": {"tag": "div", "name": null, "children": [{"__ref": "20279422"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8gD6q20o_", "parent": {"__ref": "20279390"}, "locked": null, "vsettings": [{"__ref": "20279414"}], "__type": "TplTag"}, "20279414": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279415"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279415": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279421": {"type": {"__ref": "20279425"}, "tplSlot": {"__ref": "20279422"}, "variable": {"__ref": "20279424"}, "uuid": "jUOLPt-fbn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279422": {"param": {"__ref": "20279421"}, "defaultContents": [], "uuid": "VzuZMoNgWc", "parent": {"__ref": "20279413"}, "locked": null, "vsettings": [{"__ref": "20279426"}], "__type": "TplSlot"}, "20279423": {"param": {"__ref": "20279421"}, "expr": {"__ref": "20279429"}, "__type": "Arg"}, "20279424": {"name": "children", "uuid": "pfLzAhssr", "__type": "Var"}, "20279425": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279426": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279428"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279428": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279429": {"tpl": [{"__ref": "20279430"}], "__type": "RenderExpr"}, "20279430": {"tag": "div", "name": "oldArg", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "aVrXAjwxV", "parent": {"__ref": "20279389"}, "locked": null, "vsettings": [{"__ref": "20279431"}], "__type": "TplTag"}, "20279431": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279432"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279432": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279438": {"tpl": [{"__ref": "20279389"}, {"__ref": "20279439"}], "__type": "RenderExpr"}, "20279439": {"tag": "div", "name": "section2TplTag", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lCJJw<PERSON>ey", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279440"}], "__type": "TplTag"}, "20279440": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279441"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279441": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279447": {"name": "section3", "component": {"__ref": "20279156"}, "uuid": "JAz04ZAPH", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279448"}], "__type": "TplComponent"}, "20279448": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279449"}, {"__ref": "20279450"}, {"__ref": "20279451"}], "attrs": {}, "rs": {"__ref": "20279452"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279449": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279467"}, "__type": "Arg"}, "20279450": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279469"}, "__type": "Arg"}, "20279451": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279543"}, "__type": "Arg"}, "20279452": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279458": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "v9MvSb8FMm", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279460"}], "__type": "TplTag"}, "20279459": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "sAzRkLiitY", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279461"}], "__type": "TplTag"}, "20279460": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279462"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279466"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279461": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279464"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279531"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279462": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279464": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279466": {"markers": [], "text": "3 - Fix disconnected cycle", "__type": "RawText"}, "20279467": {"tpl": [{"__ref": "20279458"}], "__type": "RenderExpr"}, "20279469": {"tpl": [{"__ref": "20279459"}], "__type": "RenderExpr"}, "20279531": {"markers": [], "text": "One branch updates \"cycle1\" to be child of \"cycle2\" and the other does the opposite, creating a cycle", "__type": "RawText"}, "20279535": {"tag": "div", "name": "cycle1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SX9c-Wqmu", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279536"}], "__type": "TplTag"}, "20279536": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279537"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279537": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279543": {"tpl": [{"__ref": "20279535"}, {"__ref": "20279544"}], "__type": "RenderExpr"}, "20279544": {"tag": "div", "name": "cycle2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3Sh_2y898", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279545"}], "__type": "TplTag"}, "20279545": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279546"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279546": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279552": {"name": "section4", "component": {"__ref": "20279156"}, "uuid": "Y14v9-2ptT", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279553"}], "__type": "TplComponent"}, "20279553": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279554"}, {"__ref": "20279555"}, {"__ref": "20279556"}], "attrs": {}, "rs": {"__ref": "20279557"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279554": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279558"}, "__type": "Arg"}, "20279555": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279559"}, "__type": "Arg"}, "20279556": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279600"}, "__type": "Arg"}, "20279557": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279558": {"tpl": [{"__ref": "20279563"}], "__type": "RenderExpr"}, "20279559": {"tpl": [{"__ref": "20279564"}], "__type": "RenderExpr"}, "20279563": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "mBO1xyL8de", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279567"}], "__type": "TplTag"}, "20279564": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ESjcl_T8pZ", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279568"}], "__type": "TplTag"}, "20279567": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279571"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279587"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279568": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279573"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279588"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279571": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279573": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279587": {"markers": [], "text": "4 - Delete Tpl Comp children", "__type": "RawText"}, "20279588": {"markers": [], "text": "Each branch will delete one of \"Section4Comp\"s children", "__type": "RawText"}, "20279600": {"tpl": [{"__ref": "20279603"}], "__type": "RenderExpr"}, "20279601": {"uuid": "CLeJRsBU3N", "name": "Section4Comp", "params": [{"__ref": "20279635"}], "states": [], "tplTree": {"__ref": "20279604"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279605"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279602": {"component": {"__ref": "20279601"}, "matrix": {"__ref": "20279606"}, "customMatrix": {"__ref": "20279607"}, "__type": "ComponentArena"}, "20279603": {"name": "section4comp", "component": {"__ref": "20279601"}, "uuid": "ZIJ_mTuI9_", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279608"}], "__type": "TplComponent"}, "20279604": {"tag": "div", "name": null, "children": [{"__ref": "20279627"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dse_ERTNk", "parent": null, "locked": null, "vsettings": [{"__ref": "20279609"}], "__type": "TplTag"}, "20279605": {"uuid": "CgFimYi1YG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279606": {"rows": [{"__ref": "20279610"}], "__type": "ArenaFrameGrid"}, "20279607": {"rows": [{"__ref": "20279611"}], "__type": "ArenaFrameGrid"}, "20279608": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279637"}], "attrs": {}, "rs": {"__ref": "20279612"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279609": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279613"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279610": {"cols": [{"__ref": "20279614"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279611": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279612": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279613": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279614": {"frame": {"__ref": "20279623"}, "cellKey": {"__ref": "20279605"}, "__type": "ArenaFrameCell"}, "20279623": {"uuid": "YbtOvxrn6h", "width": 340, "height": 340, "container": {"__ref": "20279624"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279605"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279624": {"name": null, "component": {"__ref": "20279601"}, "uuid": "_GAwC4zh2d", "parent": null, "locked": null, "vsettings": [{"__ref": "20279625"}], "__type": "TplComponent"}, "20279625": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279626"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279626": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279627": {"tag": "div", "name": null, "children": [{"__ref": "20279636"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QtFE9Oia_", "parent": {"__ref": "20279604"}, "locked": null, "vsettings": [{"__ref": "20279628"}], "__type": "TplTag"}, "20279628": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279629"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279629": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279635": {"type": {"__ref": "20279639"}, "tplSlot": {"__ref": "20279636"}, "variable": {"__ref": "20279638"}, "uuid": "SgQJP-ggIk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279636": {"param": {"__ref": "20279635"}, "defaultContents": [], "uuid": "wrNPOOE9Da", "parent": {"__ref": "20279627"}, "locked": null, "vsettings": [{"__ref": "20279640"}], "__type": "TplSlot"}, "20279637": {"param": {"__ref": "20279635"}, "expr": {"__ref": "20279652"}, "__type": "Arg"}, "20279638": {"name": "children", "uuid": "jZ-7fRk34", "__type": "Var"}, "20279639": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279640": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279642": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279644": {"tag": "div", "name": "section4child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "yMWQ5-04V", "parent": {"__ref": "20279603"}, "locked": null, "vsettings": [{"__ref": "20279645"}], "__type": "TplTag"}, "20279645": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279646"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279646": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279652": {"tpl": [{"__ref": "20279644"}, {"__ref": "20279653"}], "__type": "RenderExpr"}, "20279653": {"tag": "div", "name": "section4child2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "TF5jD1rQ0", "parent": {"__ref": "20279603"}, "locked": null, "vsettings": [{"__ref": "20279654"}], "__type": "TplTag"}, "20279654": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279655"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279655": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279661": {"name": "section5", "component": {"__ref": "20279156"}, "uuid": "OT7SKY0oy", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279662"}], "__type": "TplComponent"}, "20279662": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279663"}, {"__ref": "20279664"}, {"__ref": "20279665"}], "attrs": {}, "rs": {"__ref": "20279666"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279663": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279681"}, "__type": "Arg"}, "20279664": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279683"}, "__type": "Arg"}, "20279665": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279693"}, "__type": "Arg"}, "20279666": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279672": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "IQ4ovZjf6K", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279674"}], "__type": "TplTag"}, "20279673": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jw_jxoqivi", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279675"}], "__type": "TplTag"}, "20279674": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279676"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279680"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279675": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279678"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279682"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279676": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279678": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279680": {"markers": [], "text": "5 - Merge different values for VirtualRenderExpr", "__type": "RawText"}, "20279681": {"tpl": [{"__ref": "20279672"}], "__type": "RenderExpr"}, "20279682": {"markers": [], "text": "Each branch adds a new children to Section5Comp children default contents", "__type": "RawText"}, "20279683": {"tpl": [{"__ref": "20279673"}], "__type": "RenderExpr"}, "20279693": {"tpl": [{"__ref": "20279696"}], "__type": "RenderExpr"}, "20279694": {"uuid": "On5brgxpcp", "name": "Section5Comp", "params": [{"__ref": "20279728"}], "states": [], "tplTree": {"__ref": "20279697"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279698"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279695": {"component": {"__ref": "20279694"}, "matrix": {"__ref": "20279699"}, "customMatrix": {"__ref": "20279700"}, "__type": "ComponentArena"}, "20279696": {"name": "section5comp", "component": {"__ref": "20279694"}, "uuid": "SeaYUt22g4", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279701"}], "__type": "TplComponent"}, "20279697": {"tag": "div", "name": null, "children": [{"__ref": "20279720"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ra-PT3p7e", "parent": null, "locked": null, "vsettings": [{"__ref": "20279702"}], "__type": "TplTag"}, "20279698": {"uuid": "Ni4FKAdilw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279699": {"rows": [{"__ref": "20279703"}], "__type": "ArenaFrameGrid"}, "20279700": {"rows": [{"__ref": "20279704"}], "__type": "ArenaFrameGrid"}, "20279701": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279730"}], "attrs": {}, "rs": {"__ref": "20279705"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279702": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279706"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279703": {"cols": [{"__ref": "20279707"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279704": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279705": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279706": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279707": {"frame": {"__ref": "20279716"}, "cellKey": {"__ref": "20279698"}, "__type": "ArenaFrameCell"}, "20279716": {"uuid": "nkDDRvkObD", "width": 340, "height": 340, "container": {"__ref": "20279717"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279698"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279717": {"name": null, "component": {"__ref": "20279694"}, "uuid": "ZZU5CxNdKp", "parent": null, "locked": null, "vsettings": [{"__ref": "20279718"}], "__type": "TplComponent"}, "20279718": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279719"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279719": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279720": {"tag": "div", "name": null, "children": [{"__ref": "20279729"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "HglvzGs4y", "parent": {"__ref": "20279697"}, "locked": null, "vsettings": [{"__ref": "20279721"}], "__type": "TplTag"}, "20279721": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279722"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279722": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279728": {"type": {"__ref": "20279732"}, "tplSlot": {"__ref": "20279729"}, "variable": {"__ref": "20279731"}, "uuid": "Bb3Sv2gvMS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279729": {"param": {"__ref": "20279728"}, "defaultContents": [{"__ref": "20279736"}], "uuid": "3WYhqR7P4y", "parent": {"__ref": "20279720"}, "locked": null, "vsettings": [{"__ref": "20279733"}], "__type": "TplSlot"}, "20279730": {"param": {"__ref": "20279728"}, "expr": {"__ref": "20279753"}, "__type": "Arg"}, "20279731": {"name": "children", "uuid": "Wx7LFdB3f", "__type": "Var"}, "20279732": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279733": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279735"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279735": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279736": {"tag": "div", "name": "child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9ts1muJNE", "parent": {"__ref": "20279729"}, "locked": null, "vsettings": [{"__ref": "20279738"}], "__type": "TplTag"}, "20279738": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279740"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279740": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279753": {"tpl": [{"__ref": "20279754"}], "__type": "VirtualRenderExpr"}, "20279754": {"tag": "div", "name": "child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "f2qkXOG04", "parent": {"__ref": "20279696"}, "locked": null, "vsettings": [{"__ref": "20279755"}], "__type": "TplTag"}, "20279755": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279756"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279756": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279807": {"name": "section6", "component": {"__ref": "20279156"}, "uuid": "SXCx0spoO", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279808"}], "__type": "TplComponent"}, "20279808": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279809"}, {"__ref": "20279810"}, {"__ref": "20279811"}], "attrs": {}, "rs": {"__ref": "20279812"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279809": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279827"}, "__type": "Arg"}, "20279810": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279829"}, "__type": "Arg"}, "20279811": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279862"}, "__type": "Arg"}, "20279812": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279818": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "LpcacNPwvP", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279820"}], "__type": "TplTag"}, "20279819": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "vnNWRzw33Y", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279821"}], "__type": "TplTag"}, "20279820": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279822"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279830"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279821": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279824"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279828"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279822": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279824": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279827": {"tpl": [{"__ref": "20279818"}], "__type": "RenderExpr"}, "20279828": {"markers": [], "text": "One branch re-orders the children arg of Section6Comp, while the other just adds a new child", "__type": "RawText"}, "20279829": {"tpl": [{"__ref": "20279819"}], "__type": "RenderExpr"}, "20279830": {"markers": [], "text": "6 - Change relative order of tpl component children", "__type": "RawText"}, "20279831": {"name": "section7", "component": {"__ref": "20279156"}, "uuid": "95aRkyH0xh", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279832"}], "__type": "TplComponent"}, "20279832": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279833"}, {"__ref": "20279834"}, {"__ref": "20279835"}], "attrs": {}, "rs": {"__ref": "20279836"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279833": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279837"}, "__type": "Arg"}, "20279834": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279838"}, "__type": "Arg"}, "20279835": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280109"}, "__type": "Arg"}, "20279836": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279837": {"tpl": [{"__ref": "20279842"}], "__type": "RenderExpr"}, "20279838": {"tpl": [{"__ref": "20279843"}], "__type": "RenderExpr"}, "20279842": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cksT3m7sPC", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20279844"}], "__type": "TplTag"}, "20279843": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "npe0TarOGa", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20279845"}], "__type": "TplTag"}, "20279844": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279846"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279852"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279845": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279848"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279851"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279846": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279848": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279851": {"markers": [], "text": "Same as section 6, but with a tpl tag", "__type": "RawText"}, "20279852": {"markers": [], "text": "7 - Change relative order of tpl tag children", "__type": "RawText"}, "20279862": {"tpl": [{"__ref": "20279865"}], "__type": "RenderExpr"}, "20279863": {"uuid": "5lBYahYiCA", "name": "Section6Comp", "params": [{"__ref": "20279897"}], "states": [], "tplTree": {"__ref": "20279866"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279867"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279864": {"component": {"__ref": "20279863"}, "matrix": {"__ref": "20279868"}, "customMatrix": {"__ref": "20279869"}, "__type": "ComponentArena"}, "20279865": {"name": "section6comp", "component": {"__ref": "20279863"}, "uuid": "ihIFR8Yu_3", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279870"}], "__type": "TplComponent"}, "20279866": {"tag": "div", "name": null, "children": [{"__ref": "20279889"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "j5W5Xk2nU", "parent": null, "locked": null, "vsettings": [{"__ref": "20279871"}], "__type": "TplTag"}, "20279867": {"uuid": "fmBidXiX9V", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279868": {"rows": [{"__ref": "20279872"}], "__type": "ArenaFrameGrid"}, "20279869": {"rows": [{"__ref": "20279873"}], "__type": "ArenaFrameGrid"}, "20279870": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279899"}], "attrs": {}, "rs": {"__ref": "20279874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279871": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279875"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279872": {"cols": [{"__ref": "20279876"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279873": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279874": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279875": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279876": {"frame": {"__ref": "20279885"}, "cellKey": {"__ref": "20279867"}, "__type": "ArenaFrameCell"}, "20279885": {"uuid": "Emp_sqrl7I", "width": 340, "height": 340, "container": {"__ref": "20279886"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279867"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279886": {"name": null, "component": {"__ref": "20279863"}, "uuid": "Db1yZOq4NI", "parent": null, "locked": null, "vsettings": [{"__ref": "20279887"}], "__type": "TplComponent"}, "20279887": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279889": {"tag": "div", "name": null, "children": [{"__ref": "20279898"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9IUApZjYj", "parent": {"__ref": "20279866"}, "locked": null, "vsettings": [{"__ref": "20279890"}], "__type": "TplTag"}, "20279890": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279891"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279891": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279897": {"type": {"__ref": "20279901"}, "tplSlot": {"__ref": "20279898"}, "variable": {"__ref": "20279900"}, "uuid": "2LNEj30Vqn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279898": {"param": {"__ref": "20279897"}, "defaultContents": [], "uuid": "cQ4QCSTH4f", "parent": {"__ref": "20279889"}, "locked": null, "vsettings": [{"__ref": "20279902"}], "__type": "TplSlot"}, "20279899": {"param": {"__ref": "20279897"}, "expr": {"__ref": "20280100"}, "__type": "Arg"}, "20279900": {"name": "children", "uuid": "-idiEVMtj", "__type": "Var"}, "20279901": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279902": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279904"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279904": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280083": {"tag": "div", "name": "section6order1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LApPOCWv_", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280084"}], "__type": "TplTag"}, "20280084": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280085"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280085": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280092": {"tag": "div", "name": "section6order2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2CdFvhm1b", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280093"}], "__type": "TplTag"}, "20280093": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280094"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280094": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280100": {"tpl": [{"__ref": "20280083"}, {"__ref": "20280092"}, {"__ref": "20280101"}], "__type": "RenderExpr"}, "20280101": {"tag": "div", "name": "section6order3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E93CgQSaT", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280102"}], "__type": "TplTag"}, "20280102": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280103"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280103": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280109": {"tpl": [{"__ref": "20280110"}], "__type": "RenderExpr"}, "20280110": {"tag": "div", "name": null, "children": [{"__ref": "20280111"}, {"__ref": "20280126"}, {"__ref": "20280134"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "s5eOUbZrw", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20280112"}], "__type": "TplTag"}, "20280111": {"tag": "div", "name": "section7order1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "tF9GWxoSZ", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280113"}], "__type": "TplTag"}, "20280112": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280114"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280113": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280115"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280114": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280115": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280126": {"tag": "div", "name": "section7order2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lOEVzQ0M0", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280127"}], "__type": "TplTag"}, "20280127": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280128"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280128": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280134": {"tag": "div", "name": "section7order3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fJPVRdmRw", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280135"}], "__type": "TplTag"}, "20280135": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280136"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280136": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280142": {"name": "section8", "component": {"__ref": "20279156"}, "uuid": "CC2hvYL4D", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280143"}], "__type": "TplComponent"}, "20280143": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280144"}, {"__ref": "20280145"}, {"__ref": "20280146"}], "attrs": {}, "rs": {"__ref": "20280147"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280144": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280164"}, "__type": "Arg"}, "20280145": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280166"}, "__type": "Arg"}, "20280146": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280203"}, "__type": "Arg"}, "20280147": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280153": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KlO8gi1xbd", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280155"}], "__type": "TplTag"}, "20280154": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "UX7aYQg6Qm", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280156"}], "__type": "TplTag"}, "20280155": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280157"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280163"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280156": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280159"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280165"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280157": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280159": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280163": {"markers": [], "text": "8 - Add new slot in a branch and move nodes into it", "__type": "RawText"}, "20280164": {"tpl": [{"__ref": "20280153"}], "__type": "RenderExpr"}, "20280165": {"markers": [], "text": "A branch will create a new slot for Section8Comp, and move \"section8tpl\" into it, as well as a new tpl node", "__type": "RawText"}, "20280166": {"tpl": [{"__ref": "20280154"}], "__type": "RenderExpr"}, "20280177": {"uuid": "GK_jsBv920", "name": "Section8Comp", "params": [], "states": [], "tplTree": {"__ref": "20280180"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20280181"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20280178": {"component": {"__ref": "20280177"}, "matrix": {"__ref": "20280182"}, "customMatrix": {"__ref": "20280183"}, "__type": "ComponentArena"}, "20280179": {"name": "section8comp", "component": {"__ref": "20280177"}, "uuid": "vY7MIDRlPp", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280184"}], "__type": "TplComponent"}, "20280180": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E_04LajOm", "parent": null, "locked": null, "vsettings": [{"__ref": "20280185"}], "__type": "TplTag"}, "20280181": {"uuid": "Lbxui6QAQ6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280182": {"rows": [{"__ref": "20280186"}], "__type": "ArenaFrameGrid"}, "20280183": {"rows": [{"__ref": "20280187"}], "__type": "ArenaFrameGrid"}, "20280184": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280188"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280185": {"variants": [{"__ref": "20280181"}], "args": [], "attrs": {}, "rs": {"__ref": "20280189"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280186": {"cols": [{"__ref": "20280190"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20280187": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20280188": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20280189": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280190": {"frame": {"__ref": "20280199"}, "cellKey": {"__ref": "20280181"}, "__type": "ArenaFrameCell"}, "20280199": {"uuid": "1oXYv4UjyF", "width": 340, "height": 340, "container": {"__ref": "20280200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20280181"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20280200": {"name": null, "component": {"__ref": "20280177"}, "uuid": "oOMnkoqDS3", "parent": null, "locked": null, "vsettings": [{"__ref": "20280201"}], "__type": "TplComponent"}, "20280201": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20280202"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280202": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280203": {"tpl": [{"__ref": "20280179"}, {"__ref": "20280204"}], "__type": "RenderExpr"}, "20280204": {"tag": "div", "name": "section8tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SAk8QVjj2", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280205"}], "__type": "TplTag"}, "20280205": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280206"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280206": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280212": {"name": "section9", "component": {"__ref": "20279156"}, "uuid": "7fJ1CO8DC", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280213"}], "__type": "TplComponent"}, "20280213": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280214"}, {"__ref": "20280215"}, {"__ref": "20280216"}], "attrs": {}, "rs": {"__ref": "20280217"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280214": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280232"}, "__type": "Arg"}, "20280215": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280235"}, "__type": "Arg"}, "20280216": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280220"}, "__type": "Arg"}, "20280217": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280220": {"tpl": [], "__type": "VirtualRenderExpr"}, "20280223": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1eaGu3uj6o", "parent": {"__ref": "20280212"}, "locked": null, "vsettings": [{"__ref": "20280225"}], "__type": "TplTag"}, "20280224": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "M_WJwTURop", "parent": {"__ref": "20280212"}, "locked": null, "vsettings": [{"__ref": "20280226"}], "__type": "TplTag"}, "20280225": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280227"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280237"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280226": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280229"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280238"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280227": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280229": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280232": {"tpl": [{"__ref": "20280223"}], "__type": "RenderExpr"}, "20280233": {"uuid": "hbljPOe7V", "name": "Mobile", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "8615003"}, "mediaQuery": "(max-width:640px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280235": {"tpl": [{"__ref": "20280224"}], "__type": "RenderExpr"}, "20280237": {"markers": [], "text": "9 - Add new artboard, target global variant", "__type": "RawText"}, "20280238": {"markers": [], "text": "A branch will add a new artboard, and target the mobile screen variant", "__type": "RawText"}, "20280239": {"name": "section10", "component": {"__ref": "20279156"}, "uuid": "NSy4qU0sLE", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280240"}], "__type": "TplComponent"}, "20280240": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280241"}, {"__ref": "20280242"}, {"__ref": "20280243"}], "attrs": {}, "rs": {"__ref": "20280244"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280241": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280245"}, "__type": "Arg"}, "20280242": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280246"}, "__type": "Arg"}, "20280243": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280247"}, "__type": "Arg"}, "20280244": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280245": {"tpl": [{"__ref": "20280250"}], "__type": "RenderExpr"}, "20280246": {"tpl": [{"__ref": "20280251"}], "__type": "RenderExpr"}, "20280247": {"tpl": [], "__type": "VirtualRenderExpr"}, "20280250": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8EHJopGzPv", "parent": {"__ref": "20280239"}, "locked": null, "vsettings": [{"__ref": "20280252"}], "__type": "TplTag"}, "20280251": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "dTGs-bqYNY", "parent": {"__ref": "20280239"}, "locked": null, "vsettings": [{"__ref": "20280253"}], "__type": "TplTag"}, "20280252": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280254"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280288"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280253": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280256"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280286"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280254": {"values": {"padding-bottom": "0px", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "20280256": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280271": {"uuid": "WYVm22mkEy", "name": "Unnamed Component", "params": [], "states": [], "tplTree": {"__ref": "20280273"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "20280274"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20280272": {"uuid": "E78HznyzT4", "width": 757, "height": 1103, "container": {"__ref": "20280275"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20280274"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "section10arena", "top": 1474, "left": 1443, "__type": "ArenaFrame"}, "20280273": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "k90NmA5b1", "parent": null, "locked": null, "vsettings": [{"__ref": "20280276"}], "__type": "TplTag"}, "20280274": {"uuid": "Rd-n5lbGmC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280275": {"name": null, "component": {"__ref": "20280271"}, "uuid": "RJFPghfetr", "parent": null, "locked": null, "vsettings": [{"__ref": "20280277"}], "__type": "TplComponent"}, "20280276": {"variants": [{"__ref": "20280274"}], "args": [], "attrs": {}, "rs": {"__ref": "20280278"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280277": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20280279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280278": {"values": {"display": "block", "position": "relative", "width": "stretch", "height": "stretch"}, "mixins": [], "__type": "RuleSet"}, "20280279": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280286": {"markers": [], "text": "Each branch will change the name of \"section10arena\" to a different name, creating a harmless conflict", "__type": "RawText"}, "20280288": {"markers": [], "text": "10 - Test harmless conflict with artboard name", "__type": "RawText"}, "52044001": {"uuid": "tf2H95Pq3t", "name": "hostless-plasmic-head", "params": [{"__ref": "52044003"}, {"__ref": "52044004"}, {"__ref": "52044005"}, {"__ref": "52044006"}], "states": [], "tplTree": {"__ref": "52044007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "52044008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "52044009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "52044002": {"uuid": "wpYNJO6ypD", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "52044010"}, {"__ref": "52044011"}, {"__ref": "52044012"}, {"__ref": "52044013"}, {"__ref": "52044014"}], "states": [], "tplTree": {"__ref": "52044015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "52044016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "52044017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "52044003": {"type": {"__ref": "52044019"}, "variable": {"__ref": "52044018"}, "uuid": "upZwDAbwTl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044004": {"type": {"__ref": "52044021"}, "variable": {"__ref": "52044020"}, "uuid": "Wsr53Wu-4U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044005": {"type": {"__ref": "52044023"}, "variable": {"__ref": "52044022"}, "uuid": "dvvYk2dynQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044006": {"type": {"__ref": "52044025"}, "variable": {"__ref": "52044024"}, "uuid": "jgV0pNAwBM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2w3YqO9hPV", "parent": null, "locked": null, "vsettings": [{"__ref": "52044026"}], "__type": "TplTag"}, "52044008": {"uuid": "S1XfBSeiw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52044009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "52044010": {"type": {"__ref": "52044028"}, "variable": {"__ref": "52044027"}, "uuid": "0ACCnHBko6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044011": {"type": {"__ref": "52044030"}, "variable": {"__ref": "52044029"}, "uuid": "r7piC3Sx88u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044012": {"type": {"__ref": "52044032"}, "tplSlot": {"__ref": "52044037"}, "variable": {"__ref": "52044031"}, "uuid": "ktFptf_LgCR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "52044013": {"type": {"__ref": "52044034"}, "variable": {"__ref": "52044033"}, "uuid": "aha-3GvZdpO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044014": {"type": {"__ref": "52044036"}, "variable": {"__ref": "52044035"}, "uuid": "cJOCxKYET_B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044015": {"tag": "div", "name": null, "children": [{"__ref": "52044037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "YNljyGUCP3", "parent": null, "locked": null, "vsettings": [{"__ref": "52044038"}], "__type": "TplTag"}, "52044016": {"uuid": "LZpX1editO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52044017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "52044018": {"name": "title", "uuid": "ICA7AVWoL2", "__type": "Var"}, "52044019": {"name": "text", "__type": "Text"}, "52044020": {"name": "description", "uuid": "mz-SGD2V8z", "__type": "Var"}, "52044021": {"name": "text", "__type": "Text"}, "52044022": {"name": "image", "uuid": "SxzeekIBHW", "__type": "Var"}, "52044023": {"name": "img", "__type": "Img"}, "52044024": {"name": "canonical", "uuid": "MfPnMlxIrC", "__type": "Var"}, "52044025": {"name": "text", "__type": "Text"}, "52044026": {"variants": [{"__ref": "52044008"}], "args": [], "attrs": {}, "rs": {"__ref": "52044039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044027": {"name": "dataOp", "uuid": "MuUEM9uPCP", "__type": "Var"}, "52044028": {"name": "any", "__type": "AnyType"}, "52044029": {"name": "name", "uuid": "0lxyDqm2Sz6", "__type": "Var"}, "52044030": {"name": "text", "__type": "Text"}, "52044031": {"name": "children", "uuid": "aCWSIt95_Gm", "__type": "Var"}, "52044032": {"name": "renderFunc", "params": [{"__ref": "52044040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "52044033": {"name": "pageSize", "uuid": "caL98c6qwUc", "__type": "Var"}, "52044034": {"name": "num", "__type": "<PERSON><PERSON>"}, "52044035": {"name": "pageIndex", "uuid": "cC5YpxckKP-", "__type": "Var"}, "52044036": {"name": "num", "__type": "<PERSON><PERSON>"}, "52044037": {"param": {"__ref": "52044012"}, "defaultContents": [], "uuid": "nmWF57VYyTU", "parent": {"__ref": "52044015"}, "locked": null, "vsettings": [{"__ref": "52044041"}], "__type": "TplSlot"}, "52044038": {"variants": [{"__ref": "52044016"}], "args": [], "attrs": {}, "rs": {"__ref": "52044042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "52044040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "52044045"}, "__type": "ArgType"}, "52044041": {"variants": [{"__ref": "52044016"}], "args": [], "attrs": {}, "rs": {"__ref": "52044046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "52044045": {"name": "any", "__type": "AnyType"}, "52044046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "55993001": {"uuid": "J2Z7d5Blmq", "pkgId": "4e7d328e-a1aa-4f7b-9cf6-59579148d159", "projectId": "dNoM99U17XFRGCXZ5SUbc1", "version": "0.0.1", "name": "test_tpl_merge", "site": {"__ref": "8615001"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}, "projectId": "dNoM99U17XFRGCXZ5SUbc1", "version": "0.0.1", "branchId": "main"}], "project": {"id": "dNoM99U17XFRGCXZ5SUbc1", "name": "test_tpl_merge", "commitGraph": {"parents": {"c1b7d6be-ba8a-4290-bb6f-580db736bfb2": []}, "branches": {"main": "c1b7d6be-ba8a-4290-bb6f-580db736bfb2", "ooJVxuGbGb2UTDwLB54CHA": "c1b7d6be-ba8a-4290-bb6f-580db736bfb2"}}}, "revisions": [{"branchId": "ooJVxuGbGb2UTDwLB54CHA", "data": {"root": "8615001", "map": {"8615001": {"components": [{"__ref": "52044001"}, {"__ref": "52044002"}, {"__ref": "20279001"}, {"__ref": "20279051"}, {"__ref": "20279156"}, {"__ref": "20279387"}, {"__ref": "20279601"}, {"__ref": "20279694"}, {"__ref": "20279863"}, {"__ref": "20280177"}, {"__ref": "20280271"}, {"__ref": "65055080"}], "arenas": [{"__ref": "8615002"}], "pageArenas": [], "componentArenas": [{"__ref": "20279052"}, {"__ref": "20279157"}, {"__ref": "20279388"}, {"__ref": "20279602"}, {"__ref": "20279695"}, {"__ref": "20279864"}, {"__ref": "20280178"}], "globalVariantGroups": [{"__ref": "8615003"}], "userManagedFonts": [], "globalVariant": {"__ref": "8615007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "8615008"}], "activeTheme": {"__ref": "8615008"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "8615003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "8615002": {"name": "Custom arena 1", "children": [{"__ref": "20279002"}, {"__ref": "20280272"}, {"__ref": "65055081"}], "__type": "Arena"}, "8615003": {"type": "global-screen", "param": {"__ref": "8615004"}, "uuid": "ECHG62LxIap", "variants": [{"__ref": "20280233"}], "multi": true, "__type": "GlobalVariantGroup"}, "8615004": {"type": {"__ref": "8615006"}, "variable": {"__ref": "8615005"}, "uuid": "dWUX2PHHThx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "8615005": {"name": "Screen", "uuid": "oW8p5ZQllQ", "__type": "Var"}, "8615006": {"name": "text", "__type": "Text"}, "8615007": {"uuid": "CiJdOYqwUYG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8615008": {"defaultStyle": {"__ref": "8615009"}, "styles": [{"__ref": "8615024"}, {"__ref": "8615033"}, {"__ref": "8615042"}, {"__ref": "8615051"}, {"__ref": "8615060"}, {"__ref": "8615069"}, {"__ref": "8615077"}, {"__ref": "8615081"}, {"__ref": "8615085"}, {"__ref": "8615093"}, {"__ref": "8615118"}, {"__ref": "8615143"}, {"__ref": "8615154"}], "layout": {"__ref": "8615165"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8615009": {"name": "Default Typography", "rs": {"__ref": "8615010"}, "preview": null, "uuid": "lRD3svOrvl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "8615024": {"selector": "h1", "style": {"__ref": "8615025"}, "__type": "ThemeStyle"}, "8615025": {"name": "Default \"h1\"", "rs": {"__ref": "8615026"}, "preview": null, "uuid": "8wtJRE1kj0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "8615033": {"selector": "h2", "style": {"__ref": "8615034"}, "__type": "ThemeStyle"}, "8615034": {"name": "Default \"h2\"", "rs": {"__ref": "8615035"}, "preview": null, "uuid": "PLBNpIosis", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "8615042": {"selector": "h3", "style": {"__ref": "8615043"}, "__type": "ThemeStyle"}, "8615043": {"name": "Default \"h3\"", "rs": {"__ref": "8615044"}, "preview": null, "uuid": "1ti1p6M7tt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "8615051": {"selector": "h4", "style": {"__ref": "8615052"}, "__type": "ThemeStyle"}, "8615052": {"name": "Default \"h4\"", "rs": {"__ref": "8615053"}, "preview": null, "uuid": "shd9hbKB6g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "8615060": {"selector": "h5", "style": {"__ref": "8615061"}, "__type": "ThemeStyle"}, "8615061": {"name": "Default \"h5\"", "rs": {"__ref": "8615062"}, "preview": null, "uuid": "9gubx8aDyX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8615069": {"selector": "h6", "style": {"__ref": "8615070"}, "__type": "ThemeStyle"}, "8615070": {"name": "Default \"h6\"", "rs": {"__ref": "8615071"}, "preview": null, "uuid": "zYyvcLNLdY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8615077": {"selector": "a", "style": {"__ref": "8615078"}, "__type": "ThemeStyle"}, "8615078": {"name": "Default \"a\"", "rs": {"__ref": "8615079"}, "preview": null, "uuid": "ekX-ag13Ze", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "8615081": {"selector": "a:hover", "style": {"__ref": "8615082"}, "__type": "ThemeStyle"}, "8615082": {"name": "Default \"a:hover\"", "rs": {"__ref": "8615083"}, "preview": null, "uuid": "K9WtVdND3A", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "8615085": {"selector": "blockquote", "style": {"__ref": "8615086"}, "__type": "ThemeStyle"}, "8615086": {"name": "Default \"blockquote\"", "rs": {"__ref": "8615087"}, "preview": null, "uuid": "rnJtOH0-Sx", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "8615093": {"selector": "code", "style": {"__ref": "8615094"}, "__type": "ThemeStyle"}, "8615094": {"name": "Default \"code\"", "rs": {"__ref": "8615095"}, "preview": null, "uuid": "lPJab7QWsT", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "8615118": {"selector": "pre", "style": {"__ref": "8615119"}, "__type": "ThemeStyle"}, "8615119": {"name": "Default \"pre\"", "rs": {"__ref": "8615120"}, "preview": null, "uuid": "EEamYjn6m0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "8615143": {"selector": "ol", "style": {"__ref": "8615144"}, "__type": "ThemeStyle"}, "8615144": {"name": "Default \"ol\"", "rs": {"__ref": "8615145"}, "preview": null, "uuid": "v-GhaIYQVR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "8615154": {"selector": "ul", "style": {"__ref": "8615155"}, "__type": "ThemeStyle"}, "8615155": {"name": "Default \"ul\"", "rs": {"__ref": "8615156"}, "preview": null, "uuid": "B5mrhhC0jn", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "8615165": {"rs": {"__ref": "8615166"}, "__type": "ThemeLayoutSettings"}, "8615166": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279001": {"uuid": "jvObxJk-ch", "name": "", "params": [], "states": [], "tplTree": {"__ref": "20279003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279004"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279002": {"uuid": "zocqdP_S5b", "width": 757, "height": 2517, "container": {"__ref": "20279005"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "mainArtboard", "top": 77, "left": 506, "__type": "ArenaFrame"}, "20279003": {"tag": "div", "name": null, "children": [{"__ref": "20279155"}, {"__ref": "20279313"}, {"__ref": "20279447"}, {"__ref": "20279552"}, {"__ref": "20279661"}, {"__ref": "20279807"}, {"__ref": "20279831"}, {"__ref": "20280142"}, {"__ref": "20280212"}, {"__ref": "20280239"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "MvT3KhzVr", "parent": null, "locked": null, "vsettings": [{"__ref": "20279006"}], "__type": "TplTag"}, "20279004": {"uuid": "FhvkK6xECC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279005": {"name": null, "component": {"__ref": "20279001"}, "uuid": "20-2SDUfRJ", "parent": null, "locked": null, "vsettings": [{"__ref": "20279007"}], "__type": "TplComponent"}, "20279006": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279008"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279007": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279009"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279008": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "row-gap": "18px"}, "mixins": [], "__type": "RuleSet"}, "20279009": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279051": {"uuid": "45FQ_SMj-t", "name": "Section1Comp", "params": [{"__ref": "20279099"}], "states": [], "tplTree": {"__ref": "20279054"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279055"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279052": {"component": {"__ref": "20279051"}, "matrix": {"__ref": "20279056"}, "customMatrix": {"__ref": "20279057"}, "__type": "ComponentArena"}, "20279054": {"tag": "div", "name": null, "children": [{"__ref": "20279080"}, {"__ref": "20279100"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "reED4a-W1", "parent": null, "locked": null, "vsettings": [{"__ref": "20279059"}], "__type": "TplTag"}, "20279055": {"uuid": "o3lC58Jf4A", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279056": {"rows": [{"__ref": "20279060"}], "__type": "ArenaFrameGrid"}, "20279057": {"rows": [{"__ref": "20279061"}], "__type": "ArenaFrameGrid"}, "20279059": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279065"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279060": {"cols": [{"__ref": "20279066"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279061": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279065": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279066": {"frame": {"__ref": "20279072"}, "cellKey": {"__ref": "20279055"}, "__type": "ArenaFrameCell"}, "20279072": {"uuid": "415HmIBU1R", "width": 340, "height": 340, "container": {"__ref": "20279073"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279055"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279073": {"name": null, "component": {"__ref": "20279051"}, "uuid": "kaiFxmXH98", "parent": null, "locked": null, "vsettings": [{"__ref": "20279074"}], "__type": "TplComponent"}, "20279074": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279075"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279075": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279080": {"tag": "div", "name": "tplTag", "children": [{"__ref": "20279104"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OHNdhS6tJ", "parent": {"__ref": "20279054"}, "locked": null, "vsettings": [{"__ref": "20279081"}], "__type": "TplTag"}, "20279081": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279082"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279082": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279099": {"type": {"__ref": "20279103"}, "tplSlot": {"__ref": "20279100"}, "variable": {"__ref": "20279102"}, "uuid": "DZvLD_xOph", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279100": {"param": {"__ref": "20279099"}, "defaultContents": [{"__ref": "20279115"}], "uuid": "5j_HSOW0iJ", "parent": {"__ref": "20279054"}, "locked": null, "vsettings": [{"__ref": "20279105"}], "__type": "TplSlot"}, "20279102": {"name": "children", "uuid": "hvUvnbv_T", "__type": "Var"}, "20279103": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279104": {"tag": "div", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2ASZgfZBr8", "parent": {"__ref": "20279080"}, "locked": null, "vsettings": [{"__ref": "20279107"}], "__type": "TplTag"}, "20279105": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279108"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279107": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279110"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279111"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279108": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279110": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279111": {"markers": [], "text": "slot contents", "__type": "RawText"}, "20279115": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xeZyq2UEZ", "parent": {"__ref": "20279100"}, "locked": null, "vsettings": [{"__ref": "20279116"}], "__type": "TplTag"}, "20279116": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279120"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279133"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279120": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279133": {"markers": [], "text": "more contents", "__type": "RawText"}, "20279155": {"name": "section1", "component": {"__ref": "20279156"}, "uuid": "9i5aDR6ffM", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279158"}], "__type": "TplComponent"}, "20279156": {"uuid": "QQCZUQ2awS", "name": "TestSection", "params": [{"__ref": "20279237"}, {"__ref": "20279279"}, {"__ref": "20279298"}], "states": [], "tplTree": {"__ref": "20279159"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279160"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279157": {"component": {"__ref": "20279156"}, "matrix": {"__ref": "20279161"}, "customMatrix": {"__ref": "20279162"}, "__type": "ComponentArena"}, "20279158": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279239"}, {"__ref": "20279281"}, {"__ref": "20279299"}], "attrs": {}, "rs": {"__ref": "20279163"}, "dataCond": {"__ref": "20279164"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279159": {"tag": "div", "name": null, "children": [{"__ref": "20279165"}, {"__ref": "20279280"}, {"__ref": "20279238"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KOJVzRh6yc", "parent": null, "locked": null, "vsettings": [{"__ref": "20279168"}], "__type": "TplTag"}, "20279160": {"uuid": "-6Em6VHq4y", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279161": {"rows": [{"__ref": "20279169"}], "__type": "ArenaFrameGrid"}, "20279162": {"rows": [{"__ref": "20279170"}], "__type": "ArenaFrameGrid"}, "20279163": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279164": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279165": {"tag": "h4", "name": null, "children": [{"__ref": "20279295"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "S9ucL-nzE", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279174"}], "__type": "TplTag"}, "20279168": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279177"}, "dataCond": {"__ref": "20279178"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279169": {"cols": [{"__ref": "20279179"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279170": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279174": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279180"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279177": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "position": "relative", "row-gap": "10px"}, "mixins": [], "__type": "RuleSet"}, "20279178": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279179": {"frame": {"__ref": "20279193"}, "cellKey": {"__ref": "20279160"}, "__type": "ArenaFrameCell"}, "20279180": {"values": {"padding-top": "0px", "display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "20279193": {"uuid": "MKRoOY66Bq", "width": 340, "height": 340, "container": {"__ref": "20279200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279160"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279200": {"name": null, "component": {"__ref": "20279156"}, "uuid": "o3TwFlNKyM", "parent": null, "locked": null, "vsettings": [{"__ref": "20279203"}], "__type": "TplComponent"}, "20279203": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279206"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279206": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279237": {"type": {"__ref": "20279241"}, "tplSlot": {"__ref": "20279238"}, "variable": {"__ref": "20279240"}, "uuid": "l5VcK-bWz9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279238": {"param": {"__ref": "20279237"}, "defaultContents": [], "uuid": "nedHRtVvxY", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279243"}], "__type": "TplSlot"}, "20279239": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279375"}, "__type": "Arg"}, "20279240": {"name": "children", "uuid": "qC3M5lsu3", "__type": "Var"}, "20279241": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279243": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279246"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279246": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279247": {"name": null, "component": {"__ref": "20279051"}, "uuid": "7-oLaszagZ", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279250"}], "__type": "TplComponent"}, "20279250": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279255"}], "attrs": {}, "rs": {"__ref": "20279256"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279255": {"param": {"__ref": "20279099"}, "expr": {"__ref": "65055001"}, "__type": "Arg"}, "20279256": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279279": {"type": {"__ref": "20279283"}, "tplSlot": {"__ref": "20279280"}, "variable": {"__ref": "20279282"}, "uuid": "LFQWtR8Jy0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279280": {"param": {"__ref": "20279279"}, "defaultContents": [{"__ref": "20279284"}], "uuid": "55mnzn8_UF", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279285"}], "__type": "TplSlot"}, "20279281": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279286"}, "__type": "Arg"}, "20279282": {"name": "description", "uuid": "2asjOM6OR", "__type": "Var"}, "20279283": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279284": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "W0PvB37urM", "parent": {"__ref": "20279280"}, "locked": null, "vsettings": [{"__ref": "20279287"}], "__type": "TplTag"}, "20279285": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279288"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279286": {"tpl": [{"__ref": "20279289"}], "__type": "VirtualRenderExpr"}, "20279287": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279290"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279291"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279288": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279289": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "irGF1kbQoo", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279292"}], "__type": "TplTag"}, "20279290": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279291": {"markers": [], "text": "Update \"Section1Comp\", move \"oldDefaultContent\" out of \"children\" slot default content's (move it to \"tplTag\").", "__type": "RawText"}, "20279292": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279293"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279294"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279293": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279294": {"markers": [], "text": "Update \"Section1Comp\", move \"oldDefaultContent\" out of \"children\" slot default content's (move it to \"tplTag\").", "__type": "RawText"}, "20279295": {"param": {"__ref": "20279298"}, "defaultContents": [{"__ref": "20279300"}], "uuid": "x2uA2obsAf", "parent": {"__ref": "20279165"}, "locked": null, "vsettings": [{"__ref": "20279301"}], "__type": "TplSlot"}, "20279298": {"type": {"__ref": "20279303"}, "tplSlot": {"__ref": "20279295"}, "variable": {"__ref": "20279302"}, "uuid": "_Iv9B526Fq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279299": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279304"}, "__type": "Arg"}, "20279300": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "GZFjtY5Ish", "parent": {"__ref": "20279295"}, "locked": null, "vsettings": [{"__ref": "20279305"}], "__type": "TplTag"}, "20279301": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279302": {"name": "title", "uuid": "Zptf8vNrR7", "__type": "Var"}, "20279303": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279304": {"tpl": [{"__ref": "20279307"}], "__type": "VirtualRenderExpr"}, "20279305": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279308"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279309"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279306": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279307": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "HmFW_fkaW-", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279310"}], "__type": "TplTag"}, "20279308": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279309": {"markers": [], "text": "1 - Test update parent of TplSlot default contents", "__type": "RawText"}, "20279310": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279311"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279312"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279311": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279312": {"markers": [], "text": "1 - Test update parent of TplSlot default contents", "__type": "RawText"}, "20279313": {"name": "section2", "component": {"__ref": "20279156"}, "uuid": "OJz2ETlm3Y", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279314"}], "__type": "TplComponent"}, "20279314": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279316"}, {"__ref": "20279317"}, {"__ref": "20279376"}], "attrs": {}, "rs": {"__ref": "20279318"}, "dataCond": {"__ref": "20279319"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279316": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279355"}, "__type": "Arg"}, "20279317": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279353"}, "__type": "Arg"}, "20279318": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279319": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279327": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "4BLGLlLMj7", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279330"}], "__type": "TplTag"}, "20279328": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8XWi5MJV-i", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279331"}], "__type": "TplTag"}, "20279330": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279334"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279354"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279331": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279336"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279352"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279334": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279336": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279352": {"markers": [], "text": "2 - Test update parent of slot arg", "__type": "RawText"}, "20279353": {"tpl": [{"__ref": "20279328"}], "__type": "RenderExpr"}, "20279354": {"markers": [], "text": "Update the instance of \"Section2Comp\", move \"oldArg\" out of \"children\" slot (move it to \"section2TplTag\").", "__type": "RawText"}, "20279355": {"tpl": [{"__ref": "20279327"}], "__type": "RenderExpr"}, "20279375": {"tpl": [{"__ref": "20279247"}], "__type": "RenderExpr"}, "20279376": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279438"}, "__type": "Arg"}, "20279387": {"uuid": "vctvB2ZxYd", "name": "Section2Comp", "params": [{"__ref": "20279421"}], "states": [], "tplTree": {"__ref": "20279390"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279391"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279388": {"component": {"__ref": "20279387"}, "matrix": {"__ref": "20279392"}, "customMatrix": {"__ref": "20279393"}, "__type": "ComponentArena"}, "20279389": {"name": null, "component": {"__ref": "20279387"}, "uuid": "GF9CproY5n", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279394"}], "__type": "TplComponent"}, "20279390": {"tag": "div", "name": null, "children": [{"__ref": "20279413"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "noYEyl2aF", "parent": null, "locked": null, "vsettings": [{"__ref": "20279395"}], "__type": "TplTag"}, "20279391": {"uuid": "PjKIzFH40K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279392": {"rows": [{"__ref": "20279396"}], "__type": "ArenaFrameGrid"}, "20279393": {"rows": [{"__ref": "20279397"}], "__type": "ArenaFrameGrid"}, "20279394": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279423"}], "attrs": {}, "rs": {"__ref": "20279398"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279395": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279399"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279396": {"cols": [{"__ref": "20279400"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279397": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279398": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279399": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279400": {"frame": {"__ref": "20279409"}, "cellKey": {"__ref": "20279391"}, "__type": "ArenaFrameCell"}, "20279409": {"uuid": "gBh_vb8GMk", "width": 340, "height": 340, "container": {"__ref": "20279410"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279391"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279410": {"name": null, "component": {"__ref": "20279387"}, "uuid": "QlxSo83WWO", "parent": null, "locked": null, "vsettings": [{"__ref": "20279411"}], "__type": "TplComponent"}, "20279411": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279412"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279412": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279413": {"tag": "div", "name": null, "children": [{"__ref": "20279422"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8gD6q20o_", "parent": {"__ref": "20279390"}, "locked": null, "vsettings": [{"__ref": "20279414"}], "__type": "TplTag"}, "20279414": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279415"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279415": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279421": {"type": {"__ref": "20279425"}, "tplSlot": {"__ref": "20279422"}, "variable": {"__ref": "20279424"}, "uuid": "jUOLPt-fbn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279422": {"param": {"__ref": "20279421"}, "defaultContents": [], "uuid": "VzuZMoNgWc", "parent": {"__ref": "20279413"}, "locked": null, "vsettings": [{"__ref": "20279426"}], "__type": "TplSlot"}, "20279423": {"param": {"__ref": "20279421"}, "expr": {"__ref": "65055007"}, "__type": "Arg"}, "20279424": {"name": "children", "uuid": "pfLzAhssr", "__type": "Var"}, "20279425": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279426": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279428"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279428": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279430": {"tag": "div", "name": "oldArg", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "aVrXAjwxV", "parent": {"__ref": "20279439"}, "locked": null, "vsettings": [{"__ref": "20279431"}], "__type": "TplTag"}, "20279431": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279432"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279432": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279438": {"tpl": [{"__ref": "20279389"}, {"__ref": "20279439"}], "__type": "RenderExpr"}, "20279439": {"tag": "div", "name": "section2TplTag", "children": [{"__ref": "20279430"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lCJJw<PERSON>ey", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279440"}], "__type": "TplTag"}, "20279440": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279441"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279441": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279447": {"name": "section3", "component": {"__ref": "20279156"}, "uuid": "JAz04ZAPH", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279448"}], "__type": "TplComponent"}, "20279448": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279449"}, {"__ref": "20279450"}, {"__ref": "20279451"}], "attrs": {}, "rs": {"__ref": "20279452"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279449": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279467"}, "__type": "Arg"}, "20279450": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279469"}, "__type": "Arg"}, "20279451": {"param": {"__ref": "20279237"}, "expr": {"__ref": "65055008"}, "__type": "Arg"}, "20279452": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279458": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "v9MvSb8FMm", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279460"}], "__type": "TplTag"}, "20279459": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "sAzRkLiitY", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279461"}], "__type": "TplTag"}, "20279460": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279462"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279466"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279461": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279464"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279531"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279462": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279464": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279466": {"markers": [], "text": "3 - Fix disconnected cycle", "__type": "RawText"}, "20279467": {"tpl": [{"__ref": "20279458"}], "__type": "RenderExpr"}, "20279469": {"tpl": [{"__ref": "20279459"}], "__type": "RenderExpr"}, "20279531": {"markers": [], "text": "One branch updates \"cycle1\" to be child of \"cycle2\" and the other does the opposite, creating a cycle", "__type": "RawText"}, "20279535": {"tag": "div", "name": "cycle1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SX9c-Wqmu", "parent": {"__ref": "20279544"}, "locked": null, "vsettings": [{"__ref": "20279536"}], "__type": "TplTag"}, "20279536": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279537"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279537": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279544": {"tag": "div", "name": "cycle2", "children": [{"__ref": "20279535"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3Sh_2y898", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279545"}], "__type": "TplTag"}, "20279545": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279546"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279546": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279552": {"name": "section4", "component": {"__ref": "20279156"}, "uuid": "Y14v9-2ptT", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279553"}], "__type": "TplComponent"}, "20279553": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279554"}, {"__ref": "20279555"}, {"__ref": "20279556"}], "attrs": {}, "rs": {"__ref": "20279557"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279554": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279558"}, "__type": "Arg"}, "20279555": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279559"}, "__type": "Arg"}, "20279556": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279600"}, "__type": "Arg"}, "20279557": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279558": {"tpl": [{"__ref": "20279563"}], "__type": "RenderExpr"}, "20279559": {"tpl": [{"__ref": "20279564"}], "__type": "RenderExpr"}, "20279563": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "mBO1xyL8de", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279567"}], "__type": "TplTag"}, "20279564": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ESjcl_T8pZ", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279568"}], "__type": "TplTag"}, "20279567": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279571"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279587"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279568": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279573"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279588"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279571": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279573": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279587": {"markers": [], "text": "4 - Delete Tpl Comp children", "__type": "RawText"}, "20279588": {"markers": [], "text": "Each branch will delete one of \"Section4Comp\"s children", "__type": "RawText"}, "20279600": {"tpl": [{"__ref": "20279603"}], "__type": "RenderExpr"}, "20279601": {"uuid": "CLeJRsBU3N", "name": "Section4Comp", "params": [{"__ref": "20279635"}], "states": [], "tplTree": {"__ref": "20279604"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279605"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279602": {"component": {"__ref": "20279601"}, "matrix": {"__ref": "20279606"}, "customMatrix": {"__ref": "20279607"}, "__type": "ComponentArena"}, "20279603": {"name": "section4comp", "component": {"__ref": "20279601"}, "uuid": "ZIJ_mTuI9_", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279608"}], "__type": "TplComponent"}, "20279604": {"tag": "div", "name": null, "children": [{"__ref": "20279627"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dse_ERTNk", "parent": null, "locked": null, "vsettings": [{"__ref": "20279609"}], "__type": "TplTag"}, "20279605": {"uuid": "CgFimYi1YG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279606": {"rows": [{"__ref": "20279610"}], "__type": "ArenaFrameGrid"}, "20279607": {"rows": [{"__ref": "20279611"}], "__type": "ArenaFrameGrid"}, "20279608": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279637"}], "attrs": {}, "rs": {"__ref": "20279612"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279609": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279613"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279610": {"cols": [{"__ref": "20279614"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279611": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279612": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279613": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279614": {"frame": {"__ref": "20279623"}, "cellKey": {"__ref": "20279605"}, "__type": "ArenaFrameCell"}, "20279623": {"uuid": "YbtOvxrn6h", "width": 340, "height": 340, "container": {"__ref": "20279624"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279605"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279624": {"name": null, "component": {"__ref": "20279601"}, "uuid": "_GAwC4zh2d", "parent": null, "locked": null, "vsettings": [{"__ref": "20279625"}], "__type": "TplComponent"}, "20279625": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279626"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279626": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279627": {"tag": "div", "name": null, "children": [{"__ref": "20279636"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QtFE9Oia_", "parent": {"__ref": "20279604"}, "locked": null, "vsettings": [{"__ref": "20279628"}], "__type": "TplTag"}, "20279628": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279629"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279629": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279635": {"type": {"__ref": "20279639"}, "tplSlot": {"__ref": "20279636"}, "variable": {"__ref": "20279638"}, "uuid": "SgQJP-ggIk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279636": {"param": {"__ref": "20279635"}, "defaultContents": [], "uuid": "wrNPOOE9Da", "parent": {"__ref": "20279627"}, "locked": null, "vsettings": [{"__ref": "20279640"}], "__type": "TplSlot"}, "20279637": {"param": {"__ref": "20279635"}, "expr": {"__ref": "65055009"}, "__type": "Arg"}, "20279638": {"name": "children", "uuid": "jZ-7fRk34", "__type": "Var"}, "20279639": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279640": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279642": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279653": {"tag": "div", "name": "section4child2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "TF5jD1rQ0", "parent": {"__ref": "20279603"}, "locked": null, "vsettings": [{"__ref": "20279654"}], "__type": "TplTag"}, "20279654": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279655"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279655": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279661": {"name": "section5", "component": {"__ref": "20279156"}, "uuid": "OT7SKY0oy", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279662"}], "__type": "TplComponent"}, "20279662": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279663"}, {"__ref": "20279664"}, {"__ref": "20279665"}], "attrs": {}, "rs": {"__ref": "20279666"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279663": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279681"}, "__type": "Arg"}, "20279664": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279683"}, "__type": "Arg"}, "20279665": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279693"}, "__type": "Arg"}, "20279666": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279672": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "IQ4ovZjf6K", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279674"}], "__type": "TplTag"}, "20279673": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jw_jxoqivi", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279675"}], "__type": "TplTag"}, "20279674": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279676"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279680"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279675": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279678"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279682"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279676": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279678": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279680": {"markers": [], "text": "5 - Merge different values for VirtualRenderExpr", "__type": "RawText"}, "20279681": {"tpl": [{"__ref": "20279672"}], "__type": "RenderExpr"}, "20279682": {"markers": [], "text": "Each branch adds a new children to Section5Comp children default contents", "__type": "RawText"}, "20279683": {"tpl": [{"__ref": "20279673"}], "__type": "RenderExpr"}, "20279693": {"tpl": [{"__ref": "20279696"}], "__type": "RenderExpr"}, "20279694": {"uuid": "On5brgxpcp", "name": "Section5Comp", "params": [{"__ref": "20279728"}], "states": [], "tplTree": {"__ref": "20279697"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279698"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279695": {"component": {"__ref": "20279694"}, "matrix": {"__ref": "20279699"}, "customMatrix": {"__ref": "20279700"}, "__type": "ComponentArena"}, "20279696": {"name": "section5comp", "component": {"__ref": "20279694"}, "uuid": "SeaYUt22g4", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279701"}], "__type": "TplComponent"}, "20279697": {"tag": "div", "name": null, "children": [{"__ref": "20279720"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ra-PT3p7e", "parent": null, "locked": null, "vsettings": [{"__ref": "20279702"}], "__type": "TplTag"}, "20279698": {"uuid": "Ni4FKAdilw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279699": {"rows": [{"__ref": "20279703"}], "__type": "ArenaFrameGrid"}, "20279700": {"rows": [{"__ref": "20279704"}], "__type": "ArenaFrameGrid"}, "20279701": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279730"}], "attrs": {}, "rs": {"__ref": "20279705"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279702": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279706"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279703": {"cols": [{"__ref": "20279707"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279704": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279705": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279706": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279707": {"frame": {"__ref": "20279716"}, "cellKey": {"__ref": "20279698"}, "__type": "ArenaFrameCell"}, "20279716": {"uuid": "nkDDRvkObD", "width": 340, "height": 340, "container": {"__ref": "20279717"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279698"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279717": {"name": null, "component": {"__ref": "20279694"}, "uuid": "ZZU5CxNdKp", "parent": null, "locked": null, "vsettings": [{"__ref": "20279718"}], "__type": "TplComponent"}, "20279718": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279719"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279719": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279720": {"tag": "div", "name": null, "children": [{"__ref": "20279729"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "HglvzGs4y", "parent": {"__ref": "20279697"}, "locked": null, "vsettings": [{"__ref": "20279721"}], "__type": "TplTag"}, "20279721": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279722"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279722": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279728": {"type": {"__ref": "20279732"}, "tplSlot": {"__ref": "20279729"}, "variable": {"__ref": "20279731"}, "uuid": "Bb3Sv2gvMS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279729": {"param": {"__ref": "20279728"}, "defaultContents": [{"__ref": "20279736"}, {"__ref": "65055010"}], "uuid": "3WYhqR7P4y", "parent": {"__ref": "20279720"}, "locked": null, "vsettings": [{"__ref": "20279733"}], "__type": "TplSlot"}, "20279730": {"param": {"__ref": "20279728"}, "expr": {"__ref": "65055035"}, "__type": "Arg"}, "20279731": {"name": "children", "uuid": "Wx7LFdB3f", "__type": "Var"}, "20279732": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279733": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279735"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279735": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279736": {"tag": "div", "name": "child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9ts1muJNE", "parent": {"__ref": "20279729"}, "locked": null, "vsettings": [{"__ref": "20279738"}], "__type": "TplTag"}, "20279738": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279740"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279740": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279807": {"name": "section6", "component": {"__ref": "20279156"}, "uuid": "SXCx0spoO", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279808"}], "__type": "TplComponent"}, "20279808": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279809"}, {"__ref": "20279810"}, {"__ref": "20279811"}], "attrs": {}, "rs": {"__ref": "20279812"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279809": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279827"}, "__type": "Arg"}, "20279810": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279829"}, "__type": "Arg"}, "20279811": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279862"}, "__type": "Arg"}, "20279812": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279818": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "LpcacNPwvP", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279820"}], "__type": "TplTag"}, "20279819": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "vnNWRzw33Y", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279821"}], "__type": "TplTag"}, "20279820": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279822"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279830"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279821": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279824"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279828"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279822": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279824": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279827": {"tpl": [{"__ref": "20279818"}], "__type": "RenderExpr"}, "20279828": {"markers": [], "text": "One branch re-orders the children arg of Section6Comp, while the other just adds a new child", "__type": "RawText"}, "20279829": {"tpl": [{"__ref": "20279819"}], "__type": "RenderExpr"}, "20279830": {"markers": [], "text": "6 - Change relative order of tpl component children", "__type": "RawText"}, "20279831": {"name": "section7", "component": {"__ref": "20279156"}, "uuid": "95aRkyH0xh", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279832"}], "__type": "TplComponent"}, "20279832": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279833"}, {"__ref": "20279834"}, {"__ref": "20279835"}], "attrs": {}, "rs": {"__ref": "20279836"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279833": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279837"}, "__type": "Arg"}, "20279834": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279838"}, "__type": "Arg"}, "20279835": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280109"}, "__type": "Arg"}, "20279836": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279837": {"tpl": [{"__ref": "20279842"}], "__type": "RenderExpr"}, "20279838": {"tpl": [{"__ref": "20279843"}], "__type": "RenderExpr"}, "20279842": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cksT3m7sPC", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20279844"}], "__type": "TplTag"}, "20279843": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "npe0TarOGa", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20279845"}], "__type": "TplTag"}, "20279844": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279846"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279852"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279845": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279848"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279851"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279846": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279848": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279851": {"markers": [], "text": "Same as section 6, but with a tpl tag", "__type": "RawText"}, "20279852": {"markers": [], "text": "7 - Change relative order of tpl tag children", "__type": "RawText"}, "20279862": {"tpl": [{"__ref": "20279865"}], "__type": "RenderExpr"}, "20279863": {"uuid": "5lBYahYiCA", "name": "Section6Comp", "params": [{"__ref": "20279897"}], "states": [], "tplTree": {"__ref": "20279866"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279867"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279864": {"component": {"__ref": "20279863"}, "matrix": {"__ref": "20279868"}, "customMatrix": {"__ref": "20279869"}, "__type": "ComponentArena"}, "20279865": {"name": "section6comp", "component": {"__ref": "20279863"}, "uuid": "ihIFR8Yu_3", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279870"}], "__type": "TplComponent"}, "20279866": {"tag": "div", "name": null, "children": [{"__ref": "20279889"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "j5W5Xk2nU", "parent": null, "locked": null, "vsettings": [{"__ref": "20279871"}], "__type": "TplTag"}, "20279867": {"uuid": "fmBidXiX9V", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279868": {"rows": [{"__ref": "20279872"}], "__type": "ArenaFrameGrid"}, "20279869": {"rows": [{"__ref": "20279873"}], "__type": "ArenaFrameGrid"}, "20279870": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279899"}], "attrs": {}, "rs": {"__ref": "20279874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279871": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279875"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279872": {"cols": [{"__ref": "20279876"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279873": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279874": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279875": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279876": {"frame": {"__ref": "20279885"}, "cellKey": {"__ref": "20279867"}, "__type": "ArenaFrameCell"}, "20279885": {"uuid": "Emp_sqrl7I", "width": 340, "height": 340, "container": {"__ref": "20279886"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279867"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279886": {"name": null, "component": {"__ref": "20279863"}, "uuid": "Db1yZOq4NI", "parent": null, "locked": null, "vsettings": [{"__ref": "20279887"}], "__type": "TplComponent"}, "20279887": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279889": {"tag": "div", "name": null, "children": [{"__ref": "20279898"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9IUApZjYj", "parent": {"__ref": "20279866"}, "locked": null, "vsettings": [{"__ref": "20279890"}], "__type": "TplTag"}, "20279890": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279891"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279891": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279897": {"type": {"__ref": "20279901"}, "tplSlot": {"__ref": "20279898"}, "variable": {"__ref": "20279900"}, "uuid": "2LNEj30Vqn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279898": {"param": {"__ref": "20279897"}, "defaultContents": [], "uuid": "cQ4QCSTH4f", "parent": {"__ref": "20279889"}, "locked": null, "vsettings": [{"__ref": "20279902"}], "__type": "TplSlot"}, "20279899": {"param": {"__ref": "20279897"}, "expr": {"__ref": "65055052"}, "__type": "Arg"}, "20279900": {"name": "children", "uuid": "-idiEVMtj", "__type": "Var"}, "20279901": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279902": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279904"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279904": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280083": {"tag": "div", "name": "section6order1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LApPOCWv_", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280084"}], "__type": "TplTag"}, "20280084": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280085"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280085": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280092": {"tag": "div", "name": "section6order2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2CdFvhm1b", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280093"}], "__type": "TplTag"}, "20280093": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280094"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280094": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280101": {"tag": "div", "name": "section6order3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E93CgQSaT", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280102"}], "__type": "TplTag"}, "20280102": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280103"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280103": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280109": {"tpl": [{"__ref": "20280110"}], "__type": "RenderExpr"}, "20280110": {"tag": "div", "name": null, "children": [{"__ref": "20280134"}, {"__ref": "20280111"}, {"__ref": "20280126"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "s5eOUbZrw", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20280112"}], "__type": "TplTag"}, "20280111": {"tag": "div", "name": "section7order1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "tF9GWxoSZ", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280113"}], "__type": "TplTag"}, "20280112": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280114"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280113": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280115"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280114": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280115": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280126": {"tag": "div", "name": "section7order2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lOEVzQ0M0", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280127"}], "__type": "TplTag"}, "20280127": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280128"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280128": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280134": {"tag": "div", "name": "section7order3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fJPVRdmRw", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280135"}], "__type": "TplTag"}, "20280135": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280136"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280136": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280142": {"name": "section8", "component": {"__ref": "20279156"}, "uuid": "CC2hvYL4D", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280143"}], "__type": "TplComponent"}, "20280143": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280144"}, {"__ref": "20280145"}, {"__ref": "20280146"}], "attrs": {}, "rs": {"__ref": "20280147"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280144": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280164"}, "__type": "Arg"}, "20280145": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280166"}, "__type": "Arg"}, "20280146": {"param": {"__ref": "20279237"}, "expr": {"__ref": "65055069"}, "__type": "Arg"}, "20280147": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280153": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KlO8gi1xbd", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280155"}], "__type": "TplTag"}, "20280154": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "UX7aYQg6Qm", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280156"}], "__type": "TplTag"}, "20280155": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280157"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280163"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280156": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280159"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280165"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280157": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280159": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280163": {"markers": [], "text": "8 - Add new slot in a branch and move nodes into it", "__type": "RawText"}, "20280164": {"tpl": [{"__ref": "20280153"}], "__type": "RenderExpr"}, "20280165": {"markers": [], "text": "A branch will create a new slot for Section8Comp, and move \"section8tpl\" into it, as well as a new tpl node", "__type": "RawText"}, "20280166": {"tpl": [{"__ref": "20280154"}], "__type": "RenderExpr"}, "20280177": {"uuid": "GK_jsBv920", "name": "Section8Comp", "params": [{"__ref": "65055061"}], "states": [], "tplTree": {"__ref": "20280180"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20280181"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20280178": {"component": {"__ref": "20280177"}, "matrix": {"__ref": "20280182"}, "customMatrix": {"__ref": "20280183"}, "__type": "ComponentArena"}, "20280179": {"name": "section8comp", "component": {"__ref": "20280177"}, "uuid": "vY7MIDRlPp", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280184"}], "__type": "TplComponent"}, "20280180": {"tag": "div", "name": null, "children": [{"__ref": "65055053"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E_04LajOm", "parent": null, "locked": null, "vsettings": [{"__ref": "20280185"}], "__type": "TplTag"}, "20280181": {"uuid": "Lbxui6QAQ6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280182": {"rows": [{"__ref": "20280186"}], "__type": "ArenaFrameGrid"}, "20280183": {"rows": [{"__ref": "20280187"}], "__type": "ArenaFrameGrid"}, "20280184": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "65055063"}], "attrs": {}, "rs": {"__ref": "20280188"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280185": {"variants": [{"__ref": "20280181"}], "args": [], "attrs": {}, "rs": {"__ref": "20280189"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280186": {"cols": [{"__ref": "20280190"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20280187": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20280188": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20280189": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280190": {"frame": {"__ref": "20280199"}, "cellKey": {"__ref": "20280181"}, "__type": "ArenaFrameCell"}, "20280199": {"uuid": "1oXYv4UjyF", "width": 340, "height": 340, "container": {"__ref": "20280200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20280181"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20280200": {"name": null, "component": {"__ref": "20280177"}, "uuid": "oOMnkoqDS3", "parent": null, "locked": null, "vsettings": [{"__ref": "20280201"}], "__type": "TplComponent"}, "20280201": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20280202"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280202": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280204": {"tag": "div", "name": "section8tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SAk8QVjj2", "parent": {"__ref": "20280179"}, "locked": null, "vsettings": [{"__ref": "20280205"}], "__type": "TplTag"}, "20280205": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280206"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280206": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280212": {"name": "section9", "component": {"__ref": "20279156"}, "uuid": "7fJ1CO8DC", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280213"}], "__type": "TplComponent"}, "20280213": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280214"}, {"__ref": "20280215"}, {"__ref": "20280216"}], "attrs": {}, "rs": {"__ref": "20280217"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280214": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280232"}, "__type": "Arg"}, "20280215": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280235"}, "__type": "Arg"}, "20280216": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280220"}, "__type": "Arg"}, "20280217": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280220": {"tpl": [], "__type": "VirtualRenderExpr"}, "20280223": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1eaGu3uj6o", "parent": {"__ref": "20280212"}, "locked": null, "vsettings": [{"__ref": "20280225"}], "__type": "TplTag"}, "20280224": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "M_WJwTURop", "parent": {"__ref": "20280212"}, "locked": null, "vsettings": [{"__ref": "20280226"}], "__type": "TplTag"}, "20280225": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280227"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280237"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280226": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280229"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280238"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280227": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280229": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280232": {"tpl": [{"__ref": "20280223"}], "__type": "RenderExpr"}, "20280233": {"uuid": "hbljPOe7V", "name": "Mobile", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "8615003"}, "mediaQuery": "(max-width:640px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280235": {"tpl": [{"__ref": "20280224"}], "__type": "RenderExpr"}, "20280237": {"markers": [], "text": "9 - Add new artboard, target global variant", "__type": "RawText"}, "20280238": {"markers": [], "text": "A branch will add a new artboard, and target the mobile screen variant", "__type": "RawText"}, "20280239": {"name": "section10", "component": {"__ref": "20279156"}, "uuid": "NSy4qU0sLE", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280240"}], "__type": "TplComponent"}, "20280240": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280241"}, {"__ref": "20280242"}, {"__ref": "20280243"}], "attrs": {}, "rs": {"__ref": "20280244"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280241": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280245"}, "__type": "Arg"}, "20280242": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280246"}, "__type": "Arg"}, "20280243": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280247"}, "__type": "Arg"}, "20280244": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280245": {"tpl": [{"__ref": "20280250"}], "__type": "RenderExpr"}, "20280246": {"tpl": [{"__ref": "20280251"}], "__type": "RenderExpr"}, "20280247": {"tpl": [], "__type": "VirtualRenderExpr"}, "20280250": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8EHJopGzPv", "parent": {"__ref": "20280239"}, "locked": null, "vsettings": [{"__ref": "20280252"}], "__type": "TplTag"}, "20280251": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "dTGs-bqYNY", "parent": {"__ref": "20280239"}, "locked": null, "vsettings": [{"__ref": "20280253"}], "__type": "TplTag"}, "20280252": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280254"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280288"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280253": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280256"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280286"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280254": {"values": {"padding-bottom": "0px", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "20280256": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280271": {"uuid": "WYVm22mkEy", "name": "Unnamed Component", "params": [], "states": [], "tplTree": {"__ref": "20280273"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "20280274"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20280272": {"uuid": "E78HznyzT4", "width": 757, "height": 1103, "container": {"__ref": "20280275"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20280274"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "section10arena1", "top": 1474, "left": 1443, "__type": "ArenaFrame"}, "20280273": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "k90NmA5b1", "parent": null, "locked": null, "vsettings": [{"__ref": "20280276"}], "__type": "TplTag"}, "20280274": {"uuid": "Rd-n5lbGmC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280275": {"name": null, "component": {"__ref": "20280271"}, "uuid": "RJFPghfetr", "parent": null, "locked": null, "vsettings": [{"__ref": "20280277"}], "__type": "TplComponent"}, "20280276": {"variants": [{"__ref": "20280274"}], "args": [], "attrs": {}, "rs": {"__ref": "20280278"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280277": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20280279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280278": {"values": {"display": "block", "position": "relative", "width": "stretch", "height": "stretch"}, "mixins": [], "__type": "RuleSet"}, "20280279": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280286": {"markers": [], "text": "Each branch will change the name of \"section10arena\" to a different name, creating a harmless conflict", "__type": "RawText"}, "20280288": {"markers": [], "text": "10 - Test harmless conflict with artboard name", "__type": "RawText"}, "52044001": {"uuid": "tf2H95Pq3t", "name": "hostless-plasmic-head", "params": [{"__ref": "52044003"}, {"__ref": "52044004"}, {"__ref": "52044005"}, {"__ref": "52044006"}], "states": [], "tplTree": {"__ref": "52044007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "52044008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "52044009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "52044002": {"uuid": "wpYNJO6ypD", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "52044010"}, {"__ref": "52044011"}, {"__ref": "52044012"}, {"__ref": "52044013"}, {"__ref": "52044014"}], "states": [], "tplTree": {"__ref": "52044015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "52044016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "52044017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "52044003": {"type": {"__ref": "52044019"}, "variable": {"__ref": "52044018"}, "uuid": "upZwDAbwTl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044004": {"type": {"__ref": "52044021"}, "variable": {"__ref": "52044020"}, "uuid": "Wsr53Wu-4U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044005": {"type": {"__ref": "52044023"}, "variable": {"__ref": "52044022"}, "uuid": "dvvYk2dynQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044006": {"type": {"__ref": "52044025"}, "variable": {"__ref": "52044024"}, "uuid": "jgV0pNAwBM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2w3YqO9hPV", "parent": null, "locked": null, "vsettings": [{"__ref": "52044026"}], "__type": "TplTag"}, "52044008": {"uuid": "S1XfBSeiw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52044009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "52044010": {"type": {"__ref": "52044028"}, "variable": {"__ref": "52044027"}, "uuid": "0ACCnHBko6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044011": {"type": {"__ref": "52044030"}, "variable": {"__ref": "52044029"}, "uuid": "r7piC3Sx88u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044012": {"type": {"__ref": "52044032"}, "tplSlot": {"__ref": "52044037"}, "variable": {"__ref": "52044031"}, "uuid": "ktFptf_LgCR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "52044013": {"type": {"__ref": "52044034"}, "variable": {"__ref": "52044033"}, "uuid": "aha-3GvZdpO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044014": {"type": {"__ref": "52044036"}, "variable": {"__ref": "52044035"}, "uuid": "cJOCxKYET_B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044015": {"tag": "div", "name": null, "children": [{"__ref": "52044037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "YNljyGUCP3", "parent": null, "locked": null, "vsettings": [{"__ref": "52044038"}], "__type": "TplTag"}, "52044016": {"uuid": "LZpX1editO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52044017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "52044018": {"name": "title", "uuid": "ICA7AVWoL2", "__type": "Var"}, "52044019": {"name": "text", "__type": "Text"}, "52044020": {"name": "description", "uuid": "mz-SGD2V8z", "__type": "Var"}, "52044021": {"name": "text", "__type": "Text"}, "52044022": {"name": "image", "uuid": "SxzeekIBHW", "__type": "Var"}, "52044023": {"name": "img", "__type": "Img"}, "52044024": {"name": "canonical", "uuid": "MfPnMlxIrC", "__type": "Var"}, "52044025": {"name": "text", "__type": "Text"}, "52044026": {"variants": [{"__ref": "52044008"}], "args": [], "attrs": {}, "rs": {"__ref": "52044039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044027": {"name": "dataOp", "uuid": "MuUEM9uPCP", "__type": "Var"}, "52044028": {"name": "any", "__type": "AnyType"}, "52044029": {"name": "name", "uuid": "0lxyDqm2Sz6", "__type": "Var"}, "52044030": {"name": "text", "__type": "Text"}, "52044031": {"name": "children", "uuid": "aCWSIt95_Gm", "__type": "Var"}, "52044032": {"name": "renderFunc", "params": [{"__ref": "52044040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "52044033": {"name": "pageSize", "uuid": "caL98c6qwUc", "__type": "Var"}, "52044034": {"name": "num", "__type": "<PERSON><PERSON>"}, "52044035": {"name": "pageIndex", "uuid": "cC5YpxckKP-", "__type": "Var"}, "52044036": {"name": "num", "__type": "<PERSON><PERSON>"}, "52044037": {"param": {"__ref": "52044012"}, "defaultContents": [], "uuid": "nmWF57VYyTU", "parent": {"__ref": "52044015"}, "locked": null, "vsettings": [{"__ref": "52044041"}], "__type": "TplSlot"}, "52044038": {"variants": [{"__ref": "52044016"}], "args": [], "attrs": {}, "rs": {"__ref": "52044042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "52044040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "52044045"}, "__type": "ArgType"}, "52044041": {"variants": [{"__ref": "52044016"}], "args": [], "attrs": {}, "rs": {"__ref": "52044046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "52044045": {"name": "any", "__type": "AnyType"}, "52044046": {"values": {}, "mixins": [], "__type": "RuleSet"}, "65055001": {"tpl": [{"__ref": "65055002"}], "__type": "VirtualRenderExpr"}, "65055002": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "26yaedfQB", "parent": {"__ref": "20279247"}, "locked": null, "vsettings": [{"__ref": "65055003"}], "__type": "TplTag"}, "65055003": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "65055004"}, "dataCond": null, "dataRep": null, "text": {"__ref": "65055005"}, "columnsConfig": null, "__type": "VariantSetting"}, "65055004": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "65055005": {"markers": [], "text": "more contents", "__type": "RawText"}, "65055007": {"tpl": [], "__type": "RenderExpr"}, "65055008": {"tpl": [{"__ref": "20279544"}], "__type": "RenderExpr"}, "65055009": {"tpl": [{"__ref": "20279653"}], "__type": "RenderExpr"}, "65055010": {"tag": "div", "name": "child2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "c-iz6COfU", "parent": {"__ref": "20279729"}, "locked": null, "vsettings": [{"__ref": "65055012"}], "__type": "TplTag"}, "65055012": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "65055015"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055015": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "65055035": {"tpl": [{"__ref": "65055036"}, {"__ref": "65055037"}], "__type": "VirtualRenderExpr"}, "65055036": {"tag": "div", "name": "child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "CJzi89THD", "parent": {"__ref": "20279696"}, "locked": null, "vsettings": [{"__ref": "65055038"}], "__type": "TplTag"}, "65055037": {"tag": "div", "name": "child2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "joJW0V4sf-", "parent": {"__ref": "20279696"}, "locked": null, "vsettings": [{"__ref": "65055039"}], "__type": "TplTag"}, "65055038": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "65055040"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055039": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "65055041"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055040": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "65055041": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "65055052": {"tpl": [{"__ref": "20280101"}, {"__ref": "20280083"}, {"__ref": "20280092"}], "__type": "RenderExpr"}, "65055053": {"tag": "div", "name": null, "children": [{"__ref": "65055062"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DGESET65l", "parent": {"__ref": "20280180"}, "locked": null, "vsettings": [{"__ref": "65055054"}], "__type": "TplTag"}, "65055054": {"variants": [{"__ref": "20280181"}], "args": [], "attrs": {}, "rs": {"__ref": "65055055"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055055": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "65055061": {"type": {"__ref": "65055065"}, "tplSlot": {"__ref": "65055062"}, "variable": {"__ref": "65055064"}, "uuid": "4GoLqtzi8e", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "65055062": {"param": {"__ref": "65055061"}, "defaultContents": [], "uuid": "X5Md9o_AmB", "parent": {"__ref": "65055053"}, "locked": null, "vsettings": [{"__ref": "65055066"}], "__type": "TplSlot"}, "65055063": {"param": {"__ref": "65055061"}, "expr": {"__ref": "65055071"}, "__type": "Arg"}, "65055064": {"name": "children", "uuid": "LYrx7OO7D", "__type": "Var"}, "65055065": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "65055066": {"variants": [{"__ref": "20280181"}], "args": [], "attrs": {}, "rs": {"__ref": "65055068"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055068": {"values": {}, "mixins": [], "__type": "RuleSet"}, "65055069": {"tpl": [{"__ref": "20280179"}], "__type": "RenderExpr"}, "65055071": {"tpl": [{"__ref": "20280204"}, {"__ref": "65055072"}], "__type": "RenderExpr"}, "65055072": {"tag": "div", "name": "section8newtpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2BMgLSr-T", "parent": {"__ref": "20280179"}, "locked": null, "vsettings": [{"__ref": "65055073"}], "__type": "TplTag"}, "65055073": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "65055074"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055074": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "65055080": {"uuid": "6t5YbX67gc", "name": "Unnamed Component2", "params": [], "states": [], "tplTree": {"__ref": "65055082"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "65055083"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "65055081": {"uuid": "182lqjwsrz", "width": 640, "height": 1103, "container": {"__ref": "65055084"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {}, "targetGlobalVariants": [{"__ref": "20280233"}], "viewMode": "stretch", "bgColor": null, "name": "newartboard", "top": 191, "left": 1601, "__type": "ArenaFrame"}, "65055082": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "gl3Ahqap3", "parent": null, "locked": null, "vsettings": [{"__ref": "65055085"}, {"__ref": "65055093"}], "__type": "TplTag"}, "65055083": {"uuid": "eCTTTeUcgO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "65055084": {"name": null, "component": {"__ref": "65055080"}, "uuid": "4RQUw-FsIC", "parent": null, "locked": null, "vsettings": [{"__ref": "65055086"}], "__type": "TplComponent"}, "65055085": {"variants": [{"__ref": "65055083"}], "args": [], "attrs": {}, "rs": {"__ref": "65055087"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055086": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "65055088"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055087": {"values": {"display": "block", "position": "relative", "width": "stretch", "height": "stretch"}, "mixins": [], "__type": "RuleSet"}, "65055088": {"values": {}, "mixins": [], "__type": "RuleSet"}, "65055093": {"variants": [{"__ref": "20280233"}], "args": [], "attrs": {}, "rs": {"__ref": "65055094"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "65055094": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "251-add-data-tokens"}}, {"branchId": "main", "data": {"root": "8615001", "map": {"8615001": {"components": [{"__ref": "52044001"}, {"__ref": "52044002"}, {"__ref": "20279001"}, {"__ref": "20279051"}, {"__ref": "20279156"}, {"__ref": "20279387"}, {"__ref": "20279601"}, {"__ref": "20279694"}, {"__ref": "20279863"}, {"__ref": "20280177"}, {"__ref": "20280271"}], "arenas": [{"__ref": "8615002"}], "pageArenas": [], "componentArenas": [{"__ref": "20279052"}, {"__ref": "20279157"}, {"__ref": "20279388"}, {"__ref": "20279602"}, {"__ref": "20279695"}, {"__ref": "20279864"}, {"__ref": "20280178"}], "globalVariantGroups": [{"__ref": "8615003"}], "userManagedFonts": [], "globalVariant": {"__ref": "8615007"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "8615008"}], "activeTheme": {"__ref": "8615008"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "8615003"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "8615002": {"name": "Custom arena 1", "children": [{"__ref": "20279002"}, {"__ref": "20280272"}], "__type": "Arena"}, "8615003": {"type": "global-screen", "param": {"__ref": "8615004"}, "uuid": "ECHG62LxIap", "variants": [{"__ref": "20280233"}], "multi": true, "__type": "GlobalVariantGroup"}, "8615004": {"type": {"__ref": "8615006"}, "variable": {"__ref": "8615005"}, "uuid": "dWUX2PHHThx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "8615005": {"name": "Screen", "uuid": "oW8p5ZQllQ", "__type": "Var"}, "8615006": {"name": "text", "__type": "Text"}, "8615007": {"uuid": "CiJdOYqwUYG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8615008": {"defaultStyle": {"__ref": "8615009"}, "styles": [{"__ref": "8615024"}, {"__ref": "8615033"}, {"__ref": "8615042"}, {"__ref": "8615051"}, {"__ref": "8615060"}, {"__ref": "8615069"}, {"__ref": "8615077"}, {"__ref": "8615081"}, {"__ref": "8615085"}, {"__ref": "8615093"}, {"__ref": "8615118"}, {"__ref": "8615143"}, {"__ref": "8615154"}], "layout": {"__ref": "8615165"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "8615009": {"name": "Default Typography", "rs": {"__ref": "8615010"}, "preview": null, "uuid": "lRD3svOrvl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615010": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "8615024": {"selector": "h1", "style": {"__ref": "8615025"}, "__type": "ThemeStyle"}, "8615025": {"name": "Default \"h1\"", "rs": {"__ref": "8615026"}, "preview": null, "uuid": "8wtJRE1kj0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615026": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "8615033": {"selector": "h2", "style": {"__ref": "8615034"}, "__type": "ThemeStyle"}, "8615034": {"name": "Default \"h2\"", "rs": {"__ref": "8615035"}, "preview": null, "uuid": "PLBNpIosis", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615035": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "8615042": {"selector": "h3", "style": {"__ref": "8615043"}, "__type": "ThemeStyle"}, "8615043": {"name": "Default \"h3\"", "rs": {"__ref": "8615044"}, "preview": null, "uuid": "1ti1p6M7tt", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615044": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "8615051": {"selector": "h4", "style": {"__ref": "8615052"}, "__type": "ThemeStyle"}, "8615052": {"name": "Default \"h4\"", "rs": {"__ref": "8615053"}, "preview": null, "uuid": "shd9hbKB6g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615053": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "8615060": {"selector": "h5", "style": {"__ref": "8615061"}, "__type": "ThemeStyle"}, "8615061": {"name": "Default \"h5\"", "rs": {"__ref": "8615062"}, "preview": null, "uuid": "9gubx8aDyX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615062": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8615069": {"selector": "h6", "style": {"__ref": "8615070"}, "__type": "ThemeStyle"}, "8615070": {"name": "Default \"h6\"", "rs": {"__ref": "8615071"}, "preview": null, "uuid": "zYyvcLNLdY", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615071": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "8615077": {"selector": "a", "style": {"__ref": "8615078"}, "__type": "ThemeStyle"}, "8615078": {"name": "Default \"a\"", "rs": {"__ref": "8615079"}, "preview": null, "uuid": "ekX-ag13Ze", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615079": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "8615081": {"selector": "a:hover", "style": {"__ref": "8615082"}, "__type": "ThemeStyle"}, "8615082": {"name": "Default \"a:hover\"", "rs": {"__ref": "8615083"}, "preview": null, "uuid": "K9WtVdND3A", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615083": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "8615085": {"selector": "blockquote", "style": {"__ref": "8615086"}, "__type": "ThemeStyle"}, "8615086": {"name": "Default \"blockquote\"", "rs": {"__ref": "8615087"}, "preview": null, "uuid": "rnJtOH0-Sx", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615087": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "8615093": {"selector": "code", "style": {"__ref": "8615094"}, "__type": "ThemeStyle"}, "8615094": {"name": "Default \"code\"", "rs": {"__ref": "8615095"}, "preview": null, "uuid": "lPJab7QWsT", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615095": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "8615118": {"selector": "pre", "style": {"__ref": "8615119"}, "__type": "ThemeStyle"}, "8615119": {"name": "Default \"pre\"", "rs": {"__ref": "8615120"}, "preview": null, "uuid": "EEamYjn6m0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615120": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "8615143": {"selector": "ol", "style": {"__ref": "8615144"}, "__type": "ThemeStyle"}, "8615144": {"name": "Default \"ol\"", "rs": {"__ref": "8615145"}, "preview": null, "uuid": "v-GhaIYQVR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615145": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "8615154": {"selector": "ul", "style": {"__ref": "8615155"}, "__type": "ThemeStyle"}, "8615155": {"name": "Default \"ul\"", "rs": {"__ref": "8615156"}, "preview": null, "uuid": "B5mrhhC0jn", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "8615156": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "8615165": {"rs": {"__ref": "8615166"}, "__type": "ThemeLayoutSettings"}, "8615166": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279001": {"uuid": "jvObxJk-ch", "name": "", "params": [], "states": [], "tplTree": {"__ref": "20279003"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279004"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279002": {"uuid": "zocqdP_S5b", "width": 757, "height": 2517, "container": {"__ref": "20279005"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279004"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "mainArtboard", "top": 77, "left": 506, "__type": "ArenaFrame"}, "20279003": {"tag": "div", "name": null, "children": [{"__ref": "20279155"}, {"__ref": "20279313"}, {"__ref": "20279447"}, {"__ref": "20279552"}, {"__ref": "20279661"}, {"__ref": "20279807"}, {"__ref": "20279831"}, {"__ref": "20280142"}, {"__ref": "20280212"}, {"__ref": "20280239"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "MvT3KhzVr", "parent": null, "locked": null, "vsettings": [{"__ref": "20279006"}], "__type": "TplTag"}, "20279004": {"uuid": "FhvkK6xECC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279005": {"name": null, "component": {"__ref": "20279001"}, "uuid": "20-2SDUfRJ", "parent": null, "locked": null, "vsettings": [{"__ref": "20279007"}], "__type": "TplComponent"}, "20279006": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279008"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279007": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279009"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279008": {"values": {"display": "flex", "position": "relative", "width": "stretch", "height": "stretch", "flex-direction": "column", "row-gap": "18px"}, "mixins": [], "__type": "RuleSet"}, "20279009": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279051": {"uuid": "45FQ_SMj-t", "name": "Section1Comp", "params": [{"__ref": "20279099"}], "states": [], "tplTree": {"__ref": "20279054"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279055"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279052": {"component": {"__ref": "20279051"}, "matrix": {"__ref": "20279056"}, "customMatrix": {"__ref": "20279057"}, "__type": "ComponentArena"}, "20279054": {"tag": "div", "name": null, "children": [{"__ref": "20279080"}, {"__ref": "20279100"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "reED4a-W1", "parent": null, "locked": null, "vsettings": [{"__ref": "20279059"}], "__type": "TplTag"}, "20279055": {"uuid": "o3lC58Jf4A", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279056": {"rows": [{"__ref": "20279060"}], "__type": "ArenaFrameGrid"}, "20279057": {"rows": [{"__ref": "20279061"}], "__type": "ArenaFrameGrid"}, "20279059": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279065"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279060": {"cols": [{"__ref": "20279066"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279061": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279065": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279066": {"frame": {"__ref": "20279072"}, "cellKey": {"__ref": "20279055"}, "__type": "ArenaFrameCell"}, "20279072": {"uuid": "415HmIBU1R", "width": 340, "height": 340, "container": {"__ref": "20279073"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279055"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279073": {"name": null, "component": {"__ref": "20279051"}, "uuid": "kaiFxmXH98", "parent": null, "locked": null, "vsettings": [{"__ref": "20279074"}], "__type": "TplComponent"}, "20279074": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279075"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279075": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279080": {"tag": "div", "name": "tplTag", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OHNdhS6tJ", "parent": {"__ref": "20279054"}, "locked": null, "vsettings": [{"__ref": "20279081"}], "__type": "TplTag"}, "20279081": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279082"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279082": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279099": {"type": {"__ref": "20279103"}, "tplSlot": {"__ref": "20279100"}, "variable": {"__ref": "20279102"}, "uuid": "DZvLD_xOph", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279100": {"param": {"__ref": "20279099"}, "defaultContents": [{"__ref": "20279104"}, {"__ref": "20279115"}], "uuid": "5j_HSOW0iJ", "parent": {"__ref": "20279054"}, "locked": null, "vsettings": [{"__ref": "20279105"}], "__type": "TplSlot"}, "20279102": {"name": "children", "uuid": "hvUvnbv_T", "__type": "Var"}, "20279103": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279104": {"tag": "div", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2ASZgfZBr8", "parent": {"__ref": "20279100"}, "locked": null, "vsettings": [{"__ref": "20279107"}], "__type": "TplTag"}, "20279105": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279108"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279107": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279110"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279111"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279108": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279110": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279111": {"markers": [], "text": "slot contents", "__type": "RawText"}, "20279115": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xeZyq2UEZ", "parent": {"__ref": "20279100"}, "locked": null, "vsettings": [{"__ref": "20279116"}], "__type": "TplTag"}, "20279116": {"variants": [{"__ref": "20279055"}], "args": [], "attrs": {}, "rs": {"__ref": "20279120"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279133"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279120": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279133": {"markers": [], "text": "more contents", "__type": "RawText"}, "20279155": {"name": "section1", "component": {"__ref": "20279156"}, "uuid": "9i5aDR6ffM", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279158"}], "__type": "TplComponent"}, "20279156": {"uuid": "QQCZUQ2awS", "name": "TestSection", "params": [{"__ref": "20279237"}, {"__ref": "20279279"}, {"__ref": "20279298"}], "states": [], "tplTree": {"__ref": "20279159"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279160"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279157": {"component": {"__ref": "20279156"}, "matrix": {"__ref": "20279161"}, "customMatrix": {"__ref": "20279162"}, "__type": "ComponentArena"}, "20279158": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279239"}, {"__ref": "20279281"}, {"__ref": "20279299"}], "attrs": {}, "rs": {"__ref": "20279163"}, "dataCond": {"__ref": "20279164"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279159": {"tag": "div", "name": null, "children": [{"__ref": "20279165"}, {"__ref": "20279280"}, {"__ref": "20279238"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KOJVzRh6yc", "parent": null, "locked": null, "vsettings": [{"__ref": "20279168"}], "__type": "TplTag"}, "20279160": {"uuid": "-6Em6VHq4y", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279161": {"rows": [{"__ref": "20279169"}], "__type": "ArenaFrameGrid"}, "20279162": {"rows": [{"__ref": "20279170"}], "__type": "ArenaFrameGrid"}, "20279163": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279164": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279165": {"tag": "h4", "name": null, "children": [{"__ref": "20279295"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "S9ucL-nzE", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279174"}], "__type": "TplTag"}, "20279168": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279177"}, "dataCond": {"__ref": "20279178"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279169": {"cols": [{"__ref": "20279179"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279170": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279174": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279180"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279177": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "plasmic-display-none": "false", "position": "relative", "row-gap": "10px"}, "mixins": [], "__type": "RuleSet"}, "20279178": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279179": {"frame": {"__ref": "20279193"}, "cellKey": {"__ref": "20279160"}, "__type": "ArenaFrameCell"}, "20279180": {"values": {"padding-top": "0px", "display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "20279193": {"uuid": "MKRoOY66Bq", "width": 340, "height": 340, "container": {"__ref": "20279200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279160"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279200": {"name": null, "component": {"__ref": "20279156"}, "uuid": "o3TwFlNKyM", "parent": null, "locked": null, "vsettings": [{"__ref": "20279203"}], "__type": "TplComponent"}, "20279203": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279206"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279206": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279237": {"type": {"__ref": "20279241"}, "tplSlot": {"__ref": "20279238"}, "variable": {"__ref": "20279240"}, "uuid": "l5VcK-bWz9", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279238": {"param": {"__ref": "20279237"}, "defaultContents": [], "uuid": "nedHRtVvxY", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279243"}], "__type": "TplSlot"}, "20279239": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279375"}, "__type": "Arg"}, "20279240": {"name": "children", "uuid": "qC3M5lsu3", "__type": "Var"}, "20279241": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279243": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279246"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279246": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279247": {"name": null, "component": {"__ref": "20279051"}, "uuid": "7-oLaszagZ", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279250"}], "__type": "TplComponent"}, "20279250": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279255"}], "attrs": {}, "rs": {"__ref": "20279256"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279255": {"param": {"__ref": "20279099"}, "expr": {"__ref": "20279259"}, "__type": "Arg"}, "20279256": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279259": {"tpl": [{"__ref": "20279265"}, {"__ref": "20279266"}], "__type": "VirtualRenderExpr"}, "20279265": {"tag": "div", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jV-6NFthfZ", "parent": {"__ref": "20279247"}, "locked": null, "vsettings": [{"__ref": "20279271"}], "__type": "TplTag"}, "20279266": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cr3dBnS55Y", "parent": {"__ref": "20279247"}, "locked": null, "vsettings": [{"__ref": "20279272"}], "__type": "TplTag"}, "20279271": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279274"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279275"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279272": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279276"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279277"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279274": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279275": {"markers": [], "text": "slot contents", "__type": "RawText"}, "20279276": {"values": {"position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279277": {"markers": [], "text": "more contents", "__type": "RawText"}, "20279279": {"type": {"__ref": "20279283"}, "tplSlot": {"__ref": "20279280"}, "variable": {"__ref": "20279282"}, "uuid": "LFQWtR8Jy0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279280": {"param": {"__ref": "20279279"}, "defaultContents": [{"__ref": "20279284"}], "uuid": "55mnzn8_UF", "parent": {"__ref": "20279159"}, "locked": null, "vsettings": [{"__ref": "20279285"}], "__type": "TplSlot"}, "20279281": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279286"}, "__type": "Arg"}, "20279282": {"name": "description", "uuid": "2asjOM6OR", "__type": "Var"}, "20279283": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279284": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "W0PvB37urM", "parent": {"__ref": "20279280"}, "locked": null, "vsettings": [{"__ref": "20279287"}], "__type": "TplTag"}, "20279285": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279288"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279286": {"tpl": [{"__ref": "20279289"}], "__type": "VirtualRenderExpr"}, "20279287": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279290"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279291"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279288": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279289": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "irGF1kbQoo", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279292"}], "__type": "TplTag"}, "20279290": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279291": {"markers": [], "text": "Update \"Section1Comp\", move \"oldDefaultContent\" out of \"children\" slot default content's (move it to \"tplTag\").", "__type": "RawText"}, "20279292": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279293"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279294"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279293": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279294": {"markers": [], "text": "Update \"Section1Comp\", move \"oldDefaultContent\" out of \"children\" slot default content's (move it to \"tplTag\").", "__type": "RawText"}, "20279295": {"param": {"__ref": "20279298"}, "defaultContents": [{"__ref": "20279300"}], "uuid": "x2uA2obsAf", "parent": {"__ref": "20279165"}, "locked": null, "vsettings": [{"__ref": "20279301"}], "__type": "TplSlot"}, "20279298": {"type": {"__ref": "20279303"}, "tplSlot": {"__ref": "20279295"}, "variable": {"__ref": "20279302"}, "uuid": "_Iv9B526Fq", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279299": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279304"}, "__type": "Arg"}, "20279300": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "GZFjtY5Ish", "parent": {"__ref": "20279295"}, "locked": null, "vsettings": [{"__ref": "20279305"}], "__type": "TplTag"}, "20279301": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279306"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279302": {"name": "title", "uuid": "Zptf8vNrR7", "__type": "Var"}, "20279303": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279304": {"tpl": [{"__ref": "20279307"}], "__type": "VirtualRenderExpr"}, "20279305": {"variants": [{"__ref": "20279160"}], "args": [], "attrs": {}, "rs": {"__ref": "20279308"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279309"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279306": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279307": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "HmFW_fkaW-", "parent": {"__ref": "20279155"}, "locked": null, "vsettings": [{"__ref": "20279310"}], "__type": "TplTag"}, "20279308": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279309": {"markers": [], "text": "1 - Test update parent of TplSlot default contents", "__type": "RawText"}, "20279310": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279311"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279312"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279311": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279312": {"markers": [], "text": "1 - Test update parent of TplSlot default contents", "__type": "RawText"}, "20279313": {"name": "section2", "component": {"__ref": "20279156"}, "uuid": "OJz2ETlm3Y", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279314"}], "__type": "TplComponent"}, "20279314": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279316"}, {"__ref": "20279317"}, {"__ref": "20279376"}], "attrs": {}, "rs": {"__ref": "20279318"}, "dataCond": {"__ref": "20279319"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279316": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279355"}, "__type": "Arg"}, "20279317": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279353"}, "__type": "Arg"}, "20279318": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279319": {"code": "true", "fallback": null, "__type": "CustomCode"}, "20279327": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "4BLGLlLMj7", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279330"}], "__type": "TplTag"}, "20279328": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8XWi5MJV-i", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279331"}], "__type": "TplTag"}, "20279330": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279334"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279354"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279331": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279336"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279352"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279334": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279336": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279352": {"markers": [], "text": "2 - Test update parent of slot arg", "__type": "RawText"}, "20279353": {"tpl": [{"__ref": "20279328"}], "__type": "RenderExpr"}, "20279354": {"markers": [], "text": "Update the instance of \"Section2Comp\", move \"oldArg\" out of \"children\" slot (move it to \"section2TplTag\").", "__type": "RawText"}, "20279355": {"tpl": [{"__ref": "20279327"}], "__type": "RenderExpr"}, "20279375": {"tpl": [{"__ref": "20279247"}], "__type": "RenderExpr"}, "20279376": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279438"}, "__type": "Arg"}, "20279387": {"uuid": "vctvB2ZxYd", "name": "Section2Comp", "params": [{"__ref": "20279421"}], "states": [], "tplTree": {"__ref": "20279390"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279391"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279388": {"component": {"__ref": "20279387"}, "matrix": {"__ref": "20279392"}, "customMatrix": {"__ref": "20279393"}, "__type": "ComponentArena"}, "20279389": {"name": null, "component": {"__ref": "20279387"}, "uuid": "GF9CproY5n", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279394"}], "__type": "TplComponent"}, "20279390": {"tag": "div", "name": null, "children": [{"__ref": "20279413"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "noYEyl2aF", "parent": null, "locked": null, "vsettings": [{"__ref": "20279395"}], "__type": "TplTag"}, "20279391": {"uuid": "PjKIzFH40K", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279392": {"rows": [{"__ref": "20279396"}], "__type": "ArenaFrameGrid"}, "20279393": {"rows": [{"__ref": "20279397"}], "__type": "ArenaFrameGrid"}, "20279394": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279423"}], "attrs": {}, "rs": {"__ref": "20279398"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279395": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279399"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279396": {"cols": [{"__ref": "20279400"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279397": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279398": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279399": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279400": {"frame": {"__ref": "20279409"}, "cellKey": {"__ref": "20279391"}, "__type": "ArenaFrameCell"}, "20279409": {"uuid": "gBh_vb8GMk", "width": 340, "height": 340, "container": {"__ref": "20279410"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279391"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279410": {"name": null, "component": {"__ref": "20279387"}, "uuid": "QlxSo83WWO", "parent": null, "locked": null, "vsettings": [{"__ref": "20279411"}], "__type": "TplComponent"}, "20279411": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279412"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279412": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279413": {"tag": "div", "name": null, "children": [{"__ref": "20279422"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8gD6q20o_", "parent": {"__ref": "20279390"}, "locked": null, "vsettings": [{"__ref": "20279414"}], "__type": "TplTag"}, "20279414": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279415"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279415": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279421": {"type": {"__ref": "20279425"}, "tplSlot": {"__ref": "20279422"}, "variable": {"__ref": "20279424"}, "uuid": "jUOLPt-fbn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279422": {"param": {"__ref": "20279421"}, "defaultContents": [], "uuid": "VzuZMoNgWc", "parent": {"__ref": "20279413"}, "locked": null, "vsettings": [{"__ref": "20279426"}], "__type": "TplSlot"}, "20279423": {"param": {"__ref": "20279421"}, "expr": {"__ref": "20279429"}, "__type": "Arg"}, "20279424": {"name": "children", "uuid": "pfLzAhssr", "__type": "Var"}, "20279425": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279426": {"variants": [{"__ref": "20279391"}], "args": [], "attrs": {}, "rs": {"__ref": "20279428"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279428": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279429": {"tpl": [{"__ref": "20279430"}], "__type": "RenderExpr"}, "20279430": {"tag": "div", "name": "oldArg", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "aVrXAjwxV", "parent": {"__ref": "20279389"}, "locked": null, "vsettings": [{"__ref": "20279431"}], "__type": "TplTag"}, "20279431": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279432"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279432": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279438": {"tpl": [{"__ref": "20279389"}, {"__ref": "20279439"}], "__type": "RenderExpr"}, "20279439": {"tag": "div", "name": "section2TplTag", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lCJJw<PERSON>ey", "parent": {"__ref": "20279313"}, "locked": null, "vsettings": [{"__ref": "20279440"}], "__type": "TplTag"}, "20279440": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279441"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279441": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279447": {"name": "section3", "component": {"__ref": "20279156"}, "uuid": "JAz04ZAPH", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279448"}], "__type": "TplComponent"}, "20279448": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279449"}, {"__ref": "20279450"}, {"__ref": "20279451"}], "attrs": {}, "rs": {"__ref": "20279452"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279449": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279467"}, "__type": "Arg"}, "20279450": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279469"}, "__type": "Arg"}, "20279451": {"param": {"__ref": "20279237"}, "expr": {"__ref": "38815001"}, "__type": "Arg"}, "20279452": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279458": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "v9MvSb8FMm", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279460"}], "__type": "TplTag"}, "20279459": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "sAzRkLiitY", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279461"}], "__type": "TplTag"}, "20279460": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279462"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279466"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279461": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279464"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279531"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279462": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279464": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279466": {"markers": [], "text": "3 - Fix disconnected cycle", "__type": "RawText"}, "20279467": {"tpl": [{"__ref": "20279458"}], "__type": "RenderExpr"}, "20279469": {"tpl": [{"__ref": "20279459"}], "__type": "RenderExpr"}, "20279531": {"markers": [], "text": "One branch updates \"cycle1\" to be child of \"cycle2\" and the other does the opposite, creating a cycle", "__type": "RawText"}, "20279535": {"tag": "div", "name": "cycle1", "children": [{"__ref": "20279544"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SX9c-Wqmu", "parent": {"__ref": "20279447"}, "locked": null, "vsettings": [{"__ref": "20279536"}], "__type": "TplTag"}, "20279536": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279537"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279537": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279544": {"tag": "div", "name": "cycle2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3Sh_2y898", "parent": {"__ref": "20279535"}, "locked": null, "vsettings": [{"__ref": "20279545"}], "__type": "TplTag"}, "20279545": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279546"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279546": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279552": {"name": "section4", "component": {"__ref": "20279156"}, "uuid": "Y14v9-2ptT", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279553"}], "__type": "TplComponent"}, "20279553": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279554"}, {"__ref": "20279555"}, {"__ref": "20279556"}], "attrs": {}, "rs": {"__ref": "20279557"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279554": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279558"}, "__type": "Arg"}, "20279555": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279559"}, "__type": "Arg"}, "20279556": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279600"}, "__type": "Arg"}, "20279557": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279558": {"tpl": [{"__ref": "20279563"}], "__type": "RenderExpr"}, "20279559": {"tpl": [{"__ref": "20279564"}], "__type": "RenderExpr"}, "20279563": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "mBO1xyL8de", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279567"}], "__type": "TplTag"}, "20279564": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "ESjcl_T8pZ", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279568"}], "__type": "TplTag"}, "20279567": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279571"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279587"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279568": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279573"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279588"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279571": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279573": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279587": {"markers": [], "text": "4 - Delete Tpl Comp children", "__type": "RawText"}, "20279588": {"markers": [], "text": "Each branch will delete one of \"Section4Comp\"s children", "__type": "RawText"}, "20279600": {"tpl": [{"__ref": "20279603"}], "__type": "RenderExpr"}, "20279601": {"uuid": "CLeJRsBU3N", "name": "Section4Comp", "params": [{"__ref": "20279635"}], "states": [], "tplTree": {"__ref": "20279604"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279605"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279602": {"component": {"__ref": "20279601"}, "matrix": {"__ref": "20279606"}, "customMatrix": {"__ref": "20279607"}, "__type": "ComponentArena"}, "20279603": {"name": "section4comp", "component": {"__ref": "20279601"}, "uuid": "ZIJ_mTuI9_", "parent": {"__ref": "20279552"}, "locked": null, "vsettings": [{"__ref": "20279608"}], "__type": "TplComponent"}, "20279604": {"tag": "div", "name": null, "children": [{"__ref": "20279627"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dse_ERTNk", "parent": null, "locked": null, "vsettings": [{"__ref": "20279609"}], "__type": "TplTag"}, "20279605": {"uuid": "CgFimYi1YG", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279606": {"rows": [{"__ref": "20279610"}], "__type": "ArenaFrameGrid"}, "20279607": {"rows": [{"__ref": "20279611"}], "__type": "ArenaFrameGrid"}, "20279608": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279637"}], "attrs": {}, "rs": {"__ref": "20279612"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279609": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279613"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279610": {"cols": [{"__ref": "20279614"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279611": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279612": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279613": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279614": {"frame": {"__ref": "20279623"}, "cellKey": {"__ref": "20279605"}, "__type": "ArenaFrameCell"}, "20279623": {"uuid": "YbtOvxrn6h", "width": 340, "height": 340, "container": {"__ref": "20279624"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279605"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279624": {"name": null, "component": {"__ref": "20279601"}, "uuid": "_GAwC4zh2d", "parent": null, "locked": null, "vsettings": [{"__ref": "20279625"}], "__type": "TplComponent"}, "20279625": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279626"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279626": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279627": {"tag": "div", "name": null, "children": [{"__ref": "20279636"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "QtFE9Oia_", "parent": {"__ref": "20279604"}, "locked": null, "vsettings": [{"__ref": "20279628"}], "__type": "TplTag"}, "20279628": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279629"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279629": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279635": {"type": {"__ref": "20279639"}, "tplSlot": {"__ref": "20279636"}, "variable": {"__ref": "20279638"}, "uuid": "SgQJP-ggIk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279636": {"param": {"__ref": "20279635"}, "defaultContents": [], "uuid": "wrNPOOE9Da", "parent": {"__ref": "20279627"}, "locked": null, "vsettings": [{"__ref": "20279640"}], "__type": "TplSlot"}, "20279637": {"param": {"__ref": "20279635"}, "expr": {"__ref": "38815002"}, "__type": "Arg"}, "20279638": {"name": "children", "uuid": "jZ-7fRk34", "__type": "Var"}, "20279639": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279640": {"variants": [{"__ref": "20279605"}], "args": [], "attrs": {}, "rs": {"__ref": "20279642"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279642": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279644": {"tag": "div", "name": "section4child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "yMWQ5-04V", "parent": {"__ref": "20279603"}, "locked": null, "vsettings": [{"__ref": "20279645"}], "__type": "TplTag"}, "20279645": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279646"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279646": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279661": {"name": "section5", "component": {"__ref": "20279156"}, "uuid": "OT7SKY0oy", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279662"}], "__type": "TplComponent"}, "20279662": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279663"}, {"__ref": "20279664"}, {"__ref": "20279665"}], "attrs": {}, "rs": {"__ref": "20279666"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279663": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279681"}, "__type": "Arg"}, "20279664": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279683"}, "__type": "Arg"}, "20279665": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279693"}, "__type": "Arg"}, "20279666": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279672": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "IQ4ovZjf6K", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279674"}], "__type": "TplTag"}, "20279673": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jw_jxoqivi", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279675"}], "__type": "TplTag"}, "20279674": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279676"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279680"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279675": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279678"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279682"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279676": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279678": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279680": {"markers": [], "text": "5 - Merge different values for VirtualRenderExpr", "__type": "RawText"}, "20279681": {"tpl": [{"__ref": "20279672"}], "__type": "RenderExpr"}, "20279682": {"markers": [], "text": "Each branch adds a new children to Section5Comp children default contents", "__type": "RawText"}, "20279683": {"tpl": [{"__ref": "20279673"}], "__type": "RenderExpr"}, "20279693": {"tpl": [{"__ref": "20279696"}], "__type": "RenderExpr"}, "20279694": {"uuid": "On5brgxpcp", "name": "Section5Comp", "params": [{"__ref": "20279728"}], "states": [], "tplTree": {"__ref": "20279697"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279698"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279695": {"component": {"__ref": "20279694"}, "matrix": {"__ref": "20279699"}, "customMatrix": {"__ref": "20279700"}, "__type": "ComponentArena"}, "20279696": {"name": "section5comp", "component": {"__ref": "20279694"}, "uuid": "SeaYUt22g4", "parent": {"__ref": "20279661"}, "locked": null, "vsettings": [{"__ref": "20279701"}], "__type": "TplComponent"}, "20279697": {"tag": "div", "name": null, "children": [{"__ref": "20279720"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ra-PT3p7e", "parent": null, "locked": null, "vsettings": [{"__ref": "20279702"}], "__type": "TplTag"}, "20279698": {"uuid": "Ni4FKAdilw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279699": {"rows": [{"__ref": "20279703"}], "__type": "ArenaFrameGrid"}, "20279700": {"rows": [{"__ref": "20279704"}], "__type": "ArenaFrameGrid"}, "20279701": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279730"}], "attrs": {}, "rs": {"__ref": "20279705"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279702": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279706"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279703": {"cols": [{"__ref": "20279707"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279704": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279705": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279706": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279707": {"frame": {"__ref": "20279716"}, "cellKey": {"__ref": "20279698"}, "__type": "ArenaFrameCell"}, "20279716": {"uuid": "nkDDRvkObD", "width": 340, "height": 340, "container": {"__ref": "20279717"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279698"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279717": {"name": null, "component": {"__ref": "20279694"}, "uuid": "ZZU5CxNdKp", "parent": null, "locked": null, "vsettings": [{"__ref": "20279718"}], "__type": "TplComponent"}, "20279718": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279719"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279719": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279720": {"tag": "div", "name": null, "children": [{"__ref": "20279729"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "HglvzGs4y", "parent": {"__ref": "20279697"}, "locked": null, "vsettings": [{"__ref": "20279721"}], "__type": "TplTag"}, "20279721": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279722"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279722": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279728": {"type": {"__ref": "20279732"}, "tplSlot": {"__ref": "20279729"}, "variable": {"__ref": "20279731"}, "uuid": "Bb3Sv2gvMS", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279729": {"param": {"__ref": "20279728"}, "defaultContents": [{"__ref": "20279736"}, {"__ref": "38815045"}], "uuid": "3WYhqR7P4y", "parent": {"__ref": "20279720"}, "locked": null, "vsettings": [{"__ref": "20279733"}], "__type": "TplSlot"}, "20279730": {"param": {"__ref": "20279728"}, "expr": {"__ref": "38815070"}, "__type": "Arg"}, "20279731": {"name": "children", "uuid": "Wx7LFdB3f", "__type": "Var"}, "20279732": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279733": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279735"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279735": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279736": {"tag": "div", "name": "child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9ts1muJNE", "parent": {"__ref": "20279729"}, "locked": null, "vsettings": [{"__ref": "20279738"}], "__type": "TplTag"}, "20279738": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "20279740"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279740": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279807": {"name": "section6", "component": {"__ref": "20279156"}, "uuid": "SXCx0spoO", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279808"}], "__type": "TplComponent"}, "20279808": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279809"}, {"__ref": "20279810"}, {"__ref": "20279811"}], "attrs": {}, "rs": {"__ref": "20279812"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279809": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279827"}, "__type": "Arg"}, "20279810": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279829"}, "__type": "Arg"}, "20279811": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20279862"}, "__type": "Arg"}, "20279812": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279818": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "LpcacNPwvP", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279820"}], "__type": "TplTag"}, "20279819": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "vnNWRzw33Y", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279821"}], "__type": "TplTag"}, "20279820": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279822"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279830"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279821": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279824"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279828"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279822": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279824": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279827": {"tpl": [{"__ref": "20279818"}], "__type": "RenderExpr"}, "20279828": {"markers": [], "text": "One branch re-orders the children arg of Section6Comp, while the other just adds a new child", "__type": "RawText"}, "20279829": {"tpl": [{"__ref": "20279819"}], "__type": "RenderExpr"}, "20279830": {"markers": [], "text": "6 - Change relative order of tpl component children", "__type": "RawText"}, "20279831": {"name": "section7", "component": {"__ref": "20279156"}, "uuid": "95aRkyH0xh", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20279832"}], "__type": "TplComponent"}, "20279832": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279833"}, {"__ref": "20279834"}, {"__ref": "20279835"}], "attrs": {}, "rs": {"__ref": "20279836"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279833": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20279837"}, "__type": "Arg"}, "20279834": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20279838"}, "__type": "Arg"}, "20279835": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280109"}, "__type": "Arg"}, "20279836": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279837": {"tpl": [{"__ref": "20279842"}], "__type": "RenderExpr"}, "20279838": {"tpl": [{"__ref": "20279843"}], "__type": "RenderExpr"}, "20279842": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "cksT3m7sPC", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20279844"}], "__type": "TplTag"}, "20279843": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "npe0TarOGa", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20279845"}], "__type": "TplTag"}, "20279844": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279846"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279852"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279845": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20279848"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20279851"}, "columnsConfig": null, "__type": "VariantSetting"}, "20279846": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279848": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279851": {"markers": [], "text": "Same as section 6, but with a tpl tag", "__type": "RawText"}, "20279852": {"markers": [], "text": "7 - Change relative order of tpl tag children", "__type": "RawText"}, "20279862": {"tpl": [{"__ref": "20279865"}], "__type": "RenderExpr"}, "20279863": {"uuid": "5lBYahYiCA", "name": "Section6Comp", "params": [{"__ref": "20279897"}], "states": [], "tplTree": {"__ref": "20279866"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20279867"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20279864": {"component": {"__ref": "20279863"}, "matrix": {"__ref": "20279868"}, "customMatrix": {"__ref": "20279869"}, "__type": "ComponentArena"}, "20279865": {"name": "section6comp", "component": {"__ref": "20279863"}, "uuid": "ihIFR8Yu_3", "parent": {"__ref": "20279807"}, "locked": null, "vsettings": [{"__ref": "20279870"}], "__type": "TplComponent"}, "20279866": {"tag": "div", "name": null, "children": [{"__ref": "20279889"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "j5W5Xk2nU", "parent": null, "locked": null, "vsettings": [{"__ref": "20279871"}], "__type": "TplTag"}, "20279867": {"uuid": "fmBidXiX9V", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20279868": {"rows": [{"__ref": "20279872"}], "__type": "ArenaFrameGrid"}, "20279869": {"rows": [{"__ref": "20279873"}], "__type": "ArenaFrameGrid"}, "20279870": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20279899"}], "attrs": {}, "rs": {"__ref": "20279874"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279871": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279875"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279872": {"cols": [{"__ref": "20279876"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20279873": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20279874": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20279875": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20279876": {"frame": {"__ref": "20279885"}, "cellKey": {"__ref": "20279867"}, "__type": "ArenaFrameCell"}, "20279885": {"uuid": "Emp_sqrl7I", "width": 340, "height": 340, "container": {"__ref": "20279886"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20279867"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20279886": {"name": null, "component": {"__ref": "20279863"}, "uuid": "Db1yZOq4NI", "parent": null, "locked": null, "vsettings": [{"__ref": "20279887"}], "__type": "TplComponent"}, "20279887": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20279888"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279888": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20279889": {"tag": "div", "name": null, "children": [{"__ref": "20279898"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "9IUApZjYj", "parent": {"__ref": "20279866"}, "locked": null, "vsettings": [{"__ref": "20279890"}], "__type": "TplTag"}, "20279890": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279891"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279891": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20279897": {"type": {"__ref": "20279901"}, "tplSlot": {"__ref": "20279898"}, "variable": {"__ref": "20279900"}, "uuid": "2LNEj30Vqn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "20279898": {"param": {"__ref": "20279897"}, "defaultContents": [], "uuid": "cQ4QCSTH4f", "parent": {"__ref": "20279889"}, "locked": null, "vsettings": [{"__ref": "20279902"}], "__type": "TplSlot"}, "20279899": {"param": {"__ref": "20279897"}, "expr": {"__ref": "38815087"}, "__type": "Arg"}, "20279900": {"name": "children", "uuid": "-idiEVMtj", "__type": "Var"}, "20279901": {"name": "renderable", "params": [], "allowRootWrapper": null, "__type": "RenderableType"}, "20279902": {"variants": [{"__ref": "20279867"}], "args": [], "attrs": {}, "rs": {"__ref": "20279904"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20279904": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280083": {"tag": "div", "name": "section6order1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LApPOCWv_", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280084"}], "__type": "TplTag"}, "20280084": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280085"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280085": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280092": {"tag": "div", "name": "section6order2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2CdFvhm1b", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280093"}], "__type": "TplTag"}, "20280093": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280094"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280094": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280101": {"tag": "div", "name": "section6order3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E93CgQSaT", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "20280102"}], "__type": "TplTag"}, "20280102": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280103"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280103": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280109": {"tpl": [{"__ref": "20280110"}], "__type": "RenderExpr"}, "20280110": {"tag": "div", "name": null, "children": [{"__ref": "20280111"}, {"__ref": "20280126"}, {"__ref": "20280134"}, {"__ref": "38815096"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "s5eOUbZrw", "parent": {"__ref": "20279831"}, "locked": null, "vsettings": [{"__ref": "20280112"}], "__type": "TplTag"}, "20280111": {"tag": "div", "name": "section7order1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "tF9GWxoSZ", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280113"}], "__type": "TplTag"}, "20280112": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280114"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280113": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280115"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280114": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280115": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280126": {"tag": "div", "name": "section7order2", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "lOEVzQ0M0", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280127"}], "__type": "TplTag"}, "20280127": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280128"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280128": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280134": {"tag": "div", "name": "section7order3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "fJPVRdmRw", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "20280135"}], "__type": "TplTag"}, "20280135": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280136"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280136": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280142": {"name": "section8", "component": {"__ref": "20279156"}, "uuid": "CC2hvYL4D", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280143"}], "__type": "TplComponent"}, "20280143": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280144"}, {"__ref": "20280145"}, {"__ref": "20280146"}], "attrs": {}, "rs": {"__ref": "20280147"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280144": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280164"}, "__type": "Arg"}, "20280145": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280166"}, "__type": "Arg"}, "20280146": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280203"}, "__type": "Arg"}, "20280147": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280153": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KlO8gi1xbd", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280155"}], "__type": "TplTag"}, "20280154": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "UX7aYQg6Qm", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280156"}], "__type": "TplTag"}, "20280155": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280157"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280163"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280156": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280159"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280165"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280157": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280159": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280163": {"markers": [], "text": "8 - Add new slot in a branch and move nodes into it", "__type": "RawText"}, "20280164": {"tpl": [{"__ref": "20280153"}], "__type": "RenderExpr"}, "20280165": {"markers": [], "text": "A branch will create a new slot for Section8Comp, and move \"section8tpl\" into it, as well as a new tpl node", "__type": "RawText"}, "20280166": {"tpl": [{"__ref": "20280154"}], "__type": "RenderExpr"}, "20280177": {"uuid": "GK_jsBv920", "name": "Section8Comp", "params": [], "states": [], "tplTree": {"__ref": "20280180"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "20280181"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20280178": {"component": {"__ref": "20280177"}, "matrix": {"__ref": "20280182"}, "customMatrix": {"__ref": "20280183"}, "__type": "ComponentArena"}, "20280179": {"name": "section8comp", "component": {"__ref": "20280177"}, "uuid": "vY7MIDRlPp", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280184"}], "__type": "TplComponent"}, "20280180": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "E_04LajOm", "parent": null, "locked": null, "vsettings": [{"__ref": "20280185"}], "__type": "TplTag"}, "20280181": {"uuid": "Lbxui6QAQ6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280182": {"rows": [{"__ref": "20280186"}], "__type": "ArenaFrameGrid"}, "20280183": {"rows": [{"__ref": "20280187"}], "__type": "ArenaFrameGrid"}, "20280184": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280188"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280185": {"variants": [{"__ref": "20280181"}], "args": [], "attrs": {}, "rs": {"__ref": "20280189"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280186": {"cols": [{"__ref": "20280190"}], "rowKey": null, "__type": "ArenaFrameRow"}, "20280187": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "20280188": {"values": {"position": "relative", "width": "default", "height": "default"}, "mixins": [], "__type": "RuleSet"}, "20280189": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280190": {"frame": {"__ref": "20280199"}, "cellKey": {"__ref": "20280181"}, "__type": "ArenaFrameCell"}, "20280199": {"uuid": "1oXYv4UjyF", "width": 340, "height": 340, "container": {"__ref": "20280200"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20280181"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "20280200": {"name": null, "component": {"__ref": "20280177"}, "uuid": "oOMnkoqDS3", "parent": null, "locked": null, "vsettings": [{"__ref": "20280201"}], "__type": "TplComponent"}, "20280201": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20280202"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280202": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280203": {"tpl": [{"__ref": "20280179"}, {"__ref": "20280204"}], "__type": "RenderExpr"}, "20280204": {"tag": "div", "name": "section8tpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "SAk8QVjj2", "parent": {"__ref": "20280142"}, "locked": null, "vsettings": [{"__ref": "20280205"}], "__type": "TplTag"}, "20280205": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280206"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280206": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "20280212": {"name": "section9", "component": {"__ref": "20279156"}, "uuid": "7fJ1CO8DC", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280213"}], "__type": "TplComponent"}, "20280213": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280214"}, {"__ref": "20280215"}, {"__ref": "20280216"}], "attrs": {}, "rs": {"__ref": "20280217"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280214": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280232"}, "__type": "Arg"}, "20280215": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280235"}, "__type": "Arg"}, "20280216": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280220"}, "__type": "Arg"}, "20280217": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280220": {"tpl": [], "__type": "VirtualRenderExpr"}, "20280223": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "1eaGu3uj6o", "parent": {"__ref": "20280212"}, "locked": null, "vsettings": [{"__ref": "20280225"}], "__type": "TplTag"}, "20280224": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "M_WJwTURop", "parent": {"__ref": "20280212"}, "locked": null, "vsettings": [{"__ref": "20280226"}], "__type": "TplTag"}, "20280225": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280227"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280237"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280226": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280229"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280238"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280227": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280229": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280232": {"tpl": [{"__ref": "20280223"}], "__type": "RenderExpr"}, "20280233": {"uuid": "hbljPOe7V", "name": "Mobile", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "8615003"}, "mediaQuery": "(max-width:640px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280235": {"tpl": [{"__ref": "20280224"}], "__type": "RenderExpr"}, "20280237": {"markers": [], "text": "9 - Add new artboard, target global variant", "__type": "RawText"}, "20280238": {"markers": [], "text": "A branch will add a new artboard, and target the mobile screen variant", "__type": "RawText"}, "20280239": {"name": "section10", "component": {"__ref": "20279156"}, "uuid": "NSy4qU0sLE", "parent": {"__ref": "20279003"}, "locked": null, "vsettings": [{"__ref": "20280240"}], "__type": "TplComponent"}, "20280240": {"variants": [{"__ref": "20279004"}], "args": [{"__ref": "20280241"}, {"__ref": "20280242"}, {"__ref": "20280243"}], "attrs": {}, "rs": {"__ref": "20280244"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280241": {"param": {"__ref": "20279298"}, "expr": {"__ref": "20280245"}, "__type": "Arg"}, "20280242": {"param": {"__ref": "20279279"}, "expr": {"__ref": "20280246"}, "__type": "Arg"}, "20280243": {"param": {"__ref": "20279237"}, "expr": {"__ref": "20280247"}, "__type": "Arg"}, "20280244": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "20280245": {"tpl": [{"__ref": "20280250"}], "__type": "RenderExpr"}, "20280246": {"tpl": [{"__ref": "20280251"}], "__type": "RenderExpr"}, "20280247": {"tpl": [], "__type": "VirtualRenderExpr"}, "20280250": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "8EHJopGzPv", "parent": {"__ref": "20280239"}, "locked": null, "vsettings": [{"__ref": "20280252"}], "__type": "TplTag"}, "20280251": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "dTGs-bqYNY", "parent": {"__ref": "20280239"}, "locked": null, "vsettings": [{"__ref": "20280253"}], "__type": "TplTag"}, "20280252": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280254"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280288"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280253": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "20280256"}, "dataCond": null, "dataRep": null, "text": {"__ref": "20280286"}, "columnsConfig": null, "__type": "VariantSetting"}, "20280254": {"values": {"padding-bottom": "0px", "height": "wrap"}, "mixins": [], "__type": "RuleSet"}, "20280256": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280271": {"uuid": "WYVm22mkEy", "name": "Unnamed Component", "params": [], "states": [], "tplTree": {"__ref": "20280273"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "20280274"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "frame", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "20280272": {"uuid": "E78HznyzT4", "width": 757, "height": 1103, "container": {"__ref": "20280275"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "20280274"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "section10arena2", "top": 1474, "left": 1443, "__type": "ArenaFrame"}, "20280273": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "k90NmA5b1", "parent": null, "locked": null, "vsettings": [{"__ref": "20280276"}], "__type": "TplTag"}, "20280274": {"uuid": "Rd-n5lbGmC", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "20280275": {"name": null, "component": {"__ref": "20280271"}, "uuid": "RJFPghfetr", "parent": null, "locked": null, "vsettings": [{"__ref": "20280277"}], "__type": "TplComponent"}, "20280276": {"variants": [{"__ref": "20280274"}], "args": [], "attrs": {}, "rs": {"__ref": "20280278"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280277": {"variants": [{"__ref": "8615007"}], "args": [], "attrs": {}, "rs": {"__ref": "20280279"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "20280278": {"values": {"display": "block", "position": "relative", "width": "stretch", "height": "stretch"}, "mixins": [], "__type": "RuleSet"}, "20280279": {"values": {}, "mixins": [], "__type": "RuleSet"}, "20280286": {"markers": [], "text": "Each branch will change the name of \"section10arena\" to a different name, creating a harmless conflict", "__type": "RawText"}, "20280288": {"markers": [], "text": "10 - Test harmless conflict with artboard name", "__type": "RawText"}, "38815001": {"tpl": [{"__ref": "20279535"}], "__type": "RenderExpr"}, "38815002": {"tpl": [{"__ref": "20279644"}], "__type": "RenderExpr"}, "38815045": {"tag": "div", "name": "child3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "p3oKjP7-N", "parent": {"__ref": "20279729"}, "locked": null, "vsettings": [{"__ref": "38815047"}], "__type": "TplTag"}, "38815047": {"variants": [{"__ref": "20279698"}], "args": [], "attrs": {}, "rs": {"__ref": "38815050"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "38815050": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "38815070": {"tpl": [{"__ref": "38815071"}, {"__ref": "38815072"}], "__type": "VirtualRenderExpr"}, "38815071": {"tag": "div", "name": "child1", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5K2v-TPzS", "parent": {"__ref": "20279696"}, "locked": null, "vsettings": [{"__ref": "38815073"}], "__type": "TplTag"}, "38815072": {"tag": "div", "name": "child3", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5zptKrKUz5", "parent": {"__ref": "20279696"}, "locked": null, "vsettings": [{"__ref": "38815074"}], "__type": "TplTag"}, "38815073": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "38815075"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "38815074": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "38815076"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "38815075": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "38815076": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "38815087": {"tpl": [{"__ref": "20280083"}, {"__ref": "20280092"}, {"__ref": "20280101"}, {"__ref": "38815088"}], "__type": "RenderExpr"}, "38815088": {"tag": "div", "name": "section6order4", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "AIlIDPvoW", "parent": {"__ref": "20279865"}, "locked": null, "vsettings": [{"__ref": "38815089"}], "__type": "TplTag"}, "38815089": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "38815090"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "38815090": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "38815096": {"tag": "div", "name": "section7order4", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "v339aiDam", "parent": {"__ref": "20280110"}, "locked": null, "vsettings": [{"__ref": "38815097"}], "__type": "TplTag"}, "38815097": {"variants": [{"__ref": "20279004"}], "args": [], "attrs": {}, "rs": {"__ref": "38815098"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "38815098": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start"}, "mixins": [], "__type": "RuleSet"}, "52044001": {"uuid": "tf2H95Pq3t", "name": "hostless-plasmic-head", "params": [{"__ref": "52044003"}, {"__ref": "52044004"}, {"__ref": "52044005"}, {"__ref": "52044006"}], "states": [], "tplTree": {"__ref": "52044007"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "52044008"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "52044009"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "52044002": {"uuid": "wpYNJO6ypD", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "52044010"}, {"__ref": "52044011"}, {"__ref": "52044012"}, {"__ref": "52044013"}, {"__ref": "52044014"}], "states": [], "tplTree": {"__ref": "52044015"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "52044016"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "52044017"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "52044003": {"type": {"__ref": "52044019"}, "variable": {"__ref": "52044018"}, "uuid": "upZwDAbwTl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044004": {"type": {"__ref": "52044021"}, "variable": {"__ref": "52044020"}, "uuid": "Wsr53Wu-4U", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044005": {"type": {"__ref": "52044023"}, "variable": {"__ref": "52044022"}, "uuid": "dvvYk2dynQ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044006": {"type": {"__ref": "52044025"}, "variable": {"__ref": "52044024"}, "uuid": "jgV0pNAwBM", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044007": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "2w3YqO9hPV", "parent": null, "locked": null, "vsettings": [{"__ref": "52044026"}], "__type": "TplTag"}, "52044008": {"uuid": "S1XfBSeiw", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52044009": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "Head", "importName": "PlasmicHead", "description": "Used to add page metadata to HTML <head />.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "52044010": {"type": {"__ref": "52044028"}, "variable": {"__ref": "52044027"}, "uuid": "0ACCnHBko6", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044011": {"type": {"__ref": "52044030"}, "variable": {"__ref": "52044029"}, "uuid": "r7piC3Sx88u", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044012": {"type": {"__ref": "52044032"}, "tplSlot": {"__ref": "52044037"}, "variable": {"__ref": "52044031"}, "uuid": "ktFptf_LgCR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "52044013": {"type": {"__ref": "52044034"}, "variable": {"__ref": "52044033"}, "uuid": "aha-3GvZdpO", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044014": {"type": {"__ref": "52044036"}, "variable": {"__ref": "52044035"}, "uuid": "cJOCxKYET_B", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "52044015": {"tag": "div", "name": null, "children": [{"__ref": "52044037"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "YNljyGUCP3", "parent": null, "locked": null, "vsettings": [{"__ref": "52044038"}], "__type": "TplTag"}, "52044016": {"uuid": "LZpX1editO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "52044017": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "52044018": {"name": "title", "uuid": "ICA7AVWoL2", "__type": "Var"}, "52044019": {"name": "text", "__type": "Text"}, "52044020": {"name": "description", "uuid": "mz-SGD2V8z", "__type": "Var"}, "52044021": {"name": "text", "__type": "Text"}, "52044022": {"name": "image", "uuid": "SxzeekIBHW", "__type": "Var"}, "52044023": {"name": "img", "__type": "Img"}, "52044024": {"name": "canonical", "uuid": "MfPnMlxIrC", "__type": "Var"}, "52044025": {"name": "text", "__type": "Text"}, "52044026": {"variants": [{"__ref": "52044008"}], "args": [], "attrs": {}, "rs": {"__ref": "52044039"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044027": {"name": "dataOp", "uuid": "MuUEM9uPCP", "__type": "Var"}, "52044028": {"name": "any", "__type": "AnyType"}, "52044029": {"name": "name", "uuid": "0lxyDqm2Sz6", "__type": "Var"}, "52044030": {"name": "text", "__type": "Text"}, "52044031": {"name": "children", "uuid": "aCWSIt95_Gm", "__type": "Var"}, "52044032": {"name": "renderFunc", "params": [{"__ref": "52044040"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "52044033": {"name": "pageSize", "uuid": "caL98c6qwUc", "__type": "Var"}, "52044034": {"name": "num", "__type": "<PERSON><PERSON>"}, "52044035": {"name": "pageIndex", "uuid": "cC5YpxckKP-", "__type": "Var"}, "52044036": {"name": "num", "__type": "<PERSON><PERSON>"}, "52044037": {"param": {"__ref": "52044012"}, "defaultContents": [], "uuid": "nmWF57VYyTU", "parent": {"__ref": "52044015"}, "locked": null, "vsettings": [{"__ref": "52044041"}], "__type": "TplSlot"}, "52044038": {"variants": [{"__ref": "52044016"}], "args": [], "attrs": {}, "rs": {"__ref": "52044042"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044039": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "52044040": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "52044045"}, "__type": "ArgType"}, "52044041": {"variants": [{"__ref": "52044016"}], "args": [], "attrs": {}, "rs": {"__ref": "52044046"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "52044042": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "52044045": {"name": "any", "__type": "AnyType"}, "52044046": {"values": {}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "251-add-data-tokens"}}]}