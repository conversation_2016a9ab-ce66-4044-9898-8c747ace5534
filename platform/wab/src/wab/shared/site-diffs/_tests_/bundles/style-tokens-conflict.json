{"branches": [{"id": "fRzR3sYh9t3E2oYyGcUmiq", "name": "test"}], "pkgVersions": [{"id": "b0c142e6-2849-4812-9a7f-fdfa4136bfd2", "data": {"root": "tBjJQuAO7QeI", "map": {"JzfhVTO01Mqa": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "eigGLyrLcSeG": {"name": "Default Typography", "rs": {"__ref": "JzfhVTO01Mqa"}, "preview": null, "uuid": "5bUKBRwx871_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BX-1FYtrSYvq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8q6zX4wLwvVs": {"rs": {"__ref": "BX-1FYtrSYvq"}, "__type": "ThemeLayoutSettings"}, "-Wb2wAf4U3c6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "GjC15MwRZC6k": {"name": "Default \"h1\"", "rs": {"__ref": "-Wb2wAf4U3c6"}, "preview": null, "uuid": "oeqysAUtikAa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EYrpm9KGKTwn": {"selector": "h1", "style": {"__ref": "GjC15MwRZC6k"}, "__type": "ThemeStyle"}, "viTCbgYmauol": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "MslNbm-5V2jV": {"name": "Default \"h2\"", "rs": {"__ref": "viTCbgYmauol"}, "preview": null, "uuid": "B04gRUhEOv6W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PgTsly8lH9Nf": {"selector": "h2", "style": {"__ref": "MslNbm-5V2jV"}, "__type": "ThemeStyle"}, "DVnQDJIMA8Jd": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "TeG09gKUwt4X": {"name": "Default \"h3\"", "rs": {"__ref": "DVnQDJIMA8Jd"}, "preview": null, "uuid": "TSvlcFiFwHrD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "r6B7enMAtDHN": {"selector": "h3", "style": {"__ref": "TeG09gKUwt4X"}, "__type": "ThemeStyle"}, "699Gak-MlEa6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "2eIzVEwrAlpX": {"name": "Default \"h4\"", "rs": {"__ref": "699Gak-MlEa6"}, "preview": null, "uuid": "iIz7i2MKLLrk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "yyXAIl8dw3Bj": {"selector": "h4", "style": {"__ref": "2eIzVEwrAlpX"}, "__type": "ThemeStyle"}, "eo3PRZsvjdDi": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "W_C8Ik37WW3O": {"name": "Default \"h5\"", "rs": {"__ref": "eo3PRZsvjdDi"}, "preview": null, "uuid": "oXBXHJT3TQvl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4iNmm3JunpyH": {"selector": "h5", "style": {"__ref": "W_C8Ik37WW3O"}, "__type": "ThemeStyle"}, "jdvZdxMtZYIv": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "JxWeg2z_8-yL": {"name": "Default \"h6\"", "rs": {"__ref": "jdvZdxMtZYIv"}, "preview": null, "uuid": "YfqCyKo9ZIYd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ctq3Oa1q5kmC": {"selector": "h6", "style": {"__ref": "JxWeg2z_8-yL"}, "__type": "ThemeStyle"}, "sr2HoNy5XBW1": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "lJdTo14qAOFB": {"name": "Default \"a\"", "rs": {"__ref": "sr2HoNy5XBW1"}, "preview": null, "uuid": "Qk3LYLmTua_o", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hyjXufQ0qc3G": {"selector": "a", "style": {"__ref": "lJdTo14qAOFB"}, "__type": "ThemeStyle"}, "ypQkQDzbNVR2": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "EJ9iP_i1bDwS": {"name": "Default \"a:hover\"", "rs": {"__ref": "ypQkQDzbNVR2"}, "preview": null, "uuid": "u6fTnkS7K0yK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "zXYh8smNFTL7": {"selector": "a:hover", "style": {"__ref": "EJ9iP_i1bDwS"}, "__type": "ThemeStyle"}, "RVG1TkPbBciO": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "y4RgGp1gIqJH": {"name": "Default \"blockquote\"", "rs": {"__ref": "RVG1TkPbBciO"}, "preview": null, "uuid": "b0wXZe5s3IOi", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "sEcS2SI9RLpl": {"selector": "blockquote", "style": {"__ref": "y4RgGp1gIqJH"}, "__type": "ThemeStyle"}, "_53erUqlrEQ0": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "qOi73GkBdZsz": {"name": "Default \"code\"", "rs": {"__ref": "_53erUqlrEQ0"}, "preview": null, "uuid": "VP44_7H10EYG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "h-63JEYOUiQg": {"selector": "code", "style": {"__ref": "qOi73GkBdZsz"}, "__type": "ThemeStyle"}, "AMVny7260ssk": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "cD057gsr7zDu": {"name": "Default \"pre\"", "rs": {"__ref": "AMVny7260ssk"}, "preview": null, "uuid": "yqAZAyWcYL3v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "gzLy8K5nMCX9": {"selector": "pre", "style": {"__ref": "cD057gsr7zDu"}, "__type": "ThemeStyle"}, "pxJz_9JyMbWw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "qdNo7IKBKa92": {"name": "Default \"ol\"", "rs": {"__ref": "pxJz_9JyMbWw"}, "preview": null, "uuid": "DiOjaJCHq8oa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7YCpWdsUCQjX": {"selector": "ol", "style": {"__ref": "qdNo7IKBKa92"}, "__type": "ThemeStyle"}, "AOH-PyMc1ob_": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "rboXbUqXM9US": {"name": "Default \"ul\"", "rs": {"__ref": "AOH-PyMc1ob_"}, "preview": null, "uuid": "mZuScmuXOdnG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jL36hnK0qXVZ": {"selector": "ul", "style": {"__ref": "rboXbUqXM9US"}, "__type": "ThemeStyle"}, "Di4KTB1WGAZt": {"defaultStyle": {"__ref": "eigGLyrLcSeG"}, "styles": [{"__ref": "EYrpm9KGKTwn"}, {"__ref": "PgTsly8lH9Nf"}, {"__ref": "r6B7enMAtDHN"}, {"__ref": "yyXAIl8dw3Bj"}, {"__ref": "4iNmm3JunpyH"}, {"__ref": "Ctq3Oa1q5kmC"}, {"__ref": "hyjXufQ0qc3G"}, {"__ref": "zXYh8smNFTL7"}, {"__ref": "sEcS2SI9RLpl"}, {"__ref": "h-63JEYOUiQg"}, {"__ref": "gzLy8K5nMCX9"}, {"__ref": "7YCpWdsUCQjX"}, {"__ref": "jL36hnK0qXVZ"}], "layout": {"__ref": "8q6zX4wLwvVs"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "Ht2_mnOWN4DN": {"name": "text", "__type": "Text"}, "jrzZ74Pwdlwc": {"name": "Screen", "uuid": "RAaUMp_DkLKj", "__type": "Var"}, "ely1HQibfdQ7": {"type": {"__ref": "Ht2_mnOWN4DN"}, "variable": {"__ref": "jrzZ74Pwdlwc"}, "uuid": "d9li97kwZU75", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "aGcTdVtAM5X9": {"type": "global-screen", "param": {"__ref": "ely1HQibfdQ7"}, "uuid": "QUSa-EiV6INB", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "u1X-vg-VO0Ac": {"uuid": "yO8XGGsQMaa6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WIjomGShzm36": {"components": [{"__ref": "iDVQEnXDQ7o7"}, {"__ref": "F_d9L-fvVYyV"}, {"__ref": "VPQPTgBcDtyW"}], "arenas": [], "pageArenas": [{"__ref": "4VGm2j5r_gNF"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "aGcTdVtAM5X9"}], "userManagedFonts": [], "globalVariant": {"__ref": "u1X-vg-VO0Ac"}, "styleTokens": [{"__ref": "2tEyg1FMcCWM"}], "mixins": [], "themes": [{"__ref": "Di4KTB1WGAZt"}], "activeTheme": {"__ref": "Di4KTB1WGAZt"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "aGcTdVtAM5X9"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "iDVQEnXDQ7o7": {"uuid": "_JIRQ2EcmtLp", "name": "hostless-plasmic-head", "params": [{"__ref": "ijxbm0FnPZtU"}, {"__ref": "xm41uhJ9hB8N"}, {"__ref": "-81tlEUrAWTG"}, {"__ref": "G6hwgT6B5PBS"}], "states": [], "tplTree": {"__ref": "J3tfGnnBr-Vx"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "VVZJw5i24D-3"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "haEQrE2Lnupq"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "F_d9L-fvVYyV": {"uuid": "rU-rji0HG7v2", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "Fvmfvud2xzlF"}, {"__ref": "O3c3es9a8m9i"}, {"__ref": "0Omwy8lMUYxo"}, {"__ref": "1Q5htq5E_z_o"}, {"__ref": "R0bZCL25YX82"}], "states": [], "tplTree": {"__ref": "E4JJX3jQyOkO"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "unI6eq9ANZHR"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "VEM744OkjdvA"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "ijxbm0FnPZtU": {"type": {"__ref": "JqBHMKZ47aPh"}, "variable": {"__ref": "bhhuM5HU0rud"}, "uuid": "-NRJqtUZVoxb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "xm41uhJ9hB8N": {"type": {"__ref": "yx54lVYPxLYB"}, "variable": {"__ref": "Dr3cDkCgA0A5"}, "uuid": "SwIT-vh4RByU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-81tlEUrAWTG": {"type": {"__ref": "WCYh_cf3Sohy"}, "variable": {"__ref": "eX25NVMSLdmZ"}, "uuid": "YBiHKQzszM95", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "G6hwgT6B5PBS": {"type": {"__ref": "6y8qFKZTnXe-"}, "variable": {"__ref": "AwcDfpCreZg6"}, "uuid": "u_bgz_tm3HhG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "J3tfGnnBr-Vx": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5P4NPtkm6K3y", "parent": null, "locked": null, "vsettings": [{"__ref": "fNC65c22vEsZ"}], "__type": "TplTag"}, "VVZJw5i24D-3": {"uuid": "zirAAe8XsdNo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "haEQrE2Lnupq": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "Fvmfvud2xzlF": {"type": {"__ref": "q6r05UR3mlFM"}, "variable": {"__ref": "lHaVwLszEcdy"}, "uuid": "ybTlDVFXMSJE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "O3c3es9a8m9i": {"type": {"__ref": "eVfe-8MhxSJN"}, "variable": {"__ref": "Woghnq175Tqw"}, "uuid": "t0SAqbVJXX9G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "0Omwy8lMUYxo": {"type": {"__ref": "S1qIGI3hbeKf"}, "tplSlot": {"__ref": "ROOaubntXRI1"}, "variable": {"__ref": "kA6vOLVW_QZX"}, "uuid": "fsBoJbF6SzoL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "1Q5htq5E_z_o": {"type": {"__ref": "DyRIHMh7BJHq"}, "variable": {"__ref": "5gJyEm-3Hjdb"}, "uuid": "GjXPXDG_o-vC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R0bZCL25YX82": {"type": {"__ref": "vkw-Doi3zHEh"}, "variable": {"__ref": "H4gyuGywSu2B"}, "uuid": "VADrJrHrT6N7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "E4JJX3jQyOkO": {"tag": "div", "name": null, "children": [{"__ref": "ROOaubntXRI1"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "R9baojnX6bMd", "parent": null, "locked": null, "vsettings": [{"__ref": "HnEpFmI_Plu4"}], "__type": "TplTag"}, "unI6eq9ANZHR": {"uuid": "oozYR432Oq6A", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "VEM744OkjdvA": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "JqBHMKZ47aPh": {"name": "text", "__type": "Text"}, "bhhuM5HU0rud": {"name": "title", "uuid": "RJCp5jH3hhcJ", "__type": "Var"}, "yx54lVYPxLYB": {"name": "text", "__type": "Text"}, "Dr3cDkCgA0A5": {"name": "description", "uuid": "v-XsuiBozNyo", "__type": "Var"}, "WCYh_cf3Sohy": {"name": "img", "__type": "Img"}, "eX25NVMSLdmZ": {"name": "image", "uuid": "7WUgB5rVeatS", "__type": "Var"}, "6y8qFKZTnXe-": {"name": "text", "__type": "Text"}, "AwcDfpCreZg6": {"name": "canonical", "uuid": "mY6IGaDIHc0R", "__type": "Var"}, "fNC65c22vEsZ": {"variants": [{"__ref": "VVZJw5i24D-3"}], "args": [], "attrs": {}, "rs": {"__ref": "zSI487nvXaj2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "q6r05UR3mlFM": {"name": "any", "__type": "AnyType"}, "lHaVwLszEcdy": {"name": "dataOp", "uuid": "QC1LbnpUOPf8", "__type": "Var"}, "eVfe-8MhxSJN": {"name": "text", "__type": "Text"}, "Woghnq175Tqw": {"name": "name", "uuid": "Yw9HsM1b-oRu", "__type": "Var"}, "S1qIGI3hbeKf": {"name": "renderFunc", "params": [{"__ref": "gDig8cIM4xsM"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "kA6vOLVW_QZX": {"name": "children", "uuid": "pyVVCZOM_v8e", "__type": "Var"}, "DyRIHMh7BJHq": {"name": "num", "__type": "<PERSON><PERSON>"}, "5gJyEm-3Hjdb": {"name": "pageSize", "uuid": "GPvc204pK0PF", "__type": "Var"}, "vkw-Doi3zHEh": {"name": "num", "__type": "<PERSON><PERSON>"}, "H4gyuGywSu2B": {"name": "pageIndex", "uuid": "b8jAfaHHdrE5", "__type": "Var"}, "ROOaubntXRI1": {"param": {"__ref": "0Omwy8lMUYxo"}, "defaultContents": [], "uuid": "VBEd-zHdOt26", "parent": {"__ref": "E4JJX3jQyOkO"}, "locked": null, "vsettings": [{"__ref": "Saf5Qdnjv4mz"}], "__type": "TplSlot"}, "HnEpFmI_Plu4": {"variants": [{"__ref": "unI6eq9ANZHR"}], "args": [], "attrs": {}, "rs": {"__ref": "yFWKxthBITwQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zSI487nvXaj2": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "gDig8cIM4xsM": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "KRmEtOdJEZcX"}, "__type": "ArgType"}, "Saf5Qdnjv4mz": {"variants": [{"__ref": "unI6eq9ANZHR"}], "args": [], "attrs": {}, "rs": {"__ref": "4UFm-zBI3Li6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yFWKxthBITwQ": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "KRmEtOdJEZcX": {"name": "any", "__type": "AnyType"}, "4UFm-zBI3Li6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VPQPTgBcDtyW": {"uuid": "UOWfPevr7k00", "name": "NewPage", "params": [], "states": [], "tplTree": {"__ref": "C-dm8ZnQ7Rwx"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "tIswJwLMnCfB"}], "variantGroups": [], "pageMeta": {"__ref": "u8K4Kx8rIrxK"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "4VGm2j5r_gNF": {"component": {"__ref": "VPQPTgBcDtyW"}, "matrix": {"__ref": "puWtSELi3Dh6"}, "customMatrix": {"__ref": "BrpToF9Am06H"}, "__type": "PageArena"}, "C-dm8ZnQ7Rwx": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "RNObPkn8mUQk", "parent": null, "locked": null, "vsettings": [{"__ref": "Jg83VV9FYdfb"}], "__type": "TplTag"}, "tIswJwLMnCfB": {"uuid": "mNIe6nBHm5iO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "u8K4Kx8rIrxK": {"path": "/new-page", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "puWtSELi3Dh6": {"rows": [{"__ref": "6uBkVnD1KPfx"}], "__type": "ArenaFrameGrid"}, "BrpToF9Am06H": {"rows": [{"__ref": "UJJpzWphTYGS"}], "__type": "ArenaFrameGrid"}, "Jg83VV9FYdfb": {"variants": [{"__ref": "tIswJwLMnCfB"}], "args": [], "attrs": {}, "rs": {"__ref": "4-RKhv27v4qd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6uBkVnD1KPfx": {"cols": [{"__ref": "qtLU4R6BQHki"}, {"__ref": "sMNr4GTdXuvO"}], "rowKey": {"__ref": "tIswJwLMnCfB"}, "__type": "ArenaFrameRow"}, "UJJpzWphTYGS": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "4-RKhv27v4qd": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "qtLU4R6BQHki": {"frame": {"__ref": "62KX7VUsJBGU"}, "cellKey": null, "__type": "ArenaFrameCell"}, "sMNr4GTdXuvO": {"frame": {"__ref": "dkzTfKMdhZiz"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62KX7VUsJBGU": {"uuid": "b4-h862E_9aR", "width": 1366, "height": 768, "container": {"__ref": "tWs-LyxsPG4i"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tIswJwLMnCfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "dkzTfKMdhZiz": {"uuid": "0ynsxQDC1VN5", "width": 414, "height": 736, "container": {"__ref": "RXEeg_VdeOJ_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tIswJwLMnCfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "tWs-LyxsPG4i": {"name": null, "component": {"__ref": "VPQPTgBcDtyW"}, "uuid": "W3SzpGUw9zy2", "parent": null, "locked": null, "vsettings": [{"__ref": "egTdzk_QokyF"}], "__type": "TplComponent"}, "RXEeg_VdeOJ_": {"name": null, "component": {"__ref": "VPQPTgBcDtyW"}, "uuid": "9SV1eeAgMurD", "parent": null, "locked": null, "vsettings": [{"__ref": "7x694qGUuR9F"}], "__type": "TplComponent"}, "egTdzk_QokyF": {"variants": [{"__ref": "u1X-vg-VO0Ac"}], "args": [], "attrs": {}, "rs": {"__ref": "deAjS-6PrZ1f"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7x694qGUuR9F": {"variants": [{"__ref": "u1X-vg-VO0Ac"}], "args": [], "attrs": {}, "rs": {"__ref": "dGDakIDK8E_u"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "deAjS-6PrZ1f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dGDakIDK8E_u": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2tEyg1FMcCWM": {"name": "style-token-registered", "type": "Color", "uuid": "pYGNpg_j4GgZ", "value": "black", "variantedValues": [], "isRegistered": true, "regKey": "style-token-registered", "__type": "StyleToken"}, "tBjJQuAO7QeI": {"uuid": "pYw0t77ciq1K", "pkgId": "469da0bb-cbf1-4911-a7de-e68719c3bcc5", "projectId": "jktPeioPm3Q1RvAsGWDSCe", "version": "3.0.0", "name": "Untitled Project", "site": {"__ref": "WIjomGShzm36"}, "__type": "ProjectDependency"}}, "deps": [], "version": "251-add-data-tokens"}, "projectId": "jktPeioPm3Q1RvAsGWDSCe", "version": "3.0.0", "branchId": "main"}], "project": {"id": "jktPeioPm3Q1RvAsGWDSCe", "name": "Untitled Project", "commitGraph": {"parents": {"01915151-812f-4869-9665-c659192c28fe": ["31d02852-f009-44c6-9d6a-5f6becec8fa3"], "2c2b411c-d899-40c1-a454-70095c59dac7": ["ee84139d-b376-4cd7-8e26-4d793ddc66f8", "8d60d153-6a68-4253-a64d-27f7e9e99bf6"], "31d02852-f009-44c6-9d6a-5f6becec8fa3": ["2c2b411c-d899-40c1-a454-70095c59dac7"], "57c2899b-76a2-4241-9fc6-caae499f7dee": [], "8d60d153-6a68-4253-a64d-27f7e9e99bf6": ["57c2899b-76a2-4241-9fc6-caae499f7dee"], "b0c142e6-2849-4812-9a7f-fdfa4136bfd2": ["b270e147-0828-4f2d-b51f-6019cc7847df"], "b270e147-0828-4f2d-b51f-6019cc7847df": ["01915151-812f-4869-9665-c659192c28fe"], "ee84139d-b376-4cd7-8e26-4d793ddc66f8": ["57c2899b-76a2-4241-9fc6-caae499f7dee"]}, "branches": {"main": "b0c142e6-2849-4812-9a7f-fdfa4136bfd2", "48LfpZfREcRxefsxjZ7muj": "8d60d153-6a68-4253-a64d-27f7e9e99bf6", "fRzR3sYh9t3E2oYyGcUmiq": "b0c142e6-2849-4812-9a7f-fdfa4136bfd2", "fT7TSYNSS4UzrXku4VsKTq": "01915151-812f-4869-9665-c659192c28fe", "neQCqcEZ6u3U1QKXW1NEkL": "b270e147-0828-4f2d-b51f-6019cc7847df", "wi9bZBq9DyqXmHY5JScCTS": "31d02852-f009-44c6-9d6a-5f6becec8fa3"}}}, "revisions": [{"branchId": "fRzR3sYh9t3E2oYyGcUmiq", "data": {"root": "WIjomGShzm36", "map": {"JzfhVTO01Mqa": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "eigGLyrLcSeG": {"name": "Default Typography", "rs": {"__ref": "JzfhVTO01Mqa"}, "preview": null, "uuid": "5bUKBRwx871_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BX-1FYtrSYvq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8q6zX4wLwvVs": {"rs": {"__ref": "BX-1FYtrSYvq"}, "__type": "ThemeLayoutSettings"}, "-Wb2wAf4U3c6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "GjC15MwRZC6k": {"name": "Default \"h1\"", "rs": {"__ref": "-Wb2wAf4U3c6"}, "preview": null, "uuid": "oeqysAUtikAa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EYrpm9KGKTwn": {"selector": "h1", "style": {"__ref": "GjC15MwRZC6k"}, "__type": "ThemeStyle"}, "viTCbgYmauol": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "MslNbm-5V2jV": {"name": "Default \"h2\"", "rs": {"__ref": "viTCbgYmauol"}, "preview": null, "uuid": "B04gRUhEOv6W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PgTsly8lH9Nf": {"selector": "h2", "style": {"__ref": "MslNbm-5V2jV"}, "__type": "ThemeStyle"}, "DVnQDJIMA8Jd": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "TeG09gKUwt4X": {"name": "Default \"h3\"", "rs": {"__ref": "DVnQDJIMA8Jd"}, "preview": null, "uuid": "TSvlcFiFwHrD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "r6B7enMAtDHN": {"selector": "h3", "style": {"__ref": "TeG09gKUwt4X"}, "__type": "ThemeStyle"}, "699Gak-MlEa6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "2eIzVEwrAlpX": {"name": "Default \"h4\"", "rs": {"__ref": "699Gak-MlEa6"}, "preview": null, "uuid": "iIz7i2MKLLrk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "yyXAIl8dw3Bj": {"selector": "h4", "style": {"__ref": "2eIzVEwrAlpX"}, "__type": "ThemeStyle"}, "eo3PRZsvjdDi": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "W_C8Ik37WW3O": {"name": "Default \"h5\"", "rs": {"__ref": "eo3PRZsvjdDi"}, "preview": null, "uuid": "oXBXHJT3TQvl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4iNmm3JunpyH": {"selector": "h5", "style": {"__ref": "W_C8Ik37WW3O"}, "__type": "ThemeStyle"}, "jdvZdxMtZYIv": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "JxWeg2z_8-yL": {"name": "Default \"h6\"", "rs": {"__ref": "jdvZdxMtZYIv"}, "preview": null, "uuid": "YfqCyKo9ZIYd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ctq3Oa1q5kmC": {"selector": "h6", "style": {"__ref": "JxWeg2z_8-yL"}, "__type": "ThemeStyle"}, "sr2HoNy5XBW1": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "lJdTo14qAOFB": {"name": "Default \"a\"", "rs": {"__ref": "sr2HoNy5XBW1"}, "preview": null, "uuid": "Qk3LYLmTua_o", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hyjXufQ0qc3G": {"selector": "a", "style": {"__ref": "lJdTo14qAOFB"}, "__type": "ThemeStyle"}, "ypQkQDzbNVR2": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "EJ9iP_i1bDwS": {"name": "Default \"a:hover\"", "rs": {"__ref": "ypQkQDzbNVR2"}, "preview": null, "uuid": "u6fTnkS7K0yK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "zXYh8smNFTL7": {"selector": "a:hover", "style": {"__ref": "EJ9iP_i1bDwS"}, "__type": "ThemeStyle"}, "RVG1TkPbBciO": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "y4RgGp1gIqJH": {"name": "Default \"blockquote\"", "rs": {"__ref": "RVG1TkPbBciO"}, "preview": null, "uuid": "b0wXZe5s3IOi", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "sEcS2SI9RLpl": {"selector": "blockquote", "style": {"__ref": "y4RgGp1gIqJH"}, "__type": "ThemeStyle"}, "_53erUqlrEQ0": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "qOi73GkBdZsz": {"name": "Default \"code\"", "rs": {"__ref": "_53erUqlrEQ0"}, "preview": null, "uuid": "VP44_7H10EYG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "h-63JEYOUiQg": {"selector": "code", "style": {"__ref": "qOi73GkBdZsz"}, "__type": "ThemeStyle"}, "AMVny7260ssk": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "cD057gsr7zDu": {"name": "Default \"pre\"", "rs": {"__ref": "AMVny7260ssk"}, "preview": null, "uuid": "yqAZAyWcYL3v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "gzLy8K5nMCX9": {"selector": "pre", "style": {"__ref": "cD057gsr7zDu"}, "__type": "ThemeStyle"}, "pxJz_9JyMbWw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "qdNo7IKBKa92": {"name": "Default \"ol\"", "rs": {"__ref": "pxJz_9JyMbWw"}, "preview": null, "uuid": "DiOjaJCHq8oa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7YCpWdsUCQjX": {"selector": "ol", "style": {"__ref": "qdNo7IKBKa92"}, "__type": "ThemeStyle"}, "AOH-PyMc1ob_": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "rboXbUqXM9US": {"name": "Default \"ul\"", "rs": {"__ref": "AOH-PyMc1ob_"}, "preview": null, "uuid": "mZuScmuXOdnG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jL36hnK0qXVZ": {"selector": "ul", "style": {"__ref": "rboXbUqXM9US"}, "__type": "ThemeStyle"}, "Di4KTB1WGAZt": {"defaultStyle": {"__ref": "eigGLyrLcSeG"}, "styles": [{"__ref": "EYrpm9KGKTwn"}, {"__ref": "PgTsly8lH9Nf"}, {"__ref": "r6B7enMAtDHN"}, {"__ref": "yyXAIl8dw3Bj"}, {"__ref": "4iNmm3JunpyH"}, {"__ref": "Ctq3Oa1q5kmC"}, {"__ref": "hyjXufQ0qc3G"}, {"__ref": "zXYh8smNFTL7"}, {"__ref": "sEcS2SI9RLpl"}, {"__ref": "h-63JEYOUiQg"}, {"__ref": "gzLy8K5nMCX9"}, {"__ref": "7YCpWdsUCQjX"}, {"__ref": "jL36hnK0qXVZ"}], "layout": {"__ref": "8q6zX4wLwvVs"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "Ht2_mnOWN4DN": {"name": "text", "__type": "Text"}, "jrzZ74Pwdlwc": {"name": "Screen", "uuid": "RAaUMp_DkLKj", "__type": "Var"}, "ely1HQibfdQ7": {"type": {"__ref": "Ht2_mnOWN4DN"}, "variable": {"__ref": "jrzZ74Pwdlwc"}, "uuid": "d9li97kwZU75", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "aGcTdVtAM5X9": {"type": "global-screen", "param": {"__ref": "ely1HQibfdQ7"}, "uuid": "QUSa-EiV6INB", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "u1X-vg-VO0Ac": {"uuid": "yO8XGGsQMaa6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WIjomGShzm36": {"components": [{"__ref": "iDVQEnXDQ7o7"}, {"__ref": "F_d9L-fvVYyV"}, {"__ref": "VPQPTgBcDtyW"}], "arenas": [], "pageArenas": [{"__ref": "4VGm2j5r_gNF"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "aGcTdVtAM5X9"}], "userManagedFonts": [], "globalVariant": {"__ref": "u1X-vg-VO0Ac"}, "styleTokens": [{"__ref": "MjmV18_PN72S"}], "mixins": [], "themes": [{"__ref": "Di4KTB1WGAZt"}], "activeTheme": {"__ref": "Di4KTB1WGAZt"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "aGcTdVtAM5X9"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "iDVQEnXDQ7o7": {"uuid": "_JIRQ2EcmtLp", "name": "hostless-plasmic-head", "params": [{"__ref": "ijxbm0FnPZtU"}, {"__ref": "xm41uhJ9hB8N"}, {"__ref": "-81tlEUrAWTG"}, {"__ref": "G6hwgT6B5PBS"}], "states": [], "tplTree": {"__ref": "J3tfGnnBr-Vx"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "VVZJw5i24D-3"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "haEQrE2Lnupq"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "F_d9L-fvVYyV": {"uuid": "rU-rji0HG7v2", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "Fvmfvud2xzlF"}, {"__ref": "O3c3es9a8m9i"}, {"__ref": "0Omwy8lMUYxo"}, {"__ref": "1Q5htq5E_z_o"}, {"__ref": "R0bZCL25YX82"}], "states": [], "tplTree": {"__ref": "E4JJX3jQyOkO"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "unI6eq9ANZHR"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "VEM744OkjdvA"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "ijxbm0FnPZtU": {"type": {"__ref": "JqBHMKZ47aPh"}, "variable": {"__ref": "bhhuM5HU0rud"}, "uuid": "-NRJqtUZVoxb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "xm41uhJ9hB8N": {"type": {"__ref": "yx54lVYPxLYB"}, "variable": {"__ref": "Dr3cDkCgA0A5"}, "uuid": "SwIT-vh4RByU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-81tlEUrAWTG": {"type": {"__ref": "WCYh_cf3Sohy"}, "variable": {"__ref": "eX25NVMSLdmZ"}, "uuid": "YBiHKQzszM95", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "G6hwgT6B5PBS": {"type": {"__ref": "6y8qFKZTnXe-"}, "variable": {"__ref": "AwcDfpCreZg6"}, "uuid": "u_bgz_tm3HhG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "J3tfGnnBr-Vx": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5P4NPtkm6K3y", "parent": null, "locked": null, "vsettings": [{"__ref": "fNC65c22vEsZ"}], "__type": "TplTag"}, "VVZJw5i24D-3": {"uuid": "zirAAe8XsdNo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "haEQrE2Lnupq": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "Fvmfvud2xzlF": {"type": {"__ref": "q6r05UR3mlFM"}, "variable": {"__ref": "lHaVwLszEcdy"}, "uuid": "ybTlDVFXMSJE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "O3c3es9a8m9i": {"type": {"__ref": "eVfe-8MhxSJN"}, "variable": {"__ref": "Woghnq175Tqw"}, "uuid": "t0SAqbVJXX9G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "0Omwy8lMUYxo": {"type": {"__ref": "S1qIGI3hbeKf"}, "tplSlot": {"__ref": "ROOaubntXRI1"}, "variable": {"__ref": "kA6vOLVW_QZX"}, "uuid": "fsBoJbF6SzoL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "1Q5htq5E_z_o": {"type": {"__ref": "DyRIHMh7BJHq"}, "variable": {"__ref": "5gJyEm-3Hjdb"}, "uuid": "GjXPXDG_o-vC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R0bZCL25YX82": {"type": {"__ref": "vkw-Doi3zHEh"}, "variable": {"__ref": "H4gyuGywSu2B"}, "uuid": "VADrJrHrT6N7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "E4JJX3jQyOkO": {"tag": "div", "name": null, "children": [{"__ref": "ROOaubntXRI1"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "R9baojnX6bMd", "parent": null, "locked": null, "vsettings": [{"__ref": "HnEpFmI_Plu4"}], "__type": "TplTag"}, "unI6eq9ANZHR": {"uuid": "oozYR432Oq6A", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "VEM744OkjdvA": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "JqBHMKZ47aPh": {"name": "text", "__type": "Text"}, "bhhuM5HU0rud": {"name": "title", "uuid": "RJCp5jH3hhcJ", "__type": "Var"}, "yx54lVYPxLYB": {"name": "text", "__type": "Text"}, "Dr3cDkCgA0A5": {"name": "description", "uuid": "v-XsuiBozNyo", "__type": "Var"}, "WCYh_cf3Sohy": {"name": "img", "__type": "Img"}, "eX25NVMSLdmZ": {"name": "image", "uuid": "7WUgB5rVeatS", "__type": "Var"}, "6y8qFKZTnXe-": {"name": "text", "__type": "Text"}, "AwcDfpCreZg6": {"name": "canonical", "uuid": "mY6IGaDIHc0R", "__type": "Var"}, "fNC65c22vEsZ": {"variants": [{"__ref": "VVZJw5i24D-3"}], "args": [], "attrs": {}, "rs": {"__ref": "zSI487nvXaj2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "q6r05UR3mlFM": {"name": "any", "__type": "AnyType"}, "lHaVwLszEcdy": {"name": "dataOp", "uuid": "QC1LbnpUOPf8", "__type": "Var"}, "eVfe-8MhxSJN": {"name": "text", "__type": "Text"}, "Woghnq175Tqw": {"name": "name", "uuid": "Yw9HsM1b-oRu", "__type": "Var"}, "S1qIGI3hbeKf": {"name": "renderFunc", "params": [{"__ref": "gDig8cIM4xsM"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "kA6vOLVW_QZX": {"name": "children", "uuid": "pyVVCZOM_v8e", "__type": "Var"}, "DyRIHMh7BJHq": {"name": "num", "__type": "<PERSON><PERSON>"}, "5gJyEm-3Hjdb": {"name": "pageSize", "uuid": "GPvc204pK0PF", "__type": "Var"}, "vkw-Doi3zHEh": {"name": "num", "__type": "<PERSON><PERSON>"}, "H4gyuGywSu2B": {"name": "pageIndex", "uuid": "b8jAfaHHdrE5", "__type": "Var"}, "ROOaubntXRI1": {"param": {"__ref": "0Omwy8lMUYxo"}, "defaultContents": [], "uuid": "VBEd-zHdOt26", "parent": {"__ref": "E4JJX3jQyOkO"}, "locked": null, "vsettings": [{"__ref": "Saf5Qdnjv4mz"}], "__type": "TplSlot"}, "HnEpFmI_Plu4": {"variants": [{"__ref": "unI6eq9ANZHR"}], "args": [], "attrs": {}, "rs": {"__ref": "yFWKxthBITwQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zSI487nvXaj2": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "gDig8cIM4xsM": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "KRmEtOdJEZcX"}, "__type": "ArgType"}, "Saf5Qdnjv4mz": {"variants": [{"__ref": "unI6eq9ANZHR"}], "args": [], "attrs": {}, "rs": {"__ref": "4UFm-zBI3Li6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yFWKxthBITwQ": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "KRmEtOdJEZcX": {"name": "any", "__type": "AnyType"}, "4UFm-zBI3Li6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VPQPTgBcDtyW": {"uuid": "UOWfPevr7k00", "name": "NewPage", "params": [], "states": [], "tplTree": {"__ref": "C-dm8ZnQ7Rwx"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "tIswJwLMnCfB"}], "variantGroups": [], "pageMeta": {"__ref": "u8K4Kx8rIrxK"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "4VGm2j5r_gNF": {"component": {"__ref": "VPQPTgBcDtyW"}, "matrix": {"__ref": "puWtSELi3Dh6"}, "customMatrix": {"__ref": "BrpToF9Am06H"}, "__type": "PageArena"}, "C-dm8ZnQ7Rwx": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "RNObPkn8mUQk", "parent": null, "locked": null, "vsettings": [{"__ref": "Jg83VV9FYdfb"}], "__type": "TplTag"}, "tIswJwLMnCfB": {"uuid": "mNIe6nBHm5iO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "u8K4Kx8rIrxK": {"path": "/new-page", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "puWtSELi3Dh6": {"rows": [{"__ref": "6uBkVnD1KPfx"}], "__type": "ArenaFrameGrid"}, "BrpToF9Am06H": {"rows": [{"__ref": "UJJpzWphTYGS"}], "__type": "ArenaFrameGrid"}, "Jg83VV9FYdfb": {"variants": [{"__ref": "tIswJwLMnCfB"}], "args": [], "attrs": {}, "rs": {"__ref": "4-RKhv27v4qd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6uBkVnD1KPfx": {"cols": [{"__ref": "qtLU4R6BQHki"}, {"__ref": "sMNr4GTdXuvO"}], "rowKey": {"__ref": "tIswJwLMnCfB"}, "__type": "ArenaFrameRow"}, "UJJpzWphTYGS": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "4-RKhv27v4qd": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "qtLU4R6BQHki": {"frame": {"__ref": "62KX7VUsJBGU"}, "cellKey": null, "__type": "ArenaFrameCell"}, "sMNr4GTdXuvO": {"frame": {"__ref": "dkzTfKMdhZiz"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62KX7VUsJBGU": {"uuid": "b4-h862E_9aR", "width": 1366, "height": 768, "container": {"__ref": "tWs-LyxsPG4i"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tIswJwLMnCfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "dkzTfKMdhZiz": {"uuid": "0ynsxQDC1VN5", "width": 414, "height": 736, "container": {"__ref": "RXEeg_VdeOJ_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tIswJwLMnCfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "tWs-LyxsPG4i": {"name": null, "component": {"__ref": "VPQPTgBcDtyW"}, "uuid": "W3SzpGUw9zy2", "parent": null, "locked": null, "vsettings": [{"__ref": "egTdzk_QokyF"}], "__type": "TplComponent"}, "RXEeg_VdeOJ_": {"name": null, "component": {"__ref": "VPQPTgBcDtyW"}, "uuid": "9SV1eeAgMurD", "parent": null, "locked": null, "vsettings": [{"__ref": "7x694qGUuR9F"}], "__type": "TplComponent"}, "egTdzk_QokyF": {"variants": [{"__ref": "u1X-vg-VO0Ac"}], "args": [], "attrs": {}, "rs": {"__ref": "deAjS-6PrZ1f"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7x694qGUuR9F": {"variants": [{"__ref": "u1X-vg-VO0Ac"}], "args": [], "attrs": {}, "rs": {"__ref": "dGDakIDK8E_u"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "deAjS-6PrZ1f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dGDakIDK8E_u": {"values": {}, "mixins": [], "__type": "RuleSet"}, "MjmV18_PN72S": {"name": "style-token-registered", "type": "Color", "uuid": "TluYLP4jDc8J", "value": "white", "variantedValues": [], "isRegistered": true, "regKey": "style-token-registered", "__type": "StyleToken"}}, "deps": [], "version": "251-add-data-tokens"}}, {"branchId": "main", "data": {"root": "WIjomGShzm36", "map": {"JzfhVTO01Mqa": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "eigGLyrLcSeG": {"name": "Default Typography", "rs": {"__ref": "JzfhVTO01Mqa"}, "preview": null, "uuid": "5bUKBRwx871_", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "BX-1FYtrSYvq": {"values": {}, "mixins": [], "__type": "RuleSet"}, "8q6zX4wLwvVs": {"rs": {"__ref": "BX-1FYtrSYvq"}, "__type": "ThemeLayoutSettings"}, "-Wb2wAf4U3c6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "GjC15MwRZC6k": {"name": "Default \"h1\"", "rs": {"__ref": "-Wb2wAf4U3c6"}, "preview": null, "uuid": "oeqysAUtikAa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "EYrpm9KGKTwn": {"selector": "h1", "style": {"__ref": "GjC15MwRZC6k"}, "__type": "ThemeStyle"}, "viTCbgYmauol": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "MslNbm-5V2jV": {"name": "Default \"h2\"", "rs": {"__ref": "viTCbgYmauol"}, "preview": null, "uuid": "B04gRUhEOv6W", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "PgTsly8lH9Nf": {"selector": "h2", "style": {"__ref": "MslNbm-5V2jV"}, "__type": "ThemeStyle"}, "DVnQDJIMA8Jd": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "TeG09gKUwt4X": {"name": "Default \"h3\"", "rs": {"__ref": "DVnQDJIMA8Jd"}, "preview": null, "uuid": "TSvlcFiFwHrD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "r6B7enMAtDHN": {"selector": "h3", "style": {"__ref": "TeG09gKUwt4X"}, "__type": "ThemeStyle"}, "699Gak-MlEa6": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "2eIzVEwrAlpX": {"name": "Default \"h4\"", "rs": {"__ref": "699Gak-MlEa6"}, "preview": null, "uuid": "iIz7i2MKLLrk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "yyXAIl8dw3Bj": {"selector": "h4", "style": {"__ref": "2eIzVEwrAlpX"}, "__type": "ThemeStyle"}, "eo3PRZsvjdDi": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "W_C8Ik37WW3O": {"name": "Default \"h5\"", "rs": {"__ref": "eo3PRZsvjdDi"}, "preview": null, "uuid": "oXBXHJT3TQvl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "4iNmm3JunpyH": {"selector": "h5", "style": {"__ref": "W_C8Ik37WW3O"}, "__type": "ThemeStyle"}, "jdvZdxMtZYIv": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "JxWeg2z_8-yL": {"name": "Default \"h6\"", "rs": {"__ref": "jdvZdxMtZYIv"}, "preview": null, "uuid": "YfqCyKo9ZIYd", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Ctq3Oa1q5kmC": {"selector": "h6", "style": {"__ref": "JxWeg2z_8-yL"}, "__type": "ThemeStyle"}, "sr2HoNy5XBW1": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "lJdTo14qAOFB": {"name": "Default \"a\"", "rs": {"__ref": "sr2HoNy5XBW1"}, "preview": null, "uuid": "Qk3LYLmTua_o", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hyjXufQ0qc3G": {"selector": "a", "style": {"__ref": "lJdTo14qAOFB"}, "__type": "ThemeStyle"}, "ypQkQDzbNVR2": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "EJ9iP_i1bDwS": {"name": "Default \"a:hover\"", "rs": {"__ref": "ypQkQDzbNVR2"}, "preview": null, "uuid": "u6fTnkS7K0yK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "zXYh8smNFTL7": {"selector": "a:hover", "style": {"__ref": "EJ9iP_i1bDwS"}, "__type": "ThemeStyle"}, "RVG1TkPbBciO": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "y4RgGp1gIqJH": {"name": "Default \"blockquote\"", "rs": {"__ref": "RVG1TkPbBciO"}, "preview": null, "uuid": "b0wXZe5s3IOi", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "sEcS2SI9RLpl": {"selector": "blockquote", "style": {"__ref": "y4RgGp1gIqJH"}, "__type": "ThemeStyle"}, "_53erUqlrEQ0": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "qOi73GkBdZsz": {"name": "Default \"code\"", "rs": {"__ref": "_53erUqlrEQ0"}, "preview": null, "uuid": "VP44_7H10EYG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "h-63JEYOUiQg": {"selector": "code", "style": {"__ref": "qOi73GkBdZsz"}, "__type": "ThemeStyle"}, "AMVny7260ssk": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "cD057gsr7zDu": {"name": "Default \"pre\"", "rs": {"__ref": "AMVny7260ssk"}, "preview": null, "uuid": "yqAZAyWcYL3v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "gzLy8K5nMCX9": {"selector": "pre", "style": {"__ref": "cD057gsr7zDu"}, "__type": "ThemeStyle"}, "pxJz_9JyMbWw": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "qdNo7IKBKa92": {"name": "Default \"ol\"", "rs": {"__ref": "pxJz_9JyMbWw"}, "preview": null, "uuid": "DiOjaJCHq8oa", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "7YCpWdsUCQjX": {"selector": "ol", "style": {"__ref": "qdNo7IKBKa92"}, "__type": "ThemeStyle"}, "AOH-PyMc1ob_": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "rboXbUqXM9US": {"name": "Default \"ul\"", "rs": {"__ref": "AOH-PyMc1ob_"}, "preview": null, "uuid": "mZuScmuXOdnG", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "jL36hnK0qXVZ": {"selector": "ul", "style": {"__ref": "rboXbUqXM9US"}, "__type": "ThemeStyle"}, "Di4KTB1WGAZt": {"defaultStyle": {"__ref": "eigGLyrLcSeG"}, "styles": [{"__ref": "EYrpm9KGKTwn"}, {"__ref": "PgTsly8lH9Nf"}, {"__ref": "r6B7enMAtDHN"}, {"__ref": "yyXAIl8dw3Bj"}, {"__ref": "4iNmm3JunpyH"}, {"__ref": "Ctq3Oa1q5kmC"}, {"__ref": "hyjXufQ0qc3G"}, {"__ref": "zXYh8smNFTL7"}, {"__ref": "sEcS2SI9RLpl"}, {"__ref": "h-63JEYOUiQg"}, {"__ref": "gzLy8K5nMCX9"}, {"__ref": "7YCpWdsUCQjX"}, {"__ref": "jL36hnK0qXVZ"}], "layout": {"__ref": "8q6zX4wLwvVs"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "Ht2_mnOWN4DN": {"name": "text", "__type": "Text"}, "jrzZ74Pwdlwc": {"name": "Screen", "uuid": "RAaUMp_DkLKj", "__type": "Var"}, "ely1HQibfdQ7": {"type": {"__ref": "Ht2_mnOWN4DN"}, "variable": {"__ref": "jrzZ74Pwdlwc"}, "uuid": "d9li97kwZU75", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "aGcTdVtAM5X9": {"type": "global-screen", "param": {"__ref": "ely1HQibfdQ7"}, "uuid": "QUSa-EiV6INB", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "u1X-vg-VO0Ac": {"uuid": "yO8XGGsQMaa6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "WIjomGShzm36": {"components": [{"__ref": "iDVQEnXDQ7o7"}, {"__ref": "F_d9L-fvVYyV"}, {"__ref": "VPQPTgBcDtyW"}], "arenas": [], "pageArenas": [{"__ref": "4VGm2j5r_gNF"}], "componentArenas": [], "globalVariantGroups": [{"__ref": "aGcTdVtAM5X9"}], "userManagedFonts": [], "globalVariant": {"__ref": "u1X-vg-VO0Ac"}, "styleTokens": [{"__ref": "2tEyg1FMcCWM"}], "mixins": [], "themes": [{"__ref": "Di4KTB1WGAZt"}], "activeTheme": {"__ref": "Di4KTB1WGAZt"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "aGcTdVtAM5X9"}, "flags": {"usePlasmicImg": true, "useLoadingState": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "iDVQEnXDQ7o7": {"uuid": "_JIRQ2EcmtLp", "name": "hostless-plasmic-head", "params": [{"__ref": "ijxbm0FnPZtU"}, {"__ref": "xm41uhJ9hB8N"}, {"__ref": "-81tlEUrAWTG"}, {"__ref": "G6hwgT6B5PBS"}], "states": [], "tplTree": {"__ref": "J3tfGnnBr-Vx"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "VVZJw5i24D-3"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "haEQrE2Lnupq"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "F_d9L-fvVYyV": {"uuid": "rU-rji0HG7v2", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "Fvmfvud2xzlF"}, {"__ref": "O3c3es9a8m9i"}, {"__ref": "0Omwy8lMUYxo"}, {"__ref": "1Q5htq5E_z_o"}, {"__ref": "R0bZCL25YX82"}], "states": [], "tplTree": {"__ref": "E4JJX3jQyOkO"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "unI6eq9ANZHR"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "VEM744OkjdvA"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "ijxbm0FnPZtU": {"type": {"__ref": "JqBHMKZ47aPh"}, "variable": {"__ref": "bhhuM5HU0rud"}, "uuid": "-NRJqtUZVoxb", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "xm41uhJ9hB8N": {"type": {"__ref": "yx54lVYPxLYB"}, "variable": {"__ref": "Dr3cDkCgA0A5"}, "uuid": "SwIT-vh4RByU", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-81tlEUrAWTG": {"type": {"__ref": "WCYh_cf3Sohy"}, "variable": {"__ref": "eX25NVMSLdmZ"}, "uuid": "YBiHKQzszM95", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "G6hwgT6B5PBS": {"type": {"__ref": "6y8qFKZTnXe-"}, "variable": {"__ref": "AwcDfpCreZg6"}, "uuid": "u_bgz_tm3HhG", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "J3tfGnnBr-Vx": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "5P4NPtkm6K3y", "parent": null, "locked": null, "vsettings": [{"__ref": "fNC65c22vEsZ"}], "__type": "TplTag"}, "VVZJw5i24D-3": {"uuid": "zirAAe8XsdNo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "haEQrE2Lnupq": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "Fvmfvud2xzlF": {"type": {"__ref": "q6r05UR3mlFM"}, "variable": {"__ref": "lHaVwLszEcdy"}, "uuid": "ybTlDVFXMSJE", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "O3c3es9a8m9i": {"type": {"__ref": "eVfe-8MhxSJN"}, "variable": {"__ref": "Woghnq175Tqw"}, "uuid": "t0SAqbVJXX9G", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "0Omwy8lMUYxo": {"type": {"__ref": "S1qIGI3hbeKf"}, "tplSlot": {"__ref": "ROOaubntXRI1"}, "variable": {"__ref": "kA6vOLVW_QZX"}, "uuid": "fsBoJbF6SzoL", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "1Q5htq5E_z_o": {"type": {"__ref": "DyRIHMh7BJHq"}, "variable": {"__ref": "5gJyEm-3Hjdb"}, "uuid": "GjXPXDG_o-vC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "R0bZCL25YX82": {"type": {"__ref": "vkw-Doi3zHEh"}, "variable": {"__ref": "H4gyuGywSu2B"}, "uuid": "VADrJrHrT6N7", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "E4JJX3jQyOkO": {"tag": "div", "name": null, "children": [{"__ref": "ROOaubntXRI1"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "R9baojnX6bMd", "parent": null, "locked": null, "vsettings": [{"__ref": "HnEpFmI_Plu4"}], "__type": "TplTag"}, "unI6eq9ANZHR": {"uuid": "oozYR432Oq6A", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "VEM744OkjdvA": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "JqBHMKZ47aPh": {"name": "text", "__type": "Text"}, "bhhuM5HU0rud": {"name": "title", "uuid": "RJCp5jH3hhcJ", "__type": "Var"}, "yx54lVYPxLYB": {"name": "text", "__type": "Text"}, "Dr3cDkCgA0A5": {"name": "description", "uuid": "v-XsuiBozNyo", "__type": "Var"}, "WCYh_cf3Sohy": {"name": "img", "__type": "Img"}, "eX25NVMSLdmZ": {"name": "image", "uuid": "7WUgB5rVeatS", "__type": "Var"}, "6y8qFKZTnXe-": {"name": "text", "__type": "Text"}, "AwcDfpCreZg6": {"name": "canonical", "uuid": "mY6IGaDIHc0R", "__type": "Var"}, "fNC65c22vEsZ": {"variants": [{"__ref": "VVZJw5i24D-3"}], "args": [], "attrs": {}, "rs": {"__ref": "zSI487nvXaj2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "q6r05UR3mlFM": {"name": "any", "__type": "AnyType"}, "lHaVwLszEcdy": {"name": "dataOp", "uuid": "QC1LbnpUOPf8", "__type": "Var"}, "eVfe-8MhxSJN": {"name": "text", "__type": "Text"}, "Woghnq175Tqw": {"name": "name", "uuid": "Yw9HsM1b-oRu", "__type": "Var"}, "S1qIGI3hbeKf": {"name": "renderFunc", "params": [{"__ref": "gDig8cIM4xsM"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "kA6vOLVW_QZX": {"name": "children", "uuid": "pyVVCZOM_v8e", "__type": "Var"}, "DyRIHMh7BJHq": {"name": "num", "__type": "<PERSON><PERSON>"}, "5gJyEm-3Hjdb": {"name": "pageSize", "uuid": "GPvc204pK0PF", "__type": "Var"}, "vkw-Doi3zHEh": {"name": "num", "__type": "<PERSON><PERSON>"}, "H4gyuGywSu2B": {"name": "pageIndex", "uuid": "b8jAfaHHdrE5", "__type": "Var"}, "ROOaubntXRI1": {"param": {"__ref": "0Omwy8lMUYxo"}, "defaultContents": [], "uuid": "VBEd-zHdOt26", "parent": {"__ref": "E4JJX3jQyOkO"}, "locked": null, "vsettings": [{"__ref": "Saf5Qdnjv4mz"}], "__type": "TplSlot"}, "HnEpFmI_Plu4": {"variants": [{"__ref": "unI6eq9ANZHR"}], "args": [], "attrs": {}, "rs": {"__ref": "yFWKxthBITwQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zSI487nvXaj2": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "gDig8cIM4xsM": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "KRmEtOdJEZcX"}, "__type": "ArgType"}, "Saf5Qdnjv4mz": {"variants": [{"__ref": "unI6eq9ANZHR"}], "args": [], "attrs": {}, "rs": {"__ref": "4UFm-zBI3Li6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yFWKxthBITwQ": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "KRmEtOdJEZcX": {"name": "any", "__type": "AnyType"}, "4UFm-zBI3Li6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "VPQPTgBcDtyW": {"uuid": "UOWfPevr7k00", "name": "NewPage", "params": [], "states": [], "tplTree": {"__ref": "C-dm8ZnQ7Rwx"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "tIswJwLMnCfB"}], "variantGroups": [], "pageMeta": {"__ref": "u8K4Kx8rIrxK"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "4VGm2j5r_gNF": {"component": {"__ref": "VPQPTgBcDtyW"}, "matrix": {"__ref": "puWtSELi3Dh6"}, "customMatrix": {"__ref": "BrpToF9Am06H"}, "__type": "PageArena"}, "C-dm8ZnQ7Rwx": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "RNObPkn8mUQk", "parent": null, "locked": null, "vsettings": [{"__ref": "Jg83VV9FYdfb"}], "__type": "TplTag"}, "tIswJwLMnCfB": {"uuid": "mNIe6nBHm5iO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "u8K4Kx8rIrxK": {"path": "/new-page", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "puWtSELi3Dh6": {"rows": [{"__ref": "6uBkVnD1KPfx"}], "__type": "ArenaFrameGrid"}, "BrpToF9Am06H": {"rows": [{"__ref": "UJJpzWphTYGS"}], "__type": "ArenaFrameGrid"}, "Jg83VV9FYdfb": {"variants": [{"__ref": "tIswJwLMnCfB"}], "args": [], "attrs": {}, "rs": {"__ref": "4-RKhv27v4qd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "6uBkVnD1KPfx": {"cols": [{"__ref": "qtLU4R6BQHki"}, {"__ref": "sMNr4GTdXuvO"}], "rowKey": {"__ref": "tIswJwLMnCfB"}, "__type": "ArenaFrameRow"}, "UJJpzWphTYGS": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "4-RKhv27v4qd": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "qtLU4R6BQHki": {"frame": {"__ref": "62KX7VUsJBGU"}, "cellKey": null, "__type": "ArenaFrameCell"}, "sMNr4GTdXuvO": {"frame": {"__ref": "dkzTfKMdhZiz"}, "cellKey": null, "__type": "ArenaFrameCell"}, "62KX7VUsJBGU": {"uuid": "b4-h862E_9aR", "width": 1366, "height": 768, "container": {"__ref": "tWs-LyxsPG4i"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tIswJwLMnCfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "dkzTfKMdhZiz": {"uuid": "0ynsxQDC1VN5", "width": 414, "height": 736, "container": {"__ref": "RXEeg_VdeOJ_"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "tIswJwLMnCfB"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "tWs-LyxsPG4i": {"name": null, "component": {"__ref": "VPQPTgBcDtyW"}, "uuid": "W3SzpGUw9zy2", "parent": null, "locked": null, "vsettings": [{"__ref": "egTdzk_QokyF"}], "__type": "TplComponent"}, "RXEeg_VdeOJ_": {"name": null, "component": {"__ref": "VPQPTgBcDtyW"}, "uuid": "9SV1eeAgMurD", "parent": null, "locked": null, "vsettings": [{"__ref": "7x694qGUuR9F"}], "__type": "TplComponent"}, "egTdzk_QokyF": {"variants": [{"__ref": "u1X-vg-VO0Ac"}], "args": [], "attrs": {}, "rs": {"__ref": "deAjS-6PrZ1f"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "7x694qGUuR9F": {"variants": [{"__ref": "u1X-vg-VO0Ac"}], "args": [], "attrs": {}, "rs": {"__ref": "dGDakIDK8E_u"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "deAjS-6PrZ1f": {"values": {}, "mixins": [], "__type": "RuleSet"}, "dGDakIDK8E_u": {"values": {}, "mixins": [], "__type": "RuleSet"}, "2tEyg1FMcCWM": {"name": "style-token-registered", "type": "Color", "uuid": "pYGNpg_j4GgZ", "value": "white", "variantedValues": [], "isRegistered": true, "regKey": "style-token-registered", "__type": "StyleToken"}}, "deps": [], "version": "251-add-data-tokens"}}]}