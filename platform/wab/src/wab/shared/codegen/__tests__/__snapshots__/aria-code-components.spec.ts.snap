// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`aria code components example: codegen should codegen correct contents 1`] = `
"
--- Button.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicButton } from "./PlasmicButton"; // plasmic-import: D557GwRUy2HJ/render
function Button(props) {
    // Use PlasmicButton to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicButton are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all ButtonProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicButton, __assign({}, props));
}
export default Button;

--- Button.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicButton, DefaultButtonProps} from "./PlasmicButton";  // plasmic-import: D557GwRUy2HJ/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface ButtonProps extends Omit<DefaultButtonProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultButtonProps altogether and have
    // total control over the props for your component.
    export interface ButtonProps extends DefaultButtonProps {
    }

    
      function Button(props: ButtonProps) {
        
    // Use PlasmicButton to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicButton are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all ButtonProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicButton {...props} />;
      }

      export default Button;
    
  
--- DataFetcherGrandParent.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicDataFetcherGrandParent } from "./PlasmicDataFetcherGrandParent"; // plasmic-import: mmv9XMNdJbUm/render
function DataFetcherGrandParent(props) {
    // Use PlasmicDataFetcherGrandParent to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicDataFetcherGrandParent are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all DataFetcherGrandParentProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicDataFetcherGrandParent, __assign({}, props));
}
export default DataFetcherGrandParent;

--- DataFetcherGrandParent.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicDataFetcherGrandParent, DefaultDataFetcherGrandParentProps} from "./PlasmicDataFetcherGrandParent";  // plasmic-import: mmv9XMNdJbUm/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface DataFetcherGrandParentProps extends Omit<DefaultDataFetcherGrandParentProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultDataFetcherGrandParentProps altogether and have
    // total control over the props for your component.
    export interface DataFetcherGrandParentProps extends DefaultDataFetcherGrandParentProps {
    }

    
      function DataFetcherGrandParent(props: DataFetcherGrandParentProps) {
        
    // Use PlasmicDataFetcherGrandParent to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicDataFetcherGrandParent are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all DataFetcherGrandParentProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicDataFetcherGrandParent {...props} />;
      }

      export default DataFetcherGrandParent;
    
  
--- DataFetcherParent.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicDataFetcherParent } from "./PlasmicDataFetcherParent"; // plasmic-import: iUkwSJQ7glxH/render
function DataFetcherParent(props) {
    // Use PlasmicDataFetcherParent to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicDataFetcherParent are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all DataFetcherParentProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicDataFetcherParent, __assign({}, props));
}
export default DataFetcherParent;

--- DataFetcherParent.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicDataFetcherParent, DefaultDataFetcherParentProps} from "./PlasmicDataFetcherParent";  // plasmic-import: iUkwSJQ7glxH/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface DataFetcherParentProps extends Omit<DefaultDataFetcherParentProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultDataFetcherParentProps altogether and have
    // total control over the props for your component.
    export interface DataFetcherParentProps extends DefaultDataFetcherParentProps {
    }

    
      function DataFetcherParent(props: DataFetcherParentProps) {
        
    // Use PlasmicDataFetcherParent to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicDataFetcherParent are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all DataFetcherParentProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicDataFetcherParent {...props} />;
      }

      export default DataFetcherParent;
    
  
--- HelloWorld.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicHelloWorld } from "./PlasmicHelloWorld"; // plasmic-import: 3khyLeywU1SP/render
function HelloWorld_(props, ref) {
    // Use PlasmicHelloWorld to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicHelloWorld are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all HelloWorldProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicHelloWorld, __assign({ root: { ref: ref } }, props));
}
var HelloWorld = React.forwardRef(HelloWorld_);
export default HelloWorld;

--- HelloWorld.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicHelloWorld, DefaultHelloWorldProps} from "./PlasmicHelloWorld";  // plasmic-import: 3khyLeywU1SP/render
    import {HTMLElementRefOf} from "@plasmicapp/react-web";

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface HelloWorldProps extends Omit<DefaultHelloWorldProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultHelloWorldProps altogether and have
    // total control over the props for your component.
    export interface HelloWorldProps extends DefaultHelloWorldProps {
    }

    
      function HelloWorld_(props: HelloWorldProps, ref: HTMLElementRefOf<"div">) {
        
    // Use PlasmicHelloWorld to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicHelloWorld are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all HelloWorldProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicHelloWorld root={{ref}} {...props} />;
      }

      const HelloWorld = React.forwardRef(HelloWorld_);
      export default HelloWorld;
    
  
--- ModalDialog.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicModalDialog } from "./PlasmicModalDialog"; // plasmic-import: 4D8eN8Bv_isH/render
function ModalDialog(props) {
    // Use PlasmicModalDialog to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicModalDialog are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all ModalDialogProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicModalDialog, __assign({}, props));
}
export default ModalDialog;

--- ModalDialog.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicModalDialog, DefaultModalDialogProps} from "./PlasmicModalDialog";  // plasmic-import: 4D8eN8Bv_isH/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface ModalDialogProps extends Omit<DefaultModalDialogProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultModalDialogProps altogether and have
    // total control over the props for your component.
    export interface ModalDialogProps extends DefaultModalDialogProps {
    }

    
      function ModalDialog(props: ModalDialogProps) {
        
    // Use PlasmicModalDialog to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicModalDialog are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all ModalDialogProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicModalDialog {...props} />;
      }

      export default ModalDialog;
    
  
--- MyOverlayArrow.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicMyOverlayArrow } from "./PlasmicMyOverlayArrow"; // plasmic-import: N_LiHbyjn30l/render
function MyOverlayArrow(props) {
    // Use PlasmicMyOverlayArrow to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicMyOverlayArrow are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all MyOverlayArrowProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicMyOverlayArrow, __assign({}, props));
}
export default MyOverlayArrow;

--- MyOverlayArrow.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicMyOverlayArrow, DefaultMyOverlayArrowProps} from "./PlasmicMyOverlayArrow";  // plasmic-import: N_LiHbyjn30l/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface MyOverlayArrowProps extends Omit<DefaultMyOverlayArrowProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultMyOverlayArrowProps altogether and have
    // total control over the props for your component.
    export interface MyOverlayArrowProps extends DefaultMyOverlayArrowProps {
    }

    
      function MyOverlayArrow(props: MyOverlayArrowProps) {
        
    // Use PlasmicMyOverlayArrow to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicMyOverlayArrow are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all MyOverlayArrowProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicMyOverlayArrow {...props} />;
      }

      export default MyOverlayArrow;
    
  
--- NewPage.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicNewPage } from "./PlasmicNewPage"; // plasmic-import: TNdRzGmxrYew/render
function NewPage_(props, ref) {
    // Use PlasmicNewPage to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicNewPage are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all NewPageProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicNewPage, __assign({ root: { ref: ref } }, props));
}
var NewPage = React.forwardRef(NewPage_);
export default NewPage;

--- NewPage.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicNewPage, DefaultNewPageProps} from "./PlasmicNewPage";  // plasmic-import: TNdRzGmxrYew/render
    import {HTMLElementRefOf} from "@plasmicapp/react-web";

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface NewPageProps extends Omit<DefaultNewPageProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultNewPageProps altogether and have
    // total control over the props for your component.
    export interface NewPageProps extends DefaultNewPageProps {
    }

    
      function NewPage_(props: NewPageProps, ref: HTMLElementRefOf<"div">) {
        
    // Use PlasmicNewPage to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicNewPage are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all NewPageProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicNewPage root={{ref}} {...props} />;
      }

      const NewPage = React.forwardRef(NewPage_);
      export default NewPage;
    
  
--- PlasmicButton.css ---
 .Button__root___6YhWv.__wab_instance {
          position: relative;
background: var(--token-t3vITKRpSfAP);
display: flex;
cursor: pointer;
box-sizing: border-box;
min-width: var(--token-q8b6qKDtK1cO);
font-family: var(--token-o6yJby_7a7Ou);
font-size: var(--token-vDsSvK1cXIbX);
line-height: var(--token-OcwQZQDDtNIc);
border-radius: var(--token-2SGQcnOQi1BY);
padding: var(--token-hUExw1ok-7My);
border-width: 0px;  
      } 
 .Button__root___6YhWv[data-focus-visible].__wab_instance {
          outline-style: solid;
outline-width: var(--token-f1EInfSh9bJ1);
outline-offset: 1px;
outline-color: var(--token-t3vITKRpSfAP);  
      } 
 .Button__root___6YhWv[data-disabled].__wab_instance {
          cursor: not-allowed;  
      } 
 .Button__rootcolor_neutral___6YhWvuIxPw.__wab_instance {
          background: var(--token-oUG3E93FPdG8);  
      } 
 .Button__rootcolor_muted___6YhWvjY2Oh.__wab_instance {
          background: var(--token-RbmHOSbtxZmu);  
      } 
 .Button__rootcolor_success___6YhWvajZ5R.__wab_instance {
          background: var(--token-pjPGWdmPsMUz);  
      } 
 .Button__rootcolor_warning___6YhWvH0QYb.__wab_instance {
          background: var(--token-b43TAFXK9Pxn);  
      } 
 .Button__rootcolor_errorDestructive___6YhWvcMheu.__wab_instance {
          background: var(--token-9nmyYGHszf0U);  
      } 
 .Button__roottype_soft___6YhWvuVMhz.__wab_instance {
          background: none;  
      } 
 .Button__roottype_bordered___6YhWvvkLqX.__wab_instance {
          background: none;  
      } 
 .Button__rootsize_extraSmall___6YhWv4BkWv.__wab_instance {
          line-height: var(--token-PITewDT1atQM);
font-size: var(--token-Ao4NZQI8FNby);
column-gap: var(--token-f1EInfSh9bJ1);
row-gap: 0px;
border-radius: var(--token-oQn8gHl2FNKG);  
      } 
 .Button__rootsize_small___6YhWvY4QTe.__wab_instance {
          font-size: var(--token-Ao4NZQI8FNby);
line-height: var(--token-PITewDT1atQM);
column-gap: var(--token-oQn8gHl2FNKG);
row-gap: 0px;
border-radius: var(--token-oQn8gHl2FNKG);  
      } 
 .Button__rootsize_large___6YhWvCy3Ub.__wab_instance {
          font-size: var(--token-lA8wtoWJjrz7);
line-height: var(--token-6zoJ_0lAlBNf);  
      } 
 .Button__rootsize_extraLarge___6YhWvIr7B.__wab_instance {
          font-size: var(--token-lA8wtoWJjrz7);
line-height: var(--token-6zoJ_0lAlBNf);  
      } 
 .Button__rootroundedFull___6YhWvcE1Ts.__wab_instance {
          border-radius: 99px;  
      } 
 .Button__rootflatSide_top___6YhWvacWxs.__wab_instance {
          border-top-right-radius: var(--token-hUExw1ok-7My);
border-top-left-radius: var(--token-hUExw1ok-7My);  
      } 
 .Button__rootflatSide_right___6YhWvm8LYu.__wab_instance {
          border-top-right-radius: var(--token-hUExw1ok-7My);
border-bottom-right-radius: var(--token-hUExw1ok-7My);  
      } 
 .Button__rootflatSide_bottom___6YhWvT0Er0.__wab_instance {
          border-bottom-right-radius: var(--token-hUExw1ok-7My);
border-bottom-left-radius: var(--token-hUExw1ok-7My);  
      } 
 .Button__rootflatSide_left___6YhWvstuXq.__wab_instance {
          border-top-left-radius: var(--token-hUExw1ok-7My);
border-bottom-left-radius: var(--token-hUExw1ok-7My);  
      } 
 .Button__softBackground__hnrbV {
          display: block;
position: absolute;
top: 0px;
left: 0px;
bottom: 0px;
right: 0px;
background: var(--token-cuGgthrhEq0n);
border-radius: var(--token-2SGQcnOQi1BY);  
      } 
 .Button__softBackgroundcolor_neutral__hnrbVuIxPw {
          background: var(--token-c-Ge-TPxBoxs);  
      } 
 .Button__softBackgroundcolor_muted__hnrbVjY2Oh {
          background: var(--token-oaolgBFC7x92);  
      } 
 .Button__softBackgroundcolor_success__hnrbVajZ5R {
          background: var(--token-OHf2f9VUoB_O);  
      } 
 .Button__softBackgroundcolor_warning__hnrbVh0QYb {
          background: var(--token-gsX_1U5GfPYR);  
      } 
 .Button__softBackgroundcolor_errorDestructive__hnrbVcMheu {
          background: var(--token-hHWiWZ2Rpr4b);  
      } 
 .Button__softBackgroundtype_soft__hnrbVuVMhz {
          display: block;  
      } 
  .Button__roottype_bordered___6YhWvvkLqX[data-hovered] .Button__softBackgroundtype_bordered__hnrbVvkLqX {
          display: block;  
      } 
  .Button__roottype_bordered___6YhWvvkLqX[data-pressed] .Button__softBackgroundtype_bordered__hnrbVvkLqX {
          display: block;  
      } 
 .Button__softBackgroundroundedFull__hnrbVcE1Ts {
          border-radius: 99px;  
      } 
 .Button__border__buvbi {
          display: block;
position: absolute;
top: 0px;
left: 0px;
bottom: 0px;
right: 0px;
border-radius: var(--token-2SGQcnOQi1BY);
border: 0px solid var(--token-ULH4hQ23d_Sv);  
      } 
 .Button__bordercolor_neutral__buvbiuIxPw {
          border-color: var(--token-ScdXHpuBD4ei);  
      } 
 .Button__bordercolor_muted__buvbijY2Oh {
          border-color: var(--token-FIQKkwksz5FV);  
      } 
 .Button__bordercolor_success__buvbiajZ5R {
          border-color: var(--token-4pJCxGA_Sa7p);  
      } 
 .Button__bordercolor_warning__buvbiH0QYb {
          border-color: var(--token-glWDd2lYSUSa);  
      } 
 .Button__bordercolor_errorDestructive__buvbicMheu {
          border-color: var(--token-i87-wilVu3z7);  
      } 
 .Button__bordertype_bordered__buvbivkLqX {
          background: none;
display: block;
border-width: 1px;  
      } 
 .Button__borderroundedFull__buvbicE1Ts {
          border-radius: 99px;  
      } 
 .Button__borderflatSide_top__buvbiacWxs {
          border-top-left-radius: var(--token-AT94SE3lDMCW);
border-top-right-radius: var(--token-AT94SE3lDMCW);  
      } 
 .Button__borderflatSide_right__buvbim8LYu {
          border-top-right-radius: var(--token-AT94SE3lDMCW);
border-bottom-right-radius: var(--token-AT94SE3lDMCW);  
      } 
 .Button__borderflatSide_bottom__buvbiT0Er0 {
          border-bottom-left-radius: var(--token-AT94SE3lDMCW);
border-bottom-right-radius: var(--token-AT94SE3lDMCW);  
      } 
 .Button__borderflatSide_left__buvbistuXq {
          border-top-left-radius: var(--token-AT94SE3lDMCW);
border-bottom-left-radius: var(--token-AT94SE3lDMCW);  
      } 
 .Button__interactionEffect__qu3Be {
          display: block;
position: absolute;
top: 0px;
right: 0px;
left: 0px;
bottom: 0px;
border-radius: var(--token-2SGQcnOQi1BY);  
      } 
  .Button__root___6YhWv[data-hovered] .Button__interactionEffect__qu3Be {
          background: var(--token-RVAopeQq_eqC);  
      } 
  .Button__root___6YhWv[data-disabled] .Button__interactionEffect__qu3Be {
          background: var(--token-eccLB1btakvR);  
      } 
  .Button__root___6YhWv[data-pressed] .Button__interactionEffect__qu3Be {
          background: var(--token-W3OCHsj5ohvN);  
      } 
  .Button__roottype_bordered___6YhWvvkLqX[data-hovered] .Button__interactionEffecttype_bordered__qu3BEvkLqX {
          display: none;  
      } 
 .Button__interactionEffectroundedFull__qu3BEcE1Ts {
          border-radius: 99px;  
      } 
 .Button__freeBox__o2Ucr {
          display: flex;
justify-content: center;
align-items: center;
width: 100%;
height: 100%;
column-gap: var(--token-2SGQcnOQi1BY);
min-width: 0;
min-height: 0;
padding: var(--token-2SGQcnOQi1BY) var(--token-wlM-_lPQTiUn);  
      } 
 .Button__freeBoxsize_extraSmall__o2Ucr4BkWv {
          padding: var(--token-oQn8gHl2FNKG) var(--token-2SGQcnOQi1BY);  
      } 
 .Button__freeBoxsize_small__o2UcrY4QTe {
          padding: var(--token-2SGQcnOQi1BY) var(--token-q8b6qKDtK1cO);  
      } 
 .Button__freeBoxsize_large__o2UcrCy3Ub {
          padding-top: var(--token-oQn8gHl2FNKG);
padding-bottom: var(--token-oQn8gHl2FNKG);  
      } 
 .Button__freeBoxsize_extraLarge__o2UcrIr7B {
          padding-left: var(--token-IZkbCcBWqxrg);
padding-right: var(--token-IZkbCcBWqxrg);  
      } 
 .Button__freeBoxroundedFull__o2UcrcE1Ts {
          padding-right: var(--token-wlM-_lPQTiUn);
padding-left: var(--token-wlM-_lPQTiUn);  
      } 
 .Button__freeBox__dWlDm {
          flex-direction: column;
flex-shrink: 0;
display: none;  
      } 
 .Button__freeBoxiconStart__dWlDme62Ve {
          display: flex;  
      } 
 .Button__slotTargetStart__hwuAw {
          color: var(--token-khAiiMlnWfNQ);  
      } 
 .Button__slotTargetStartcolor_neutral__hwuAwuIxPw {
          color: var(--token-XZj0GlRukXD4);  
      } 
 .Button__slotTargetStartcolor_muted__hwuAwjY2Oh {
          color: var(--token-V7R45kYSsL2g);  
      } 
 .Button__slotTargetStartcolor_success__hwuAwajZ5R {
          color: var(--token-q5_E41GOjKS6);  
      } 
 .Button__slotTargetStartcolor_warning__hwuAwH0QYb {
          color: var(--token-9A3Vv0G2WK2I);  
      } 
 .Button__slotTargetStartcolor_errorDestructive__hwuAwcMheu {
          color: var(--token-DI2Jhin1tpeq);  
      } 
 .Button__slotTargetStarttype_soft__hwuAwuVMhz {
          color: var(--token-d71RMbT3EJe6);  
      } 
 .Button__slotTargetStarttype_bordered__hwuAwvkLqX {
          color: var(--token-d71RMbT3EJe6);  
      } 
 .Button__slotTargetStartsize_extraSmall__hwuAw4BkWv {
          font-size: var(--token-Ao4NZQI8FNby);  
      } 
 .Button__slotTargetStartsize_small__hwuAwY4QTe {
          font-size: var(--token-Ao4NZQI8FNby);  
      } 
 .Button__slotTargetStartsize_large__hwuAwCy3Ub {
          font-size: var(--token-lA8wtoWJjrz7);  
      } 
 .Button__slotTargetStartsize_extraLarge__hwuAwIr7B {
          font-size: var(--token-lA8wtoWJjrz7);  
      } 
 .Button__slotTargetStartcolor_neutral_type_soft__hwuAwuIxPwUVMhz {
          color: var(--token-LDvOzPYSOKeL);  
      } 
 .Button__slotTargetStartcolor_neutral_type_bordered__hwuAwuIxPwVkLqX {
          color: var(--token-LDvOzPYSOKeL);  
      } 
 .Button__slotTargetStartcolor_muted_type_soft__hwuAwjY2OhUVMhz {
          color: var(--token-XsbmIM-jiG1T);  
      } 
 .Button__slotTargetStartcolor_muted_type_bordered__hwuAwjY2OhVkLqX {
          color: var(--token-XsbmIM-jiG1T);  
      } 
 .Button__slotTargetStartcolor_success_type_soft__hwuAwajZ5RUVMhz {
          color: var(--token-jgnKGJBIrlpY);  
      } 
 .Button__slotTargetStartcolor_success_type_bordered__hwuAwajZ5RVkLqX {
          color: var(--token-jgnKGJBIrlpY);  
      } 
 .Button__slotTargetStartcolor_warning_type_soft__hwuAwH0QYbUVMhz {
          color: var(--token-nm_aC4o1kFsY);  
      } 
 .Button__slotTargetStartcolor_warning_type_bordered__hwuAwH0QYbVkLqX {
          color: var(--token-nm_aC4o1kFsY);  
      } 
 .Button__slotTargetStartcolor_errorDestructive_type_soft__hwuAwcMheuUVMhz {
          color: var(--token-Mwf0LwBRtoUP);  
      } 
 .Button__slotTargetStartcolor_errorDestructive_type_bordered__hwuAwcMheuVkLqX {
          color: var(--token-Mwf0LwBRtoUP);  
      } 
 .Button__svg__pz6Ky {
          position: relative;
height: 1em;
flex-shrink: 0;  
      } 
 .Button__slotTargetLabel__nGmiT {
          color: var(--token-khAiiMlnWfNQ);  
      } 
 .Button__slotTargetLabelcolor_neutral__nGmiTuIxPw {
          color: var(--token-XZj0GlRukXD4);  
      } 
 .Button__slotTargetLabelcolor_muted__nGmiTjY2Oh {
          color: var(--token-V7R45kYSsL2g);  
      } 
 .Button__slotTargetLabelcolor_success__nGmiTajZ5R {
          color: var(--token-q5_E41GOjKS6);  
      } 
 .Button__slotTargetLabelcolor_warning__nGmiTh0QYb {
          color: var(--token-9A3Vv0G2WK2I);  
      } 
 .Button__slotTargetLabelcolor_errorDestructive__nGmiTcMheu {
          color: var(--token-DI2Jhin1tpeq);  
      } 
 .Button__slotTargetLabeltype_soft__nGmiTuVMhz {
          color: var(--token-d71RMbT3EJe6);  
      } 
 .Button__slotTargetLabeltype_bordered__nGmiTvkLqX {
          color: var(--token-d71RMbT3EJe6);  
      } 
 .Button__slotTargetLabelcolor_neutral_type_soft__nGmiTuIxPwUVMhz {
          color: var(--token-LDvOzPYSOKeL);  
      } 
 .Button__slotTargetLabelcolor_muted_type_soft__nGmiTjY2OhUVMhz {
          color: var(--token-XsbmIM-jiG1T);  
      } 
 .Button__slotTargetLabelcolor_neutral_type_bordered__nGmiTuIxPwVkLqX {
          color: var(--token-LDvOzPYSOKeL);  
      } 
 .Button__slotTargetLabelcolor_success_type_soft__nGmiTajZ5RUVMhz {
          color: var(--token-jgnKGJBIrlpY);  
      } 
 .Button__slotTargetLabelcolor_muted_type_bordered__nGmiTjY2OhVkLqX {
          color: var(--token-XsbmIM-jiG1T);  
      } 
 .Button__slotTargetLabelcolor_warning_type_soft__nGmiTh0QYbUVMhz {
          color: var(--token-nm_aC4o1kFsY);  
      } 
 .Button__slotTargetLabelcolor_success_type_bordered__nGmiTajZ5RVkLqX {
          color: var(--token-jgnKGJBIrlpY);  
      } 
 .Button__slotTargetLabelcolor_errorDestructive_type_soft__nGmiTcMheuUVMhz {
          color: var(--token-Mwf0LwBRtoUP);  
      } 
 .Button__slotTargetLabelcolor_warning_type_bordered__nGmiTh0QYbVkLqX {
          color: var(--token-nm_aC4o1kFsY);  
      } 
 .Button__slotTargetLabelcolor_errorDestructive_type_bordered__nGmiTcMheuVkLqX {
          color: var(--token-Mwf0LwBRtoUP);  
      } 
 .Button__text__uJl5C {
          white-space: pre;  
      } 
 .Button__freeBox__bstQz {
          flex-direction: column;
flex-shrink: 0;
display: none;  
      } 
 .Button__freeBoxiconEnd__bstQzfrDp5 {
          display: flex;  
      } 
 .Button__slotTargetEnd__ooNSu {
          color: var(--token-khAiiMlnWfNQ);  
      } 
 .Button__slotTargetEndcolor_neutral__ooNSuuIxPw {
          color: var(--token-XZj0GlRukXD4);  
      } 
 .Button__slotTargetEndcolor_muted__ooNSujY2Oh {
          color: var(--token-V7R45kYSsL2g);  
      } 
 .Button__slotTargetEndcolor_success__ooNSuajZ5R {
          color: var(--token-q5_E41GOjKS6);  
      } 
 .Button__slotTargetEndcolor_warning__ooNSuH0QYb {
          color: var(--token-9A3Vv0G2WK2I);  
      } 
 .Button__slotTargetEndcolor_errorDestructive__ooNSucMheu {
          color: var(--token-DI2Jhin1tpeq);  
      } 
 .Button__slotTargetEndtype_soft__ooNSuuVMhz {
          color: var(--token-d71RMbT3EJe6);  
      } 
 .Button__slotTargetEndtype_bordered__ooNSuvkLqX {
          color: var(--token-d71RMbT3EJe6);  
      } 
 .Button__slotTargetEndsize_extraSmall__ooNSu4BkWv {
          font-size: var(--token-Ao4NZQI8FNby);  
      } 
 .Button__slotTargetEndsize_small__ooNSuY4QTe {
          font-size: var(--token-Ao4NZQI8FNby);  
      } 
 .Button__slotTargetEndsize_large__ooNSuCy3Ub {
          font-size: var(--token-lA8wtoWJjrz7);  
      } 
 .Button__slotTargetEndsize_extraLarge__ooNSuIr7B {
          font-size: var(--token-lA8wtoWJjrz7);  
      } 
 .Button__slotTargetEndcolor_neutral_type_soft__ooNSuuIxPwUVMhz {
          color: var(--token-LDvOzPYSOKeL);  
      } 
 .Button__slotTargetEndcolor_neutral_type_bordered__ooNSuuIxPwVkLqX {
          color: var(--token-LDvOzPYSOKeL);  
      } 
 .Button__slotTargetEndcolor_muted_type_soft__ooNSujY2OhUVMhz {
          color: var(--token-XsbmIM-jiG1T);  
      } 
 .Button__slotTargetEndcolor_muted_type_bordered__ooNSujY2OhVkLqX {
          color: var(--token-XsbmIM-jiG1T);  
      } 
 .Button__slotTargetEndcolor_success_type_soft__ooNSuajZ5RUVMhz {
          color: var(--token-jgnKGJBIrlpY);  
      } 
 .Button__slotTargetEndcolor_success_type_bordered__ooNSuajZ5RVkLqX {
          color: var(--token-jgnKGJBIrlpY);  
      } 
 .Button__slotTargetEndcolor_warning_type_soft__ooNSuH0QYbUVMhz {
          color: var(--token-nm_aC4o1kFsY);  
      } 
 .Button__slotTargetEndcolor_warning_type_bordered__ooNSuH0QYbVkLqX {
          color: var(--token-nm_aC4o1kFsY);  
      } 
 .Button__slotTargetEndcolor_errorDestructive_type_soft__ooNSucMheuUVMhz {
          color: var(--token-Mwf0LwBRtoUP);  
      } 
 .Button__slotTargetEndcolor_errorDestructive_type_bordered__ooNSucMheuVkLqX {
          color: var(--token-Mwf0LwBRtoUP);  
      } 
 .Button__svg__ySq0C {
          position: relative;
height: 1em;
flex-shrink: 0;  
      } 
--- PlasmicButton.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: D557GwRUy2HJ
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, hasVariant, renderPlasmicSlot, useDollarState, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { BaseButton } from "@plasmicpkgs/react-aria/skinny/registerButton";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicButton.css"; // plasmic-import: D557GwRUy2HJ/css
import CircleIcon from "./PlasmicIcon__Circle"; // plasmic-import: 4hS9BR4cg9TV/icon
import ChevronDownIcon from "./PlasmicIcon__ChevronDown"; // plasmic-import: lZo9OmVojh_m/icon
createPlasmicElementProxy;
export var PlasmicButton__VariantProps = new Array("color", "type", "size", "iconStart", "iconEnd", "roundedFull", "flatSide");
export var PlasmicButton__ArgProps = new Array("disabled", "submitsForm", "resetsForm", "ariaLabel", "onClick", "label", "start", "end");
var $$ = {};
function PlasmicButton__RenderFunc(props) {
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({}, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var stateSpecs = React.useMemo(function () {
        return ([{
                path: "color",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.color);
                },
            }, {
                path: "size",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.size);
                },
            }, {
                path: "iconStart",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.iconStart);
                },
            }, {
                path: "iconEnd",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.iconEnd);
                },
            }, {
                path: "roundedFull",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.roundedFull);
                },
            }, {
                path: "type",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.type);
                },
            }, {
                path: "flatSide",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.flatSide);
                },
            }]);
    }, [$props, $ctx, $refs]);
    var $state = useDollarState(stateSpecs, { $props: $props, $ctx: $ctx, $queries: {}, $refs: $refs });
    var styleTokensClassNames = _useStyleTokens();
    var _a = React.useState({
        hovered: false,
        pressed: false,
        focused: false,
        focusVisible: false,
        disabled: false
    }), $ccVariants = _a[0], setDollarCcVariants = _a[1];
    var updateVariant = React.useCallback(function (changes) {
        setDollarCcVariants(function (prev) {
            if (!Object.keys(changes).some(function (k) { return prev[k] !== changes[k]; })) {
                return prev;
            }
            return __assign(__assign({}, prev), changes);
        });
    }, []);
    return (createPlasmicElementProxy(BaseButton, { "data-plasmic-name": "root", "data-plasmic-override": overrides.root, "data-plasmic-root": true, "data-plasmic-for-node": forNode, "aria-label": args.ariaLabel, className: classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "Button__root___6YhWv", { "Button__rootcolor_errorDestructive___6YhWvcMheu": hasVariant($state, "color", "errorDestructive"), "Button__rootcolor_errorDestructive_type_bordered___6YhWvcMheuVkLqX": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "bordered"), "Button__rootcolor_errorDestructive_type_soft___6YhWvcMheuUVMhz": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "soft"), "Button__rootcolor_muted___6YhWvjY2Oh": hasVariant($state, "color", "muted"), "Button__rootcolor_muted_type_bordered___6YhWvjY2OhVkLqX": hasVariant($state, "color", "muted") && hasVariant($state, "type", "bordered"), "Button__rootcolor_muted_type_soft___6YhWvjY2OhUVMhz": hasVariant($state, "color", "muted") && hasVariant($state, "type", "soft"), "Button__rootcolor_neutral___6YhWvuIxPw": hasVariant($state, "color", "neutral"), "Button__rootcolor_neutral_type_bordered___6YhWvuIxPwVkLqX": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "bordered"), "Button__rootcolor_neutral_type_soft___6YhWvuIxPwUVMhz": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "soft"), "Button__rootcolor_success___6YhWvajZ5R": hasVariant($state, "color", "success"), "Button__rootcolor_success_type_bordered___6YhWvajZ5RVkLqX": hasVariant($state, "color", "success") && hasVariant($state, "type", "bordered"), "Button__rootcolor_success_type_soft___6YhWvajZ5RUVMhz": hasVariant($state, "color", "success") && hasVariant($state, "type", "soft"), "Button__rootcolor_warning___6YhWvH0QYb": hasVariant($state, "color", "warning"), "Button__rootcolor_warning_type_bordered___6YhWvH0QYbVkLqX": hasVariant($state, "color", "warning") && hasVariant($state, "type", "bordered"), "Button__rootcolor_warning_type_soft___6YhWvH0QYbUVMhz": hasVariant($state, "color", "warning") && hasVariant($state, "type", "soft"), "Button__rootflatSide_bottom___6YhWvT0Er0": hasVariant($state, "flatSide", "bottom"), "Button__rootflatSide_left___6YhWvstuXq": hasVariant($state, "flatSide", "left"), "Button__rootflatSide_right___6YhWvm8LYu": hasVariant($state, "flatSide", "right"), "Button__rootflatSide_top___6YhWvacWxs": hasVariant($state, "flatSide", "top"), "Button__rooticonEnd___6YhWvfrDp5": hasVariant($state, "iconEnd", "iconEnd"), "Button__rooticonStart___6YhWvE62Ve": hasVariant($state, "iconStart", "iconStart"), "Button__rootroundedFull___6YhWvcE1Ts": hasVariant($state, "roundedFull", "roundedFull"), "Button__rootsize_extraLarge___6YhWvIr7B": hasVariant($state, "size", "extraLarge"), "Button__rootsize_extraSmall___6YhWv4BkWv": hasVariant($state, "size", "extraSmall"), "Button__rootsize_large___6YhWvCy3Ub": hasVariant($state, "size", "large"), "Button__rootsize_small___6YhWvY4QTe": hasVariant($state, "size", "small"), "Button__roottype_bordered___6YhWvvkLqX": hasVariant($state, "type", "bordered"), "Button__roottype_soft___6YhWvuVMhz": hasVariant($state, "type", "soft") }), isDisabled: args.disabled, onPress: args.onClick, plasmicUpdateVariant: updateVariant, resetsForm: args.resetsForm, submitsForm: args.submitsForm },
        (hasVariant($state, "type", "bordered") && $ccVariants["pressed"] ? true : hasVariant($state, "type", "bordered") && $ccVariants["hovered"] ? true : hasVariant($state, "type", "soft") ? true : false) ? (createPlasmicElementProxy("div", { "data-plasmic-name": "softBackground", "data-plasmic-override": overrides.softBackground, className: classNames("plasmic_default__all", "plasmic_default__div", "Button__softBackground__hnrbV", { "Button__softBackgroundcolor_errorDestructive__hnrbVcMheu": hasVariant($state, "color", "errorDestructive"), "Button__softBackgroundcolor_muted__hnrbVjY2Oh": hasVariant($state, "color", "muted"), "Button__softBackgroundcolor_neutral__hnrbVuIxPw": hasVariant($state, "color", "neutral"), "Button__softBackgroundcolor_success__hnrbVajZ5R": hasVariant($state, "color", "success"), "Button__softBackgroundcolor_warning__hnrbVh0QYb": hasVariant($state, "color", "warning"), "Button__softBackgroundroundedFull__hnrbVcE1Ts": hasVariant($state, "roundedFull", "roundedFull"), "Button__softBackgroundsize_large__hnrbVcy3Ub": hasVariant($state, "size", "large"), "Button__softBackgroundtype_bordered__hnrbVvkLqX": hasVariant($state, "type", "bordered"), "Button__softBackgroundtype_soft__hnrbVuVMhz": hasVariant($state, "type", "soft") }) })) : null,
        (hasVariant($state, "type", "bordered") ? true : false) ? (createPlasmicElementProxy("div", { "data-plasmic-name": "border", "data-plasmic-override": overrides.border, className: classNames("plasmic_default__all", "plasmic_default__div", "Button__border__buvbi", { "Button__bordercolor_errorDestructive__buvbicMheu": hasVariant($state, "color", "errorDestructive"), "Button__bordercolor_muted__buvbijY2Oh": hasVariant($state, "color", "muted"), "Button__bordercolor_muted_type_bordered__buvbijY2OhVkLqX": hasVariant($state, "color", "muted") && hasVariant($state, "type", "bordered"), "Button__bordercolor_neutral__buvbiuIxPw": hasVariant($state, "color", "neutral"), "Button__bordercolor_success__buvbiajZ5R": hasVariant($state, "color", "success"), "Button__bordercolor_warning__buvbiH0QYb": hasVariant($state, "color", "warning"), "Button__borderflatSide_bottom__buvbiT0Er0": hasVariant($state, "flatSide", "bottom"), "Button__borderflatSide_left__buvbistuXq": hasVariant($state, "flatSide", "left"), "Button__borderflatSide_right__buvbim8LYu": hasVariant($state, "flatSide", "right"), "Button__borderflatSide_top__buvbiacWxs": hasVariant($state, "flatSide", "top"), "Button__borderroundedFull__buvbicE1Ts": hasVariant($state, "roundedFull", "roundedFull"), "Button__bordersize_large__buvbiCy3Ub": hasVariant($state, "size", "large"), "Button__bordertype_bordered__buvbivkLqX": hasVariant($state, "type", "bordered"), "Button__bordertype_soft__buvbiuVMhz": hasVariant($state, "type", "soft") }) })) : null,
        createPlasmicElementProxy("div", { "data-plasmic-name": "interactionEffect", "data-plasmic-override": overrides.interactionEffect, className: classNames("plasmic_default__all", "plasmic_default__div", "Button__interactionEffect__qu3Be", { "Button__interactionEffectcolor_errorDestructive__qu3BEcMheu": hasVariant($state, "color", "errorDestructive"), "Button__interactionEffectcolor_muted__qu3BEjY2Oh": hasVariant($state, "color", "muted"), "Button__interactionEffectcolor_neutral__qu3BEuIxPw": hasVariant($state, "color", "neutral"), "Button__interactionEffectcolor_success__qu3BEajZ5R": hasVariant($state, "color", "success"), "Button__interactionEffectcolor_success_type_bordered__qu3BEajZ5RVkLqX": hasVariant($state, "color", "success") && hasVariant($state, "type", "bordered"), "Button__interactionEffectcolor_warning__qu3Beh0QYb": hasVariant($state, "color", "warning"), "Button__interactionEffectroundedFull__qu3BEcE1Ts": hasVariant($state, "roundedFull", "roundedFull"), "Button__interactionEffectsize_large__qu3Becy3Ub": hasVariant($state, "size", "large"), "Button__interactionEffectsize_small__qu3Bey4QTe": hasVariant($state, "size", "small"), "Button__interactionEffecttype_bordered__qu3BEvkLqX": hasVariant($state, "type", "bordered"), "Button__interactionEffecttype_soft__qu3BEuVMhz": hasVariant($state, "type", "soft") }), style: hasVariant($state, "type", "bordered") && $ccVariants["pressed"] ? { "display": "block" } : undefined }),
        createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "Button__freeBox__o2Ucr", { "Button__freeBoxcolor_neutral__o2UcruIxPw": hasVariant($state, "color", "neutral"), "Button__freeBoxcolor_success_type_bordered__o2UcrajZ5RVkLqX": hasVariant($state, "color", "success") && hasVariant($state, "type", "bordered"), "Button__freeBoxroundedFull__o2UcrcE1Ts": hasVariant($state, "roundedFull", "roundedFull"), "Button__freeBoxsize_extraLarge__o2UcrIr7B": hasVariant($state, "size", "extraLarge"), "Button__freeBoxsize_extraSmall__o2Ucr4BkWv": hasVariant($state, "size", "extraSmall"), "Button__freeBoxsize_large__o2UcrCy3Ub": hasVariant($state, "size", "large"), "Button__freeBoxsize_small__o2UcrY4QTe": hasVariant($state, "size", "small"), "Button__freeBoxtype_bordered__o2UcrvkLqX": hasVariant($state, "type", "bordered") }) },
            createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "Button__freeBox__dWlDm", { "Button__freeBoxcolor_neutral__dWlDMuIxPw": hasVariant($state, "color", "neutral"), "Button__freeBoxcolor_neutral_type_soft__dWlDMuIxPwUVMhz": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "soft"), "Button__freeBoxiconStart__dWlDme62Ve": hasVariant($state, "iconStart", "iconStart"), "Button__freeBoxsize_extraLarge__dWlDmIr7B": hasVariant($state, "size", "extraLarge"), "Button__freeBoxsize_large__dWlDmcy3Ub": hasVariant($state, "size", "large"), "Button__freeBoxsize_small__dWlDmy4QTe": hasVariant($state, "size", "small"), "Button__freeBoxtype_bordered__dWlDMvkLqX": hasVariant($state, "type", "bordered") }) }, renderPlasmicSlot({
                defaultContents: createPlasmicElementProxy(CircleIcon, { className: classNames("plasmic_default__all", "plasmic_default__svg", "Button__svg__pz6Ky"), role: "img" }),
                value: args.start,
                className: classNames("Button__slotTargetStart__hwuAw", { "Button__slotTargetStartcolor_errorDestructive__hwuAwcMheu": hasVariant($state, "color", "errorDestructive"), "Button__slotTargetStartcolor_errorDestructive_type_bordered__hwuAwcMheuVkLqX": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "bordered"), "Button__slotTargetStartcolor_errorDestructive_type_soft__hwuAwcMheuUVMhz": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "soft"), "Button__slotTargetStartcolor_muted__hwuAwjY2Oh": hasVariant($state, "color", "muted"), "Button__slotTargetStartcolor_muted_type_bordered__hwuAwjY2OhVkLqX": hasVariant($state, "color", "muted") && hasVariant($state, "type", "bordered"), "Button__slotTargetStartcolor_muted_type_soft__hwuAwjY2OhUVMhz": hasVariant($state, "color", "muted") && hasVariant($state, "type", "soft"), "Button__slotTargetStartcolor_neutral__hwuAwuIxPw": hasVariant($state, "color", "neutral"), "Button__slotTargetStartcolor_neutral_type_bordered__hwuAwuIxPwVkLqX": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "bordered"), "Button__slotTargetStartcolor_neutral_type_soft__hwuAwuIxPwUVMhz": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "soft"), "Button__slotTargetStartcolor_success__hwuAwajZ5R": hasVariant($state, "color", "success"), "Button__slotTargetStartcolor_success_type_bordered__hwuAwajZ5RVkLqX": hasVariant($state, "color", "success") && hasVariant($state, "type", "bordered"), "Button__slotTargetStartcolor_success_type_soft__hwuAwajZ5RUVMhz": hasVariant($state, "color", "success") && hasVariant($state, "type", "soft"), "Button__slotTargetStartcolor_warning__hwuAwH0QYb": hasVariant($state, "color", "warning"), "Button__slotTargetStartcolor_warning_type_bordered__hwuAwH0QYbVkLqX": hasVariant($state, "color", "warning") && hasVariant($state, "type", "bordered"), "Button__slotTargetStartcolor_warning_type_soft__hwuAwH0QYbUVMhz": hasVariant($state, "color", "warning") && hasVariant($state, "type", "soft"), "Button__slotTargetStarticonStart__hwuAwE62Ve": hasVariant($state, "iconStart", "iconStart"), "Button__slotTargetStartsize_extraLarge__hwuAwIr7B": hasVariant($state, "size", "extraLarge"), "Button__slotTargetStartsize_extraSmall__hwuAw4BkWv": hasVariant($state, "size", "extraSmall"), "Button__slotTargetStartsize_large__hwuAwCy3Ub": hasVariant($state, "size", "large"), "Button__slotTargetStartsize_small__hwuAwY4QTe": hasVariant($state, "size", "small"), "Button__slotTargetStarttype_bordered__hwuAwvkLqX": hasVariant($state, "type", "bordered"), "Button__slotTargetStarttype_soft__hwuAwuVMhz": hasVariant($state, "type", "soft") })
            })),
            renderPlasmicSlot({
                defaultContents: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Button__text__uJl5C") }, "Text"),
                value: args.label,
                className: classNames("Button__slotTargetLabel__nGmiT", { "Button__slotTargetLabelcolor_errorDestructive__nGmiTcMheu": hasVariant($state, "color", "errorDestructive"), "Button__slotTargetLabelcolor_errorDestructive_type_bordered__nGmiTcMheuVkLqX": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "bordered"), "Button__slotTargetLabelcolor_errorDestructive_type_soft__nGmiTcMheuUVMhz": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "soft"), "Button__slotTargetLabelcolor_muted__nGmiTjY2Oh": hasVariant($state, "color", "muted"), "Button__slotTargetLabelcolor_muted_type_bordered__nGmiTjY2OhVkLqX": hasVariant($state, "color", "muted") && hasVariant($state, "type", "bordered"), "Button__slotTargetLabelcolor_muted_type_soft__nGmiTjY2OhUVMhz": hasVariant($state, "color", "muted") && hasVariant($state, "type", "soft"), "Button__slotTargetLabelcolor_neutral__nGmiTuIxPw": hasVariant($state, "color", "neutral"), "Button__slotTargetLabelcolor_neutral_type_bordered__nGmiTuIxPwVkLqX": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "bordered"), "Button__slotTargetLabelcolor_neutral_type_soft__nGmiTuIxPwUVMhz": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "soft"), "Button__slotTargetLabelcolor_success__nGmiTajZ5R": hasVariant($state, "color", "success"), "Button__slotTargetLabelcolor_success_type_bordered__nGmiTajZ5RVkLqX": hasVariant($state, "color", "success") && hasVariant($state, "type", "bordered"), "Button__slotTargetLabelcolor_success_type_soft__nGmiTajZ5RUVMhz": hasVariant($state, "color", "success") && hasVariant($state, "type", "soft"), "Button__slotTargetLabelcolor_warning__nGmiTh0QYb": hasVariant($state, "color", "warning"), "Button__slotTargetLabelcolor_warning_type_bordered__nGmiTh0QYbVkLqX": hasVariant($state, "color", "warning") && hasVariant($state, "type", "bordered"), "Button__slotTargetLabelcolor_warning_type_soft__nGmiTh0QYbUVMhz": hasVariant($state, "color", "warning") && hasVariant($state, "type", "soft"), "Button__slotTargetLabeliconEnd__nGmiTfrDp5": hasVariant($state, "iconEnd", "iconEnd"), "Button__slotTargetLabeliconStart__nGmiTe62Ve": hasVariant($state, "iconStart", "iconStart"), "Button__slotTargetLabelsize_extraLarge__nGmiTIr7B": hasVariant($state, "size", "extraLarge"), "Button__slotTargetLabelsize_extraSmall__nGmiT4BkWv": hasVariant($state, "size", "extraSmall"), "Button__slotTargetLabelsize_large__nGmiTcy3Ub": hasVariant($state, "size", "large"), "Button__slotTargetLabelsize_small__nGmiTy4QTe": hasVariant($state, "size", "small"), "Button__slotTargetLabeltype_bordered__nGmiTvkLqX": hasVariant($state, "type", "bordered"), "Button__slotTargetLabeltype_soft__nGmiTuVMhz": hasVariant($state, "type", "soft") })
            }),
            createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "Button__freeBox__bstQz", { "Button__freeBoxiconEnd__bstQzfrDp5": hasVariant($state, "iconEnd", "iconEnd"), "Button__freeBoxsize_extraLarge__bstQzIr7B": hasVariant($state, "size", "extraLarge"), "Button__freeBoxsize_large__bstQzCy3Ub": hasVariant($state, "size", "large"), "Button__freeBoxsize_small__bstQzY4QTe": hasVariant($state, "size", "small") }) }, renderPlasmicSlot({
                defaultContents: createPlasmicElementProxy(ChevronDownIcon, { className: classNames("plasmic_default__all", "plasmic_default__svg", "Button__svg__ySq0C"), role: "img" }),
                value: args.end,
                className: classNames("Button__slotTargetEnd__ooNSu", { "Button__slotTargetEndcolor_errorDestructive__ooNSucMheu": hasVariant($state, "color", "errorDestructive"), "Button__slotTargetEndcolor_errorDestructive_type_bordered__ooNSucMheuVkLqX": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "bordered"), "Button__slotTargetEndcolor_errorDestructive_type_soft__ooNSucMheuUVMhz": hasVariant($state, "color", "errorDestructive") && hasVariant($state, "type", "soft"), "Button__slotTargetEndcolor_muted__ooNSujY2Oh": hasVariant($state, "color", "muted"), "Button__slotTargetEndcolor_muted_type_bordered__ooNSujY2OhVkLqX": hasVariant($state, "color", "muted") && hasVariant($state, "type", "bordered"), "Button__slotTargetEndcolor_muted_type_soft__ooNSujY2OhUVMhz": hasVariant($state, "color", "muted") && hasVariant($state, "type", "soft"), "Button__slotTargetEndcolor_neutral__ooNSuuIxPw": hasVariant($state, "color", "neutral"), "Button__slotTargetEndcolor_neutral_type_bordered__ooNSuuIxPwVkLqX": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "bordered"), "Button__slotTargetEndcolor_neutral_type_soft__ooNSuuIxPwUVMhz": hasVariant($state, "color", "neutral") && hasVariant($state, "type", "soft"), "Button__slotTargetEndcolor_success__ooNSuajZ5R": hasVariant($state, "color", "success"), "Button__slotTargetEndcolor_success_type_bordered__ooNSuajZ5RVkLqX": hasVariant($state, "color", "success") && hasVariant($state, "type", "bordered"), "Button__slotTargetEndcolor_success_type_soft__ooNSuajZ5RUVMhz": hasVariant($state, "color", "success") && hasVariant($state, "type", "soft"), "Button__slotTargetEndcolor_warning__ooNSuH0QYb": hasVariant($state, "color", "warning"), "Button__slotTargetEndcolor_warning_type_bordered__ooNSuH0QYbVkLqX": hasVariant($state, "color", "warning") && hasVariant($state, "type", "bordered"), "Button__slotTargetEndcolor_warning_type_soft__ooNSuH0QYbUVMhz": hasVariant($state, "color", "warning") && hasVariant($state, "type", "soft"), "Button__slotTargetEndiconEnd__ooNSufrDp5": hasVariant($state, "iconEnd", "iconEnd"), "Button__slotTargetEndiconStart__ooNSuE62Ve": hasVariant($state, "iconStart", "iconStart"), "Button__slotTargetEndsize_extraLarge__ooNSuIr7B": hasVariant($state, "size", "extraLarge"), "Button__slotTargetEndsize_extraSmall__ooNSu4BkWv": hasVariant($state, "size", "extraSmall"), "Button__slotTargetEndsize_large__ooNSuCy3Ub": hasVariant($state, "size", "large"), "Button__slotTargetEndsize_small__ooNSuY4QTe": hasVariant($state, "size", "small"), "Button__slotTargetEndtype_bordered__ooNSuvkLqX": hasVariant($state, "type", "bordered"), "Button__slotTargetEndtype_soft__ooNSuuVMhz": hasVariant($state, "type", "soft") })
            })))));
}
var PlasmicDescendants = {
    root: ["root", "softBackground", "border", "interactionEffect"],
    softBackground: ["softBackground"],
    border: ["border"],
    interactionEffect: ["interactionEffect"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicButton__ArgProps,
            internalVariantPropNames: PlasmicButton__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicButton__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "root") {
        func.displayName = "PlasmicButton";
    }
    else {
        func.displayName = "PlasmicButton.".concat(nodeName);
    }
    return func;
}
export var PlasmicButton = Object.assign(
// Top-level PlasmicButton renders the root element
makeNodeComponent("root"), {
    // Helper components rendering sub-elements
    softBackground: makeNodeComponent("softBackground"),
    border: makeNodeComponent("border"),
    interactionEffect: makeNodeComponent("interactionEffect"),
    // Metadata about props expected for PlasmicButton
    internalVariantProps: PlasmicButton__VariantProps,
    internalArgProps: PlasmicButton__ArgProps,
});
export default PlasmicButton;
/* prettier-ignore-end */

--- PlasmicButton.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: D557GwRUy2HJ
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { BaseButton } from "@plasmicpkgs/react-aria/skinny/registerButton";
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicButton.css" // plasmic-import: D557GwRUy2HJ/css
  
    import CircleIcon from "./PlasmicIcon__Circle";  // plasmic-import: 4hS9BR4cg9TV/icon
import ChevronDownIcon from "./PlasmicIcon__ChevronDown";  // plasmic-import: lZo9OmVojh_m/icon
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicButton__VariantMembers = {
      color: "neutral" | "muted" | "success" | "warning" | "errorDestructive";
type: "soft" | "bordered";
size: "extraSmall" | "small" | "large" | "extraLarge";
iconStart: "iconStart";
iconEnd: "iconEnd";
roundedFull: "roundedFull";
flatSide: "top" | "right" | "bottom" | "left";
    };
    export type PlasmicButton__VariantsArgs = {
    color?: SingleChoiceArg<"neutral" | "muted" | "success" | "warning" | "errorDestructive">
type?: SingleChoiceArg<"soft" | "bordered">
size?: SingleChoiceArg<"extraSmall" | "small" | "large" | "extraLarge">
iconStart?: SingleBooleanChoiceArg<"iconStart">
iconEnd?: SingleBooleanChoiceArg<"iconEnd">
roundedFull?: SingleBooleanChoiceArg<"roundedFull">
flatSide?: MultiChoiceArg<"top" | "right" | "bottom" | "left">
  };
    type VariantPropType = keyof PlasmicButton__VariantsArgs;
    export const PlasmicButton__VariantProps = new Array<VariantPropType>("color","type","size","iconStart","iconEnd","roundedFull","flatSide");
  

    
export type PlasmicButton__ArgsType = {"disabled"?: boolean;
"submitsForm"?: boolean;
"resetsForm"?: boolean;
"ariaLabel"?: string;
"onClick"?: (event: any) => void;
"label"?: React.ReactNode;
"start"?: React.ReactNode;
"end"?: React.ReactNode;};
type ArgPropType = keyof PlasmicButton__ArgsType;
export const PlasmicButton__ArgProps = new Array<ArgPropType>("disabled", "submitsForm", "resetsForm", "ariaLabel", "onClick", "label", "start", "end");


    export type PlasmicButton__OverridesType = {
    root?: Flex__<typeof BaseButton>;
softBackground?: Flex__<"div">;
border?: Flex__<"div">;
interactionEffect?: Flex__<"div">;
  };

    
    export interface DefaultButtonProps {
      "disabled"?: boolean;
"submitsForm"?: boolean;
"resetsForm"?: boolean;
"ariaLabel"?: string;
"onClick"?: (event: any) => void;
"label"?: React.ReactNode;
"start"?: React.ReactNode;
"end"?: React.ReactNode;
"color"?: SingleChoiceArg<"neutral" | "muted" | "success" | "warning" | "errorDestructive">;
"type"?: SingleChoiceArg<"soft" | "bordered">;
"size"?: SingleChoiceArg<"extraSmall" | "small" | "large" | "extraLarge">;
"iconStart"?: SingleBooleanChoiceArg<"iconStart">;
"iconEnd"?: SingleBooleanChoiceArg<"iconEnd">;
"roundedFull"?: SingleBooleanChoiceArg<"roundedFull">;
"flatSide"?: MultiChoiceArg<"top" | "right" | "bottom" | "left">
      className?: string;
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicButton__RenderFunc(
      props: {
        variants: PlasmicButton__VariantsArgs,
        args: PlasmicButton__ArgsType,
        overrides: PlasmicButton__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(() =>
          ([{
      path: "color",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.color),
      
      
      
      
    },{
      path: "size",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.size),
      
      
      
      
    },{
      path: "iconStart",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.iconStart),
      
      
      
      
    },{
      path: "iconEnd",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.iconEnd),
      
      
      
      
    },{
      path: "roundedFull",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.roundedFull),
      
      
      
      
    },{
      path: "type",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.type),
      
      
      
      
    },{
      path: "flatSide",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.flatSide),
      
      
      
      
    }])
        , [$props, $ctx, $refs]);
        const $state = useDollarState(stateSpecs, {$props, $ctx, $queries: {}, $refs});
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
    const [$ccVariants, setDollarCcVariants] = React.useState<Record<string, boolean>>({
      hovered: false,
pressed: false,
focused: false,
focusVisible: false,
disabled: false
    });
    const updateVariant = React.useCallback((changes: Record<string, boolean>) => {
      setDollarCcVariants((prev) => {
        if (!Object.keys(changes).some((k) => prev[k] !== changes[k])) {
          return prev;
        }
        return { ...prev, ...changes }
      });
    }, []);
  
  
      return (
        <BaseButton
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      aria-label={args.ariaLabel}
className={classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "Button__root___6YhWv", { "Button__rootcolor_errorDestructive___6YhWvcMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__rootcolor_errorDestructive_type_bordered___6YhWvcMheuVkLqX": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__rootcolor_errorDestructive_type_soft___6YhWvcMheuUVMhz": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__rootcolor_muted___6YhWvjY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__rootcolor_muted_type_bordered___6YhWvjY2OhVkLqX": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__rootcolor_muted_type_soft___6YhWvjY2OhUVMhz": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__rootcolor_neutral___6YhWvuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__rootcolor_neutral_type_bordered___6YhWvuIxPwVkLqX": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__rootcolor_neutral_type_soft___6YhWvuIxPwUVMhz": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__rootcolor_success___6YhWvajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__rootcolor_success_type_bordered___6YhWvajZ5RVkLqX": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__rootcolor_success_type_soft___6YhWvajZ5RUVMhz": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__rootcolor_warning___6YhWvH0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__rootcolor_warning_type_bordered___6YhWvH0QYbVkLqX": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__rootcolor_warning_type_soft___6YhWvH0QYbUVMhz": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__rootflatSide_bottom___6YhWvT0Er0": hasVariant(
      $state,
      "flatSide",
      "bottom"
    ), "Button__rootflatSide_left___6YhWvstuXq": hasVariant(
      $state,
      "flatSide",
      "left"
    ), "Button__rootflatSide_right___6YhWvm8LYu": hasVariant(
      $state,
      "flatSide",
      "right"
    ), "Button__rootflatSide_top___6YhWvacWxs": hasVariant(
      $state,
      "flatSide",
      "top"
    ), "Button__rooticonEnd___6YhWvfrDp5": hasVariant(
      $state,
      "iconEnd",
      "iconEnd"
    ), "Button__rooticonStart___6YhWvE62Ve": hasVariant(
      $state,
      "iconStart",
      "iconStart"
    ), "Button__rootroundedFull___6YhWvcE1Ts": hasVariant(
      $state,
      "roundedFull",
      "roundedFull"
    ), "Button__rootsize_extraLarge___6YhWvIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__rootsize_extraSmall___6YhWv4BkWv": hasVariant(
      $state,
      "size",
      "extraSmall"
    ), "Button__rootsize_large___6YhWvCy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__rootsize_small___6YhWvY4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__roottype_bordered___6YhWvvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__roottype_soft___6YhWvuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })}
isDisabled={args.disabled}
onPress={args.onClick}
plasmicUpdateVariant={updateVariant}
resetsForm={args.resetsForm}
submitsForm={args.submitsForm}
      
      >
            { (hasVariant(
      $state,
      "type",
      "bordered"
    ) && $ccVariants["pressed"] ? true : hasVariant(
      $state,
      "type",
      "bordered"
    ) && $ccVariants["hovered"] ? true : hasVariant(
      $state,
      "type",
      "soft"
    ) ? true : false) ? (<div
      data-plasmic-name={"softBackground"}
      data-plasmic-override={overrides.softBackground}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Button__softBackground__hnrbV", { "Button__softBackgroundcolor_errorDestructive__hnrbVcMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__softBackgroundcolor_muted__hnrbVjY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__softBackgroundcolor_neutral__hnrbVuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__softBackgroundcolor_success__hnrbVajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__softBackgroundcolor_warning__hnrbVh0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__softBackgroundroundedFull__hnrbVcE1Ts": hasVariant(
      $state,
      "roundedFull",
      "roundedFull"
    ), "Button__softBackgroundsize_large__hnrbVcy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__softBackgroundtype_bordered__hnrbVvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__softBackgroundtype_soft__hnrbVuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })}
      
      />) : null }
{ (hasVariant(
      $state,
      "type",
      "bordered"
    ) ? true : false) ? (<div
      data-plasmic-name={"border"}
      data-plasmic-override={overrides.border}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Button__border__buvbi", { "Button__bordercolor_errorDestructive__buvbicMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__bordercolor_muted__buvbijY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__bordercolor_muted_type_bordered__buvbijY2OhVkLqX": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__bordercolor_neutral__buvbiuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__bordercolor_success__buvbiajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__bordercolor_warning__buvbiH0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__borderflatSide_bottom__buvbiT0Er0": hasVariant(
      $state,
      "flatSide",
      "bottom"
    ), "Button__borderflatSide_left__buvbistuXq": hasVariant(
      $state,
      "flatSide",
      "left"
    ), "Button__borderflatSide_right__buvbim8LYu": hasVariant(
      $state,
      "flatSide",
      "right"
    ), "Button__borderflatSide_top__buvbiacWxs": hasVariant(
      $state,
      "flatSide",
      "top"
    ), "Button__borderroundedFull__buvbicE1Ts": hasVariant(
      $state,
      "roundedFull",
      "roundedFull"
    ), "Button__bordersize_large__buvbiCy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__bordertype_bordered__buvbivkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__bordertype_soft__buvbiuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })}
      
      />) : null }
<div
      data-plasmic-name={"interactionEffect"}
      data-plasmic-override={overrides.interactionEffect}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Button__interactionEffect__qu3Be", { "Button__interactionEffectcolor_errorDestructive__qu3BEcMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__interactionEffectcolor_muted__qu3BEjY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__interactionEffectcolor_neutral__qu3BEuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__interactionEffectcolor_success__qu3BEajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__interactionEffectcolor_success_type_bordered__qu3BEajZ5RVkLqX": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__interactionEffectcolor_warning__qu3Beh0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__interactionEffectroundedFull__qu3BEcE1Ts": hasVariant(
      $state,
      "roundedFull",
      "roundedFull"
    ), "Button__interactionEffectsize_large__qu3Becy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__interactionEffectsize_small__qu3Bey4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__interactionEffecttype_bordered__qu3BEvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__interactionEffecttype_soft__qu3BEuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })}
style={hasVariant(
      $state,
      "type",
      "bordered"
    ) && $ccVariants["pressed"] ? {"display":"block"} : undefined}
      
      />
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Button__freeBox__o2Ucr", { "Button__freeBoxcolor_neutral__o2UcruIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__freeBoxcolor_success_type_bordered__o2UcrajZ5RVkLqX": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__freeBoxroundedFull__o2UcrcE1Ts": hasVariant(
      $state,
      "roundedFull",
      "roundedFull"
    ), "Button__freeBoxsize_extraLarge__o2UcrIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__freeBoxsize_extraSmall__o2Ucr4BkWv": hasVariant(
      $state,
      "size",
      "extraSmall"
    ), "Button__freeBoxsize_large__o2UcrCy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__freeBoxsize_small__o2UcrY4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__freeBoxtype_bordered__o2UcrvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ) })}
      
      >
            <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Button__freeBox__dWlDm", { "Button__freeBoxcolor_neutral__dWlDMuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__freeBoxcolor_neutral_type_soft__dWlDMuIxPwUVMhz": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__freeBoxiconStart__dWlDme62Ve": hasVariant(
      $state,
      "iconStart",
      "iconStart"
    ), "Button__freeBoxsize_extraLarge__dWlDmIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__freeBoxsize_large__dWlDmcy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__freeBoxsize_small__dWlDmy4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__freeBoxtype_bordered__dWlDMvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ) })}
      
      >
            { renderPlasmicSlot({
      defaultContents: <CircleIcon
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__svg", "Button__svg__pz6Ky")}
role={"img"}
      
      />,
      value: args.start,
      className: classNames("Button__slotTargetStart__hwuAw", { "Button__slotTargetStartcolor_errorDestructive__hwuAwcMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__slotTargetStartcolor_errorDestructive_type_bordered__hwuAwcMheuVkLqX": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetStartcolor_errorDestructive_type_soft__hwuAwcMheuUVMhz": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetStartcolor_muted__hwuAwjY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__slotTargetStartcolor_muted_type_bordered__hwuAwjY2OhVkLqX": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetStartcolor_muted_type_soft__hwuAwjY2OhUVMhz": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetStartcolor_neutral__hwuAwuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__slotTargetStartcolor_neutral_type_bordered__hwuAwuIxPwVkLqX": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetStartcolor_neutral_type_soft__hwuAwuIxPwUVMhz": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetStartcolor_success__hwuAwajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__slotTargetStartcolor_success_type_bordered__hwuAwajZ5RVkLqX": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetStartcolor_success_type_soft__hwuAwajZ5RUVMhz": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetStartcolor_warning__hwuAwH0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__slotTargetStartcolor_warning_type_bordered__hwuAwH0QYbVkLqX": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetStartcolor_warning_type_soft__hwuAwH0QYbUVMhz": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetStarticonStart__hwuAwE62Ve": hasVariant(
      $state,
      "iconStart",
      "iconStart"
    ), "Button__slotTargetStartsize_extraLarge__hwuAwIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__slotTargetStartsize_extraSmall__hwuAw4BkWv": hasVariant(
      $state,
      "size",
      "extraSmall"
    ), "Button__slotTargetStartsize_large__hwuAwCy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__slotTargetStartsize_small__hwuAwY4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__slotTargetStarttype_bordered__hwuAwvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetStarttype_soft__hwuAwuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })
  }) }
          </div>
{ renderPlasmicSlot({
      defaultContents: <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Button__text__uJl5C")}
      
      >
            { "Text" }
          </div>,
      value: args.label,
      className: classNames("Button__slotTargetLabel__nGmiT", { "Button__slotTargetLabelcolor_errorDestructive__nGmiTcMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__slotTargetLabelcolor_errorDestructive_type_bordered__nGmiTcMheuVkLqX": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetLabelcolor_errorDestructive_type_soft__nGmiTcMheuUVMhz": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetLabelcolor_muted__nGmiTjY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__slotTargetLabelcolor_muted_type_bordered__nGmiTjY2OhVkLqX": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetLabelcolor_muted_type_soft__nGmiTjY2OhUVMhz": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetLabelcolor_neutral__nGmiTuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__slotTargetLabelcolor_neutral_type_bordered__nGmiTuIxPwVkLqX": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetLabelcolor_neutral_type_soft__nGmiTuIxPwUVMhz": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetLabelcolor_success__nGmiTajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__slotTargetLabelcolor_success_type_bordered__nGmiTajZ5RVkLqX": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetLabelcolor_success_type_soft__nGmiTajZ5RUVMhz": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetLabelcolor_warning__nGmiTh0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__slotTargetLabelcolor_warning_type_bordered__nGmiTh0QYbVkLqX": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetLabelcolor_warning_type_soft__nGmiTh0QYbUVMhz": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetLabeliconEnd__nGmiTfrDp5": hasVariant(
      $state,
      "iconEnd",
      "iconEnd"
    ), "Button__slotTargetLabeliconStart__nGmiTe62Ve": hasVariant(
      $state,
      "iconStart",
      "iconStart"
    ), "Button__slotTargetLabelsize_extraLarge__nGmiTIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__slotTargetLabelsize_extraSmall__nGmiT4BkWv": hasVariant(
      $state,
      "size",
      "extraSmall"
    ), "Button__slotTargetLabelsize_large__nGmiTcy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__slotTargetLabelsize_small__nGmiTy4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__slotTargetLabeltype_bordered__nGmiTvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetLabeltype_soft__nGmiTuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })
  }) }
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Button__freeBox__bstQz", { "Button__freeBoxiconEnd__bstQzfrDp5": hasVariant(
      $state,
      "iconEnd",
      "iconEnd"
    ), "Button__freeBoxsize_extraLarge__bstQzIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__freeBoxsize_large__bstQzCy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__freeBoxsize_small__bstQzY4QTe": hasVariant(
      $state,
      "size",
      "small"
    ) })}
      
      >
            { renderPlasmicSlot({
      defaultContents: <ChevronDownIcon
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__svg", "Button__svg__ySq0C")}
role={"img"}
      
      />,
      value: args.end,
      className: classNames("Button__slotTargetEnd__ooNSu", { "Button__slotTargetEndcolor_errorDestructive__ooNSucMheu": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ), "Button__slotTargetEndcolor_errorDestructive_type_bordered__ooNSucMheuVkLqX": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetEndcolor_errorDestructive_type_soft__ooNSucMheuUVMhz": hasVariant(
      $state,
      "color",
      "errorDestructive"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetEndcolor_muted__ooNSujY2Oh": hasVariant(
      $state,
      "color",
      "muted"
    ), "Button__slotTargetEndcolor_muted_type_bordered__ooNSujY2OhVkLqX": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetEndcolor_muted_type_soft__ooNSujY2OhUVMhz": hasVariant(
      $state,
      "color",
      "muted"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetEndcolor_neutral__ooNSuuIxPw": hasVariant(
      $state,
      "color",
      "neutral"
    ), "Button__slotTargetEndcolor_neutral_type_bordered__ooNSuuIxPwVkLqX": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetEndcolor_neutral_type_soft__ooNSuuIxPwUVMhz": hasVariant(
      $state,
      "color",
      "neutral"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetEndcolor_success__ooNSuajZ5R": hasVariant(
      $state,
      "color",
      "success"
    ), "Button__slotTargetEndcolor_success_type_bordered__ooNSuajZ5RVkLqX": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetEndcolor_success_type_soft__ooNSuajZ5RUVMhz": hasVariant(
      $state,
      "color",
      "success"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetEndcolor_warning__ooNSuH0QYb": hasVariant(
      $state,
      "color",
      "warning"
    ), "Button__slotTargetEndcolor_warning_type_bordered__ooNSuH0QYbVkLqX": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetEndcolor_warning_type_soft__ooNSuH0QYbUVMhz": hasVariant(
      $state,
      "color",
      "warning"
    ) && hasVariant(
      $state,
      "type",
      "soft"
    ), "Button__slotTargetEndiconEnd__ooNSufrDp5": hasVariant(
      $state,
      "iconEnd",
      "iconEnd"
    ), "Button__slotTargetEndiconStart__ooNSuE62Ve": hasVariant(
      $state,
      "iconStart",
      "iconStart"
    ), "Button__slotTargetEndsize_extraLarge__ooNSuIr7B": hasVariant(
      $state,
      "size",
      "extraLarge"
    ), "Button__slotTargetEndsize_extraSmall__ooNSu4BkWv": hasVariant(
      $state,
      "size",
      "extraSmall"
    ), "Button__slotTargetEndsize_large__ooNSuCy3Ub": hasVariant(
      $state,
      "size",
      "large"
    ), "Button__slotTargetEndsize_small__ooNSuY4QTe": hasVariant(
      $state,
      "size",
      "small"
    ), "Button__slotTargetEndtype_bordered__ooNSuvkLqX": hasVariant(
      $state,
      "type",
      "bordered"
    ), "Button__slotTargetEndtype_soft__ooNSuuVMhz": hasVariant(
      $state,
      "type",
      "soft"
    ) })
  }) }
          </div>
          </div>
          </BaseButton>
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    root: ["root", "softBackground", "border", "interactionEffect"],
softBackground: ["softBackground"],
border: ["border"],
interactionEffect: ["interactionEffect"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      root: typeof BaseButton;
softBackground: "div";
border: "div";
interactionEffect: "div"
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicButton__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicButton__VariantsArgs;
        args?: PlasmicButton__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicButton__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicButton__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicButton__ArgProps,
          internalVariantPropNames: PlasmicButton__VariantProps,
        }), [props, nodeName]);
        return PlasmicButton__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "root") {
        func.displayName = "PlasmicButton";
      } else {
        func.displayName = \`PlasmicButton.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicButton = Object.assign(
      // Top-level PlasmicButton renders the root element
      makeNodeComponent("root"),
      {
        // Helper components rendering sub-elements
        softBackground: makeNodeComponent("softBackground"),
border: makeNodeComponent("border"),
interactionEffect: makeNodeComponent("interactionEffect"),

        // Metadata about props expected for PlasmicButton
        internalVariantProps: PlasmicButton__VariantProps,
        internalArgProps: PlasmicButton__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicButton;
    /* prettier-ignore-end */
  
--- PlasmicDataFetcherGrandParent.css ---
 .DataFetcherGrandParent__root__oy7X5.__wab_instance.__wab_instance {
          position: relative;  
      } 
--- PlasmicDataFetcherGrandParent.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: mmv9XMNdJbUm
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import DataFetcherParent from "./DataFetcherParent"; // plasmic-import: iUkwSJQ7glxH/component
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicDataFetcherGrandParent.css"; // plasmic-import: mmv9XMNdJbUm/css
createPlasmicElementProxy;
export var PlasmicDataFetcherGrandParent__VariantProps = new Array();
export var PlasmicDataFetcherGrandParent__ArgProps = new Array();
var $$ = {};
function PlasmicDataFetcherGrandParent__RenderFunc(props) {
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({}, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var styleTokensClassNames = _useStyleTokens();
    return (createPlasmicElementProxy(DataFetcherParent, { "data-plasmic-name": "root", "data-plasmic-override": overrides.root, "data-plasmic-root": true, "data-plasmic-for-node": forNode, className: classNames("__wab_instance", "DataFetcherGrandParent__root__oy7X5") }));
}
var PlasmicDescendants = {
    root: ["root"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicDataFetcherGrandParent__ArgProps,
            internalVariantPropNames: PlasmicDataFetcherGrandParent__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicDataFetcherGrandParent__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "root") {
        func.displayName = "PlasmicDataFetcherGrandParent";
    }
    else {
        func.displayName = "PlasmicDataFetcherGrandParent.".concat(nodeName);
    }
    return func;
}
export var PlasmicDataFetcherGrandParent = Object.assign(
// Top-level PlasmicDataFetcherGrandParent renders the root element
makeNodeComponent("root"), {
    // Helper components rendering sub-elements
    // Metadata about props expected for PlasmicDataFetcherGrandParent
    internalVariantProps: PlasmicDataFetcherGrandParent__VariantProps,
    internalArgProps: PlasmicDataFetcherGrandParent__ArgProps,
});
export default PlasmicDataFetcherGrandParent;
/* prettier-ignore-end */

--- PlasmicDataFetcherGrandParent.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: mmv9XMNdJbUm
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import DataFetcherParent from "./DataFetcherParent";  // plasmic-import: iUkwSJQ7glxH/component
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicDataFetcherGrandParent.css" // plasmic-import: mmv9XMNdJbUm/css
  
    
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicDataFetcherGrandParent__VariantMembers = {
      
    };
    export type PlasmicDataFetcherGrandParent__VariantsArgs = {};
    type VariantPropType = keyof PlasmicDataFetcherGrandParent__VariantsArgs;
    export const PlasmicDataFetcherGrandParent__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicDataFetcherGrandParent__ArgsType = {};
type ArgPropType = keyof PlasmicDataFetcherGrandParent__ArgsType;
export const PlasmicDataFetcherGrandParent__ArgProps = new Array<ArgPropType>();


    export type PlasmicDataFetcherGrandParent__OverridesType = {
    root?: Flex__<typeof DataFetcherParent>;
  };

    
    export interface DefaultDataFetcherGrandParentProps {
      
      
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicDataFetcherGrandParent__RenderFunc(
      props: {
        variants: PlasmicDataFetcherGrandParent__VariantsArgs,
        args: PlasmicDataFetcherGrandParent__ArgsType,
        overrides: PlasmicDataFetcherGrandParent__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
  
      return (
        <DataFetcherParent
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "DataFetcherGrandParent__root__oy7X5")}
      
      />
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    root: ["root"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      root: typeof DataFetcherParent
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicDataFetcherGrandParent__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicDataFetcherGrandParent__VariantsArgs;
        args?: PlasmicDataFetcherGrandParent__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicDataFetcherGrandParent__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicDataFetcherGrandParent__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicDataFetcherGrandParent__ArgProps,
          internalVariantPropNames: PlasmicDataFetcherGrandParent__VariantProps,
        }), [props, nodeName]);
        return PlasmicDataFetcherGrandParent__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "root") {
        func.displayName = "PlasmicDataFetcherGrandParent";
      } else {
        func.displayName = \`PlasmicDataFetcherGrandParent.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicDataFetcherGrandParent = Object.assign(
      // Top-level PlasmicDataFetcherGrandParent renders the root element
      makeNodeComponent("root"),
      {
        // Helper components rendering sub-elements
        

        // Metadata about props expected for PlasmicDataFetcherGrandParent
        internalVariantProps: PlasmicDataFetcherGrandParent__VariantProps,
        internalArgProps: PlasmicDataFetcherGrandParent__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicDataFetcherGrandParent;
    /* prettier-ignore-end */
  
--- PlasmicDataFetcherParent.css ---
 .DataFetcherParent__dataFetcher__iIahy.__wab_instance {
          max-width: 100%;
object-fit: cover;
position: relative;  
      } 
--- PlasmicDataFetcherParent.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: iUkwSJQ7glxH
// plasmic-unformatted
import * as React from "react";
import { createPlasmicElementProxy, deriveRenderOpts, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { Fetcher } from "@plasmicapp/react-web/lib/data-sources";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicDataFetcherParent.css"; // plasmic-import: iUkwSJQ7glxH/css
createPlasmicElementProxy;
export var PlasmicDataFetcherParent__VariantProps = new Array();
export var PlasmicDataFetcherParent__ArgProps = new Array();
var $$ = {};
function PlasmicDataFetcherParent__RenderFunc(props) {
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({}, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var styleTokensClassNames = _useStyleTokens();
    return (createPlasmicElementProxy(Fetcher, { "data-plasmic-name": "dataFetcher", "data-plasmic-override": overrides.dataFetcher, "data-plasmic-root": true, "data-plasmic-for-node": forNode, queries: {} }));
}
var PlasmicDescendants = {
    dataFetcher: ["dataFetcher"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicDataFetcherParent__ArgProps,
            internalVariantPropNames: PlasmicDataFetcherParent__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicDataFetcherParent__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "dataFetcher") {
        func.displayName = "PlasmicDataFetcherParent";
    }
    else {
        func.displayName = "PlasmicDataFetcherParent.".concat(nodeName);
    }
    return func;
}
export var PlasmicDataFetcherParent = Object.assign(
// Top-level PlasmicDataFetcherParent renders the root element
makeNodeComponent("dataFetcher"), {
    // Helper components rendering sub-elements
    // Metadata about props expected for PlasmicDataFetcherParent
    internalVariantProps: PlasmicDataFetcherParent__VariantProps,
    internalArgProps: PlasmicDataFetcherParent__ArgProps,
});
export default PlasmicDataFetcherParent;
/* prettier-ignore-end */

--- PlasmicDataFetcherParent.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: iUkwSJQ7glxH
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { Fetcher } from "@plasmicapp/react-web/lib/data-sources";
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicDataFetcherParent.css" // plasmic-import: iUkwSJQ7glxH/css
  
    
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicDataFetcherParent__VariantMembers = {
      
    };
    export type PlasmicDataFetcherParent__VariantsArgs = {};
    type VariantPropType = keyof PlasmicDataFetcherParent__VariantsArgs;
    export const PlasmicDataFetcherParent__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicDataFetcherParent__ArgsType = {};
type ArgPropType = keyof PlasmicDataFetcherParent__ArgsType;
export const PlasmicDataFetcherParent__ArgProps = new Array<ArgPropType>();


    export type PlasmicDataFetcherParent__OverridesType = {
    dataFetcher?: Flex__<typeof Fetcher>;
  };

    
    export interface DefaultDataFetcherParentProps {
      
      
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicDataFetcherParent__RenderFunc(
      props: {
        variants: PlasmicDataFetcherParent__VariantsArgs,
        args: PlasmicDataFetcherParent__ArgsType,
        overrides: PlasmicDataFetcherParent__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
  
      return (
        <Fetcher
      data-plasmic-name={"dataFetcher"}
      data-plasmic-override={overrides.dataFetcher}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      queries={{}}
      
      />
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    dataFetcher: ["dataFetcher"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      dataFetcher: typeof Fetcher
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicDataFetcherParent__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicDataFetcherParent__VariantsArgs;
        args?: PlasmicDataFetcherParent__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicDataFetcherParent__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicDataFetcherParent__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicDataFetcherParent__ArgProps,
          internalVariantPropNames: PlasmicDataFetcherParent__VariantProps,
        }), [props, nodeName]);
        return PlasmicDataFetcherParent__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "dataFetcher") {
        func.displayName = "PlasmicDataFetcherParent";
      } else {
        func.displayName = \`PlasmicDataFetcherParent.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicDataFetcherParent = Object.assign(
      // Top-level PlasmicDataFetcherParent renders the root element
      makeNodeComponent("dataFetcher"),
      {
        // Helper components rendering sub-elements
        

        // Metadata about props expected for PlasmicDataFetcherParent
        internalVariantProps: PlasmicDataFetcherParent__VariantProps,
        internalArgProps: PlasmicDataFetcherParent__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicDataFetcherParent;
    /* prettier-ignore-end */
  
--- PlasmicHelloWorld.css ---
 .HelloWorld__root__cgR3R {
          display: flex;
flex-direction: column;
position: relative;
width: 100%;
height: auto;
justify-content: flex-start;
align-items: center;
min-width: 0;  
      } 
 .HelloWorld__text__eUcDa {
          position: relative;
width: 100%;
height: auto;
max-width: 100%;
min-width: 0;  
      } 
--- PlasmicHelloWorld.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: 3khyLeywU1SP
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicHelloWorld.css"; // plasmic-import: 3khyLeywU1SP/css
createPlasmicElementProxy;
export var PlasmicHelloWorld__VariantProps = new Array();
export var PlasmicHelloWorld__ArgProps = new Array();
var $$ = {};
function PlasmicHelloWorld__RenderFunc(props) {
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({}, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var styleTokensClassNames = _useStyleTokens();
    return (createPlasmicElementProxy("div", { "data-plasmic-name": "root", "data-plasmic-override": overrides.root, "data-plasmic-root": true, "data-plasmic-for-node": forNode, className: classNames("plasmic_default__all", "plasmic_default__div", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "HelloWorld__root__cgR3R") },
        createPlasmicElementProxy("div", { "data-plasmic-name": "text", "data-plasmic-override": overrides.text, className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "HelloWorld__text__eUcDa") }, "My world")));
}
var PlasmicDescendants = {
    root: ["root", "text"],
    text: ["text"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicHelloWorld__ArgProps,
            internalVariantPropNames: PlasmicHelloWorld__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicHelloWorld__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "root") {
        func.displayName = "PlasmicHelloWorld";
    }
    else {
        func.displayName = "PlasmicHelloWorld.".concat(nodeName);
    }
    return func;
}
export var PlasmicHelloWorld = Object.assign(
// Top-level PlasmicHelloWorld renders the root element
makeNodeComponent("root"), {
    // Helper components rendering sub-elements
    text: makeNodeComponent("text"),
    // Metadata about props expected for PlasmicHelloWorld
    internalVariantProps: PlasmicHelloWorld__VariantProps,
    internalArgProps: PlasmicHelloWorld__ArgProps,
});
export default PlasmicHelloWorld;
/* prettier-ignore-end */

--- PlasmicHelloWorld.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: 3khyLeywU1SP
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicHelloWorld.css" // plasmic-import: 3khyLeywU1SP/css
  
    
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicHelloWorld__VariantMembers = {
      
    };
    export type PlasmicHelloWorld__VariantsArgs = {};
    type VariantPropType = keyof PlasmicHelloWorld__VariantsArgs;
    export const PlasmicHelloWorld__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicHelloWorld__ArgsType = {};
type ArgPropType = keyof PlasmicHelloWorld__ArgsType;
export const PlasmicHelloWorld__ArgProps = new Array<ArgPropType>();


    export type PlasmicHelloWorld__OverridesType = {
    root?: Flex__<"div">;
text?: Flex__<"div">;
  };

    
    export interface DefaultHelloWorldProps {
      
      className?: string;
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicHelloWorld__RenderFunc(
      props: {
        variants: PlasmicHelloWorld__VariantsArgs,
        args: PlasmicHelloWorld__ArgsType,
        overrides: PlasmicHelloWorld__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
  
      return (
        <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("plasmic_default__all", "plasmic_default__div", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "HelloWorld__root__cgR3R")}
      
      >
            <div
      data-plasmic-name={"text"}
      data-plasmic-override={overrides.text}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "HelloWorld__text__eUcDa")}
      
      >
            { "My world" }
          </div>
          </div>
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    root: ["root", "text"],
text: ["text"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      root: "div";
text: "div"
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicHelloWorld__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicHelloWorld__VariantsArgs;
        args?: PlasmicHelloWorld__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicHelloWorld__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicHelloWorld__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicHelloWorld__ArgProps,
          internalVariantPropNames: PlasmicHelloWorld__VariantProps,
        }), [props, nodeName]);
        return PlasmicHelloWorld__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "root") {
        func.displayName = "PlasmicHelloWorld";
      } else {
        func.displayName = \`PlasmicHelloWorld.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicHelloWorld = Object.assign(
      // Top-level PlasmicHelloWorld renders the root element
      makeNodeComponent("root"),
      {
        // Helper components rendering sub-elements
        text: makeNodeComponent("text"),

        // Metadata about props expected for PlasmicHelloWorld
        internalVariantProps: PlasmicHelloWorld__VariantProps,
        internalArgProps: PlasmicHelloWorld__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicHelloWorld;
    /* prettier-ignore-end */
  
--- PlasmicIcon__ChevronDown.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";
export function ChevronDownIcon(props) {
    var className = props.className, style = props.style, title = props.title, restProps = __rest(props, ["className", "style", "title"]);
    return (React.createElement("svg", __assign({ xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", height: "1em", className: classNames("plasmic-default__svg", className), style: style }, restProps),
        title && React.createElement("title", null, title),
        React.createElement("path", { d: "M6 9l6 6 6-6", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" })));
}
export default ChevronDownIcon;
/* prettier-ignore-end */

--- PlasmicIcon__ChevronDown.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    import React from "react";
    import {classNames} from "@plasmicapp/react-web";

    export type ChevronDownIconProps = React.ComponentProps<"svg"> & {
      title?: string;
    }

    export function ChevronDownIcon(props: ChevronDownIconProps) {
      const {
        className,
        style,
        title,
        ...restProps
      } = props;
      return (
        <svg
          xmlns={"http://www.w3.org/2000/svg"} fill={"none"} viewBox={"0 0 24 24"} height={"1em"} className={classNames("plasmic-default__svg", className)} style={style}
          {...restProps}
        >
          {title && <title>{title}</title>}
          
  

    <path
      d={"M6 9l6 6 6-6"} stroke={"currentColor"} strokeWidth={"2"} strokeLinecap={"round"} strokeLinejoin={"round"}
    >
      
    </path>
  


        </svg>
      );
    }

    export default ChevronDownIcon;
    /* prettier-ignore-end */
  
--- PlasmicIcon__Circle.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";
export function CircleIcon(props) {
    var className = props.className, style = props.style, title = props.title, restProps = __rest(props, ["className", "style", "title"]);
    return (React.createElement("svg", __assign({ xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", height: "1em", className: classNames("plasmic-default__svg", className), style: style }, restProps),
        title && React.createElement("title", null, title),
        React.createElement("path", { d: "M3 12a9 9 0 1018.001 0A9 9 0 003 12z", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" })));
}
export default CircleIcon;
/* prettier-ignore-end */

--- PlasmicIcon__Circle.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    import React from "react";
    import {classNames} from "@plasmicapp/react-web";

    export type CircleIconProps = React.ComponentProps<"svg"> & {
      title?: string;
    }

    export function CircleIcon(props: CircleIconProps) {
      const {
        className,
        style,
        title,
        ...restProps
      } = props;
      return (
        <svg
          xmlns={"http://www.w3.org/2000/svg"} fill={"none"} viewBox={"0 0 24 24"} height={"1em"} className={classNames("plasmic-default__svg", className)} style={style}
          {...restProps}
        >
          {title && <title>{title}</title>}
          
  

    <path
      d={"M3 12a9 9 0 1018.001 0A9 9 0 003 12z"} stroke={"currentColor"} strokeWidth={"2"} strokeLinecap={"round"} strokeLinejoin={"round"}
    >
      
    </path>
  


        </svg>
      );
    }

    export default CircleIcon;
    /* prettier-ignore-end */
  
--- PlasmicIcon__TriangleFilled.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";
export function TriangleFilledIcon(props) {
    var className = props.className, style = props.style, title = props.title, restProps = __rest(props, ["className", "style", "title"]);
    return (React.createElement("svg", __assign({ xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", height: "1em", className: classNames("plasmic-default__svg", className), style: style }, restProps),
        title && React.createElement("title", null, title),
        React.createElement("path", { d: "M12 1.67a2.914 2.914 0 00-2.492 1.403L1.398 16.61a2.914 2.914 0 002.484 4.385h16.225a2.914 2.914 0 002.503-4.371L14.494 3.078A2.917 2.917 0 0012 1.67z", fill: "currentColor" })));
}
export default TriangleFilledIcon;
/* prettier-ignore-end */

--- PlasmicIcon__TriangleFilled.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    import React from "react";
    import {classNames} from "@plasmicapp/react-web";

    export type TriangleFilledIconProps = React.ComponentProps<"svg"> & {
      title?: string;
    }

    export function TriangleFilledIcon(props: TriangleFilledIconProps) {
      const {
        className,
        style,
        title,
        ...restProps
      } = props;
      return (
        <svg
          xmlns={"http://www.w3.org/2000/svg"} fill={"none"} viewBox={"0 0 24 24"} height={"1em"} className={classNames("plasmic-default__svg", className)} style={style}
          {...restProps}
        >
          {title && <title>{title}</title>}
          
  

    <path
      d={"M12 1.67a2.914 2.914 0 00-2.492 1.403L1.398 16.61a2.914 2.914 0 002.484 4.385h16.225a2.914 2.914 0 002.503-4.371L14.494 3.078A2.917 2.917 0 0012 1.67z"} fill={"currentColor"}
    >
      
    </path>
  


        </svg>
      );
    }

    export default TriangleFilledIcon;
    /* prettier-ignore-end */
  
--- PlasmicIcon__TriangleInvertedFilled.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
import React from "react";
import { classNames } from "@plasmicapp/react-web";
export function TriangleInvertedFilledIcon(props) {
    var className = props.className, style = props.style, title = props.title, restProps = __rest(props, ["className", "style", "title"]);
    return (React.createElement("svg", __assign({ xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", height: "1em", className: classNames("plasmic-default__svg", className), style: style }, restProps),
        title && React.createElement("title", null, title),
        React.createElement("path", { d: "M20.118 3H3.893A2.914 2.914 0 001.39 7.371L9.506 20.92a2.916 2.916 0 004.987.005l8.11-13.539A2.914 2.914 0 0020.118 3z", fill: "currentColor" })));
}
export default TriangleInvertedFilledIcon;
/* prettier-ignore-end */

--- PlasmicIcon__TriangleInvertedFilled.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    import React from "react";
    import {classNames} from "@plasmicapp/react-web";

    export type TriangleInvertedFilledIconProps = React.ComponentProps<"svg"> & {
      title?: string;
    }

    export function TriangleInvertedFilledIcon(props: TriangleInvertedFilledIconProps) {
      const {
        className,
        style,
        title,
        ...restProps
      } = props;
      return (
        <svg
          xmlns={"http://www.w3.org/2000/svg"} fill={"none"} viewBox={"0 0 24 24"} height={"1em"} className={classNames("plasmic-default__svg", className)} style={style}
          {...restProps}
        >
          {title && <title>{title}</title>}
          
  

    <path
      d={"M20.118 3H3.893A2.914 2.914 0 001.39 7.371L9.506 20.92a2.916 2.916 0 004.987.005l8.11-13.539A2.914 2.914 0 0020.118 3z"} fill={"currentColor"}
    >
      
    </path>
  


        </svg>
      );
    }

    export default TriangleInvertedFilledIcon;
    /* prettier-ignore-end */
  
--- PlasmicModalDialog.css ---
 .ModalDialog__ariaDialogTrigger__oSAta.__wab_instance {
          position: relative;  
      } 
 .ModalDialog__svg__o1RZv {
          position: relative;
height: 1em;  
      } 
 .ModalDialog__svg__r5Nhg {
          position: relative;
height: 1em;  
      } 
 .ModalDialog__ariaModal__eaRon.__wab_instance {
          background: var(--token-i3U49r6ROHvf);
display: flex;
flex-direction: column;
color: var(--token-f1PNVAbV3p2v);
font-family: var(--token-o6yJby_7a7Ou);
font-size: var(--token-vDsSvK1cXIbX);
line-height: var(--token-OcwQZQDDtNIc);
overflow: hidden;
align-items: center;
justify-content: flex-start;
max-height: 90vh;
max-width: 90vw;
outline-style: none;
box-shadow: 0px 24px 48px -12px #00000040;
row-gap: var(--token-NpZyVjRiZdqg);
border-radius: var(--token-NpZyVjRiZdqg);  
      } 
 .pcls_QFOecPzS37aO {   background: var(--token-C1pr-KlZHji9);
left: 0px;
top: 0px;
z-index: 10;
position: fixed;
right: 0px;
bottom: 0px;
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
height: 100vh;
width: 100vw;
outline: none;   } 
 .ModalDialog__freeBox__rwLCc {
          display: flex;
flex-direction: column;
justify-content: space-between;
align-items: stretch;
position: relative;
left: auto;
top: auto;
width: 100%;
height: 100%;
min-width: 0;
min-height: 0;  
      } 
 .ModalDialog__ariaHeading__jLi53.__wab_instance {
          flex-shrink: 0;
padding: var(--token-h43DmGzvrmlx);
margin: 0px;  
      } 
 .ModalDialog__scrollableContent__aHjbC {
          display: flex;
flex-direction: column;
position: relative;
align-items: stretch;
justify-content: flex-start;
overflow: auto;
padding-left: var(--token-h43DmGzvrmlx);
padding-right: var(--token-h43DmGzvrmlx);  
      } 
 .ModalDialog__freeBox__u7Zde {
          display: flex;
flex-direction: column;
position: relative;
align-items: flex-start;
justify-content: flex-start;
row-gap: var(--token-hI_1KF0x1IC1);  
      } 
 .ModalDialog__text___8YSHj {
          position: relative;  
      } 
 .ModalDialog__freeBox__ofcdm {
          display: flex;
position: relative;
flex-shrink: 0;
padding: var(--token-h43DmGzvrmlx);  
      } 
 .ModalDialog__freeBox__qo9Cq {
          display: flex;
flex-direction: row;
position: relative;
align-items: center;
justify-content: flex-end;
width: 100%;
min-width: 0;  
      } 
 .ModalDialog__svg__jg8RG {
          position: relative;
height: 1em;  
      } 
 .ModalDialog__text__cz0N8 {
          padding-left: 0px;  
      } 
 .ModalDialog__svg___0Mesm {
          position: relative;
height: 1em;  
      } 
--- PlasmicModalDialog.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: 4D8eN8Bv_isH
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, generateStateOnChangeProp, generateStateValueProp, hasVariant, renderPlasmicSlot, set as $stateSet, useDollarState, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { BaseDialogTrigger } from "@plasmicpkgs/react-aria/skinny/registerDialogTrigger";
import Button from "./Button"; // plasmic-import: D557GwRUy2HJ/component
import { BaseModal } from "@plasmicpkgs/react-aria/skinny/registerModal";
import { BaseHeading } from "@plasmicpkgs/react-aria/skinny/registerHeading";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicModalDialog.css"; // plasmic-import: 4D8eN8Bv_isH/css
createPlasmicElementProxy;
export var PlasmicModalDialog__VariantProps = new Array("noTrigger");
export var PlasmicModalDialog__ArgProps = new Array("isOpen", "trigger", "closeOnBackdropClick", "content", "heading", "showHeader", "showFooter", "isKeyboardDismissDisabled", "onOpenChange", "footer");
var $$ = {};
function PlasmicModalDialog__RenderFunc(props) {
    var _this = this;
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({
        "closeOnBackdropClick": true,
        "showHeader": true,
        "showFooter": true,
    }, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var stateSpecs = React.useMemo(function () {
        return ([{
                path: "ariaDialogTrigger.isOpen",
                type: "writable",
                variableType: "boolean",
                valueProp: "isOpen",
                onChangeProp: "onOpenChange",
            }, {
                path: "ariaModal.isOpen",
                type: "private",
                variableType: "boolean",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return (true);
                },
                refName: "ariaModal",
            }, {
                path: "noTrigger",
                type: "private",
                variableType: "variant",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return ($props.noTrigger);
                },
            }]);
    }, [$props, $ctx, $refs]);
    var $state = useDollarState(stateSpecs, { $props: $props, $ctx: $ctx, $queries: {}, $refs: $refs });
    var styleTokensClassNames = _useStyleTokens();
    return (createPlasmicElementProxy(BaseDialogTrigger, { "data-plasmic-name": "ariaDialogTrigger", "data-plasmic-override": overrides.ariaDialogTrigger, "data-plasmic-root": true, "data-plasmic-for-node": forNode, dialog: createPlasmicElementProxy(BaseModal, { "data-plasmic-name": "ariaModal", "data-plasmic-override": overrides.ariaModal, className: classNames("__wab_instance", "ModalDialog__ariaModal__eaRon", { "ModalDialog__ariaModalnoTrigger__eaRoneQ53M": hasVariant($state, "noTrigger", "noTrigger") }), defaultOpen: true, isDismissable: args.closeOnBackdropClick, isKeyboardDismissDisabled: args.isKeyboardDismissDisabled, isOpen: generateStateValueProp($state, ["ariaModal", "isOpen"]), modalOverlayClass: classNames({ "pcls_QFOecPzS37aO": true }), onOpenChange: function () {
                var eventArgs = [];
                for (var _i = 0; _i < arguments.length; _i++) {
                    eventArgs[_i] = arguments[_i];
                }
                return __awaiter(_this, void 0, void 0, function () {
                    return __generator(this, function (_a) {
                        (generateStateOnChangeProp($state, ["ariaModal", "isOpen"])).apply(null, eventArgs);
                        return [2 /*return*/];
                    });
                });
            }, ref: function (ref) { $refs["ariaModal"] = ref; }, resetClassName: classNames("root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames) },
            createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__rwLCc") },
                (($props.showHeader)) ? (createPlasmicElementProxy(BaseHeading, { "data-plasmic-name": "ariaHeading", "data-plasmic-override": overrides.ariaHeading, className: classNames("__wab_instance", "ModalDialog__ariaHeading__jLi53"), slot: "title" }, renderPlasmicSlot({
                    defaultContents: "Heading",
                    value: args.heading,
                }))) : null,
                createPlasmicElementProxy("div", { "data-plasmic-name": "scrollableContent", "data-plasmic-override": overrides.scrollableContent, className: classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__scrollableContent__aHjbC") }, renderPlasmicSlot({
                    defaultContents: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__u7Zde") },
                        createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "ModalDialog__text___5BY7K") }, "This is a Modal!"),
                        createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "ModalDialog__text___8YSHj") }, "You can put anything here!\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.")),
                    value: args.content,
                })),
                (($props.showFooter)) ? (createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__ofcdm") }, renderPlasmicSlot({
                    defaultContents: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__qo9Cq") },
                        createPlasmicElementProxy(Button, { color: "muted", label: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "ModalDialog__text__cz0N8") }, "Close"), onClick: function (event) { return __awaiter(_this, void 0, void 0, function () {
                                var $steps, _a, _b;
                                return __generator(this, function (_c) {
                                    switch (_c.label) {
                                        case 0:
                                            $steps = {};
                                            $steps["updateAriaDialogTriggerIsOpen"] = true ? (function () {
                                                var _a;
                                                var actionArgs = { variable: {
                                                        objRoot: $state,
                                                        variablePath: ["ariaDialogTrigger", "isOpen"]
                                                    }, operation: 0, value: (false) };
                                                return (_a = (function (_a) {
                                                    var variable = _a.variable, value = _a.value, startIndex = _a.startIndex, deleteCount = _a.deleteCount;
                                                    if (!variable) {
                                                        return;
                                                    }
                                                    var objRoot = variable.objRoot, variablePath = variable.variablePath;
                                                    $stateSet(objRoot, variablePath, value);
                                                    return value;
                                                })) === null || _a === void 0 ? void 0 : _a.apply(null, [actionArgs]);
                                            })() : undefined;
                                            if (!($steps["updateAriaDialogTriggerIsOpen"] != null &&
                                                typeof $steps["updateAriaDialogTriggerIsOpen"] === "object" &&
                                                typeof $steps["updateAriaDialogTriggerIsOpen"].then === "function")) return [3 /*break*/, 2];
                                            _a = $steps;
                                            _b = "updateAriaDialogTriggerIsOpen";
                                            return [4 /*yield*/, ($steps["updateAriaDialogTriggerIsOpen"])];
                                        case 1:
                                            _a[_b] = _c.sent();
                                            _c.label = 2;
                                        case 2: return [2 /*return*/];
                                    }
                                });
                            }); }, type: "soft" })),
                    value: args.footer,
                }))) : null)), isOpen: generateStateValueProp($state, ["ariaDialogTrigger", "isOpen"]), onOpenChange: function () {
            var eventArgs = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                eventArgs[_i] = arguments[_i];
            }
            return __awaiter(_this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    (generateStateOnChangeProp($state, ["ariaDialogTrigger", "isOpen"])).apply(null, eventArgs);
                    return [2 /*return*/];
                });
            });
        }, trigger: (hasVariant($state, "noTrigger", "noTrigger") ? false : true) ? (renderPlasmicSlot({
            defaultContents: createPlasmicElementProxy(Button, { label: "Open Modal" }),
            value: args.trigger,
        })) : null }));
}
var PlasmicDescendants = {
    ariaDialogTrigger: ["ariaDialogTrigger", "ariaModal", "ariaHeading", "scrollableContent"],
    ariaModal: ["ariaModal", "ariaHeading", "scrollableContent"],
    ariaHeading: ["ariaHeading"],
    scrollableContent: ["scrollableContent"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicModalDialog__ArgProps,
            internalVariantPropNames: PlasmicModalDialog__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicModalDialog__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "ariaDialogTrigger") {
        func.displayName = "PlasmicModalDialog";
    }
    else {
        func.displayName = "PlasmicModalDialog.".concat(nodeName);
    }
    return func;
}
export var PlasmicModalDialog = Object.assign(
// Top-level PlasmicModalDialog renders the root element
makeNodeComponent("ariaDialogTrigger"), {
    // Helper components rendering sub-elements
    ariaModal: makeNodeComponent("ariaModal"),
    ariaHeading: makeNodeComponent("ariaHeading"),
    scrollableContent: makeNodeComponent("scrollableContent"),
    // Metadata about props expected for PlasmicModalDialog
    internalVariantProps: PlasmicModalDialog__VariantProps,
    internalArgProps: PlasmicModalDialog__ArgProps,
});
export default PlasmicModalDialog;
/* prettier-ignore-end */

--- PlasmicModalDialog.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: 4D8eN8Bv_isH
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { BaseDialogTrigger } from "@plasmicpkgs/react-aria/skinny/registerDialogTrigger";
import Button from "./Button";  // plasmic-import: D557GwRUy2HJ/component
import { BaseModal } from "@plasmicpkgs/react-aria/skinny/registerModal";
import { BaseHeading } from "@plasmicpkgs/react-aria/skinny/registerHeading";
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicModalDialog.css" // plasmic-import: 4D8eN8Bv_isH/css
  
    import CircleIcon from "./PlasmicIcon__Circle";  // plasmic-import: 4hS9BR4cg9TV/icon
import ChevronDownIcon from "./PlasmicIcon__ChevronDown";  // plasmic-import: lZo9OmVojh_m/icon
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicModalDialog__VariantMembers = {
      noTrigger: "noTrigger";
    };
    export type PlasmicModalDialog__VariantsArgs = {
    noTrigger?: SingleBooleanChoiceArg<"noTrigger">
  };
    type VariantPropType = keyof PlasmicModalDialog__VariantsArgs;
    export const PlasmicModalDialog__VariantProps = new Array<VariantPropType>("noTrigger");
  

    
export type PlasmicModalDialog__ArgsType = {"isOpen"?: boolean;
"trigger"?: React.ReactNode;
"closeOnBackdropClick"?: boolean;
"content"?: React.ReactNode;
"heading"?: React.ReactNode;
"showHeader"?: boolean;
"showFooter"?: boolean;
"isKeyboardDismissDisabled"?: boolean;
"onOpenChange"?: (val: boolean) => void;
"footer"?: React.ReactNode;};
type ArgPropType = keyof PlasmicModalDialog__ArgsType;
export const PlasmicModalDialog__ArgProps = new Array<ArgPropType>("isOpen", "trigger", "closeOnBackdropClick", "content", "heading", "showHeader", "showFooter", "isKeyboardDismissDisabled", "onOpenChange", "footer");


    export type PlasmicModalDialog__OverridesType = {
    ariaDialogTrigger?: Flex__<typeof BaseDialogTrigger>;
ariaModal?: Flex__<typeof BaseModal>;
ariaHeading?: Flex__<typeof BaseHeading>;
scrollableContent?: Flex__<"div">;
  };

    
    export interface DefaultModalDialogProps {
      "isOpen"?: boolean;
"trigger"?: React.ReactNode;
"closeOnBackdropClick"?: boolean;
"content"?: React.ReactNode;
"heading"?: React.ReactNode;
"showHeader"?: boolean;
"showFooter"?: boolean;
"isKeyboardDismissDisabled"?: boolean;
"onOpenChange"?: (val: boolean) => void;
"footer"?: React.ReactNode;
"noTrigger"?: SingleBooleanChoiceArg<"noTrigger">
      
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicModalDialog__RenderFunc(
      props: {
        variants: PlasmicModalDialog__VariantsArgs,
        args: PlasmicModalDialog__ArgsType,
        overrides: PlasmicModalDialog__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    "closeOnBackdropClick": true,
"showHeader": true,
"showFooter": true,
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(() =>
          ([{
      path: "ariaDialogTrigger.isOpen",
      type: "writable",
      variableType: "boolean",
      
      valueProp: "isOpen",
      onChangeProp: "onOpenChange",
      
      
    },{
      path: "ariaModal.isOpen",
      type: "private",
      variableType: "boolean",
      initFunc: ({$props, $state, $queries, $ctx}) => (true),
      
      
      refName: "ariaModal",
      
    },{
      path: "noTrigger",
      type: "private",
      variableType: "variant",
      initFunc: ({$props, $state, $queries, $ctx}) => ( $props.noTrigger),
      
      
      
      
    }])
        , [$props, $ctx, $refs]);
        const $state = useDollarState(stateSpecs, {$props, $ctx, $queries: {}, $refs});
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
  
      return (
        <BaseDialogTrigger
      data-plasmic-name={"ariaDialogTrigger"}
      data-plasmic-override={overrides.ariaDialogTrigger}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      dialog={<BaseModal
      data-plasmic-name={"ariaModal"}
      data-plasmic-override={overrides.ariaModal}
      
      
      className={classNames("__wab_instance", "ModalDialog__ariaModal__eaRon", { "ModalDialog__ariaModalnoTrigger__eaRoneQ53M": hasVariant(
      $state,
      "noTrigger",
      "noTrigger"
    ) })}
defaultOpen={true}
isDismissable={args.closeOnBackdropClick}
isKeyboardDismissDisabled={args.isKeyboardDismissDisabled}
isOpen={generateStateValueProp($state, ["ariaModal","isOpen"])}
modalOverlayClass={classNames({ "pcls_QFOecPzS37aO": true })}
onOpenChange={async (...eventArgs: any) => {
        (generateStateOnChangeProp($state, ["ariaModal","isOpen"])).apply(null, eventArgs);

        

        
      }}
ref={(ref) => { $refs["ariaModal"] = ref; }}
resetClassName={classNames("root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames)}
      
      >
            <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__rwLCc")}
      
      >
            { ((
      $props.showHeader
    )) ? (<BaseHeading
      data-plasmic-name={"ariaHeading"}
      data-plasmic-override={overrides.ariaHeading}
      
      
      className={classNames("__wab_instance", "ModalDialog__ariaHeading__jLi53")}
slot={"title"}
      
      >
            { renderPlasmicSlot({
      defaultContents: "Heading",
      value: args.heading,
      
  }) }
          </BaseHeading>) : null }
<div
      data-plasmic-name={"scrollableContent"}
      data-plasmic-override={overrides.scrollableContent}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__scrollableContent__aHjbC")}
      
      >
            { renderPlasmicSlot({
      defaultContents: <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__u7Zde")}
      
      >
            <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "ModalDialog__text___5BY7K")}
      
      >
            { "This is a Modal!" }
          </div>
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "ModalDialog__text___8YSHj")}
      
      >
            { "You can put anything here!\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\\n\\nLorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum." }
          </div>
          </div>,
      value: args.content,
      
  }) }
          </div>
{ ((
      $props.showFooter
    )) ? (<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__ofcdm")}
      
      >
            { renderPlasmicSlot({
      defaultContents: <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "ModalDialog__freeBox__qo9Cq")}
      
      >
            <Button
      
      
      
      
      color={"muted"}
label={<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "ModalDialog__text__cz0N8")}
      
      >
            { "Close" }
          </div>}
onClick={async (event) => {
        
        const $steps = {};
        
            
            $steps["updateAriaDialogTriggerIsOpen"] = true ? (() => {
          const actionArgs = {variable: {
                objRoot: $state,
                variablePath: ["ariaDialogTrigger", "isOpen"]
              },operation: 0,value: (
      false
    )};
          return (({ variable, value, startIndex, deleteCount }) => {
          if (!variable) {
            return ;
          }
          const { objRoot, variablePath } = variable;
          
    $stateSet(objRoot, variablePath, value);
    return value;
  
        })?.apply(null, [actionArgs]);
        })() : undefined;
            if (
              $steps["updateAriaDialogTriggerIsOpen"] != null &&
              typeof $steps["updateAriaDialogTriggerIsOpen"] === "object" &&
              typeof $steps["updateAriaDialogTriggerIsOpen"].then === "function"
            ) {
              $steps["updateAriaDialogTriggerIsOpen"] = await ($steps["updateAriaDialogTriggerIsOpen"]);
            }
            
            
            
      }}
type={"soft"}
      
      />
          </div>,
      value: args.footer,
      
  }) }
          </div>) : null }
          </div>
          </BaseModal>}
isOpen={generateStateValueProp($state, ["ariaDialogTrigger","isOpen"])}
onOpenChange={async (...eventArgs: any) => {
        (generateStateOnChangeProp($state, ["ariaDialogTrigger","isOpen"])).apply(null, eventArgs);

        

        
      }}
trigger={(hasVariant(
      $state,
      "noTrigger",
      "noTrigger"
    ) ? false : true) ? (renderPlasmicSlot({
      defaultContents: <Button
      
      
      
      
      label={"Open Modal"}
      
      />,
      value: args.trigger,
      
  })) : null}
      
      />
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    ariaDialogTrigger: ["ariaDialogTrigger", "ariaModal", "ariaHeading", "scrollableContent"],
ariaModal: ["ariaModal", "ariaHeading", "scrollableContent"],
ariaHeading: ["ariaHeading"],
scrollableContent: ["scrollableContent"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      ariaDialogTrigger: typeof BaseDialogTrigger;
ariaModal: typeof BaseModal;
ariaHeading: typeof BaseHeading;
scrollableContent: "div"
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicModalDialog__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicModalDialog__VariantsArgs;
        args?: PlasmicModalDialog__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicModalDialog__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicModalDialog__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicModalDialog__ArgProps,
          internalVariantPropNames: PlasmicModalDialog__VariantProps,
        }), [props, nodeName]);
        return PlasmicModalDialog__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "ariaDialogTrigger") {
        func.displayName = "PlasmicModalDialog";
      } else {
        func.displayName = \`PlasmicModalDialog.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicModalDialog = Object.assign(
      // Top-level PlasmicModalDialog renders the root element
      makeNodeComponent("ariaDialogTrigger"),
      {
        // Helper components rendering sub-elements
        ariaModal: makeNodeComponent("ariaModal"),
ariaHeading: makeNodeComponent("ariaHeading"),
scrollableContent: makeNodeComponent("scrollableContent"),

        // Metadata about props expected for PlasmicModalDialog
        internalVariantProps: PlasmicModalDialog__VariantProps,
        internalArgProps: PlasmicModalDialog__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicModalDialog;
    /* prettier-ignore-end */
  
--- PlasmicMyOverlayArrow.css ---
 .MyOverlayArrow__root___043Jz.__wab_instance {
          box-sizing: border-box;
max-width: 100%;
object-fit: cover;
position: relative;  
      } 
 .MyOverlayArrow__freeBox__pU7Yq {
          display: flex;
flex-direction: row;
position: relative;
align-items: stretch;
justify-content: flex-start;
width: 0;
height: 0;
max-width: 100%;
padding: 0;
border-top: 5px solid black;
border-right: 5px solid transparent;
border-left: 5px solid transparent;  
      } 
--- PlasmicMyOverlayArrow.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: N_LiHbyjn30l
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { BaseOverlayArrow } from "@plasmicpkgs/react-aria/skinny/registerOverlayArrow";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicMyOverlayArrow.css"; // plasmic-import: N_LiHbyjn30l/css
createPlasmicElementProxy;
export var PlasmicMyOverlayArrow__VariantProps = new Array();
export var PlasmicMyOverlayArrow__ArgProps = new Array();
var $$ = {};
function PlasmicMyOverlayArrow__RenderFunc(props) {
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({}, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var styleTokensClassNames = _useStyleTokens();
    var _a = React.useState({
        placementTop: false,
        placementLeft: false,
        placementRight: false
    }), $ccVariants = _a[0], setDollarCcVariants = _a[1];
    var updateVariant = React.useCallback(function (changes) {
        setDollarCcVariants(function (prev) {
            if (!Object.keys(changes).some(function (k) { return prev[k] !== changes[k]; })) {
                return prev;
            }
            return __assign(__assign({}, prev), changes);
        });
    }, []);
    return (createPlasmicElementProxy(BaseOverlayArrow, { "data-plasmic-name": "root", "data-plasmic-override": overrides.root, "data-plasmic-root": true, "data-plasmic-for-node": forNode, className: classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "MyOverlayArrow__root___043Jz"), plasmicUpdateVariant: updateVariant },
        createPlasmicElementProxy("div", { "data-plasmic-name": "freeBox", "data-plasmic-override": overrides.freeBox, className: classNames("plasmic_default__all", "plasmic_default__div", "MyOverlayArrow__freeBox__pU7Yq") })));
}
var PlasmicDescendants = {
    root: ["root", "freeBox"],
    freeBox: ["freeBox"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicMyOverlayArrow__ArgProps,
            internalVariantPropNames: PlasmicMyOverlayArrow__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicMyOverlayArrow__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "root") {
        func.displayName = "PlasmicMyOverlayArrow";
    }
    else {
        func.displayName = "PlasmicMyOverlayArrow.".concat(nodeName);
    }
    return func;
}
export var PlasmicMyOverlayArrow = Object.assign(
// Top-level PlasmicMyOverlayArrow renders the root element
makeNodeComponent("root"), {
    // Helper components rendering sub-elements
    freeBox: makeNodeComponent("freeBox"),
    // Metadata about props expected for PlasmicMyOverlayArrow
    internalVariantProps: PlasmicMyOverlayArrow__VariantProps,
    internalArgProps: PlasmicMyOverlayArrow__ArgProps,
});
export default PlasmicMyOverlayArrow;
/* prettier-ignore-end */

--- PlasmicMyOverlayArrow.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: N_LiHbyjn30l
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { BaseOverlayArrow } from "@plasmicpkgs/react-aria/skinny/registerOverlayArrow";
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicMyOverlayArrow.css" // plasmic-import: N_LiHbyjn30l/css
  
    
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicMyOverlayArrow__VariantMembers = {
      
    };
    export type PlasmicMyOverlayArrow__VariantsArgs = {};
    type VariantPropType = keyof PlasmicMyOverlayArrow__VariantsArgs;
    export const PlasmicMyOverlayArrow__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicMyOverlayArrow__ArgsType = {};
type ArgPropType = keyof PlasmicMyOverlayArrow__ArgsType;
export const PlasmicMyOverlayArrow__ArgProps = new Array<ArgPropType>();


    export type PlasmicMyOverlayArrow__OverridesType = {
    root?: Flex__<typeof BaseOverlayArrow>;
freeBox?: Flex__<"div">;
  };

    
    export interface DefaultMyOverlayArrowProps {
      
      className?: string;
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicMyOverlayArrow__RenderFunc(
      props: {
        variants: PlasmicMyOverlayArrow__VariantsArgs,
        args: PlasmicMyOverlayArrow__ArgsType,
        overrides: PlasmicMyOverlayArrow__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
    const [$ccVariants, setDollarCcVariants] = React.useState<Record<string, boolean>>({
      placementTop: false,
placementLeft: false,
placementRight: false
    });
    const updateVariant = React.useCallback((changes: Record<string, boolean>) => {
      setDollarCcVariants((prev) => {
        if (!Object.keys(changes).some((k) => prev[k] !== changes[k])) {
          return prev;
        }
        return { ...prev, ...changes }
      });
    }, []);
  
  
      return (
        <BaseOverlayArrow
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "MyOverlayArrow__root___043Jz")}
plasmicUpdateVariant={updateVariant}
      
      >
            <div
      data-plasmic-name={"freeBox"}
      data-plasmic-override={overrides.freeBox}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "MyOverlayArrow__freeBox__pU7Yq")}
      
      />
          </BaseOverlayArrow>
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    root: ["root", "freeBox"],
freeBox: ["freeBox"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      root: typeof BaseOverlayArrow;
freeBox: "div"
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicMyOverlayArrow__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicMyOverlayArrow__VariantsArgs;
        args?: PlasmicMyOverlayArrow__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicMyOverlayArrow__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicMyOverlayArrow__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicMyOverlayArrow__ArgProps,
          internalVariantPropNames: PlasmicMyOverlayArrow__VariantProps,
        }), [props, nodeName]);
        return PlasmicMyOverlayArrow__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "root") {
        func.displayName = "PlasmicMyOverlayArrow";
      } else {
        func.displayName = \`PlasmicMyOverlayArrow.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicMyOverlayArrow = Object.assign(
      // Top-level PlasmicMyOverlayArrow renders the root element
      makeNodeComponent("root"),
      {
        // Helper components rendering sub-elements
        freeBox: makeNodeComponent("freeBox"),

        // Metadata about props expected for PlasmicMyOverlayArrow
        internalVariantProps: PlasmicMyOverlayArrow__VariantProps,
        internalArgProps: PlasmicMyOverlayArrow__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicMyOverlayArrow;
    /* prettier-ignore-end */
  
--- PlasmicNewPage.css ---
 .NewPage__root__naS0D {
          display: grid;
flex-direction: column;
position: relative;
width: 100%;
height: 100%;
align-content: flex-start;
justify-items: center;
justify-content: flex-start;
align-items: center;
min-width: 0;
min-height: 0;
grid-template-columns: var(--plsmc-viewport-gap) 1fr minmax(0, var(--plsmc-wide-chunk)) min(var(--plsmc-standard-width), calc(100% - var(--plsmc-viewport-gap) - var(--plsmc-viewport-gap))) minmax(0, var(--plsmc-wide-chunk)) 1fr var(--plsmc-viewport-gap) ;  
      } 
 .NewPage__root__naS0D > * {
        grid-column: 4
      } 
 .NewPage__popoverDialog__jyuy8.__wab_instance.__wab_instance {
          max-width: 100%;  
      } 
 .NewPage__button__rSuiv.__wab_instance.__wab_instance {
          max-width: 100%;  
      } 
 .NewPage__svg__g877S {
          position: relative;
height: 1em;  
      } 
 .NewPage__svg__qUbAf {
          position: relative;
height: 1em;  
      } 
 .NewPage__freeBox__xaoCv {
          display: flex;
flex-direction: column;
position: relative;
justify-content: flex-start;
background: var(--token-i3U49r6ROHvf);
align-items: flex-start;
width: 320px;
row-gap: var(--token-q8b6qKDtK1cO);
border-radius: var(--token-hI_1KF0x1IC1);
padding: var(--token-NpZyVjRiZdqg);  
      } 
 .NewPage__text__fVpg0 {
          position: relative;  
      } 
 .NewPage__text__h3DT {
          position: relative;
font-weight: 500;  
      } 
 .NewPage__tooltip__tvWgF.__wab_instance.__wab_instance {
          max-width: 100%;  
      } 
 .NewPage__text__fl7Z4 {
          position: relative;  
      } 
 .NewPage__dataFetcher__sJSn.__wab_instance {
          max-width: 100%;
object-fit: cover;  
      } 
 .NewPage__text__j2Q51 {
          position: relative;
width: 100%;
height: auto;
max-width: 100%;
min-width: 0;  
      } 
 .NewPage__modalDialog__eb26A.__wab_instance.__wab_instance {
          max-width: 100%;  
      } 
 .NewPage__svg___4BEzg {
          position: relative;
height: 1em;  
      } 
 .NewPage__svg___5IO9K {
          position: relative;
height: 1em;  
      } 
 .NewPage__freeBox__iz8D {
          display: flex;
flex-direction: column;
position: relative;
align-items: flex-start;
justify-content: flex-start;
row-gap: var(--token-hI_1KF0x1IC1);  
      } 
 .NewPage__text__kQcT {
          position: relative;  
      } 
 .NewPage__freeBox__zMqn {
          display: flex;
flex-direction: row;
position: relative;
align-items: center;
justify-content: flex-end;
width: 100%;
min-width: 0;  
      } 
 .NewPage__svg___0GBuD {
          position: relative;
height: 1em;  
      } 
 .NewPage__text__yeElH {
          padding-left: 0px;  
      } 
 .NewPage__svg__c7CgQ {
          position: relative;
height: 1em;  
      } 
 .NewPage__button___8MwN4.__wab_instance.__wab_instance {
          max-width: 100%;  
      } 
 .NewPage__svg__f91E {
          position: relative;
height: 1em;  
      } 
 .NewPage__text__z3GsF {
          white-space: pre;  
      } 
 .NewPage__svg__x3YuJ {
          position: relative;
height: 1em;  
      } 
 .NewPage__helloWorld__obrji.__wab_instance {
          max-width: 100%;  
      } 
 .NewPage__text__j1W8I {
          position: relative;
width: 100%;
height: auto;
max-width: 100%;
min-width: 0;  
      } 
--- PlasmicNewPage.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: TNdRzGmxrYew
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, generateStateOnChangeProp, generateStateValueProp, useDollarState, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import PopoverDialog from "./PopoverDialog"; // plasmic-import: 5gJnBNi_9h0B/component
import Button from "./Button"; // plasmic-import: D557GwRUy2HJ/component
import Tooltip from "./Tooltip"; // plasmic-import: pI05-1YiCa_b/component
import MyOverlayArrow from "./MyOverlayArrow"; // plasmic-import: N_LiHbyjn30l/component
import { Fetcher } from "@plasmicapp/react-web/lib/data-sources";
import ModalDialog from "./ModalDialog"; // plasmic-import: 4D8eN8Bv_isH/component
import HelloWorld from "./HelloWorld"; // plasmic-import: 3khyLeywU1SP/component
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicNewPage.css"; // plasmic-import: TNdRzGmxrYew/css
createPlasmicElementProxy;
export var PlasmicNewPage__VariantProps = new Array();
export var PlasmicNewPage__ArgProps = new Array();
var $$ = {};
function PlasmicNewPage__RenderFunc(props) {
    var _this = this;
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({}, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var stateSpecs = React.useMemo(function () {
        return ([{
                path: "popoverDialog.isOpen",
                type: "private",
                variableType: "boolean",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return (undefined);
                },
            }, {
                path: "modalDialog.isOpen",
                type: "private",
                variableType: "boolean",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return (undefined);
                },
            }]);
    }, [$props, $ctx, $refs]);
    var $state = useDollarState(stateSpecs, { $props: $props, $ctx: $ctx, $queries: {}, $refs: $refs });
    var styleTokensClassNames = _useStyleTokens();
    return (createPlasmicElementProxy(React.Fragment, null,
        createPlasmicElementProxy("div", { className: "plasmic_page_wrapper" },
            createPlasmicElementProxy("div", { "data-plasmic-name": "root", "data-plasmic-override": overrides.root, "data-plasmic-root": true, "data-plasmic-for-node": forNode, className: classNames("plasmic_default__all", "plasmic_default__div", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "NewPage__root__naS0D") },
                createPlasmicElementProxy(PopoverDialog, { "data-plasmic-name": "popoverDialog", "data-plasmic-override": overrides.popoverDialog, className: classNames("__wab_instance", "NewPage__popoverDialog__jyuy8"), onOpenChange: function () {
                        var eventArgs = [];
                        for (var _i = 0; _i < arguments.length; _i++) {
                            eventArgs[_i] = arguments[_i];
                        }
                        return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                (generateStateOnChangeProp($state, ["popoverDialog", "isOpen"])).apply(null, eventArgs);
                                if (eventArgs.length > 1 && eventArgs[1] && eventArgs[1]._plasmic_state_init_) {
                                    return [2 /*return*/];
                                }
                                return [2 /*return*/];
                            });
                        });
                    } }),
                createPlasmicElementProxy(Tooltip, { "data-plasmic-name": "tooltip", "data-plasmic-override": overrides.tooltip, className: classNames("__wab_instance", "NewPage__tooltip__tvWgF") }),
                createPlasmicElementProxy(MyOverlayArrow, { "data-plasmic-name": "myOverlayArrow", "data-plasmic-override": overrides.myOverlayArrow, className: classNames("__wab_instance", "NewPage__myOverlayArrow__h7L7T") }),
                createPlasmicElementProxy(Fetcher, { "data-plasmic-name": "dataFetcher", "data-plasmic-override": overrides.dataFetcher, queries: {} }, function ($queries) { return createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "NewPage__text__j2Q51") }, "Enter some text"); }),
                createPlasmicElementProxy(ModalDialog, { "data-plasmic-name": "modalDialog", "data-plasmic-override": overrides.modalDialog, className: classNames("__wab_instance", "NewPage__modalDialog__eb26A"), isOpen: generateStateValueProp($state, ["modalDialog", "isOpen"]), onOpenChange: function () {
                        var eventArgs = [];
                        for (var _i = 0; _i < arguments.length; _i++) {
                            eventArgs[_i] = arguments[_i];
                        }
                        return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                (generateStateOnChangeProp($state, ["modalDialog", "isOpen"])).apply(null, eventArgs);
                                if (eventArgs.length > 1 && eventArgs[1] && eventArgs[1]._plasmic_state_init_) {
                                    return [2 /*return*/];
                                }
                                return [2 /*return*/];
                            });
                        });
                    } }),
                createPlasmicElementProxy(Button, { "data-plasmic-name": "button", "data-plasmic-override": overrides.button, className: classNames("__wab_instance", "NewPage__button___8MwN4") }),
                createPlasmicElementProxy(HelloWorld, { "data-plasmic-name": "helloWorld", "data-plasmic-override": overrides.helloWorld, className: classNames("__wab_instance", "NewPage__helloWorld__obrji") }),
                createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "NewPage__text__j1W8I") }, "Enter some text")))));
}
var PlasmicDescendants = {
    root: ["root", "popoverDialog", "tooltip", "myOverlayArrow", "dataFetcher", "modalDialog", "button", "helloWorld"],
    popoverDialog: ["popoverDialog"],
    tooltip: ["tooltip"],
    myOverlayArrow: ["myOverlayArrow"],
    dataFetcher: ["dataFetcher"],
    modalDialog: ["modalDialog"],
    button: ["button"],
    helloWorld: ["helloWorld"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicNewPage__ArgProps,
            internalVariantPropNames: PlasmicNewPage__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicNewPage__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "root") {
        func.displayName = "PlasmicNewPage";
    }
    else {
        func.displayName = "PlasmicNewPage.".concat(nodeName);
    }
    return func;
}
export var PlasmicNewPage = Object.assign(
// Top-level PlasmicNewPage renders the root element
makeNodeComponent("root"), {
    // Helper components rendering sub-elements
    popoverDialog: makeNodeComponent("popoverDialog"),
    tooltip: makeNodeComponent("tooltip"),
    myOverlayArrow: makeNodeComponent("myOverlayArrow"),
    dataFetcher: makeNodeComponent("dataFetcher"),
    modalDialog: makeNodeComponent("modalDialog"),
    button: makeNodeComponent("button"),
    helloWorld: makeNodeComponent("helloWorld"),
    // Metadata about props expected for PlasmicNewPage
    internalVariantProps: PlasmicNewPage__VariantProps,
    internalArgProps: PlasmicNewPage__ArgProps,
    // Page metadata
    pageMetadata: {
        "title": "",
        "description": "",
        "ogImageSrc": "",
        "canonical": ""
    },
});
export default PlasmicNewPage;
/* prettier-ignore-end */

--- PlasmicNewPage.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: TNdRzGmxrYew
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import PopoverDialog from "./PopoverDialog";  // plasmic-import: 5gJnBNi_9h0B/component
import Button from "./Button";  // plasmic-import: D557GwRUy2HJ/component
import Tooltip from "./Tooltip";  // plasmic-import: pI05-1YiCa_b/component
import MyOverlayArrow from "./MyOverlayArrow";  // plasmic-import: N_LiHbyjn30l/component
import { Fetcher } from "@plasmicapp/react-web/lib/data-sources";
import ModalDialog from "./ModalDialog";  // plasmic-import: 4D8eN8Bv_isH/component
import HelloWorld from "./HelloWorld";  // plasmic-import: 3khyLeywU1SP/component
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicNewPage.css" // plasmic-import: TNdRzGmxrYew/css
  
    import CircleIcon from "./PlasmicIcon__Circle";  // plasmic-import: 4hS9BR4cg9TV/icon
import ChevronDownIcon from "./PlasmicIcon__ChevronDown";  // plasmic-import: lZo9OmVojh_m/icon
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicNewPage__VariantMembers = {
      
    };
    export type PlasmicNewPage__VariantsArgs = {};
    type VariantPropType = keyof PlasmicNewPage__VariantsArgs;
    export const PlasmicNewPage__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicNewPage__ArgsType = {};
type ArgPropType = keyof PlasmicNewPage__ArgsType;
export const PlasmicNewPage__ArgProps = new Array<ArgPropType>();


    export type PlasmicNewPage__OverridesType = {
    root?: Flex__<"div">;
popoverDialog?: Flex__<typeof PopoverDialog>;
tooltip?: Flex__<typeof Tooltip>;
myOverlayArrow?: Flex__<typeof MyOverlayArrow>;
dataFetcher?: Flex__<typeof Fetcher>;
modalDialog?: Flex__<typeof ModalDialog>;
button?: Flex__<typeof Button>;
helloWorld?: Flex__<typeof HelloWorld>;
  };

    
    export interface DefaultNewPageProps {
      
      className?: string;
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicNewPage__RenderFunc(
      props: {
        variants: PlasmicNewPage__VariantsArgs,
        args: PlasmicNewPage__ArgsType,
        overrides: PlasmicNewPage__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(() =>
          ([{
      path: "popoverDialog.isOpen",
      type: "private",
      variableType: "boolean",
      initFunc: ({$props, $state, $queries, $ctx}) => (undefined),
      
      
      
      
    },{
      path: "modalDialog.isOpen",
      type: "private",
      variableType: "boolean",
      initFunc: ({$props, $state, $queries, $ctx}) => (undefined),
      
      
      
      
    }])
        , [$props, $ctx, $refs]);
        const $state = useDollarState(stateSpecs, {$props, $ctx, $queries: {}, $refs});
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
  
      return (
        <React.Fragment>
  

      <div className={"plasmic_page_wrapper"}>
        <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("plasmic_default__all", "plasmic_default__div", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "NewPage__root__naS0D")}
      
      >
            <PopoverDialog
      data-plasmic-name={"popoverDialog"}
      data-plasmic-override={overrides.popoverDialog}
      
      
      className={classNames("__wab_instance", "NewPage__popoverDialog__jyuy8")}
onOpenChange={async (...eventArgs: any) => {
        (generateStateOnChangeProp($state, ["popoverDialog","isOpen"])).apply(null, eventArgs);

        if(eventArgs.length > 1 && eventArgs[1] && eventArgs[1]._plasmic_state_init_) {
  return;
}

        
      }}
      
      />
<Tooltip
      data-plasmic-name={"tooltip"}
      data-plasmic-override={overrides.tooltip}
      
      
      className={classNames("__wab_instance", "NewPage__tooltip__tvWgF")}
      
      />
<MyOverlayArrow
      data-plasmic-name={"myOverlayArrow"}
      data-plasmic-override={overrides.myOverlayArrow}
      
      
      className={classNames("__wab_instance", "NewPage__myOverlayArrow__h7L7T")}
      
      />
<Fetcher
      data-plasmic-name={"dataFetcher"}
      data-plasmic-override={overrides.dataFetcher}
      
      
      queries={{}}
      
      >
            { ($queries: any) => <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "NewPage__text__j2Q51")}
      
      >
            { "Enter some text" }
          </div> }
          </Fetcher>
<ModalDialog
      data-plasmic-name={"modalDialog"}
      data-plasmic-override={overrides.modalDialog}
      
      
      className={classNames("__wab_instance", "NewPage__modalDialog__eb26A")}
isOpen={generateStateValueProp($state, ["modalDialog","isOpen"])}
onOpenChange={async (...eventArgs: any) => {
        (generateStateOnChangeProp($state, ["modalDialog","isOpen"])).apply(null, eventArgs);

        if(eventArgs.length > 1 && eventArgs[1] && eventArgs[1]._plasmic_state_init_) {
  return;
}

        
      }}
      
      />
<Button
      data-plasmic-name={"button"}
      data-plasmic-override={overrides.button}
      
      
      className={classNames("__wab_instance", "NewPage__button___8MwN4")}
      
      />
<HelloWorld
      data-plasmic-name={"helloWorld"}
      data-plasmic-override={overrides.helloWorld}
      
      
      className={classNames("__wab_instance", "NewPage__helloWorld__obrji")}
      
      />
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "NewPage__text__j1W8I")}
      
      >
            { "Enter some text" }
          </div>
          </div>
      </div>
    
</React.Fragment>
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    root: ["root", "popoverDialog", "tooltip", "myOverlayArrow", "dataFetcher", "modalDialog", "button", "helloWorld"],
popoverDialog: ["popoverDialog"],
tooltip: ["tooltip"],
myOverlayArrow: ["myOverlayArrow"],
dataFetcher: ["dataFetcher"],
modalDialog: ["modalDialog"],
button: ["button"],
helloWorld: ["helloWorld"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      root: "div";
popoverDialog: typeof PopoverDialog;
tooltip: typeof Tooltip;
myOverlayArrow: typeof MyOverlayArrow;
dataFetcher: typeof Fetcher;
modalDialog: typeof ModalDialog;
button: typeof Button;
helloWorld: typeof HelloWorld
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicNewPage__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicNewPage__VariantsArgs;
        args?: PlasmicNewPage__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicNewPage__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicNewPage__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicNewPage__ArgProps,
          internalVariantPropNames: PlasmicNewPage__VariantProps,
        }), [props, nodeName]);
        return PlasmicNewPage__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "root") {
        func.displayName = "PlasmicNewPage";
      } else {
        func.displayName = \`PlasmicNewPage.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicNewPage = Object.assign(
      // Top-level PlasmicNewPage renders the root element
      makeNodeComponent("root"),
      {
        // Helper components rendering sub-elements
        popoverDialog: makeNodeComponent("popoverDialog"),
tooltip: makeNodeComponent("tooltip"),
myOverlayArrow: makeNodeComponent("myOverlayArrow"),
dataFetcher: makeNodeComponent("dataFetcher"),
modalDialog: makeNodeComponent("modalDialog"),
button: makeNodeComponent("button"),
helloWorld: makeNodeComponent("helloWorld"),

        // Metadata about props expected for PlasmicNewPage
        internalVariantProps: PlasmicNewPage__VariantProps,
        internalArgProps: PlasmicNewPage__ArgProps,

        

        

        

        
    // Page metadata
    pageMetadata: {
  "title": "",
  "description": "",
  "ogImageSrc": "",
  "canonical": ""
},
  
      }
    );
  

    

    export default PlasmicNewPage;
    /* prettier-ignore-end */
  
--- PlasmicPopover.css ---
 .Popover__root__v9B4R.__wab_instance {
          position: relative;
color: var(--token-f1PNVAbV3p2v);
font-family: var(--token-o6yJby_7a7Ou);
font-size: var(--token-vDsSvK1cXIbX);
line-height: var(--token-OcwQZQDDtNIc);
box-shadow: 0px 0px 20px 0px #00000040;
border-radius: var(--token-hI_1KF0x1IC1);  
      } 
 .Popover__svg___50Dg {
          color: var(--token-i3U49r6ROHvf);
filter: drop-shadow(0px 0px 10px #00000080);
object-fit: cover;
transform: translateX(-50%) translateY(0px) translateZ(0px);
left: 50%;
top: -11px;
position: absolute;
height: 1em;  
      } 
  .Popover__root__v9B4R[data-placement=top] .Popover__svg___50Dg {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(60deg) translateX(-50%) translateY(0%) translateZ(0px);
bottom: -16px;
left: 50%;
top: auto;  
      } 
  .Popover__root__v9B4R[data-placement=right] .Popover__svg___50Dg {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(30deg) translateX(0%) translateY(-50%) translateZ(0px);
left: -14px;
top: 50%;  
      } 
  .Popover__root__v9B4R[data-placement=left] .Popover__svg___50Dg {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(-30deg) translateX(0px) translateY(-50%) translateZ(0px);
right: -14px;
left: auto;
top: 50%;  
      } 
 .Popover__slotTargetContent__i5KaR {
          color: var(--token-f1PNVAbV3p2v);  
      } 
 .Popover__freeBox__cdco {
          display: flex;
flex-direction: column;
position: relative;
justify-content: flex-start;
background: var(--token-i3U49r6ROHvf);
align-items: flex-start;
width: 320px;
row-gap: var(--token-q8b6qKDtK1cO);
border-radius: var(--token-hI_1KF0x1IC1);
padding: var(--token-NpZyVjRiZdqg);  
      } 
 .Popover__text__tzMIc {
          position: relative;  
      } 
 .Popover__text__hzjpw {
          position: relative;
font-weight: 500;  
      } 
 .Popover__text__vYaOz {
          position: relative;  
      } 
--- PlasmicPopover.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: 6YHxfeslQ2vo
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, renderPlasmicSlot, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { BasePopover } from "@plasmicpkgs/react-aria/skinny/registerPopover";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicPopover.css"; // plasmic-import: 6YHxfeslQ2vo/css
import TriangleFilledIcon from "./PlasmicIcon__TriangleFilled"; // plasmic-import: OXFt6YDTVY2b/icon
createPlasmicElementProxy;
export var PlasmicPopover__VariantProps = new Array();
export var PlasmicPopover__ArgProps = new Array("offset", "shouldFlip", "placement", "content", "showArrow");
var $$ = {};
function PlasmicPopover__RenderFunc(props) {
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({
        "shouldFlip": true,
        "placement": "bottom",
        "showArrow": true,
    }, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var styleTokensClassNames = _useStyleTokens();
    var _a = React.useState({
        placementTop: false,
        placementBottom: false,
        placementLeft: false,
        placementRight: false
    }), $ccVariants = _a[0], setDollarCcVariants = _a[1];
    var updateVariant = React.useCallback(function (changes) {
        setDollarCcVariants(function (prev) {
            if (!Object.keys(changes).some(function (k) { return prev[k] !== changes[k]; })) {
                return prev;
            }
            return __assign(__assign({}, prev), changes);
        });
    }, []);
    return (createPlasmicElementProxy(BasePopover, { "data-plasmic-name": "root", "data-plasmic-override": overrides.root, "data-plasmic-root": true, "data-plasmic-for-node": forNode, className: classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "Popover__root__v9B4R"), matchTriggerWidth: true, offset: args.offset, placement: args.placement, plasmicUpdateVariant: updateVariant, resetClassName: classNames("root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames), shouldFlip: args.shouldFlip },
        ((function () {
            try {
                return ($props.showArrow);
            }
            catch (e) {
                if (e instanceof TypeError) {
                    return (true);
                }
                throw e;
            }
        })()) ? (createPlasmicElementProxy(TriangleFilledIcon, { "data-plasmic-name": "svg", "data-plasmic-override": overrides.svg, className: classNames("plasmic_default__all", "plasmic_default__svg", "Popover__svg___50Dg"), role: "img" })) : null,
        renderPlasmicSlot({
            defaultContents: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "Popover__freeBox__cdco") },
                createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Popover__text__tzMIc") }, "This is a Popover!"),
                createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Popover__text__hzjpw") }, "You can put anything here!"),
                createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Popover__text__vYaOz") }, "Use it in a \`Aria Dialog Trigger\` component to trigger it on a button click.")),
            value: args.content,
            className: classNames("Popover__slotTargetContent__i5KaR")
        })));
}
var PlasmicDescendants = {
    root: ["root", "svg"],
    svg: ["svg"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicPopover__ArgProps,
            internalVariantPropNames: PlasmicPopover__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicPopover__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "root") {
        func.displayName = "PlasmicPopover";
    }
    else {
        func.displayName = "PlasmicPopover.".concat(nodeName);
    }
    return func;
}
export var PlasmicPopover = Object.assign(
// Top-level PlasmicPopover renders the root element
makeNodeComponent("root"), {
    // Helper components rendering sub-elements
    svg: makeNodeComponent("svg"),
    // Metadata about props expected for PlasmicPopover
    internalVariantProps: PlasmicPopover__VariantProps,
    internalArgProps: PlasmicPopover__ArgProps,
});
export default PlasmicPopover;
/* prettier-ignore-end */

--- PlasmicPopover.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: 6YHxfeslQ2vo
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { BasePopover } from "@plasmicpkgs/react-aria/skinny/registerPopover";
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicPopover.css" // plasmic-import: 6YHxfeslQ2vo/css
  
    import TriangleFilledIcon from "./PlasmicIcon__TriangleFilled";  // plasmic-import: OXFt6YDTVY2b/icon
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicPopover__VariantMembers = {
      
    };
    export type PlasmicPopover__VariantsArgs = {};
    type VariantPropType = keyof PlasmicPopover__VariantsArgs;
    export const PlasmicPopover__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicPopover__ArgsType = {"offset"?: number;
"shouldFlip"?: boolean;
"placement"?: "bottom"|"bottom left"|"bottom right"|"top"|"top left"|"top right";
"content"?: React.ReactNode;
"showArrow"?: boolean;};
type ArgPropType = keyof PlasmicPopover__ArgsType;
export const PlasmicPopover__ArgProps = new Array<ArgPropType>("offset", "shouldFlip", "placement", "content", "showArrow");


    export type PlasmicPopover__OverridesType = {
    root?: Flex__<typeof BasePopover>;
svg?: Flex__<"svg">;
  };

    
    export interface DefaultPopoverProps {
      "offset"?: number;
"shouldFlip"?: boolean;
"placement"?: "bottom"|"bottom left"|"bottom right"|"top"|"top left"|"top right";
"content"?: React.ReactNode;
"showArrow"?: boolean
      className?: string;
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicPopover__RenderFunc(
      props: {
        variants: PlasmicPopover__VariantsArgs,
        args: PlasmicPopover__ArgsType,
        overrides: PlasmicPopover__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    "shouldFlip": true,
"placement": "bottom",
"showArrow": true,
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
    const [$ccVariants, setDollarCcVariants] = React.useState<Record<string, boolean>>({
      placementTop: false,
placementBottom: false,
placementLeft: false,
placementRight: false
    });
    const updateVariant = React.useCallback((changes: Record<string, boolean>) => {
      setDollarCcVariants((prev) => {
        if (!Object.keys(changes).some((k) => prev[k] !== changes[k])) {
          return prev;
        }
        return { ...prev, ...changes }
      });
    }, []);
  
  
      return (
        <BasePopover
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "Popover__root__v9B4R")}
matchTriggerWidth={true}
offset={args.offset}
placement={args.placement}
plasmicUpdateVariant={updateVariant}
resetClassName={classNames("root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames)}
shouldFlip={args.shouldFlip}
      
      >
            { ((() => {
      try {
        return (
          $props.showArrow
        );
      } catch (e) {
        if(e instanceof TypeError) {
          return (
            true
          );
        }
        throw e;
      }
    })()) ? (<TriangleFilledIcon
      data-plasmic-name={"svg"}
      data-plasmic-override={overrides.svg}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__svg", "Popover__svg___50Dg")}
role={"img"}
      
      />) : null }
{ renderPlasmicSlot({
      defaultContents: <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Popover__freeBox__cdco")}
      
      >
            <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Popover__text__tzMIc")}
      
      >
            { "This is a Popover!" }
          </div>
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Popover__text__hzjpw")}
      
      >
            { "You can put anything here!" }
          </div>
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Popover__text__vYaOz")}
      
      >
            { "Use it in a \`Aria Dialog Trigger\` component to trigger it on a button click." }
          </div>
          </div>,
      value: args.content,
      className: classNames("Popover__slotTargetContent__i5KaR")
  }) }
          </BasePopover>
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    root: ["root", "svg"],
svg: ["svg"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      root: typeof BasePopover;
svg: "svg"
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicPopover__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicPopover__VariantsArgs;
        args?: PlasmicPopover__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicPopover__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicPopover__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPopover__ArgProps,
          internalVariantPropNames: PlasmicPopover__VariantProps,
        }), [props, nodeName]);
        return PlasmicPopover__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "root") {
        func.displayName = "PlasmicPopover";
      } else {
        func.displayName = \`PlasmicPopover.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicPopover = Object.assign(
      // Top-level PlasmicPopover renders the root element
      makeNodeComponent("root"),
      {
        // Helper components rendering sub-elements
        svg: makeNodeComponent("svg"),

        // Metadata about props expected for PlasmicPopover
        internalVariantProps: PlasmicPopover__VariantProps,
        internalArgProps: PlasmicPopover__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicPopover;
    /* prettier-ignore-end */
  
--- PlasmicPopoverDialog.css ---
 .PopoverDialog__ariaDialogTrigger__cyboy.__wab_instance {
          position: relative;  
      } 
 .PopoverDialog__button__k3IZm.__wab_instance.__wab_instance {
          max-width: 100%;  
      } 
 .PopoverDialog__svg__dk3Wl {
          position: relative;
height: 1em;  
      } 
 .PopoverDialog__svg__xHb0J {
          position: relative;
height: 1em;  
      } 
 .PopoverDialog__freeBox__tC4Ze {
          display: flex;
flex-direction: column;
position: relative;
justify-content: flex-start;
background: var(--token-i3U49r6ROHvf);
align-items: flex-start;
width: 320px;
row-gap: var(--token-q8b6qKDtK1cO);
border-radius: var(--token-hI_1KF0x1IC1);
padding: var(--token-NpZyVjRiZdqg);  
      } 
 .PopoverDialog__text__pn4M {
          position: relative;  
      } 
 .PopoverDialog__text__fcSFe {
          position: relative;
font-weight: 500;  
      } 
--- PlasmicPopoverDialog.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: 5gJnBNi_9h0B
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, generateStateOnChangeProp, generateStateValueProp, renderPlasmicSlot, useDollarState, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { BaseDialogTrigger } from "@plasmicpkgs/react-aria/skinny/registerDialogTrigger";
import Button from "./Button"; // plasmic-import: D557GwRUy2HJ/component
import Popover from "./Popover"; // plasmic-import: 6YHxfeslQ2vo/component
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicPopoverDialog.css"; // plasmic-import: 5gJnBNi_9h0B/css
createPlasmicElementProxy;
export var PlasmicPopoverDialog__VariantProps = new Array();
export var PlasmicPopoverDialog__ArgProps = new Array("trigger", "shouldFlip", "placement", "offset", "onOpenChange", "showArrow", "content");
var $$ = {};
function PlasmicPopoverDialog__RenderFunc(props) {
    var _this = this;
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({
        "shouldFlip": true,
        "placement": "bottom",
        "offset": 12,
    }, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var stateSpecs = React.useMemo(function () {
        return ([{
                path: "ariaDialogTrigger.isOpen",
                type: "readonly",
                variableType: "boolean",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return (undefined);
                },
                onChangeProp: "onOpenChange",
            }]);
    }, [$props, $ctx, $refs]);
    var $state = useDollarState(stateSpecs, { $props: $props, $ctx: $ctx, $queries: {}, $refs: $refs });
    var styleTokensClassNames = _useStyleTokens();
    return (createPlasmicElementProxy(BaseDialogTrigger, { "data-plasmic-name": "ariaDialogTrigger", "data-plasmic-override": overrides.ariaDialogTrigger, "data-plasmic-root": true, "data-plasmic-for-node": forNode, dialog: createPlasmicElementProxy(Popover, { "data-plasmic-name": "popover", "data-plasmic-override": overrides.popover, content: renderPlasmicSlot({
                defaultContents: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "PopoverDialog__freeBox__tC4Ze") },
                    createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "PopoverDialog__text__pn4M") }, "This is a Popover!"),
                    createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "PopoverDialog__text__fcSFe") }, "You can put anything here!")),
                value: args.content,
            }), offset: args.offset, placement: args.placement, shouldFlip: args.shouldFlip, showArrow: args.showArrow }), isOpen: generateStateValueProp($state, ["ariaDialogTrigger", "isOpen"]), onOpenChange: function () {
            var eventArgs = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                eventArgs[_i] = arguments[_i];
            }
            return __awaiter(_this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    (generateStateOnChangeProp($state, ["ariaDialogTrigger", "isOpen"])).apply(null, eventArgs);
                    return [2 /*return*/];
                });
            });
        }, trigger: renderPlasmicSlot({
            defaultContents: createPlasmicElementProxy(Button, { className: classNames("__wab_instance", "PopoverDialog__button__k3IZm"), label: "Open Popover" }),
            value: args.trigger,
        }) }));
}
var PlasmicDescendants = {
    ariaDialogTrigger: ["ariaDialogTrigger", "popover"],
    popover: ["popover"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicPopoverDialog__ArgProps,
            internalVariantPropNames: PlasmicPopoverDialog__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicPopoverDialog__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "ariaDialogTrigger") {
        func.displayName = "PlasmicPopoverDialog";
    }
    else {
        func.displayName = "PlasmicPopoverDialog.".concat(nodeName);
    }
    return func;
}
export var PlasmicPopoverDialog = Object.assign(
// Top-level PlasmicPopoverDialog renders the root element
makeNodeComponent("ariaDialogTrigger"), {
    // Helper components rendering sub-elements
    popover: makeNodeComponent("popover"),
    // Metadata about props expected for PlasmicPopoverDialog
    internalVariantProps: PlasmicPopoverDialog__VariantProps,
    internalArgProps: PlasmicPopoverDialog__ArgProps,
});
export default PlasmicPopoverDialog;
/* prettier-ignore-end */

--- PlasmicPopoverDialog.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: 5gJnBNi_9h0B
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { BaseDialogTrigger } from "@plasmicpkgs/react-aria/skinny/registerDialogTrigger";
import Button from "./Button";  // plasmic-import: D557GwRUy2HJ/component
import Popover from "./Popover";  // plasmic-import: 6YHxfeslQ2vo/component
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicPopoverDialog.css" // plasmic-import: 5gJnBNi_9h0B/css
  
    import CircleIcon from "./PlasmicIcon__Circle";  // plasmic-import: 4hS9BR4cg9TV/icon
import ChevronDownIcon from "./PlasmicIcon__ChevronDown";  // plasmic-import: lZo9OmVojh_m/icon
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicPopoverDialog__VariantMembers = {
      
    };
    export type PlasmicPopoverDialog__VariantsArgs = {};
    type VariantPropType = keyof PlasmicPopoverDialog__VariantsArgs;
    export const PlasmicPopoverDialog__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicPopoverDialog__ArgsType = {"trigger"?: React.ReactNode;
"shouldFlip"?: boolean;
"placement"?: "bottom"|"bottom left"|"bottom right"|"top"|"top left"|"top right";
"offset"?: number;
"onOpenChange"?: (val: boolean) => void;
"showArrow"?: boolean;
"content"?: React.ReactNode;};
type ArgPropType = keyof PlasmicPopoverDialog__ArgsType;
export const PlasmicPopoverDialog__ArgProps = new Array<ArgPropType>("trigger", "shouldFlip", "placement", "offset", "onOpenChange", "showArrow", "content");


    export type PlasmicPopoverDialog__OverridesType = {
    ariaDialogTrigger?: Flex__<typeof BaseDialogTrigger>;
popover?: Flex__<typeof Popover>;
  };

    
    export interface DefaultPopoverDialogProps {
      "trigger"?: React.ReactNode;
"shouldFlip"?: boolean;
"placement"?: "bottom"|"bottom left"|"bottom right"|"top"|"top left"|"top right";
"offset"?: number;
"onOpenChange"?: (val: boolean) => void;
"showArrow"?: boolean;
"content"?: React.ReactNode
      
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicPopoverDialog__RenderFunc(
      props: {
        variants: PlasmicPopoverDialog__VariantsArgs,
        args: PlasmicPopoverDialog__ArgsType,
        overrides: PlasmicPopoverDialog__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    "shouldFlip": true,
"placement": "bottom",
"offset": 12,
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(() =>
          ([{
      path: "ariaDialogTrigger.isOpen",
      type: "readonly",
      variableType: "boolean",
      initFunc: ({$props, $state, $queries, $ctx}) => (undefined),
      
      onChangeProp: "onOpenChange",
      
      
    }])
        , [$props, $ctx, $refs]);
        const $state = useDollarState(stateSpecs, {$props, $ctx, $queries: {}, $refs});
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
  
      return (
        <BaseDialogTrigger
      data-plasmic-name={"ariaDialogTrigger"}
      data-plasmic-override={overrides.ariaDialogTrigger}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      dialog={<Popover
      data-plasmic-name={"popover"}
      data-plasmic-override={overrides.popover}
      
      
      content={renderPlasmicSlot({
      defaultContents: <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "PopoverDialog__freeBox__tC4Ze")}
      
      >
            <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "PopoverDialog__text__pn4M")}
      
      >
            { "This is a Popover!" }
          </div>
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "PopoverDialog__text__fcSFe")}
      
      >
            { "You can put anything here!" }
          </div>
          </div>,
      value: args.content,
      
  })}
offset={args.offset}
placement={args.placement}
shouldFlip={args.shouldFlip}
showArrow={args.showArrow}
      
      />}
isOpen={generateStateValueProp($state, ["ariaDialogTrigger","isOpen"])}
onOpenChange={async (...eventArgs: any) => {
        (generateStateOnChangeProp($state, ["ariaDialogTrigger","isOpen"])).apply(null, eventArgs);

        

        
      }}
trigger={renderPlasmicSlot({
      defaultContents: <Button
      
      
      
      
      className={classNames("__wab_instance", "PopoverDialog__button__k3IZm")}
label={"Open Popover"}
      
      />,
      value: args.trigger,
      
  })}
      
      />
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    ariaDialogTrigger: ["ariaDialogTrigger", "popover"],
popover: ["popover"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      ariaDialogTrigger: typeof BaseDialogTrigger;
popover: typeof Popover
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicPopoverDialog__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicPopoverDialog__VariantsArgs;
        args?: PlasmicPopoverDialog__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicPopoverDialog__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicPopoverDialog__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicPopoverDialog__ArgProps,
          internalVariantPropNames: PlasmicPopoverDialog__VariantProps,
        }), [props, nodeName]);
        return PlasmicPopoverDialog__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "ariaDialogTrigger") {
        func.displayName = "PlasmicPopoverDialog";
      } else {
        func.displayName = \`PlasmicPopoverDialog.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicPopoverDialog = Object.assign(
      // Top-level PlasmicPopoverDialog renders the root element
      makeNodeComponent("ariaDialogTrigger"),
      {
        // Helper components rendering sub-elements
        popover: makeNodeComponent("popover"),

        // Metadata about props expected for PlasmicPopoverDialog
        internalVariantProps: PlasmicPopoverDialog__VariantProps,
        internalArgProps: PlasmicPopoverDialog__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicPopoverDialog;
    /* prettier-ignore-end */
  
--- PlasmicStyleTokensProvider.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
// This code is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// plasmic-unformatted
import { createStyleTokensProvider, createUseStyleTokens } from "@plasmicapp/react-web";
import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
var data = {
    base: "plasmic_tokens_**********",
    varianted: [],
};
export var _useStyleTokens = createUseStyleTokens(data, _useGlobalVariants);
export var StyleTokensProvider = createStyleTokensProvider(data, _useGlobalVariants);

--- PlasmicStyleTokensProvider.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
  
    // This code is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // plasmic-unformatted
  
    import { createStyleTokensProvider, createUseStyleTokens } from "@plasmicapp/react-web";

    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
  
    import projectcss from "./plasmic.css"; // plasmic-import: **********/projectcss
  
    const data = {
    base: "plasmic_tokens_**********",
    varianted: [
      
    ],
  };
  
    export const _useStyleTokens = createUseStyleTokens(
      data,
      _useGlobalVariants,
    );
  
    export const StyleTokensProvider = createStyleTokensProvider(
      data,
      _useGlobalVariants,
    );
--- PlasmicTooltip.css ---
 .Tooltip__ariaTooltip__ry2S4.__wab_instance {
          box-sizing: border-box;
position: relative;
color: #FFFFFF;  
      } 
 .Tooltip__slotTargetTrigger__tRz5 {
          color: var(--token-f1PNVAbV3p2v);
font-family: var(--token-o6yJby_7a7Ou);
font-size: var(--token-vDsSvK1cXIbX);
line-height: var(--token-OcwQZQDDtNIc);  
      } 
 .Tooltip__text__kvNiE {
          position: relative;  
      } 
 .Tooltip__svg___3Q8Bc {
          object-fit: cover;
max-width: 100%;
position: absolute;
left: 50%;
color: var(--token-SX0Pi19x0IVO);
transform: translateX(-50%) translateY(0px) translateZ(0px);
width: auto;
height: 1em;
bottom: 1px;  
      } 
  .Tooltip__ariaTooltip__ry2S4[data-placement=bottom] .Tooltip__svg___3Q8Bc {
          bottom: auto;
left: 50%;
top: 10px;
transform: rotateX(0deg) rotateY(0deg) rotateZ(60deg) translateX(-50%) translateY(0px) translateZ(0px);  
      } 
  .Tooltip__ariaTooltip__ry2S4[data-placement=left] .Tooltip__svg___3Q8Bc {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(30deg) translateX(0%) translateY(-50%) translateZ(0px);
bottom: auto;
left: auto;
top: 50%;
right: 6px;  
      } 
  .Tooltip__ariaTooltip__ry2S4[data-placement=right] .Tooltip__svg___3Q8Bc {
          transform: rotateX(0deg) rotateY(0deg) rotateZ(-30deg) translateX(0%) translateY(-50%) translateZ(0px);
bottom: auto;
left: 6px;
top: 50%;  
      } 
 .Tooltip__freeBox__jXb6 {
          display: flex;
background: none;
flex-direction: column;
min-width: 200px;
align-items: center;
justify-content: center;
padding: var(--token-IZkbCcBWqxrg);  
      } 
 .Tooltip__freeBox__zfjIv {
          display: flex;
position: relative;
background: var(--token-SX0Pi19x0IVO);
flex-direction: column;
justify-content: flex-start;
align-items: flex-start;
width: auto;
row-gap: var(--token-oQn8gHl2FNKG);
border-radius: var(--token-YHjOGM4GdhsU);
padding: var(--token-IZkbCcBWqxrg);  
      } 
 .Tooltip__slotTargetContent__kYbf4 {
          color: var(--token-XZj0GlRukXD4);
font-family: var(--token-o6yJby_7a7Ou);
font-size: var(--token-vDsSvK1cXIbX);
line-height: var(--token-OcwQZQDDtNIc);  
      } 
--- PlasmicTooltip.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/* prettier-ignore-start */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */
// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// Component: pI05-1YiCa_b
// plasmic-unformatted
import * as React from "react";
import { classNames, createPlasmicElementProxy, deriveRenderOpts, generateStateOnChangeProp, generateStateValueProp, renderPlasmicSlot, useDollarState, } from "@plasmicapp/react-web";
import { useDataEnv, } from "@plasmicapp/react-web/lib/host";
import { BaseTooltip } from "@plasmicpkgs/react-aria/skinny/registerTooltip";
import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
import "@plasmicapp/react-web/lib/plasmic.css";
import "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
import "./plasmic.css"; // plasmic-import: **********/projectcss
import "./PlasmicTooltip.css"; // plasmic-import: pI05-1YiCa_b/css
import TriangleInvertedFilledIcon from "./PlasmicIcon__TriangleInvertedFilled"; // plasmic-import: fCmbgRnChaU7/icon
createPlasmicElementProxy;
export var PlasmicTooltip__VariantProps = new Array();
export var PlasmicTooltip__ArgProps = new Array("delay", "closeDelay", "placement", "isDisabled", "trigger", "content", "showArrow");
var $$ = {};
function PlasmicTooltip__RenderFunc(props) {
    var _this = this;
    var variants = props.variants, overrides = props.overrides, forNode = props.forNode;
    var args = React.useMemo(function () { return Object.assign({
        "delay": 0,
        "closeDelay": 0,
        "placement": "top",
        "showArrow": false,
    }, Object.fromEntries(Object.entries(props.args).filter(function (_a) {
        var _ = _a[0], v = _a[1];
        return v !== undefined;
    }))); }, [props.args]);
    var $props = __assign(__assign({}, args), variants);
    var $ctx = (useDataEnv === null || useDataEnv === void 0 ? void 0 : useDataEnv()) || {};
    var refsRef = React.useRef({});
    var $refs = refsRef.current;
    var stateSpecs = React.useMemo(function () {
        return ([{
                path: "ariaTooltip.isOpen",
                type: "private",
                variableType: "boolean",
                initFunc: function (_a) {
                    var $props = _a.$props, $state = _a.$state, $queries = _a.$queries, $ctx = _a.$ctx;
                    return (undefined);
                },
            }]);
    }, [$props, $ctx, $refs]);
    var $state = useDollarState(stateSpecs, { $props: $props, $ctx: $ctx, $queries: {}, $refs: $refs });
    var styleTokensClassNames = _useStyleTokens();
    var _a = React.useState({
        placementTop: false,
        placementBottom: false,
        placementLeft: false,
        placementRight: false
    }), $ccVariants = _a[0], setDollarCcVariants = _a[1];
    var updateVariant = React.useCallback(function (changes) {
        setDollarCcVariants(function (prev) {
            if (!Object.keys(changes).some(function (k) { return prev[k] !== changes[k]; })) {
                return prev;
            }
            return __assign(__assign({}, prev), changes);
        });
    }, []);
    return (createPlasmicElementProxy(BaseTooltip, { "data-plasmic-name": "ariaTooltip", "data-plasmic-override": overrides.ariaTooltip, "data-plasmic-root": true, "data-plasmic-for-node": forNode, className: classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "Tooltip__ariaTooltip__ry2S4"), closeDelay: args.closeDelay, delay: args.delay, isDisabled: args.isDisabled, isOpen: generateStateValueProp($state, ["ariaTooltip", "isOpen"]), onOpenChange: function () {
            var eventArgs = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                eventArgs[_i] = arguments[_i];
            }
            return __awaiter(_this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    (generateStateOnChangeProp($state, ["ariaTooltip", "isOpen"])).apply(null, eventArgs);
                    return [2 /*return*/];
                });
            });
        }, placement: args.placement, plasmicUpdateVariant: updateVariant, resetClassName: classNames("root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames), tooltipContent: createPlasmicElementProxy(React.Fragment, null,
            ((function () {
                try {
                    return ($props.showArrow);
                }
                catch (e) {
                    if (e instanceof TypeError) {
                        return (true);
                    }
                    throw e;
                }
            })()) ? (createPlasmicElementProxy(TriangleInvertedFilledIcon, { "data-plasmic-name": "svg", "data-plasmic-override": overrides.svg, className: classNames("plasmic_default__all", "plasmic_default__svg", "Tooltip__svg___3Q8Bc"), role: "img" })) : null,
            createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "Tooltip__freeBox__jXb6") },
                createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "Tooltip__freeBox__zfjIv") }, renderPlasmicSlot({
                    defaultContents: createPlasmicElementProxy(React.Fragment, null,
                        createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Tooltip__text__vIx67") }, "Hello from Tooltip! "),
                        createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Tooltip__text__iHyHe") }, "Lorem ipsum dolor sit amet")),
                    value: args.content,
                    className: classNames("Tooltip__slotTargetContent__kYbf4")
                })))) }, renderPlasmicSlot({
        defaultContents: createPlasmicElementProxy("div", { className: classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Tooltip__text__kvNiE") }, "Hover me!"),
        value: args.trigger,
        className: classNames("Tooltip__slotTargetTrigger__tRz5")
    })));
}
var PlasmicDescendants = {
    ariaTooltip: ["ariaTooltip", "svg"],
    svg: ["svg"],
};
function makeNodeComponent(nodeName) {
    var func = function (props) {
        var _a = React.useMemo(function () { return deriveRenderOpts(props, {
            name: nodeName,
            descendantNames: PlasmicDescendants[nodeName],
            internalArgPropNames: PlasmicTooltip__ArgProps,
            internalVariantPropNames: PlasmicTooltip__VariantProps,
        }); }, [props, nodeName]), variants = _a.variants, args = _a.args, overrides = _a.overrides;
        return PlasmicTooltip__RenderFunc({ variants: variants, args: args, overrides: overrides, forNode: nodeName });
    };
    if (nodeName === "ariaTooltip") {
        func.displayName = "PlasmicTooltip";
    }
    else {
        func.displayName = "PlasmicTooltip.".concat(nodeName);
    }
    return func;
}
export var PlasmicTooltip = Object.assign(
// Top-level PlasmicTooltip renders the root element
makeNodeComponent("ariaTooltip"), {
    // Helper components rendering sub-elements
    svg: makeNodeComponent("svg"),
    // Metadata about props expected for PlasmicTooltip
    internalVariantProps: PlasmicTooltip__VariantProps,
    internalArgProps: PlasmicTooltip__ArgProps,
});
export default PlasmicTooltip;
/* prettier-ignore-end */

--- PlasmicTooltip.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
    /* prettier-ignore-start */
    
    /** @jsxRuntime classic */
    /** @jsx createPlasmicElementProxy */
    /** @jsxFrag React.Fragment */
    
    // This class is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // Component: pI05-1YiCa_b
    // plasmic-unformatted

    

    import * as React from "react";
    

    import {
      Flex as Flex__,
  MultiChoiceArg,
  PlasmicDataSourceContextProvider as PlasmicDataSourceContextProvider__,
  PlasmicIcon as PlasmicIcon__,
  PlasmicImg as PlasmicImg__,
  PlasmicLink as PlasmicLink__,
  PlasmicPageGuard as PlasmicPageGuard__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  Trans as Trans__,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  generateOnMutateForSpec,
  generateStateOnChangeProp,
  generateStateOnChangePropForCodeComponents,
  generateStateValueProp,
  get as $stateGet,
  hasVariant,
  initializeCodeComponentStates,
  initializePlasmicStates,
  makeFragment,
  omit,
  pick,
  renderPlasmicSlot,
  set as $stateSet,
  useCurrentUser,
  useDollarState,
  usePlasmicTranslator,
  useTrigger,
  wrapWithClassName,
  
    } from  "@plasmicapp/react-web";
    import {
      
  DataCtxReader as DataCtxReader__,
  useDataEnv,
  useGlobalActions,
  
    } from "@plasmicapp/react-web/lib/host";
    
    
    
    
    
    import { BaseTooltip } from "@plasmicpkgs/react-aria/skinny/registerTooltip";
    import { _useGlobalVariants } from "./plasmic.tsx"; // plasmic-import: **********/projectModule
    import { _useStyleTokens } from "./PlasmicStyleTokensProvider.tsx"; // plasmic-import: **********/styleTokensProvider
    
    
    import "@plasmicapp/react-web/lib/plasmic.css";
    import  "./plasmic__default_style.css"; // plasmic-import: global/defaultcss
    import  "./plasmic.css"; // plasmic-import: **********/projectcss
    import  "./PlasmicTooltip.css" // plasmic-import: pI05-1YiCa_b/css
  
    import TriangleInvertedFilledIcon from "./PlasmicIcon__TriangleInvertedFilled";  // plasmic-import: fCmbgRnChaU7/icon
    
    
    
    

    createPlasmicElementProxy

    
    

    
    export type PlasmicTooltip__VariantMembers = {
      
    };
    export type PlasmicTooltip__VariantsArgs = {};
    type VariantPropType = keyof PlasmicTooltip__VariantsArgs;
    export const PlasmicTooltip__VariantProps = new Array<VariantPropType>();
  

    
export type PlasmicTooltip__ArgsType = {"delay"?: number;
"closeDelay"?: number;
"placement"?: "top"|"bottom"|"left"|"right";
"isDisabled"?: boolean;
"trigger"?: React.ReactNode;
"content"?: React.ReactNode;
"showArrow"?: boolean;};
type ArgPropType = keyof PlasmicTooltip__ArgsType;
export const PlasmicTooltip__ArgProps = new Array<ArgPropType>("delay", "closeDelay", "placement", "isDisabled", "trigger", "content", "showArrow");


    export type PlasmicTooltip__OverridesType = {
    ariaTooltip?: Flex__<typeof BaseTooltip>;
svg?: Flex__<"svg">;
  };

    
    export interface DefaultTooltipProps {
      "delay"?: number;
"closeDelay"?: number;
"placement"?: "top"|"bottom"|"left"|"right";
"isDisabled"?: boolean;
"trigger"?: React.ReactNode;
"content"?: React.ReactNode;
"showArrow"?: boolean
      className?: string;
  }

    

    

    

    const $$ = {};

    

    

    

    
    function PlasmicTooltip__RenderFunc(
      props: {
        variants: PlasmicTooltip__VariantsArgs,
        args: PlasmicTooltip__ArgsType,
        overrides: PlasmicTooltip__OverridesType,
        forNode?: string
      }
    ) {
      
      
      const {variants, overrides, forNode } = props;

      

      const args = React.useMemo(() => Object.assign({
    "delay": 0,
"closeDelay": 0,
"placement": "top",
"showArrow": false,
  }, Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )),
        [props.args]
      );

      
    const $props = {
      ...args,
      ...variants,
    };

    
    
    const $ctx = useDataEnv?.() || {};
    const refsRef = React.useRef({});
    const $refs = refsRef.current;

    

    

    
    const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(() =>
          ([{
      path: "ariaTooltip.isOpen",
      type: "private",
      variableType: "boolean",
      initFunc: ({$props, $state, $queries, $ctx}) => (undefined),
      
      
      
      
    }])
        , [$props, $ctx, $refs]);
        const $state = useDollarState(stateSpecs, {$props, $ctx, $queries: {}, $refs});
    
    

    

    

    
    
     const styleTokensClassNames = _useStyleTokens();
    
    const [$ccVariants, setDollarCcVariants] = React.useState<Record<string, boolean>>({
      placementTop: false,
placementBottom: false,
placementLeft: false,
placementRight: false
    });
    const updateVariant = React.useCallback((changes: Record<string, boolean>) => {
      setDollarCcVariants((prev) => {
        if (!Object.keys(changes).some((k) => prev[k] !== changes[k])) {
          return prev;
        }
        return { ...prev, ...changes }
      });
    }, []);
  
  
      return (
        <BaseTooltip
      data-plasmic-name={"ariaTooltip"}
      data-plasmic-override={overrides.ariaTooltip}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames("__wab_instance", "root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames, "Tooltip__ariaTooltip__ry2S4")}
closeDelay={args.closeDelay}
delay={args.delay}
isDisabled={args.isDisabled}
isOpen={generateStateValueProp($state, ["ariaTooltip","isOpen"])}
onOpenChange={async (...eventArgs: any) => {
        (generateStateOnChangeProp($state, ["ariaTooltip","isOpen"])).apply(null, eventArgs);

        

        
      }}
placement={args.placement}
plasmicUpdateVariant={updateVariant}
resetClassName={classNames("root_reset_**********", "plasmic_default_styles", "plasmic_mixins", styleTokensClassNames)}
tooltipContent={<React.Fragment>{ ((() => {
      try {
        return (
          $props.showArrow
        );
      } catch (e) {
        if(e instanceof TypeError) {
          return (
            true
          );
        }
        throw e;
      }
    })()) ? (<TriangleInvertedFilledIcon
      data-plasmic-name={"svg"}
      data-plasmic-override={overrides.svg}
      
      
      className={classNames("plasmic_default__all", "plasmic_default__svg", "Tooltip__svg___3Q8Bc")}
role={"img"}
      
      />) : null }
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Tooltip__freeBox__jXb6")}
      
      >
            <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "Tooltip__freeBox__zfjIv")}
      
      >
            { renderPlasmicSlot({
      defaultContents: <React.Fragment><div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Tooltip__text__vIx67")}
      
      >
            { "Hello from Tooltip! " }
          </div>
<div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Tooltip__text__iHyHe")}
      
      >
            { "Lorem ipsum dolor sit amet" }
          </div></React.Fragment>,
      value: args.content,
      className: classNames("Tooltip__slotTargetContent__kYbf4")
  }) }
          </div>
          </div></React.Fragment>}
      
      >
            { renderPlasmicSlot({
      defaultContents: <div
      
      
      
      
      className={classNames("plasmic_default__all", "plasmic_default__div", "__wab_text", "Tooltip__text__kvNiE")}
      
      >
            { "Hover me!" }
          </div>,
      value: args.trigger,
      className: classNames("Tooltip__slotTargetTrigger__tRz5")
  }) }
          </BaseTooltip>
      ) as React.ReactElement | null;
    }

    

    
    const PlasmicDescendants = {
    ariaTooltip: ["ariaTooltip", "svg"],
svg: ["svg"],
  } as const;
    type NodeNameType = keyof typeof PlasmicDescendants;
    type DescendantsType<T extends NodeNameType> = (typeof PlasmicDescendants)[T][number];
    type NodeDefaultElementType = {
      ariaTooltip: typeof BaseTooltip;
svg: "svg"
    };
  

    
    type ReservedPropsType = "variants" | "args" | "overrides";
    type NodeOverridesType<T extends NodeNameType> = Pick<PlasmicTooltip__OverridesType, DescendantsType<T>>;
    type NodeComponentProps<T extends NodeNameType> = (
      // Explicitly specify variants, args, and overrides as objects
      & {
        variants?: PlasmicTooltip__VariantsArgs;
        args?: PlasmicTooltip__ArgsType;
        overrides?: NodeOverridesType<T>;
      }
      // Specify variants directly as props
      & Omit<PlasmicTooltip__VariantsArgs, ReservedPropsType>
      // Specify args directly as props
      & Omit<PlasmicTooltip__ArgsType, ReservedPropsType>
      // Specify overrides for each element directly as props
      & Omit<NodeOverridesType<T>, ReservedPropsType | VariantPropType | ArgPropType>
      // Specify props for the root element
      & Omit<Partial<React.ComponentProps<NodeDefaultElementType[T]>>, ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>>
    );
    function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
      type PropsType = NodeComponentProps<NodeName> & {key?: React.Key};
      const func = function<T extends PropsType>(props: T & StrictProps<T, PropsType>) {
        const {variants, args, overrides} = React.useMemo(() => deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicTooltip__ArgProps,
          internalVariantPropNames: PlasmicTooltip__VariantProps,
        }), [props, nodeName]);
        return PlasmicTooltip__RenderFunc({ variants, args, overrides, forNode: nodeName });
      };
      if (nodeName === "ariaTooltip") {
        func.displayName = "PlasmicTooltip";
      } else {
        func.displayName = \`PlasmicTooltip.\${nodeName}\`;
      }
      return func;
    }

    
    

    export const PlasmicTooltip = Object.assign(
      // Top-level PlasmicTooltip renders the root element
      makeNodeComponent("ariaTooltip"),
      {
        // Helper components rendering sub-elements
        svg: makeNodeComponent("svg"),

        // Metadata about props expected for PlasmicTooltip
        internalVariantProps: PlasmicTooltip__VariantProps,
        internalArgProps: PlasmicTooltip__ArgProps,

        

        

        

        
      }
    );
  

    

    export default PlasmicTooltip;
    /* prettier-ignore-end */
  
--- Popover.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicPopover } from "./PlasmicPopover"; // plasmic-import: 6YHxfeslQ2vo/render
function Popover(props) {
    // Use PlasmicPopover to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicPopover are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all PopoverProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicPopover, __assign({}, props));
}
export default Popover;

--- Popover.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicPopover, DefaultPopoverProps} from "./PlasmicPopover";  // plasmic-import: 6YHxfeslQ2vo/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface PopoverProps extends Omit<DefaultPopoverProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultPopoverProps altogether and have
    // total control over the props for your component.
    export interface PopoverProps extends DefaultPopoverProps {
    }

    
      function Popover(props: PopoverProps) {
        
    // Use PlasmicPopover to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicPopover are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all PopoverProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicPopover {...props} />;
      }

      export default Popover;
    
  
--- PopoverDialog.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicPopoverDialog } from "./PlasmicPopoverDialog"; // plasmic-import: 5gJnBNi_9h0B/render
function PopoverDialog(props) {
    // Use PlasmicPopoverDialog to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicPopoverDialog are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all PopoverDialogProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicPopoverDialog, __assign({}, props));
}
export default PopoverDialog;

--- PopoverDialog.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicPopoverDialog, DefaultPopoverDialogProps} from "./PlasmicPopoverDialog";  // plasmic-import: 5gJnBNi_9h0B/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface PopoverDialogProps extends Omit<DefaultPopoverDialogProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultPopoverDialogProps altogether and have
    // total control over the props for your component.
    export interface PopoverDialogProps extends DefaultPopoverDialogProps {
    }

    
      function PopoverDialog(props: PopoverDialogProps) {
        
    // Use PlasmicPopoverDialog to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicPopoverDialog are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all PopoverDialogProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicPopoverDialog {...props} />;
      }

      export default PopoverDialog;
    
  
--- Tooltip.js ---
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
// This is a skeleton starter React component generated by Plasmic.
// This file is owned by you, feel free to edit as you see fit.
import * as React from "react";
import { PlasmicTooltip } from "./PlasmicTooltip"; // plasmic-import: pI05-1YiCa_b/render
function Tooltip(props) {
    // Use PlasmicTooltip to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicTooltip are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all TooltipProps here, but feel free
    // to do whatever works for you.
    return React.createElement(PlasmicTooltip, __assign({}, props));
}
export default Tooltip;

--- Tooltip.tsx ---

    // This is a skeleton starter React component generated by Plasmic.
    // This file is owned by you, feel free to edit as you see fit.
    import * as React from "react";
    import {PlasmicTooltip, DefaultTooltipProps} from "./PlasmicTooltip";  // plasmic-import: pI05-1YiCa_b/render
    

    

    

    // Your component props start with props for variants and slots you defined
    // in Plasmic, but you can add more here, like event handlers that you can
    // attach to named nodes in your component.
    //
    // If you don't want to expose certain variants or slots as a prop, you can use
    // Omit to hide them:
    //
    // interface TooltipProps extends Omit<DefaultTooltipProps, "hideProps1"|"hideProp2"> {
    //   // etc.
    // }
    //
    // You can also stop extending from DefaultTooltipProps altogether and have
    // total control over the props for your component.
    export interface TooltipProps extends DefaultTooltipProps {
    }

    
      function Tooltip(props: TooltipProps) {
        
    // Use PlasmicTooltip to render this component as it was
    // designed in Plasmic, by activating the appropriate variants,
    // attaching the appropriate event handlers, etc.  You
    // can also install whatever React hooks you need here to manage state or
    // fetch data.
    //
    // Props you can pass into PlasmicTooltip are:
    // 1. Variants you want to activate,
    // 2. Contents for slots you want to fill,
    // 3. Overrides for any named node in the component to attach behavior and data,
    // 4. Props to set on the root node.
    //
    // By default, we are just piping all TooltipProps here, but feel free
    // to do whatever works for you.
  
        return <PlasmicTooltip {...props} />;
      }

      export default Tooltip;
    
  
--- plasmic.css ---

      @import url('https://fonts.googleapis.com/css2?family=Roboto%3Aital%2Cwght%400%2C400%3B0%2C500%3B0%2C700%3B0%2C900&family=Inter%3Aital%2Cwght%400%2C400%3B0%2C500%3B0%2C600%3B0%2C700%3B0%2C900&family=Inconsolata%3Aital%2Cwght%400%2C400%3B0%2C500%3B0%2C600%3B0%2C700%3B0%2C900&display=swap');
      
        
        .plasmic_tokens_********** {
          --token-q8b6qKDtK1cO: 0.5rem; --plasmic-token-size-8: var(--token-q8b6qKDtK1cO); --token-NpZyVjRiZdqg: 1rem; --plasmic-token-size-16: var(--token-NpZyVjRiZdqg); --token-hI_1KF0x1IC1: 0.75rem; --plasmic-token-3: var(--token-hI_1KF0x1IC1); --token-i3U49r6ROHvf: #FFFFFF; --plasmic-token-basic-container-background: var(--token-i3U49r6ROHvf); --token-t3vITKRpSfAP: #2563EB; --plasmic-token-brand-brand: var(--token-t3vITKRpSfAP); --token-o6yJby_7a7Ou: Inter; --plasmic-token-sans-serif: var(--token-o6yJby_7a7Ou); --token-vDsSvK1cXIbX: 1rem; --plasmic-token-font-md: var(--token-vDsSvK1cXIbX); --token-OcwQZQDDtNIc: 1.25; --plasmic-token-line-height-md: var(--token-OcwQZQDDtNIc); --token-hUExw1ok-7My: 0rem; --plasmic-token-size-0: var(--token-hUExw1ok-7My); --token-2SGQcnOQi1BY: 0.375rem; --plasmic-token-size-6: var(--token-2SGQcnOQi1BY); --token-oUG3E93FPdG8: #374151; --plasmic-token-neutral-neutral: var(--token-oUG3E93FPdG8); --token-9nmyYGHszf0U: #DC2626; --plasmic-token-destructive-destructive: var(--token-9nmyYGHszf0U); --token-RbmHOSbtxZmu: #6B7280; --plasmic-token-muted-muted: var(--token-RbmHOSbtxZmu); --token-oQn8gHl2FNKG: 0.25rem; --plasmic-token-size-4: var(--token-oQn8gHl2FNKG); --token-Ao4NZQI8FNby: 0.75rem; --plasmic-token-font-sm: var(--token-Ao4NZQI8FNby); --token-PITewDT1atQM: 1; --plasmic-token-line-height-sm: var(--token-PITewDT1atQM); --token-lA8wtoWJjrz7: 1.25rem; --plasmic-token-font-lg: var(--token-lA8wtoWJjrz7); --token-6zoJ_0lAlBNf: 1.4; --plasmic-token-line-height-lg: var(--token-6zoJ_0lAlBNf); --token-f1EInfSh9bJ1: 0.125rem; --plasmic-token-size-2: var(--token-f1EInfSh9bJ1); --token-pjPGWdmPsMUz: #16A34A; --plasmic-token-success-success: var(--token-pjPGWdmPsMUz); --token-b43TAFXK9Pxn: #FACC15; --plasmic-token-warning-warning: var(--token-b43TAFXK9Pxn); --token-cuGgthrhEq0n: #DBEAFE; --plasmic-token-brand-brand-soft: var(--token-cuGgthrhEq0n); --token-c-Ge-TPxBoxs: #E5E7EB; --plasmic-token-neutral-neutral-soft: var(--token-c-Ge-TPxBoxs); --token-oaolgBFC7x92: #F3F4F6; --plasmic-token-muted-muted-soft: var(--token-oaolgBFC7x92); --token-OHf2f9VUoB_O: #DCFCE7; --plasmic-token-success-success-soft: var(--token-OHf2f9VUoB_O); --token-gsX_1U5GfPYR: #FEF9C3; --plasmic-token-warning-warning-soft: var(--token-gsX_1U5GfPYR); --token-hHWiWZ2Rpr4b: #FEE2E2; --plasmic-token-destructive-destructive-soft: var(--token-hHWiWZ2Rpr4b); --token-ULH4hQ23d_Sv: #93C5FD; --plasmic-token-brand-brand-border: var(--token-ULH4hQ23d_Sv); --token-ScdXHpuBD4ei: #D1D5DB; --plasmic-token-neutral-neutral-border: var(--token-ScdXHpuBD4ei); --token-FIQKkwksz5FV: #E5E7EB; --plasmic-token-muted-muted-border: var(--token-FIQKkwksz5FV); --token-4pJCxGA_Sa7p: #86EFAC; --plasmic-token-success-success-border: var(--token-4pJCxGA_Sa7p); --token-glWDd2lYSUSa: #FDE047; --plasmic-token-warning-warning-border: var(--token-glWDd2lYSUSa); --token-i87-wilVu3z7: #FCA5A5; --plasmic-token-destructive-destructive-border: var(--token-i87-wilVu3z7); --token-AT94SE3lDMCW: 0rem; --plasmic-token-0: var(--token-AT94SE3lDMCW); --token-RVAopeQq_eqC: #0000001A; --plasmic-token-interaction-hovered: var(--token-RVAopeQq_eqC); --token-eccLB1btakvR: #FFFFFF40; --plasmic-token-interaction-disabled: var(--token-eccLB1btakvR); --token-W3OCHsj5ohvN: #00000033; --plasmic-token-interaction-pressed: var(--token-W3OCHsj5ohvN); --token-wlM-_lPQTiUn: 0.625rem; --plasmic-token-size-10: var(--token-wlM-_lPQTiUn); --token-IZkbCcBWqxrg: 0.75rem; --plasmic-token-size-12: var(--token-IZkbCcBWqxrg); --token-khAiiMlnWfNQ: #EFF6FF; --plasmic-token-brand-brand-foreground: var(--token-khAiiMlnWfNQ); --token-XZj0GlRukXD4: #F9FAFB; --plasmic-token-neutral-neutral-foreground: var(--token-XZj0GlRukXD4); --token-V7R45kYSsL2g: #E5E7EB; --plasmic-token-muted-muted-foreground: var(--token-V7R45kYSsL2g); --token-d71RMbT3EJe6: #1E3A8A; --plasmic-token-brand-brand-soft-foreground: var(--token-d71RMbT3EJe6); --token-9A3Vv0G2WK2I: #422006; --plasmic-token-warning-warning-foreground: var(--token-9A3Vv0G2WK2I); --token-q5_E41GOjKS6: #F0FDF4; --plasmic-token-success-success-foreground: var(--token-q5_E41GOjKS6); --token-DI2Jhin1tpeq: #FEF2F2; --plasmic-token-destructive-destructive-foreground: var(--token-DI2Jhin1tpeq); --token-LDvOzPYSOKeL: #111827; --plasmic-token-neutral-neutral-soft-foreground: var(--token-LDvOzPYSOKeL); --token-XsbmIM-jiG1T: #6B7280; --plasmic-token-muted-muted-soft-foreground: var(--token-XsbmIM-jiG1T); --token-jgnKGJBIrlpY: #14532D; --plasmic-token-success-success-soft-foreground: var(--token-jgnKGJBIrlpY); --token-nm_aC4o1kFsY: #713F12; --plasmic-token-warning-warning-soft-foreground: var(--token-nm_aC4o1kFsY); --token-Mwf0LwBRtoUP: #7F1D1D; --plasmic-token-destructive-destructive-soft-foreground: var(--token-Mwf0LwBRtoUP); --token-f1PNVAbV3p2v: #030712; --plasmic-token-basic-text-primary: var(--token-f1PNVAbV3p2v); --token-qF9YcyLJz1G9: #ffffff; --plasmic-token-unnamed-style-token: var(--token-qF9YcyLJz1G9); --token-SX0Pi19x0IVO: #171717; --plasmic-token-neutral-900: var(--token-SX0Pi19x0IVO); --token-YHjOGM4GdhsU: 0.5rem; --plasmic-token-2: var(--token-YHjOGM4GdhsU); --token-C1pr-KlZHji9: #0A0A0A80; --plasmic-token-basic-overlay-background: var(--token-C1pr-KlZHji9); --token-h43DmGzvrmlx: 1.5rem; --plasmic-token-size-24: var(--token-h43DmGzvrmlx)
        }
        
      
  .plasmic_tokens_********** {
    --plsmc-standard-width: 800px;
    --plsmc-wide-width: 1280px;
    --plsmc-viewport-gap: 16px;
    --plsmc-wide-chunk: calc(((var(--plsmc-wide-width) - var(--plsmc-standard-width)) / 2) - var(--plsmc-viewport-gap));
  }
    
      
        
        .plasmic_default_styles {
          --mixin-xbpnamo4RagB_font-family: "Roboto", sans-serif; --mixin-xbpnamo4RagB_font-size: 16px; --mixin-xbpnamo4RagB_font-weight: 400; --mixin-xbpnamo4RagB_font-style: normal; --mixin-xbpnamo4RagB_color: #535353; --mixin-xbpnamo4RagB_text-align: left; --mixin-xbpnamo4RagB_text-transform: none; --mixin-xbpnamo4RagB_line-height: 1.5; --mixin-xbpnamo4RagB_letter-spacing: normal; --mixin-xbpnamo4RagB_white-space: pre-wrap; --mixin-xbpnamo4RagB_user-select: text; --mixin-xbpnamo4RagB_text-decoration-line: none; --mixin-xbpnamo4RagB_text-overflow: clip; --mixin-ZpcZYCfrdhwc_font-family: "Inter", sans-serif; --mixin-ZpcZYCfrdhwc_color: #000000; --mixin-ZpcZYCfrdhwc_font-size: 72px; --mixin-ZpcZYCfrdhwc_font-weight: 900; --mixin-ZpcZYCfrdhwc_letter-spacing: -4px; --mixin-ZpcZYCfrdhwc_line-height: 1; --mixin-ZpcZYCfrdhwc_white-space: pre-wrap; --mixin-zEreiBzDcWX8_font-family: "Inter", sans-serif; --mixin-zEreiBzDcWX8_color: #000000; --mixin-zEreiBzDcWX8_font-size: 48px; --mixin-zEreiBzDcWX8_font-weight: 700; --mixin-zEreiBzDcWX8_letter-spacing: -1px; --mixin-zEreiBzDcWX8_line-height: 1.1; --mixin-zEreiBzDcWX8_white-space: pre-wrap; --mixin-z4uR8NTRgzqw_font-family: "Inter", sans-serif; --mixin-z4uR8NTRgzqw_color: #000000; --mixin-z4uR8NTRgzqw_font-size: 32px; --mixin-z4uR8NTRgzqw_font-weight: 600; --mixin-z4uR8NTRgzqw_letter-spacing: -0.8px; --mixin-z4uR8NTRgzqw_line-height: 1.2; --mixin-z4uR8NTRgzqw_white-space: pre-wrap; --mixin-60T-rxPFDSmt_font-family: "Inter", sans-serif; --mixin-60T-rxPFDSmt_color: #000000; --mixin-60T-rxPFDSmt_font-size: 24px; --mixin-60T-rxPFDSmt_font-weight: 600; --mixin-60T-rxPFDSmt_letter-spacing: -0.5px; --mixin-60T-rxPFDSmt_line-height: 1.3; --mixin-60T-rxPFDSmt_white-space: pre-wrap; --mixin--PO0rD7k6KOq_font-family: "Inter", sans-serif; --mixin--PO0rD7k6KOq_color: #000000; --mixin--PO0rD7k6KOq_font-size: 20px; --mixin--PO0rD7k6KOq_font-weight: 600; --mixin--PO0rD7k6KOq_letter-spacing: -0.3px; --mixin--PO0rD7k6KOq_line-height: 1.5; --mixin--PO0rD7k6KOq_white-space: pre-wrap; --mixin-ZxgNJMFmlsHI_font-family: "Inter", sans-serif; --mixin-ZxgNJMFmlsHI_color: #000000; --mixin-ZxgNJMFmlsHI_font-size: 16px; --mixin-ZxgNJMFmlsHI_font-weight: 600; --mixin-ZxgNJMFmlsHI_line-height: 1.5; --mixin-ZxgNJMFmlsHI_white-space: pre-wrap; --mixin-_I8SLonmXBRB_color: #0070f3; --mixin-_I8SLonmXBRB_white-space: pre-wrap; --mixin-nGYsAPgBucgP_color: #3291ff; --mixin-nGYsAPgBucgP_white-space: pre-wrap; --mixin-ZhATVRpjLkPR_border-left-color: #dddddd; --mixin-ZhATVRpjLkPR_border-left-style: solid; --mixin-ZhATVRpjLkPR_border-left-width: 3px; --mixin-ZhATVRpjLkPR_color: #888888; --mixin-ZhATVRpjLkPR_padding-left: 10px; --mixin-ZhATVRpjLkPR_white-space: pre-wrap; --mixin-hTAKkhlPFzr__background: linear-gradient(#f8f8f8, #f8f8f8); --mixin-hTAKkhlPFzr__border-bottom-color: #dddddd; --mixin-hTAKkhlPFzr__border-bottom-style: solid; --mixin-hTAKkhlPFzr__border-bottom-width: 1px; --mixin-hTAKkhlPFzr__border-left-color: #dddddd; --mixin-hTAKkhlPFzr__border-left-style: solid; --mixin-hTAKkhlPFzr__border-left-width: 1px; --mixin-hTAKkhlPFzr__border-right-color: #dddddd; --mixin-hTAKkhlPFzr__border-right-style: solid; --mixin-hTAKkhlPFzr__border-right-width: 1px; --mixin-hTAKkhlPFzr__border-top-color: #dddddd; --mixin-hTAKkhlPFzr__border-top-style: solid; --mixin-hTAKkhlPFzr__border-top-width: 1px; --mixin-hTAKkhlPFzr__border-bottom-left-radius: 3px; --mixin-hTAKkhlPFzr__border-bottom-right-radius: 3px; --mixin-hTAKkhlPFzr__border-top-left-radius: 3px; --mixin-hTAKkhlPFzr__border-top-right-radius: 3px; --mixin-hTAKkhlPFzr__font-family: "Inconsolata"; --mixin-hTAKkhlPFzr__padding-bottom: 1px; --mixin-hTAKkhlPFzr__padding-left: 4px; --mixin-hTAKkhlPFzr__padding-right: 4px; --mixin-hTAKkhlPFzr__padding-top: 1px; --mixin-hTAKkhlPFzr__white-space: pre-wrap; --mixin-fjDwK3uTQLt8_background: linear-gradient(#f8f8f8, #f8f8f8); --mixin-fjDwK3uTQLt8_border-bottom-color: #dddddd; --mixin-fjDwK3uTQLt8_border-bottom-style: solid; --mixin-fjDwK3uTQLt8_border-bottom-width: 1px; --mixin-fjDwK3uTQLt8_border-left-color: #dddddd; --mixin-fjDwK3uTQLt8_border-left-style: solid; --mixin-fjDwK3uTQLt8_border-left-width: 1px; --mixin-fjDwK3uTQLt8_border-right-color: #dddddd; --mixin-fjDwK3uTQLt8_border-right-style: solid; --mixin-fjDwK3uTQLt8_border-right-width: 1px; --mixin-fjDwK3uTQLt8_border-top-color: #dddddd; --mixin-fjDwK3uTQLt8_border-top-style: solid; --mixin-fjDwK3uTQLt8_border-top-width: 1px; --mixin-fjDwK3uTQLt8_border-bottom-left-radius: 3px; --mixin-fjDwK3uTQLt8_border-bottom-right-radius: 3px; --mixin-fjDwK3uTQLt8_border-top-left-radius: 3px; --mixin-fjDwK3uTQLt8_border-top-right-radius: 3px; --mixin-fjDwK3uTQLt8_font-family: "Inconsolata"; --mixin-fjDwK3uTQLt8_padding-bottom: 3px; --mixin-fjDwK3uTQLt8_padding-left: 6px; --mixin-fjDwK3uTQLt8_padding-right: 6px; --mixin-fjDwK3uTQLt8_padding-top: 3px; --mixin-fjDwK3uTQLt8_white-space: pre-wrap; --mixin-wcZze0bZcVEh_display: flex; --mixin-wcZze0bZcVEh_flex-direction: column; --mixin-wcZze0bZcVEh_align-items: stretch; --mixin-wcZze0bZcVEh_justify-content: flex-start; --mixin-wcZze0bZcVEh_list-style-position: outside; --mixin-wcZze0bZcVEh_padding-left: 40px; --mixin-wcZze0bZcVEh_position: relative; --mixin-wcZze0bZcVEh_list-style-type: decimal; --mixin-wcZze0bZcVEh_white-space: pre-wrap; --mixin-SFG6O5da3UHa_display: flex; --mixin-SFG6O5da3UHa_flex-direction: column; --mixin-SFG6O5da3UHa_align-items: stretch; --mixin-SFG6O5da3UHa_justify-content: flex-start; --mixin-SFG6O5da3UHa_list-style-position: outside; --mixin-SFG6O5da3UHa_padding-left: 40px; --mixin-SFG6O5da3UHa_position: relative; --mixin-SFG6O5da3UHa_list-style-type: disc; --mixin-SFG6O5da3UHa_white-space: pre-wrap
        }
        
      
      
      :where(.root_reset_**********) {
      font-family: var(--mixin-xbpnamo4RagB_font-family);
font-size: var(--mixin-xbpnamo4RagB_font-size);
font-weight: var(--mixin-xbpnamo4RagB_font-weight);
font-style: var(--mixin-xbpnamo4RagB_font-style);
color: var(--mixin-xbpnamo4RagB_color);
text-align: var(--mixin-xbpnamo4RagB_text-align);
text-transform: var(--mixin-xbpnamo4RagB_text-transform);
line-height: var(--mixin-xbpnamo4RagB_line-height);
letter-spacing: var(--mixin-xbpnamo4RagB_letter-spacing);
white-space: var(--mixin-xbpnamo4RagB_white-space);  
  }
      
    :where(.root_reset_**********) h1:where(.plasmic_default__h1), h1:where(.root_reset_**********.plasmic_default__h1), :where(.root_reset_********** .__wab_expr_html_text) h1, :where(.root_reset_**********_tags) h1, h1:where(.root_reset_**********_tags) {
        font-family: var(--mixin-ZpcZYCfrdhwc_font-family);
color: var(--mixin-ZpcZYCfrdhwc_color);
font-size: var(--mixin-ZpcZYCfrdhwc_font-size);
font-weight: var(--mixin-ZpcZYCfrdhwc_font-weight);
letter-spacing: var(--mixin-ZpcZYCfrdhwc_letter-spacing);
line-height: var(--mixin-ZpcZYCfrdhwc_line-height);  
    }
  

    :where(.root_reset_**********) h2:where(.plasmic_default__h2), h2:where(.root_reset_**********.plasmic_default__h2), :where(.root_reset_********** .__wab_expr_html_text) h2, :where(.root_reset_**********_tags) h2, h2:where(.root_reset_**********_tags) {
        font-family: var(--mixin-zEreiBzDcWX8_font-family);
color: var(--mixin-zEreiBzDcWX8_color);
font-size: var(--mixin-zEreiBzDcWX8_font-size);
font-weight: var(--mixin-zEreiBzDcWX8_font-weight);
letter-spacing: var(--mixin-zEreiBzDcWX8_letter-spacing);
line-height: var(--mixin-zEreiBzDcWX8_line-height);  
    }
  

    :where(.root_reset_**********) h3:where(.plasmic_default__h3), h3:where(.root_reset_**********.plasmic_default__h3), :where(.root_reset_********** .__wab_expr_html_text) h3, :where(.root_reset_**********_tags) h3, h3:where(.root_reset_**********_tags) {
        font-family: var(--mixin-z4uR8NTRgzqw_font-family);
color: var(--mixin-z4uR8NTRgzqw_color);
font-size: var(--mixin-z4uR8NTRgzqw_font-size);
font-weight: var(--mixin-z4uR8NTRgzqw_font-weight);
letter-spacing: var(--mixin-z4uR8NTRgzqw_letter-spacing);
line-height: var(--mixin-z4uR8NTRgzqw_line-height);  
    }
  

    :where(.root_reset_**********) h4:where(.plasmic_default__h4), h4:where(.root_reset_**********.plasmic_default__h4), :where(.root_reset_********** .__wab_expr_html_text) h4, :where(.root_reset_**********_tags) h4, h4:where(.root_reset_**********_tags) {
        font-family: var(--mixin-60T-rxPFDSmt_font-family);
color: var(--mixin-60T-rxPFDSmt_color);
font-size: var(--mixin-60T-rxPFDSmt_font-size);
font-weight: var(--mixin-60T-rxPFDSmt_font-weight);
letter-spacing: var(--mixin-60T-rxPFDSmt_letter-spacing);
line-height: var(--mixin-60T-rxPFDSmt_line-height);  
    }
  

    :where(.root_reset_**********) h5:where(.plasmic_default__h5), h5:where(.root_reset_**********.plasmic_default__h5), :where(.root_reset_********** .__wab_expr_html_text) h5, :where(.root_reset_**********_tags) h5, h5:where(.root_reset_**********_tags) {
        font-family: var(--mixin--PO0rD7k6KOq_font-family);
color: var(--mixin--PO0rD7k6KOq_color);
font-size: var(--mixin--PO0rD7k6KOq_font-size);
font-weight: var(--mixin--PO0rD7k6KOq_font-weight);
letter-spacing: var(--mixin--PO0rD7k6KOq_letter-spacing);
line-height: var(--mixin--PO0rD7k6KOq_line-height);  
    }
  

    :where(.root_reset_**********) h6:where(.plasmic_default__h6), h6:where(.root_reset_**********.plasmic_default__h6), :where(.root_reset_********** .__wab_expr_html_text) h6, :where(.root_reset_**********_tags) h6, h6:where(.root_reset_**********_tags) {
        font-family: var(--mixin-ZxgNJMFmlsHI_font-family);
color: var(--mixin-ZxgNJMFmlsHI_color);
font-size: var(--mixin-ZxgNJMFmlsHI_font-size);
font-weight: var(--mixin-ZxgNJMFmlsHI_font-weight);
line-height: var(--mixin-ZxgNJMFmlsHI_line-height);  
    }
  

    :where(.root_reset_**********) a:where(.plasmic_default__a), a:where(.root_reset_**********.plasmic_default__a), :where(.root_reset_********** .__wab_expr_html_text) a, :where(.root_reset_**********_tags) a, a:where(.root_reset_**********_tags) {
        color: var(--mixin-_I8SLonmXBRB_color);  
    }
  

    :where(.root_reset_**********) a:where(.plasmic_default__a):hover, a:where(.root_reset_**********.plasmic_default__a):hover, :where(.root_reset_********** .__wab_expr_html_text) a:hover, :where(.root_reset_**********_tags) a:hover, a:where(.root_reset_**********_tags):hover {
        color: var(--mixin-nGYsAPgBucgP_color);  
    }
  

    :where(.root_reset_**********) blockquote:where(.plasmic_default__blockquote), blockquote:where(.root_reset_**********.plasmic_default__blockquote), :where(.root_reset_********** .__wab_expr_html_text) blockquote, :where(.root_reset_**********_tags) blockquote, blockquote:where(.root_reset_**********_tags) {
        color: var(--mixin-ZhATVRpjLkPR_color);
padding-left: var(--mixin-ZhATVRpjLkPR_padding-left);
border-left: var(--mixin-ZhATVRpjLkPR_border-left-width) var(--mixin-ZhATVRpjLkPR_border-left-style) var(--mixin-ZhATVRpjLkPR_border-left-color);  
    }
  

    :where(.root_reset_**********) code:where(.plasmic_default__code), code:where(.root_reset_**********.plasmic_default__code), :where(.root_reset_********** .__wab_expr_html_text) code, :where(.root_reset_**********_tags) code, code:where(.root_reset_**********_tags) {
        background: #f8f8f8;
font-family: var(--mixin-hTAKkhlPFzr__font-family);
border-radius: var(--mixin-hTAKkhlPFzr__border-top-left-radius) var(--mixin-hTAKkhlPFzr__border-top-right-radius) var(--mixin-hTAKkhlPFzr__border-bottom-right-radius) var(--mixin-hTAKkhlPFzr__border-bottom-left-radius);
padding: var(--mixin-hTAKkhlPFzr__padding-top) var(--mixin-hTAKkhlPFzr__padding-right) var(--mixin-hTAKkhlPFzr__padding-bottom) var(--mixin-hTAKkhlPFzr__padding-left);
border-top: var(--mixin-hTAKkhlPFzr__border-top-width) var(--mixin-hTAKkhlPFzr__border-top-style) var(--mixin-hTAKkhlPFzr__border-top-color);
border-right: var(--mixin-hTAKkhlPFzr__border-right-width) var(--mixin-hTAKkhlPFzr__border-right-style) var(--mixin-hTAKkhlPFzr__border-right-color);
border-bottom: var(--mixin-hTAKkhlPFzr__border-bottom-width) var(--mixin-hTAKkhlPFzr__border-bottom-style) var(--mixin-hTAKkhlPFzr__border-bottom-color);
border-left: var(--mixin-hTAKkhlPFzr__border-left-width) var(--mixin-hTAKkhlPFzr__border-left-style) var(--mixin-hTAKkhlPFzr__border-left-color);  
    }
  

    :where(.root_reset_**********) pre:where(.plasmic_default__pre), pre:where(.root_reset_**********.plasmic_default__pre), :where(.root_reset_********** .__wab_expr_html_text) pre, :where(.root_reset_**********_tags) pre, pre:where(.root_reset_**********_tags) {
        background: #f8f8f8;
font-family: var(--mixin-fjDwK3uTQLt8_font-family);
border-radius: var(--mixin-fjDwK3uTQLt8_border-top-left-radius) var(--mixin-fjDwK3uTQLt8_border-top-right-radius) var(--mixin-fjDwK3uTQLt8_border-bottom-right-radius) var(--mixin-fjDwK3uTQLt8_border-bottom-left-radius);
padding: var(--mixin-fjDwK3uTQLt8_padding-top) var(--mixin-fjDwK3uTQLt8_padding-right) var(--mixin-fjDwK3uTQLt8_padding-bottom) var(--mixin-fjDwK3uTQLt8_padding-left);
border-top: var(--mixin-fjDwK3uTQLt8_border-top-width) var(--mixin-fjDwK3uTQLt8_border-top-style) var(--mixin-fjDwK3uTQLt8_border-top-color);
border-right: var(--mixin-fjDwK3uTQLt8_border-right-width) var(--mixin-fjDwK3uTQLt8_border-right-style) var(--mixin-fjDwK3uTQLt8_border-right-color);
border-bottom: var(--mixin-fjDwK3uTQLt8_border-bottom-width) var(--mixin-fjDwK3uTQLt8_border-bottom-style) var(--mixin-fjDwK3uTQLt8_border-bottom-color);
border-left: var(--mixin-fjDwK3uTQLt8_border-left-width) var(--mixin-fjDwK3uTQLt8_border-left-style) var(--mixin-fjDwK3uTQLt8_border-left-color);  
    }
  

    :where(.root_reset_**********) ol:where(.plasmic_default__ol), ol:where(.root_reset_**********.plasmic_default__ol), :where(.root_reset_********** .__wab_expr_html_text) ol, :where(.root_reset_**********_tags) ol, ol:where(.root_reset_**********_tags) {
        display: var(--mixin-wcZze0bZcVEh_display);
flex-direction: var(--mixin-wcZze0bZcVEh_flex-direction);
align-items: var(--mixin-wcZze0bZcVEh_align-items);
justify-content: var(--mixin-wcZze0bZcVEh_justify-content);
list-style-position: var(--mixin-wcZze0bZcVEh_list-style-position);
padding-left: var(--mixin-wcZze0bZcVEh_padding-left);
position: var(--mixin-wcZze0bZcVEh_position);
list-style-type: var(--mixin-wcZze0bZcVEh_list-style-type);
column-gap: var(--mixin-wcZze0bZcVEh_column-gap);  
    }
  

    :where(.root_reset_**********) ul:where(.plasmic_default__ul), ul:where(.root_reset_**********.plasmic_default__ul), :where(.root_reset_********** .__wab_expr_html_text) ul, :where(.root_reset_**********_tags) ul, ul:where(.root_reset_**********_tags) {
        display: var(--mixin-SFG6O5da3UHa_display);
flex-direction: var(--mixin-SFG6O5da3UHa_flex-direction);
align-items: var(--mixin-SFG6O5da3UHa_align-items);
justify-content: var(--mixin-SFG6O5da3UHa_justify-content);
list-style-position: var(--mixin-SFG6O5da3UHa_list-style-position);
padding-left: var(--mixin-SFG6O5da3UHa_padding-left);
position: var(--mixin-SFG6O5da3UHa_position);
list-style-type: var(--mixin-SFG6O5da3UHa_list-style-type);
column-gap: var(--mixin-SFG6O5da3UHa_column-gap);  
    }
  
    
--- plasmic.js ---
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
// This code is auto-generated by Plasmic; please do not edit!
// Plasmic Project: **********
// plasmic-unformatted
import { createUseGlobalVariants } from "@plasmicapp/react-web";
export var _useGlobalVariants = createUseGlobalVariants({});

--- plasmic.tsx ---

    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
  
    // This code is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: **********
    // plasmic-unformatted
  
    import { createUseGlobalVariants } from "@plasmicapp/react-web";
    
  
    
  export const _useGlobalVariants = createUseGlobalVariants({
    
  });

  
--- plasmic__default_style.css ---

        :where(.plasmic_default__all) {   display: block;
  white-space: inherit;
  grid-row: auto;
  grid-column: auto;
  position: relative;
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  row-gap: 0px;
  column-gap: 0px;
  box-shadow: none;
  box-sizing: border-box;
  text-decoration-line: none;
  margin: 0;
  border-width: 0px; }
        :where(.__wab_expr_html_text *) {   white-space: inherit;
  grid-row: auto;
  grid-column: auto;
  background: none;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  row-gap: 0px;
  column-gap: 0px;
  box-shadow: none;
  box-sizing: border-box;
  margin: 0;
  border-width: 0px; }
      

      :where(.plasmic_default__img) {   display: inline-block; }
      :where(.__wab_expr_html_text img) {   white-space: inherit; }
    

      :where(.plasmic_default__li) {   display: list-item; }
      :where(.__wab_expr_html_text li) {   white-space: inherit; }
    

      :where(.plasmic_default__span) {   display: inline;
  position: static;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit; }
      :where(.__wab_expr_html_text span) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit; }
    

      :where(.plasmic_default__input) {   font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: linear-gradient(#ffffff, #ffffff);
  padding: 2px;
  border: 1px solid lightgray; }
      :where(.__wab_expr_html_text input) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: linear-gradient(#ffffff, #ffffff);
  padding: 2px;
  border: 1px solid lightgray; }
    

      :where(.plasmic_default__textarea) {   font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  padding: 2px;
  border: 1px solid lightgray; }
      :where(.__wab_expr_html_text textarea) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  padding: 2px;
  border: 1px solid lightgray; }
    

      :where(.plasmic_default__button) {   font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: none;
  align-items: flex-start;
  text-align: center;
  padding: 2px 6px;
  border: 1px solid lightgray; }
      :where(.__wab_expr_html_text button) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit;
  background-image: none;
  align-items: flex-start;
  text-align: center;
  padding: 2px 6px;
  border: 1px solid lightgray; }
    

      :where(.plasmic_default__code) {   font-family: inherit;
  line-height: inherit; }
      :where(.__wab_expr_html_text code) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit; }
    

      :where(.plasmic_default__pre) {   font-family: inherit;
  line-height: inherit; }
      :where(.__wab_expr_html_text pre) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit; }
    

      :where(.plasmic_default__p) {   font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit; }
      :where(.__wab_expr_html_text p) {   white-space: inherit;
  font-family: inherit;
  line-height: inherit;
  font-size: inherit;
  font-style: inherit;
  font-weight: inherit;
  color: inherit;
  text-transform: inherit; }
    

      :where(.plasmic_default__h1) {   font-size: inherit;
  font-weight: inherit; }
      :where(.__wab_expr_html_text h1) {   white-space: inherit;
  font-size: inherit;
  font-weight: inherit; }
    

      :where(.plasmic_default__h2) {   font-size: inherit;
  font-weight: inherit; }
      :where(.__wab_expr_html_text h2) {   white-space: inherit;
  font-size: inherit;
  font-weight: inherit; }
    

      :where(.plasmic_default__h3) {   font-size: inherit;
  font-weight: inherit; }
      :where(.__wab_expr_html_text h3) {   white-space: inherit;
  font-size: inherit;
  font-weight: inherit; }
    

      :where(.plasmic_default__h4) {   font-size: inherit;
  font-weight: inherit; }
      :where(.__wab_expr_html_text h4) {   white-space: inherit;
  font-size: inherit;
  font-weight: inherit; }
    

      :where(.plasmic_default__h5) {   font-size: inherit;
  font-weight: inherit; }
      :where(.__wab_expr_html_text h5) {   white-space: inherit;
  font-size: inherit;
  font-weight: inherit; }
    

      :where(.plasmic_default__h6) {   font-size: inherit;
  font-weight: inherit; }
      :where(.__wab_expr_html_text h6) {   white-space: inherit;
  font-size: inherit;
  font-weight: inherit; }
    

      :where(.plasmic_default__address) {   font-style: inherit; }
      :where(.__wab_expr_html_text address) {   white-space: inherit;
  font-style: inherit; }
    

      :where(.plasmic_default__a) {   color: inherit; }
      :where(.__wab_expr_html_text a) {   white-space: inherit;
  color: inherit; }
    

      :where(.plasmic_default__ol) {   list-style-type: none;
  padding: 0; }
      :where(.__wab_expr_html_text ol) {   white-space: inherit;
  list-style-type: none;
  padding: 0; }
    

      :where(.plasmic_default__ul) {   list-style-type: none;
  padding: 0; }
      :where(.__wab_expr_html_text ul) {   white-space: inherit;
  list-style-type: none;
  padding: 0; }
    

      :where(.plasmic_default__select) {   padding: 2px 6px; }
      :where(.__wab_expr_html_text select) {   white-space: inherit;
  padding: 2px 6px; }
    
.plasmic_default__component_wrapper { display: grid; }
.plasmic_default__inline { display: inline; }
.plasmic_page_wrapper { display: flex; width: 100%; min-height: 100vh; align-items: stretch; align-self: start;}
.plasmic_page_wrapper > * { height: auto !important; }
.__wab_expr_html_text { white-space: normal; }
--- tsconfig.json ---
{
  "compilerOptions": {
    "target": "es5",
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "jsx": "react",
    "module": "esnext",
    "moduleResolution": "node",
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "strict": true
  }
}"
`;
