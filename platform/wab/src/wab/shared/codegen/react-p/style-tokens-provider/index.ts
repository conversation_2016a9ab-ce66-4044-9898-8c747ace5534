import { isValidComboForToken } from "@/wab/shared/Variants";
import {
  makeCssClassNameForVariantCombo,
  serializeClassExpr,
} from "@/wab/shared/codegen/react-p/class-names";
import { getContextGlobalVariantsWithVariantedTokens } from "@/wab/shared/codegen/react-p/global-variants";
import {
  makeCreateStyleTokensProviderName,
  makeCreateUseStyleTokensName,
  makePlasmicTokensClassName,
  makeProjectModuleImports,
  makeStyleTokensProviderFileName,
  makeStyleTokensProviderName,
  makeTaggedPlasmicStarImport,
  makeUseGlobalVariantsName,
  makeUseStyleTokensName,
  projectStyleCssImportName,
} from "@/wab/shared/codegen/react-p/serialize-utils";
import { getReactWebPackageName } from "@/wab/shared/codegen/react-p/utils";
import { ExportOpts, ProjectConfig } from "@/wab/shared/codegen/types";
import { jsLiteral, toVarName } from "@/wab/shared/codegen/util";
import { assert, ensure } from "@/wab/shared/common";
import { Site } from "@/wab/shared/model/classes";
import {
  makeGlobalVariantComboSorter,
  sortedVariantCombos,
} from "@/wab/shared/variant-sort";
import type { SetRequired } from "type-fest";

export function makeStyleTokensProviderBundle(
  site: Site,
  projectId: string,
  imports: {
    cssFileName: string;
    projectModuleBundle: ProjectConfig["projectModuleBundle"];
  },
  exportOpts: SetRequired<Partial<ExportOpts>, "targetEnv">,
): ProjectConfig["styleTokensProviderBundle"] {
  const module = `
    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
  
    // This code is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: ${projectId}
    // plasmic-unformatted
  
    import { ${makeCreateStyleTokensProviderName()}, ${makeCreateUseStyleTokensName()} } from "${getReactWebPackageName(
    exportOpts
  )}";

    ${makeProjectModuleImports(imports.projectModuleBundle)}
  
    ${makeTaggedPlasmicStarImport(
      projectStyleCssImportName,
      imports.cssFileName,
      projectId,
      "projectcss"
    )}
  
    const data = ${projectStyleTokenData(
      site,
      projectId,
      exportOpts,
    )};
  
    export const ${makeUseStyleTokensName()} = ${makeCreateUseStyleTokensName()}(
      data,
      ${makeUseGlobalVariantsName()},
    );
  
    export const ${makeStyleTokensProviderName()} = ${makeCreateStyleTokensProviderName()}(
      data,
      ${makeUseGlobalVariantsName()},
    );`;

  return {
    id: projectId,
    module,
    fileName: makeStyleTokensProviderFileName(projectId, exportOpts),
  };
}

function projectStyleTokenData(
  site: Site,
  projectId: string,
  exportOpts: SetRequired<Partial<ExportOpts>, "targetEnv">,
) {
  const base = serializeClassExpr(
    exportOpts,
    makePlasmicTokensClassName(projectId, exportOpts)
  );

  const contextGlobalVariantCombos =
    getContextGlobalVariantsWithVariantedTokens(site).map((v) => [v]);
  const sorter = makeGlobalVariantComboSorter(site);

  const globalVariantDataEntries = sortedVariantCombos(
    contextGlobalVariantCombos,
    sorter
  )
    .map((vc) => {
      assert(
        isValidComboForToken(vc),
        "Can only build varianted combos with one variant"
      );
      const variant = vc[0];
      const variantName = toVarName(variant.name);
      const variantGroup = ensure(
        variant.parent,
        "Global variants always have parent group"
      );
      const groupName = toVarName(variantGroup.param.variable.name);
      const classNameExpr = serializeClassExpr(
        exportOpts,
        makeCssClassNameForVariantCombo(vc, exportOpts)
      );
      return `{
            className: ${classNameExpr},
            groupName: ${jsLiteral(groupName)},
            variant: ${jsLiteral(variantName)},
          }`;
    })
    .join(",\n");

  return `{
    base: ${base},
    varianted: [
      ${globalVariantDataEntries}
    ],
  }`;
}
