import { serializeUseGlobalVariants } from "@/wab/shared/codegen/react-p/global-variants";
import {
  makeCreateUseGlobalVariantsName,
  makeProjectModuleFileName,
} from "@/wab/shared/codegen/react-p/serialize-utils";
import { getReactWebPackageName } from "@/wab/shared/codegen/react-p/utils";
import { ExportOpts, ProjectConfig } from "@/wab/shared/codegen/types";
import { makeGlobalVariantGroupImportTemplate } from "@/wab/shared/codegen/variants";
import { allGlobalVariantGroups } from "@/wab/shared/core/sites";
import { Site } from "@/wab/shared/model/classes";
import type { SetRequired } from "type-fest";

export function makeProjectModuleBundle(
  site: Site,
  projectId: string,
  exportOpts: SetRequired<Partial<ExportOpts>, "targetEnv">
): ProjectConfig["projectModuleBundle"] {
  const globalVariantGroups = new Set(
    allGlobalVariantGroups(site, {
      includeDeps: "all",
      excludeEmpty: true,
    })
  );

  const globalVariantImports =
    globalVariantGroups.size === 0
      ? ""
      : `
          ${[...globalVariantGroups]
            .map((vg) =>
              makeGlobalVariantGroupImportTemplate(vg, ".", exportOpts)
            )
            .join("\n")}
        `;

  const module = `
    /* eslint-disable */
    /* tslint:disable */
    // @ts-nocheck
  
    // This code is auto-generated by Plasmic; please do not edit!
    // Plasmic Project: ${projectId}
    // plasmic-unformatted
  
    import { ${makeCreateUseGlobalVariantsName()} } from "${getReactWebPackageName(
    exportOpts
  )}";
    ${globalVariantImports}
  
    ${serializeUseGlobalVariants(globalVariantGroups)}
  `;

  return {
    id: projectId,
    module,
    fileName: makeProjectModuleFileName(projectId, exportOpts),
  };
}
