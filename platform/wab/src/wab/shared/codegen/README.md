# Codegen

This directory contains the code generation logic for Plasmic projects.

## Overview

The codegen system transforms Plasmic designs into production-ready React code. It handles:
- Component generation
- Style generation (CSS, CSS modules)
- Global variants and contexts
- Style tokens and theming
- Data fetching and state management
- Code components integration

## Architecture

### Key Modules

- `react-p/` - React code generation
  - `index.ts` - Main entry point for React codegen
  - `class-names/` - CSS class name generation
  - `style-tokens-provider/` - Style token management
  - `global-variants/` - Global variant handling
  - `serialize-utils.ts` - Common serialization utilities

- `types.ts` - TypeScript types for codegen
- `util.ts` - Common utilities
- `__tests__/` - Test suites and fixtures

## StyleTokensProvider System

The StyleTokensProvider system manages design tokens (colors, spacing, etc.) that can vary based on global variants like themes.

### How it works

1. **Token Collection**: Gathers all style tokens that have variant-specific values
2. **Provider Generation**: Creates a React context provider that supplies token values
3. **Class Name Management**: Generates CSS classes for each variant combination
4. **Runtime Integration**: Provides hooks (`useStyleTokens`) for components to access token values

### Key Files

- `style-tokens-provider/index.ts` - Main provider generation logic
- `serialize-utils.ts` - Helper functions for imports and naming
- `class-names/index.ts` - CSS class serialization

### Generated Files

- `PlasmicStyleTokensProvider.tsx` - The provider component and hook
- `plasmic.tsx` - Project module with global variant hooks

## Testing

### Running Tests

```bash
# Run all codegen tests
yarn test codegen

# Run specific test file
yarn test global-variant.spec.tsx

# Update snapshots
yarn test:update-snapshots
```

### Test Structure

Tests use real Plasmic project bundles stored in `__tests__/bundles/`. Each test:
1. Unbundles the project
2. Runs codegen
3. Verifies the generated code

### Recommended Tests for StyleTokensProvider

#### 1. Tests for Existing Files

**`__tests__/global-variant.spec.tsx`** - Add tests for:
- StyleTokensProvider exports validation
- Generated style token data structure
- Class name inclusion for theme variants

**`server/loader/gen-code-bundle.spec.ts`** - Add tests for:
- `styleTokensProviderFileName` in project metadata
- Bundle output structure validation

**`react-p/index.spec.ts`** - Add tests for:
- Serialization utility functions
- File naming conventions
- Component wrapping logic

#### 2. New Test Files Needed

**`react-p/style-tokens-provider/index.spec.ts`**
- Test bundle generation with various site configurations
- Verify module structure and imports
- Test variant combination handling
- Edge cases (no variants, no tokens)

**`server/loader/module-writer.spec.ts`**
- Test file writing for style tokens provider
- Verify entrypoint generation
- Import path validation

**`__tests__/style-tokens-performance.spec.tsx`**
- Performance tests for large variant sets
- Memory usage validation
- Deduplication verification

### Test Utilities

`codegen-tests-util.ts` provides helpers for:
- Setting up test directories
- Running codegen
- Importing generated modules
- Reading generated files

## Best Practices

1. **Naming Conventions**
   - Use consistent prefixes for generated files
   - Follow `make*` pattern for factory functions
   - Use descriptive names for CSS classes

2. **Performance**
   - Minimize generated code size
   - Deduplicate repeated patterns
   - Use efficient data structures

3. **Compatibility**
   - Support multiple export targets (loader, codegen, preview)
   - Handle CSS modules and regular CSS
   - Maintain backward compatibility

4. **Testing**
   - Test with real project bundles
   - Cover edge cases
   - Verify runtime behavior
   - Check generated code quality

## Common Issues

1. **Import Paths**: Ensure correct relative paths between generated files
2. **CSS Module Names**: Must be valid JS identifiers
3. **Circular Dependencies**: Avoid circular imports between generated modules
4. **Bundle Size**: Monitor generated code size for large projects

## Future Improvements

- Lazy loading for rarely-used token combinations
- Better tree-shaking support
- Incremental regeneration
- Token inheritance optimization