export const PERSONAL_WORKSPACE = "My playground";

export const ARENAS_CAP = "Custom arenas";

export const ARENA_CAP = "Custom arena";
export const ARENA_LOWER = ARENA_CAP.toLowerCase();

export const VARIABLE_CAP = "State variable";
export const VARIABLE_LOWER = VARIABLE_CAP.toLowerCase();

export const VARIABLE_PLURAL_CAP = "State variables";
export const VARIABLE_PLURAL_LOWER = VARIABLE_PLURAL_CAP.toLowerCase();

export const DATA_QUERY_CAP = "Data query";
export const DATA_QUERY_PLURAL_CAP = "Data queries";
export const DATA_QUERY_LOWER = DATA_QUERY_CAP.toLowerCase();
export const DATA_QUERY_PLURAL_LOWER = DATA_QUERY_PLURAL_CAP.toLowerCase();

export const DATA_SOURCE_CAP = "Integration";
export const DATA_SOURCE_PLURAL_CAP = "Integrations";
export const A_DATA_SOURCE_LOWER = "an " + DATA_SOURCE_CAP.toLowerCase();
export const DATA_SOURCE_LOWER = DATA_SOURCE_CAP.toLowerCase();
export const DATA_SOURCE_PLURAL_LOWER = DATA_SOURCE_PLURAL_CAP.toLowerCase();

export const DATA_SOURCE_OPERATION_CAP = "Operation";
export const DATA_SOURCE_OPERATION_PLURAL_CAP = "Operations";
export const DATA_SOURCE_OPERATION_LOWER =
  DATA_SOURCE_OPERATION_CAP.toLowerCase();
export const DATA_SOURCE_OPERATION_PLURAL_LOWER =
  DATA_SOURCE_OPERATION_PLURAL_CAP.toLowerCase();

export const SERVER_QUERY_CAP = "Server query";
export const SERVER_QUERY_PLURAL_CAP = "Server queries";
export const A_SERVER_QUERY_LOWER = "an " + SERVER_QUERY_CAP.toLowerCase();
export const SERVER_QUERY_LOWER = SERVER_QUERY_CAP.toLowerCase();
export const SERVER_QUERY_PLURAL_LOWER = SERVER_QUERY_PLURAL_CAP.toLowerCase();

export const COMPONENT_PROP_CAP = "Prop";
export const COMPONENT_PROP_LOWER = COMPONENT_PROP_CAP.toLowerCase();

export const COMPONENT_PROP_PLURAL_CAP = "Props";
export const COMPONENT_PROP_PLURAL_LOWER =
  COMPONENT_PROP_PLURAL_CAP.toLowerCase();

/**
 * Verb for resetting/clearing/removing a prop/style value,
 * resulting in the prop/style reverting to its default value.
 */
export const RESET_CAP = "Remove";

export const CONTAINER_LOWER = "box";
export const STACK_LOWER = "stack";

export const FREE_CONTAINER_CAP = `Free ${CONTAINER_LOWER}`;
export const FREE_CONTAINER_LOWER = FREE_CONTAINER_CAP.toLowerCase();

export const VERT_CONTAINER_CAP = `Vertical ${STACK_LOWER}`;
export const VERT_CONTAINER_LOWER = VERT_CONTAINER_CAP.toLowerCase();

export const HORIZ_CONTAINER_CAP = `Horizontal ${STACK_LOWER}`;
export const HORIZ_CONTAINER_LOWER = HORIZ_CONTAINER_CAP.toLowerCase();

export const LAYOUT_CONTAINER_CAP = `Page section`;
export const LAYOUT_CONTAINER_LOWER = LAYOUT_CONTAINER_CAP.toLowerCase();

export const GRID_LOWER = "grid";

export const FRAMES_CAP = "Artboards";
export const FRAMES_LOWER = FRAMES_CAP.toLowerCase();

export const FRAME_CAP = "Artboard";
export const FRAME_LOWER = FRAME_CAP.toLowerCase();

export const MIXINS_CAP = "Style presets";

export const MIXIN_CAP = "Style preset";
export const MIXIN_LOWER = MIXIN_CAP.toLowerCase();

export const SLOT_CAP = "Slot";
export const TOKENS_CAP = "Tokens";
export const TOKEN_CAP = "Token";
export const TOKEN_LOWER = "token";

export const VARIANTS_CAP = "Variants";
export const VARIANTS_LOWER = VARIANTS_CAP.toLowerCase();

export const VARIANT_CAP = "Variant";

export const VARIANT_GROUP_CAP = "Group of variants";
export const VARIANT_GROUP_LOWER = VARIANT_GROUP_CAP.toLowerCase();

export const VARIANT_LOWER = "variant";

export const VARIANT_OPTION_CAP = "Variant";
export const VARIANT_OPTION_LOWER = VARIANT_OPTION_CAP.toLowerCase();

export const INTERACTION_VARIANTS_LOWER = "interaction variants";

export const PRIVATE_STYLE_VARIANTS_CAP = "Element variants";

export const ARENAS_DESCRIPTION = `${ARENAS_CAP} are where you can lay out artboards however you want, similar to vector design tools.`;

export const COMBINATIONS_CAP = `Combinations`;

export const CUSTOM_BEHAVIORS_CAP = "Custom behaviors";

export const ORGANIZATION_CAP = "Organization";
export const ORGANIZATION_PLURAL_CAP = "Organizations";
export const ORGANIZATION_LOWER = ORGANIZATION_CAP.toLowerCase();
export const ORGANIZATION_PLURAL_LOWER = ORGANIZATION_PLURAL_CAP.toLowerCase();

export const WORKSPACE_CAP = "Workspace";

export const INTERACTIVE_CAP = "Interactive";

export const REPEATED_CAP = "Repeated";

export const COMMENTS_LOWER = "comments";
