import { extractMentionedEmails } from "@/wab/shared/comments-utils";

describe("extractMentionedEmails", function () {
  it("should extract user emails", () => {
    const body = "check this out @<<EMAIL>>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>"]);
  });

  it("should not extract invalid user emails", () => {
    const body =
      "check this out @<abc@xyz@.com>, please test it @<<EMAIL>>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>"]);
  });

  it("should extract multiple valid emails", () => {
    const body = "Contact @<<EMAIL>> and @<<EMAIL>>.";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
  });

  it("should not extract emails without preceding @", () => {
    const body = "<EMAIL> is my email, but not mentioned.";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual([]);
  });

  it("should extract emails with special characters", () => {
    const body =
      "Follow @<<EMAIL>> or @<<EMAIL>>!";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual([
      "<EMAIL>",
      "<EMAIL>",
    ]);
  });

  it("should not extract invalid emails", () => {
    const body =
      "Invalid emails like @<EMAIL> or @user@domain@com should not be extracted.";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual([]);
  });

  it("should return an empty array if no valid emails are found", () => {
    const body = "Hello @user, how are you? Also, @someone!";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual([]);
  });

  it("should extract emails even with newlines and extra spaces", () => {
    const body =
      "Mention: @<<EMAIL>> \n And also @<<EMAIL>>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
  });

  it("should extract emails with subdomains", () => {
    const body = "Use @<<EMAIL>> for help.";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>"]);
  });

  it("should extract mention at start", () => {
    const body = "@<a@b.c> foo";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["a@b.c"]);
  });

  it("should extract mention at end", () => {
    const body = "foo @<a@b.c>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["a@b.c"]);
  });

  it("should extract mention at start and end (standalone)", () => {
    const body = "@<a@b.c>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["a@b.c"]);
  });

  it("should not extract mention without preceding @", () => {
    const body = "#<a@b.c>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual([]);

    const body1 = "a@b.c";
    const emails1 = extractMentionedEmails(body1);
    expect(emails1).toEqual([]);
  });

  it("should extract mention on second line", () => {
    const body = "foo\n@<a@b.c>\nbar";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["a@b.c"]);
  });

  it("should not extract emails with spaces in domain", () => {
    const body = "Check @<user@domain .com> and @<<EMAIL>>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>"]);
  });

  it("should extract emails with multiple mentions in a row", () => {
    const body = "Team: @<<EMAIL>> @<<EMAIL>> @<<EMAIL>>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>", "<EMAIL>", "<EMAIL>"]);
  });

  it("should handle mentions with trailing punctuation", () => {
    const body = "Email @<<EMAIL>>, or @<<EMAIL>>!";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
  });

  it("should not extract incomplete emails", () => {
    const body = "Incomplete @<user@ or @domain.com> or @<user@domain";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual([]);
  });

  it("should handle mixed valid and invalid mentions", () => {
    const body =
      "Try @<<EMAIL>> and @<<EMAIL>> then @<<EMAIL>>";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>", "<EMAIL>"]);
  });

  it("should extract email with minimal valid format", () => {
    const body = "Short @<<EMAIL>> works.";
    const emails = extractMentionedEmails(body);
    expect(emails).toEqual(["<EMAIL>"]);
  });
});
