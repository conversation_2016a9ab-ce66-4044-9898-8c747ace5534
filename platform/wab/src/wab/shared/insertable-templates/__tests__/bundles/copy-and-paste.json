[["qLzALHPWpGjShYEMtDJha2", {"root": "N_sDfln3cUfc", "map": {"iLwMGkL58DTH": {"uuid": "bhBV7GHgPDet", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "8TlNM6pNu1h2": {"values": {"display": "plasmic-content-layout", "position": "relative", "width": "stretch", "height": "stretch", "padding-top": "0px", "padding-right": "0px", "padding-bottom": "0px", "padding-left": "0px", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "i9NvijMcX3WN": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "8TlNM6pNu1h2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "PJ08rbWJjhik": {"values": {}, "mixins": [], "__type": "RuleSet"}, "rqbs1FiMI3si": {"variants": [{"__ref": "u25CLvpWsqCf"}], "args": [], "attrs": {}, "rs": {"__ref": "PJ08rbWJjhik"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "tzjk1QgKQs_m": {"tag": "div", "name": null, "children": [{"__ref": "3Hq942nvkmI5"}, {"__ref": "8xCfK4xak63j"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "4AzkYyczsQp9", "parent": null, "locked": null, "vsettings": [{"__ref": "i9NvijMcX3WN"}, {"__ref": "rqbs1FiMI3si"}], "__type": "TplTag"}, "oJGQ_-0ytbCO": {"path": "/", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "mY7zFIRQ0Tpr": {"uuid": "Xt9yIq6Xn3yJ", "name": "Homepage", "params": [{"__ref": "rmCvBSxt_muX"}, {"__ref": "-ZZqSorqTpeq"}, {"__ref": "KqwgzPqIGqYZ"}, {"__ref": "uQqg7pn8ix3i"}], "states": [{"__ref": "pbtOuAKHftIR"}, {"__ref": "YfYhnY-GtBkl"}], "tplTree": {"__ref": "tzjk1QgKQs_m"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "iLwMGkL58DTH"}], "variantGroups": [], "pageMeta": {"__ref": "oJGQ_-0ytbCO"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "hwJ-868__zyH": {"name": "title", "uuid": "EofZRyWtKZFv", "__type": "Var"}, "EhZT3W_ZW8o8": {"name": "text", "__type": "Text"}, "RJeSk2Vky6cp": {"type": {"__ref": "EhZT3W_ZW8o8"}, "variable": {"__ref": "hwJ-868__zyH"}, "uuid": "GLw-pLpuPD_e", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "JLOL69etz-wl": {"name": "description", "uuid": "l7SB2mzecrm7", "__type": "Var"}, "yW0GoHQ8pzOr": {"name": "text", "__type": "Text"}, "B7qFvoxQHGVj": {"type": {"__ref": "yW0GoHQ8pzOr"}, "variable": {"__ref": "JLOL69etz-wl"}, "uuid": "Au-gfa6KaJ37", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "imfEvaxgwHgH": {"name": "image", "uuid": "xHZPc1ZwG8UN", "__type": "Var"}, "4ZvhQF945UEn": {"name": "img", "__type": "Img"}, "xOOJiPK3g74v": {"type": {"__ref": "4ZvhQF945UEn"}, "variable": {"__ref": "imfEvaxgwHgH"}, "uuid": "qmqqSsN72stZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "b0A-grUfEqMG": {"name": "canonical", "uuid": "i7K7rSTFEnNn", "__type": "Var"}, "o2OTAXA4pjr3": {"name": "text", "__type": "Text"}, "7-bSANvjiDTD": {"type": {"__ref": "o2OTAXA4pjr3"}, "variable": {"__ref": "b0A-grUfEqMG"}, "uuid": "qF4UeSXqKmOW", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Ve5Re7fC5-8r": {"uuid": "vrgZsUDk_pbO", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "eJ3Gx4r5egbU": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "mxyUaXP0zSwS": {"variants": [{"__ref": "Ve5Re7fC5-8r"}], "args": [], "attrs": {}, "rs": {"__ref": "eJ3Gx4r5egbU"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dIf68Q0siWbs": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "nI-zyA1kmKZJ", "parent": null, "locked": null, "vsettings": [{"__ref": "mxyUaXP0zSwS"}], "__type": "TplTag"}, "2zfLy4FwWK8v": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "MNPxg3RpWkPG": {"uuid": "ZLEARI2Ib_Lz", "name": "hostless-plasmic-head", "params": [{"__ref": "RJeSk2Vky6cp"}, {"__ref": "B7qFvoxQHGVj"}, {"__ref": "xOOJiPK3g74v"}, {"__ref": "7-bSANvjiDTD"}], "states": [], "tplTree": {"__ref": "dIf68Q0siWbs"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "Ve5Re7fC5-8r"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "2zfLy4FwWK8v"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "W5QoCDQfMVkS": {"name": "dataOp", "uuid": "lkUbR4W8Q5BN", "__type": "Var"}, "wdt_s2lZ4F6i": {"name": "any", "__type": "AnyType"}, "lp4SfW8Pj8Us": {"type": {"__ref": "wdt_s2lZ4F6i"}, "variable": {"__ref": "W5QoCDQfMVkS"}, "uuid": "TOH3n1MgQwB0", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "sm-4jV7gKcV0": {"name": "name", "uuid": "s-N5W8XPOj9a", "__type": "Var"}, "lvdbUEZW9wzu": {"name": "text", "__type": "Text"}, "aChvtE3WBZhl": {"type": {"__ref": "lvdbUEZW9wzu"}, "variable": {"__ref": "sm-4jV7gKcV0"}, "uuid": "B2j5FjDw_hgk", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "VFaJTFCrt0wj": {"name": "children", "uuid": "B3x4d-T-xh-R", "__type": "Var"}, "DgqTkS_FgtJ8": {"name": "any", "__type": "AnyType"}, "0p7mnwZbTV6C": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "DgqTkS_FgtJ8"}, "__type": "ArgType"}, "0XJNixFeiait": {"name": "renderFunc", "params": [{"__ref": "0p7mnwZbTV6C"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "BYl6Ke3w3N3a": {"type": {"__ref": "0XJNixFeiait"}, "tplSlot": {"__ref": "mHQ5AgKCsA1w"}, "variable": {"__ref": "VFaJTFCrt0wj"}, "uuid": "FNXtfAPDLZwY", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "dHVWpaL1ccuw": {"name": "pageSize", "uuid": "Go141XIouT3w", "__type": "Var"}, "Lk7iWCigELCA": {"name": "num", "__type": "<PERSON><PERSON>"}, "DlsmwU4xIR7Q": {"type": {"__ref": "Lk7iWCigELCA"}, "variable": {"__ref": "dHVWpaL1ccuw"}, "uuid": "hTTZoKKSes3v", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "CKjUTvzsmsuo": {"name": "pageIndex", "uuid": "GzbYx_nKzid9", "__type": "Var"}, "xYbyUz9qvRJY": {"name": "num", "__type": "<PERSON><PERSON>"}, "Fh6Vb6uGOWn0": {"type": {"__ref": "xYbyUz9qvRJY"}, "variable": {"__ref": "CKjUTvzsmsuo"}, "uuid": "D-3CZRY-F3Kx", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "daWZLXyXN7XL": {"uuid": "0ylKxk_tYKoo", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "-RxQXWiM6P3d": {"values": {}, "mixins": [], "__type": "RuleSet"}, "gxtqqjn6NFWV": {"variants": [{"__ref": "daWZLXyXN7XL"}], "args": [], "attrs": {}, "rs": {"__ref": "-RxQXWiM6P3d"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "mHQ5AgKCsA1w": {"param": {"__ref": "BYl6Ke3w3N3a"}, "defaultContents": [], "uuid": "B7VzhvKOP0OF", "parent": {"__ref": "2YB3GyGJFjdi"}, "locked": null, "vsettings": [{"__ref": "gxtqqjn6NFWV"}], "__type": "TplSlot"}, "dib00pBG4p9n": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "mvPR5y_PaehJ": {"variants": [{"__ref": "daWZLXyXN7XL"}], "args": [], "attrs": {}, "rs": {"__ref": "dib00pBG4p9n"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2YB3GyGJFjdi": {"tag": "div", "name": null, "children": [{"__ref": "mHQ5AgKCsA1w"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "3iyGbguGmCmh", "parent": null, "locked": null, "vsettings": [{"__ref": "mvPR5y_PaehJ"}], "__type": "TplTag"}, "hg8Ahp3R8SjR": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "g1Ae5397e-Hh": {"uuid": "5DOwG81twq46", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "lp4SfW8Pj8Us"}, {"__ref": "aChvtE3WBZhl"}, {"__ref": "BYl6Ke3w3N3a"}, {"__ref": "DlsmwU4xIR7Q"}, {"__ref": "Fh6Vb6uGOWn0"}], "states": [], "tplTree": {"__ref": "2YB3GyGJFjdi"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "daWZLXyXN7XL"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "hg8Ahp3R8SjR"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "eBFlfj-UrIIx": {"values": {"font-family": "Inter", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "y4RCNrrQVJND": {"name": "Default Typography", "rs": {"__ref": "eBFlfj-UrIIx"}, "preview": null, "uuid": "jq0PWtHW1hGu", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "luVm7gU6Q2uc": {"values": {"color": "#000000", "font-weight": "700", "font-size": "64px", "line-height": "1", "letter-spacing": "-1px"}, "mixins": [], "__type": "RuleSet"}, "n4mDlCcFslVt": {"name": "Default \"h1\"", "rs": {"__ref": "luVm7gU6Q2uc"}, "preview": null, "uuid": "pZqrJ2FNLGF-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "hRHuPEe82M3d": {"selector": "h1", "style": {"__ref": "n4mDlCcFslVt"}, "__type": "ThemeStyle"}, "gMOv9kfBcK0x": {"values": {"color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-0.5px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "cFRzNtY0A_Hx": {"name": "Default \"h2\"", "rs": {"__ref": "gMOv9kfBcK0x"}, "preview": null, "uuid": "G9U16FGZxCWp", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "SRhbVuo82bg8": {"selector": "h2", "style": {"__ref": "cFRzNtY0A_Hx"}, "__type": "ThemeStyle"}, "WNddAYpiKfEe": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "eSgJqibR-paV": {"name": "Default \"a\"", "rs": {"__ref": "WNddAYpiKfEe"}, "preview": null, "uuid": "AoSGIMTCPA_H", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "WDj_qizjbJa-": {"selector": "a", "style": {"__ref": "eSgJqibR-paV"}, "__type": "ThemeStyle"}, "nZ6Qoz1QmHn4": {"values": {"color": "#000000", "font-size": "32px", "font-weight": "600", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "zxKtYwFSHWHe": {"name": "Default \"h3\"", "rs": {"__ref": "nZ6Qoz1QmHn4"}, "preview": null, "uuid": "PV-YrWTG3jzA", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "nzVIKq2WJnwa": {"selector": "h3", "style": {"__ref": "zxKtYwFSHWHe"}, "__type": "ThemeStyle"}, "jgWx6TcAS4rA": {"values": {"color": "#000000", "font-size": "24px", "font-weight": "600", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "t_Z17ipxvLJG": {"name": "Default \"h4\"", "rs": {"__ref": "jgWx6TcAS4rA"}, "preview": null, "uuid": "Jxec4wVuwZGX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "yIVRUeBQf8xK": {"selector": "h4", "style": {"__ref": "t_Z17ipxvLJG"}, "__type": "ThemeStyle"}, "NBRcveGeU3q7": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "0tupZciJ1JzF": {"name": "Default \"code\"", "rs": {"__ref": "NBRcveGeU3q7"}, "preview": null, "uuid": "_RMu0baPfMTQ", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "aJrR1C2uxHbL": {"selector": "code", "style": {"__ref": "0tupZciJ1JzF"}, "__type": "ThemeStyle"}, "HLKsy_PUUzZy": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "k1gK_oaUlwPL": {"name": "Default \"blockquote\"", "rs": {"__ref": "HLKsy_PUUzZy"}, "preview": null, "uuid": "qQDotvA5fm1z", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DC53Pp6614tC": {"selector": "blockquote", "style": {"__ref": "k1gK_oaUlwPL"}, "__type": "ThemeStyle"}, "owOGxjDBTc8l": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "Pt6YsEmDHeqo": {"name": "Default \"pre\"", "rs": {"__ref": "owOGxjDBTc8l"}, "preview": null, "uuid": "h_GF-ff_tdaR", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "_-Ba1nkmR9Ow": {"selector": "pre", "style": {"__ref": "Pt6YsEmDHeqo"}, "__type": "ThemeStyle"}, "ShIMoj0Zb_LO": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "twUtGOncLJu5": {"name": "Default \"ul\"", "rs": {"__ref": "ShIMoj0Zb_LO"}, "preview": null, "uuid": "bR2NlKzRSSVb", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "vkdNTUzTx3XH": {"selector": "ul", "style": {"__ref": "twUtGOncLJu5"}, "__type": "ThemeStyle"}, "ploL8VtVy9J7": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "MdsM1WO5SnHG": {"name": "Default \"ol\"", "rs": {"__ref": "ploL8VtVy9J7"}, "preview": null, "uuid": "CEKOoDMj4I6g", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "O_w5CoU0KLdR": {"selector": "ol", "style": {"__ref": "MdsM1WO5SnHG"}, "__type": "ThemeStyle"}, "VwcUF0YPda2-": {"values": {"color": "#000000", "font-size": "20px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "N13HRjkctdSE": {"name": "Default \"h5\"", "rs": {"__ref": "VwcUF0YPda2-"}, "preview": null, "uuid": "CWHC60XkkZ1v", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "sWDMJTMMUzDF": {"selector": "h5", "style": {"__ref": "N13HRjkctdSE"}, "__type": "ThemeStyle"}, "ag3kI0gzp7UJ": {"values": {"color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "fs6OvD-bR2yq": {"name": "Default \"h6\"", "rs": {"__ref": "ag3kI0gzp7UJ"}, "preview": null, "uuid": "3Y8J2P7PGbA-", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "KIcpl095lOjF": {"selector": "h6", "style": {"__ref": "fs6OvD-bR2yq"}, "__type": "ThemeStyle"}, "yqk0eKt6EgJY": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "0Xhc63CtJqHC": {"name": "Default \"a:hover\"", "rs": {"__ref": "yqk0eKt6EgJY"}, "preview": null, "uuid": "_0APYMdXOG-G", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Pwl44OEGHgmO": {"selector": "a:hover", "style": {"__ref": "0Xhc63CtJqHC"}, "__type": "ThemeStyle"}, "TW2dR93JR9AT": {"values": {}, "mixins": [], "__type": "RuleSet"}, "YhN9_ECj0VKi": {"name": "Default \"li\"", "rs": {"__ref": "TW2dR93JR9AT"}, "preview": null, "uuid": "xFp2EdoQyWST", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "UWbVtY4ddrnx": {"selector": "li", "style": {"__ref": "YhN9_ECj0VKi"}, "__type": "ThemeStyle"}, "yJTGwha5uQ8D": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kOOszDL9SAYQ": {"name": "Default \"p\"", "rs": {"__ref": "yJTGwha5uQ8D"}, "preview": null, "uuid": "ymDqT27gMe8t", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "63tz7FEWE4xy": {"selector": "p", "style": {"__ref": "kOOszDL9SAYQ"}, "__type": "ThemeStyle"}, "5osvMSLmoXd1": {"defaultStyle": {"__ref": "y4RCNrrQVJND"}, "styles": [{"__ref": "hRHuPEe82M3d"}, {"__ref": "SRhbVuo82bg8"}, {"__ref": "WDj_qizjbJa-"}, {"__ref": "nzVIKq2WJnwa"}, {"__ref": "yIVRUeBQf8xK"}, {"__ref": "aJrR1C2uxHbL"}, {"__ref": "DC53Pp6614tC"}, {"__ref": "_-Ba1nkmR9Ow"}, {"__ref": "vkdNTUzTx3XH"}, {"__ref": "O_w5CoU0KLdR"}, {"__ref": "sWDMJTMMUzDF"}, {"__ref": "KIcpl095lOjF"}, {"__ref": "Pwl44OEGHgmO"}, {"__ref": "UWbVtY4ddrnx"}, {"__ref": "63tz7FEWE4xy"}], "layout": null, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "d9mMC33dLdN9": {"name": "Screen", "uuid": "p2zStuSP7OIc", "__type": "Var"}, "DlWhnn1PV4Ut": {"name": "text", "__type": "Text"}, "TvlpeeDzU0vO": {"type": {"__ref": "DlWhnn1PV4Ut"}, "variable": {"__ref": "d9mMC33dLdN9"}, "uuid": "FYoDTcLNF7Di", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "u25CLvpWsqCf": {"uuid": "H4ZSCZ6TNVkr", "name": "Mobile only", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": {"__ref": "FikhiaEVxKC9"}, "mediaQuery": "(min-width:0px) and (max-width:768px)", "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "FikhiaEVxKC9": {"type": "global-screen", "param": {"__ref": "TvlpeeDzU0vO"}, "uuid": "9I23aIYF4ksX", "variants": [{"__ref": "u25CLvpWsqCf"}], "multi": true, "__type": "GlobalVariantGroup"}, "iYeaT6SpPTBr": {"values": {}, "mixins": [], "__type": "RuleSet"}, "oyd2W5d9RwVq": {"variants": [{"__ref": "PCo-Mei8FONz"}], "args": [], "attrs": {}, "rs": {"__ref": "iYeaT6SpPTBr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "1VWtCCuc2Pn4": {"name": null, "component": {"__ref": "mY7zFIRQ0Tpr"}, "uuid": "rT7d7PM-rhN6", "parent": null, "locked": null, "vsettings": [{"__ref": "oyd2W5d9RwVq"}], "__type": "TplComponent"}, "p6JCBMdov7Kz": {"uuid": "jqdYDVmnu9Of", "width": 1440, "height": 768, "container": {"__ref": "1VWtCCuc2Pn4"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "iLwMGkL58DTH"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "lkJAeYBwXYAg": {"frame": {"__ref": "p6JCBMdov7Kz"}, "cellKey": null, "__type": "ArenaFrameCell"}, "TRaWK8sBndQ1": {"values": {}, "mixins": [], "__type": "RuleSet"}, "wCesUqySqBDM": {"variants": [{"__ref": "PCo-Mei8FONz"}], "args": [], "attrs": {}, "rs": {"__ref": "TRaWK8sBndQ1"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "R2A6315TrqPd": {"name": null, "component": {"__ref": "mY7zFIRQ0Tpr"}, "uuid": "J8kRiP2JY6j0", "parent": null, "locked": null, "vsettings": [{"__ref": "wCesUqySqBDM"}], "__type": "TplComponent"}, "zkC1hsbX2p-g": {"uuid": "evYVEKrFq5uK", "width": 414, "height": 736, "container": {"__ref": "R2A6315TrqPd"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [], "pinnedGlobalVariants": {"H4ZSCZ6TNVkr": true}, "targetGlobalVariants": [{"__ref": "u25CLvpWsqCf"}], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "jOGaO6x4UXFQ": {"frame": {"__ref": "zkC1hsbX2p-g"}, "cellKey": null, "__type": "ArenaFrameCell"}, "CJQ8t1a2zOv9": {"cols": [{"__ref": "lkJAeYBwXYAg"}, {"__ref": "jOGaO6x4UXFQ"}], "rowKey": {"__ref": "iLwMGkL58DTH"}, "__type": "ArenaFrameRow"}, "VTLuoHi-mjMD": {"rows": [{"__ref": "CJQ8t1a2zOv9"}], "__type": "ArenaFrameGrid"}, "WfdxMbk_RtoH": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "LFxWvsmgKos9": {"rows": [{"__ref": "WfdxMbk_RtoH"}], "__type": "ArenaFrameGrid"}, "IjhrBnffe7dV": {"component": {"__ref": "mY7zFIRQ0Tpr"}, "matrix": {"__ref": "VTLuoHi-mjMD"}, "customMatrix": {"__ref": "LFxWvsmgKos9"}, "__type": "PageArena"}, "PCo-Mei8FONz": {"uuid": "e7heRUZRlOyN", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "N_sDfln3cUfc": {"components": [{"__ref": "mY7zFIRQ0Tpr"}, {"__ref": "MNPxg3RpWkPG"}, {"__ref": "g1Ae5397e-Hh"}, {"__ref": "1GEuPT7CEAql"}, {"__ref": "pQqULoBb29eu"}, {"__ref": "iU_MSMf0qWxV"}], "arenas": [], "pageArenas": [{"__ref": "IjhrBnffe7dV"}], "componentArenas": [{"__ref": "lNS3uO2nuvgV"}, {"__ref": "J1R4V9ewcmog"}, {"__ref": "h8ijY8XRq4ZB"}], "globalVariantGroups": [{"__ref": "FikhiaEVxKC9"}], "userManagedFonts": [], "globalVariant": {"__ref": "PCo-Mei8FONz"}, "styleTokens": [{"__ref": "8ZU5DPIFgctD"}, {"__ref": "gMkCatWR0Y-H"}, {"__ref": "eM56m4EybwQU"}, {"__ref": "oa0gNEDz4Ft0"}, {"__ref": "RrOuG-Ij_ME1"}, {"__ref": "Fvhm44SSK7Zp"}, {"__ref": "n0XSa8r5VqOD"}, {"__ref": "_8Utb3m_wQKi"}, {"__ref": "kPQwqlT6GH3q"}, {"__ref": "USv0n8rUkb0E"}, {"__ref": "hE9zp5Qz8Vnx"}, {"__ref": "zH_yFs6IMX9z"}], "mixins": [], "themes": [{"__ref": "5osvMSLmoXd1"}], "activeTheme": {"__ref": "5osvMSLmoXd1"}, "imageAssets": [{"__ref": "tbdBncJERLPx"}, {"__ref": "cu7APL4ixoPJ"}, {"__ref": "YYp_-EfFTSzm"}, {"__ref": "hQfmf6Gcq4Ku"}], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "FikhiaEVxKC9"}, "flags": {"usePlasmicImg": true}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "J3avHBQl3VkF": {"tag": "div", "name": "repeatedTpls", "children": [{"__ref": "qk9Msgs2vZEQ"}, {"__ref": "IihAyWE07hm_"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "gfn5Kq_ZzuYm", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "cfkJ6q2DD_TN"}], "__type": "TplTag"}, "cfkJ6q2DD_TN": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "uPTlvCgBqzw9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uPTlvCgBqzw9": {"values": {"display": "flex", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "qk9Msgs2vZEQ": {"tag": "div", "name": "repeatedTpls0", "children": [{"__ref": "OXfe9q-F0vDn"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "GX37JBfX4FuE", "parent": {"__ref": "J3avHBQl3VkF"}, "locked": null, "vsettings": [{"__ref": "lKGRzXKKxWFr"}], "__type": "TplTag"}, "lKGRzXKKxWFr": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "zf-Mb03X-Y6z"}, "dataCond": null, "dataRep": {"__ref": "0IMCOPuFvvDH"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "zf-Mb03X-Y6z": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "0IMCOPuFvvDH": {"element": {"__ref": "Lc9Vo1FciFu9"}, "index": {"__ref": "Houy0ywxl0MR"}, "collection": {"__ref": "-K4MjIxR7ZJg"}, "__type": "Rep"}, "-K4MjIxR7ZJg": {"code": "([\n  1,\n  2,\n  3\n])", "fallback": {"__ref": "a7_2v0X_VHvB"}, "__type": "CustomCode"}, "a7_2v0X_VHvB": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "OXfe9q-F0vDn": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "P9OKvN_EJLcD", "parent": {"__ref": "qk9Msgs2vZEQ"}, "locked": null, "vsettings": [{"__ref": "mK1QS1pxaiiv"}], "__type": "TplTag"}, "mK1QS1pxaiiv": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "VgA4FuIvyc7O"}, "dataCond": null, "dataRep": null, "text": {"__ref": "fTZ-1sHJM3YI"}, "columnsConfig": null, "__type": "VariantSetting"}, "VgA4FuIvyc7O": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "font-family": "<PERSON><PERSON>"}, "mixins": [], "__type": "RuleSet"}, "Lc9Vo1FciFu9": {"name": "currentItem", "uuid": "j6HSoO1gMeYN", "__type": "Var"}, "Houy0ywxl0MR": {"name": "currentIndex", "uuid": "yfvVq38S0ydX", "__type": "Var"}, "fTZ-1sHJM3YI": {"expr": {"__ref": "VtHiuboiN3ls"}, "html": false, "__type": "ExprText"}, "VtHiuboiN3ls": {"path": ["currentItem"], "fallback": {"__ref": "nbrDyFbq2_Sx"}, "__type": "ObjectPath"}, "nbrDyFbq2_Sx": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "IihAyWE07hm_": {"tag": "div", "name": "repeatedTpls1", "children": [{"__ref": "YSCh2hL_jzM-"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "gYaUFWLLi1Rt", "parent": {"__ref": "J3avHBQl3VkF"}, "locked": null, "vsettings": [{"__ref": "QaMh77Qv6BOf"}], "__type": "TplTag"}, "YSCh2hL_jzM-": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "lE5Cu2AeE0Lm", "parent": {"__ref": "IihAyWE07hm_"}, "locked": null, "vsettings": [{"__ref": "qQwnivktl1lP"}], "__type": "TplTag"}, "QaMh77Qv6BOf": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "HXWM3Upw6EVc"}, "dataCond": null, "dataRep": {"__ref": "k-Ckwu39Dwjz"}, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "qQwnivktl1lP": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "mMiAxkoHyS5x"}, "dataCond": null, "dataRep": null, "text": {"__ref": "U4NCLzQCaMXh"}, "columnsConfig": null, "__type": "VariantSetting"}, "HXWM3Upw6EVc": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "k-Ckwu39Dwjz": {"element": {"__ref": "5pkHuLS_Bbpn"}, "index": {"__ref": "t9CbBhGQ8GtT"}, "collection": {"__ref": "kDz9h1-3nQKO"}, "__type": "Rep"}, "mMiAxkoHyS5x": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "5pkHuLS_Bbpn": {"name": "currentItem", "uuid": "gFyhMQb2bn3f", "__type": "Var"}, "t9CbBhGQ8GtT": {"name": "currentIndex", "uuid": "iw0NBuytuOux", "__type": "Var"}, "kDz9h1-3nQKO": {"code": "([\n  1,\n  2,\n  3\n])", "fallback": {"__ref": "bPkUGgpd-Hvk"}, "__type": "CustomCode"}, "bPkUGgpd-Hvk": {"code": "([])", "fallback": null, "__type": "CustomCode"}, "U4NCLzQCaMXh": {"expr": {"__ref": "GehlXGN4QOZS"}, "html": false, "__type": "ExprText"}, "GehlXGN4QOZS": {"code": "(`${ currentItem } - ${ currentIndex }`)", "fallback": {"__ref": "fVUsd30bL6hw"}, "__type": "CustomCode"}, "fVUsd30bL6hw": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "TdA4jKZocLh2": {"tag": "div", "name": "styledTpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "CR6kTMFjuZ_r", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "ADcHkUk8Td53"}], "__type": "TplTag"}, "ADcHkUk8Td53": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "0CjJ5jK-GLm_"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "0CjJ5jK-GLm_": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "200px", "height": "100px", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "background": "linear-gradient(#FF5454, #FF5454)"}, "mixins": [], "__type": "RuleSet"}, "LOJelv6ZCh0Z": {"tag": "div", "name": "backgroundImageTpl", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "LdkxqJOQiw-T", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "2XyxO6eU7gHn"}], "__type": "TplTag"}, "2XyxO6eU7gHn": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "8m8IreIYdDYH"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8m8IreIYdDYH": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "300px", "height": "200px", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "background": "var(--image-XkRu_IZ7Wvhb)"}, "mixins": [], "__type": "RuleSet"}, "tbdBncJERLPx": {"uuid": "XkRu_IZ7Wvhb", "name": "image", "type": "picture", "dataUri": "https://site-assets.plasmic.app/551540ff38e067810daf2cb9f2f8d571.png", "width": 330, "height": 220, "aspectRatio": null, "__type": "ImageAsset"}, "HB3jlWSBSysQ": {"tag": "div", "name": "tplWithDynamicStyle", "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "HaCqby76LYQE", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "-nnqHGVhzaCr"}], "__type": "TplTag"}, "-nnqHGVhzaCr": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {"style": {"__ref": "3upfTd2eLG7k"}}, "rs": {"__ref": "Zla7EfVY75l2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Zla7EfVY75l2": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "50px", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "3upfTd2eLG7k": {"code": "{\"backgroundColor\":\"blue\"}", "fallback": null, "__type": "CustomCode"}, "1GEuPT7CEAql": {"uuid": "H7yr6ISYKTk1", "name": "StatefulComponent", "params": [{"__ref": "kiUCqDRW4SRi"}, {"__ref": "X1yNT8CA3JPb"}, {"__ref": "o8DNyjKPSH2K"}, {"__ref": "8mmLaDdEPMSW"}], "states": [{"__ref": "ZBFTL7Wa7N3O"}, {"__ref": "tZAY05Jx7jn3"}], "tplTree": {"__ref": "OryIOqQcJSZB"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "Ep7k67GIcRVo"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "lNS3uO2nuvgV": {"component": {"__ref": "1GEuPT7CEAql"}, "matrix": {"__ref": "g3Mfh_tQMxsj"}, "customMatrix": {"__ref": "RJk3YJfvHSqn"}, "__type": "ComponentArena"}, "OryIOqQcJSZB": {"tag": "div", "name": null, "children": [{"__ref": "L2-utZ2-bgnr"}, {"__ref": "xmO2oIU9a6-K"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "I85szfSCEtwd", "parent": null, "locked": null, "vsettings": [{"__ref": "eF_jTezJK08x"}], "__type": "TplTag"}, "Ep7k67GIcRVo": {"uuid": "nkBAhhyZoYKi", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "g3Mfh_tQMxsj": {"rows": [{"__ref": "Rn-CgldaG9Oe"}], "__type": "ArenaFrameGrid"}, "RJk3YJfvHSqn": {"rows": [{"__ref": "Lny0Lpwk-P6z"}], "__type": "ArenaFrameGrid"}, "eF_jTezJK08x": {"variants": [{"__ref": "Ep7k67GIcRVo"}], "args": [], "attrs": {}, "rs": {"__ref": "xcpCtDtSB5qC"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Rn-CgldaG9Oe": {"cols": [{"__ref": "W6Hv1TOfaBLB"}], "rowKey": null, "__type": "ArenaFrameRow"}, "Lny0Lpwk-P6z": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "xcpCtDtSB5qC": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "W6Hv1TOfaBLB": {"frame": {"__ref": "ccE_ishQ8pI_"}, "cellKey": {"__ref": "Ep7k67GIcRVo"}, "__type": "ArenaFrameCell"}, "ccE_ishQ8pI_": {"uuid": "wznggKZf_kdd", "width": 1180, "height": 540, "container": {"__ref": "_6pNyV-FY490"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "Ep7k67GIcRVo"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "_6pNyV-FY490": {"name": null, "component": {"__ref": "1GEuPT7CEAql"}, "uuid": "tO6D1EtsIi5Z", "parent": null, "locked": null, "vsettings": [{"__ref": "E4tVHcbq0zMF"}], "__type": "TplComponent"}, "E4tVHcbq0zMF": {"variants": [{"__ref": "PCo-Mei8FONz"}], "args": [], "attrs": {}, "rs": {"__ref": "yE3qLS9vHAc2"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "yE3qLS9vHAc2": {"values": {}, "mixins": [], "__type": "RuleSet"}, "ZBFTL7Wa7N3O": {"param": {"__ref": "kiUCqDRW4SRi"}, "accessType": "private", "variableType": "boolean", "onChangeParam": {"__ref": "X1yNT8CA3JPb"}, "tplNode": null, "implicitState": null, "__type": "State"}, "kiUCqDRW4SRi": {"type": {"__ref": "b-KVxxaULYcF"}, "state": {"__ref": "ZBFTL7Wa7N3O"}, "variable": {"__ref": "bhCYvSUs-lFb"}, "uuid": "vKu5fPhwRVX8", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "Prt2nfxESseV"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "X1yNT8CA3JPb": {"type": {"__ref": "mCJ5HMoqZxuS"}, "state": {"__ref": "ZBFTL7Wa7N3O"}, "variable": {"__ref": "9XFgkQ0-JqsJ"}, "uuid": "rq4oRYQ65we3", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "bhCYvSUs-lFb": {"name": "local", "uuid": "00UekvBS3q9K", "__type": "Var"}, "mCJ5HMoqZxuS": {"name": "func", "params": [{"__ref": "ec0D6DSxhRPl"}], "__type": "FunctionType"}, "9XFgkQ0-JqsJ": {"name": "On local Change", "uuid": "-LFnj5XAR_TV", "__type": "Var"}, "ec0D6DSxhRPl": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "FfkWD9pBBZVJ"}, "__type": "ArgType"}, "FfkWD9pBBZVJ": {"name": "text", "__type": "Text"}, "b-KVxxaULYcF": {"name": "bool", "__type": "BoolType"}, "Prt2nfxESseV": {"code": "false", "fallback": null, "__type": "CustomCode"}, "tZAY05Jx7jn3": {"param": {"__ref": "o8DNyjKPSH2K"}, "accessType": "writable", "variableType": "text", "onChangeParam": {"__ref": "8mmLaDdEPMSW"}, "tplNode": null, "implicitState": null, "__type": "State"}, "o8DNyjKPSH2K": {"type": {"__ref": "IsbxRXDqCLEk"}, "state": {"__ref": "tZAY05Jx7jn3"}, "variable": {"__ref": "ldHdAtQM73Vc"}, "uuid": "sRVcWhfJfjG8", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "0iBD8pXK45UE"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "8mmLaDdEPMSW": {"type": {"__ref": "A45Cbrm0AoWX"}, "state": {"__ref": "tZAY05Jx7jn3"}, "variable": {"__ref": "P0gIf08fIo-h"}, "uuid": "yeEvCoRj52LA", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "IsbxRXDqCLEk": {"name": "text", "__type": "Text"}, "ldHdAtQM73Vc": {"name": "outside", "uuid": "oK65YEBpeV_d", "__type": "Var"}, "A45Cbrm0AoWX": {"name": "func", "params": [{"__ref": "OxyZRLay1KPS"}], "__type": "FunctionType"}, "P0gIf08fIo-h": {"name": "On outside change", "uuid": "vrq68LgPXXF4", "__type": "Var"}, "OxyZRLay1KPS": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "YyJyIiMpNmyy"}, "__type": "ArgType"}, "YyJyIiMpNmyy": {"name": "text", "__type": "Text"}, "0iBD8pXK45UE": {"text": ["test"], "__type": "TemplatedString"}, "L2-utZ2-bgnr": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "6jJ-wlixcKnk", "parent": {"__ref": "OryIOqQcJSZB"}, "locked": null, "vsettings": [{"__ref": "TTdMy6V1pzFD"}], "__type": "TplTag"}, "TTdMy6V1pzFD": {"variants": [{"__ref": "Ep7k67GIcRVo"}], "args": [], "attrs": {}, "rs": {"__ref": "xnxpMvj8cIPk"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ENJGbg7_8gYL"}, "columnsConfig": null, "__type": "VariantSetting"}, "xnxpMvj8cIPk": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "ENJGbg7_8gYL": {"expr": {"__ref": "IjEZKYhCcBbQ"}, "html": false, "__type": "ExprText"}, "IjEZKYhCcBbQ": {"code": "($state.local ? \"I am true\" : \"I am false\")", "fallback": {"__ref": "Nm99TexqBlCq"}, "__type": "CustomCode"}, "Nm99TexqBlCq": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "xmO2oIU9a6-K": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "HABAQt_k44tE", "parent": {"__ref": "OryIOqQcJSZB"}, "locked": null, "vsettings": [{"__ref": "qtB8a0GWQzAP"}], "__type": "TplTag"}, "qtB8a0GWQzAP": {"variants": [{"__ref": "Ep7k67GIcRVo"}], "args": [], "attrs": {}, "rs": {"__ref": "adAcQAbXFGgM"}, "dataCond": null, "dataRep": null, "text": {"__ref": "WR2Jq4e_dEze"}, "columnsConfig": null, "__type": "VariantSetting"}, "adAcQAbXFGgM": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "WR2Jq4e_dEze": {"expr": {"__ref": "ISWWBCEXuqYH"}, "html": false, "__type": "ExprText"}, "ISWWBCEXuqYH": {"code": "($state.outside)", "fallback": {"__ref": "jz55C1xw6pbT"}, "__type": "CustomCode"}, "jz55C1xw6pbT": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "LtrUeTL_fWAv": {"name": "statefulComponent", "component": {"__ref": "1GEuPT7CEAql"}, "uuid": "lBIcVuF6mUNE", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "7kfz5g_QkAPu"}], "__type": "TplComponent"}, "pbtOuAKHftIR": {"param": {"__ref": "rmCvBSxt_muX"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "-ZZqSorqTpeq"}, "tplNode": {"__ref": "LtrUeTL_fWAv"}, "implicitState": {"__ref": "tZAY05Jx7jn3"}, "__type": "State"}, "rmCvBSxt_muX": {"type": {"__ref": "Gl0Ji3GcdW1s"}, "state": {"__ref": "pbtOuAKHftIR"}, "variable": {"__ref": "6cW6Im4L7Krs"}, "uuid": "g5jg7YhYO25p", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "-ZZqSorqTpeq": {"type": {"__ref": "_DkCqfuUKWZ2"}, "state": {"__ref": "pbtOuAKHftIR"}, "variable": {"__ref": "AW6oDEzIZR4f"}, "uuid": "gEb-Vtq8cBQv", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "7kfz5g_QkAPu": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [{"__ref": "lHJywwY3uwdt"}], "attrs": {}, "rs": {"__ref": "_On14PDG5HiR"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Gl0Ji3GcdW1s": {"name": "text", "__type": "Text"}, "6cW6Im4L7Krs": {"name": "StatefulComponent outside", "uuid": "KfPc4XaGQEol", "__type": "Var"}, "_DkCqfuUKWZ2": {"name": "func", "params": [{"__ref": "OCqYMg8vIIZ6"}], "__type": "FunctionType"}, "AW6oDEzIZR4f": {"name": "On StatefulComponent outside change", "uuid": "eWE5L57euVfs", "__type": "Var"}, "_On14PDG5HiR": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "OCqYMg8vIIZ6": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "tEO889_1ep08"}, "__type": "ArgType"}, "tEO889_1ep08": {"name": "text", "__type": "Text"}, "lHJywwY3uwdt": {"param": {"__ref": "o8DNyjKPSH2K"}, "expr": {"__ref": "wGJdxaujb4cF"}, "__type": "Arg"}, "wGJdxaujb4cF": {"text": ["updatedValue"], "__type": "TemplatedString"}, "KgVE1orDcIG2": {"tag": "div", "name": "bunchOfCustomCode", "children": [{"__ref": "Sf4xtUNVvp5T"}, {"__ref": "yZbyIjXPwNjI"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "8NZ0HFu8MJ7l", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "vIoPjGl5vo47"}], "__type": "TplTag"}, "vIoPjGl5vo47": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "uueRCnXSktqZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "uueRCnXSktqZ": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "Sf4xtUNVvp5T": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "iyx99qF_QgdA", "parent": {"__ref": "KgVE1orDcIG2"}, "locked": null, "vsettings": [{"__ref": "vxTNlmQgSMRp"}], "__type": "TplTag"}, "vxTNlmQgSMRp": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "HjUS97_MxMqI"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ACeNvtGKTjOG"}, "columnsConfig": null, "__type": "VariantSetting"}, "HjUS97_MxMqI": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "ACeNvtGKTjOG": {"expr": {"__ref": "a0sr6UlBFIGg"}, "html": false, "__type": "ExprText"}, "a0sr6UlBFIGg": {"code": "($state.statefulComponent.outside + \" this is nice\")", "fallback": {"__ref": "hbL4OLosdTbI"}, "__type": "CustomCode"}, "hbL4OLosdTbI": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "yZbyIjXPwNjI": {"tag": "div", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "9axXuGqWJIyk", "parent": {"__ref": "KgVE1orDcIG2"}, "locked": null, "vsettings": [{"__ref": "s_FLKW2EISq9"}], "__type": "TplTag"}, "s_FLKW2EISq9": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "TMHjAZYAtvWn"}, "dataCond": null, "dataRep": null, "text": {"__ref": "EbhzyMd2JEa1"}, "columnsConfig": null, "__type": "VariantSetting"}, "TMHjAZYAtvWn": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "YfYhnY-GtBkl": {"param": {"__ref": "KqwgzPqIGqYZ"}, "accessType": "private", "variableType": "text", "onChangeParam": {"__ref": "uQqg7pn8ix3i"}, "tplNode": null, "implicitState": null, "__type": "State"}, "KqwgzPqIGqYZ": {"type": {"__ref": "gXAx2szu_YbM"}, "state": {"__ref": "YfYhnY-GtBkl"}, "variable": {"__ref": "1dhuDRVsLjdm"}, "uuid": "YoO6JLPRUGQl", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": {"__ref": "wahimYxq-X0X"}, "previewExpr": null, "propEffect": null, "description": "text", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateParam"}, "uQqg7pn8ix3i": {"type": {"__ref": "EmgTx-AtEa-n"}, "state": {"__ref": "YfYhnY-GtBkl"}, "variable": {"__ref": "r7kJzwp9USI5"}, "uuid": "iQbpNKKh905h", "enumValues": [], "origin": null, "exportType": "ToolsOnly", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "EventHandler", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "StateChangeHandlerParam"}, "gXAx2szu_YbM": {"name": "text", "__type": "Text"}, "1dhuDRVsLjdm": {"name": "variable", "uuid": "tAXzEdznMjD5", "__type": "Var"}, "EmgTx-AtEa-n": {"name": "func", "params": [{"__ref": "2SCx9TO6T-1L"}], "__type": "FunctionType"}, "r7kJzwp9USI5": {"name": "On variable change", "uuid": "Vo3nHFgafKId", "__type": "Var"}, "2SCx9TO6T-1L": {"name": "arg", "argName": "val", "displayName": null, "type": {"__ref": "CCRYRz6YW9Iq"}, "__type": "ArgType"}, "CCRYRz6YW9Iq": {"name": "text", "__type": "Text"}, "wahimYxq-X0X": {"text": ["look at this"], "__type": "TemplatedString"}, "EbhzyMd2JEa1": {"expr": {"__ref": "fQByUnYqczGh"}, "html": false, "__type": "ExprText"}, "fQByUnYqczGh": {"code": "($state.variable.toUpperCase())", "fallback": {"__ref": "N9qB2DKos2uF"}, "__type": "CustomCode"}, "N9qB2DKos2uF": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "pQqULoBb29eu": {"uuid": "jvMwr7GJ4sks", "name": "ComponentWithImagesAndProps", "params": [{"__ref": "7buSXg0RBISE"}, {"__ref": "aECDHZ6f9Ds5"}, {"__ref": "aSfFst2oaGwj"}, {"__ref": "5h_rWC3PSPLC"}, {"__ref": "tdny320f916e"}, {"__ref": "c2SIJjmxM5cE"}, {"__ref": "XwWH8nFeoxBA"}, {"__ref": "tOIfaAKH32I2"}], "states": [], "tplTree": {"__ref": "II10h3OkhXQe"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "P5Xbsi8ElZbA"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "J1R4V9ewcmog": {"component": {"__ref": "pQqULoBb29eu"}, "matrix": {"__ref": "PAdKEjD-3Xsm"}, "customMatrix": {"__ref": "rABLvbK1Ypip"}, "__type": "ComponentArena"}, "II10h3OkhXQe": {"tag": "div", "name": null, "children": [{"__ref": "lyVIjIUpjc0Y"}, {"__ref": "N7sp8CLFyJNR"}, {"__ref": "MGNaoFBn-Kgz"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "6_0-ZwcXlolc", "parent": null, "locked": null, "vsettings": [{"__ref": "cX6NRl-RX-P9"}], "__type": "TplTag"}, "P5Xbsi8ElZbA": {"uuid": "UzxvSK-Sw7dz", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "PAdKEjD-3Xsm": {"rows": [{"__ref": "dAKe7WaHKF91"}], "__type": "ArenaFrameGrid"}, "rABLvbK1Ypip": {"rows": [{"__ref": "s7GB_xuAFYmJ"}], "__type": "ArenaFrameGrid"}, "cX6NRl-RX-P9": {"variants": [{"__ref": "P5Xbsi8ElZbA"}], "args": [], "attrs": {}, "rs": {"__ref": "O9wZMZk-adoZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dAKe7WaHKF91": {"cols": [{"__ref": "qNDMbwYDG2YO"}], "rowKey": null, "__type": "ArenaFrameRow"}, "s7GB_xuAFYmJ": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "O9wZMZk-adoZ": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "qNDMbwYDG2YO": {"frame": {"__ref": "YttJBQN8vA3Q"}, "cellKey": {"__ref": "P5Xbsi8ElZbA"}, "__type": "ArenaFrameCell"}, "YttJBQN8vA3Q": {"uuid": "7UYHP4r3w9Ru", "width": 1180, "height": 540, "container": {"__ref": "uI8CQs93xaJW"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "P5Xbsi8ElZbA"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "uI8CQs93xaJW": {"name": null, "component": {"__ref": "pQqULoBb29eu"}, "uuid": "2GxEccBiuXT1", "parent": null, "locked": null, "vsettings": [{"__ref": "kY0vCAoIN1-H"}], "__type": "TplComponent"}, "kY0vCAoIN1-H": {"variants": [{"__ref": "PCo-Mei8FONz"}], "args": [], "attrs": {}, "rs": {"__ref": "NBVZTPHAo7VJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NBVZTPHAo7VJ": {"values": {}, "mixins": [], "__type": "RuleSet"}, "lyVIjIUpjc0Y": {"tag": "img", "name": "rawImage", "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "Zg6qsS1JGX5I", "parent": {"__ref": "II10h3OkhXQe"}, "locked": null, "vsettings": [{"__ref": "G5r5tfvpknDf"}], "__type": "TplTag"}, "G5r5tfvpknDf": {"variants": [{"__ref": "P5Xbsi8ElZbA"}], "args": [], "attrs": {"loading": {"__ref": "frPHDMssaagk"}, "src": {"__ref": "Ft7rn4q6euMH"}}, "rs": {"__ref": "cpy-cijpviyy"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "frPHDMssaagk": {"code": "\"lazy\"", "fallback": null, "__type": "CustomCode"}, "cpy-cijpviyy": {"values": {"position": "relative", "object-fit": "cover", "max-width": "100%", "width": "200px", "height": "200px"}, "mixins": [], "__type": "RuleSet"}, "cu7APL4ixoPJ": {"uuid": "twz8GANpqpPz", "name": "image 2", "type": "picture", "dataUri": "https://site-assets.plasmic.app/65db314b94e67bd1cd85bb7a51a93f85.png", "width": 1200, "height": 690, "aspectRatio": null, "__type": "ImageAsset"}, "Ft7rn4q6euMH": {"asset": {"__ref": "cu7APL4ixoPJ"}, "__type": "ImageAssetRef"}, "N7sp8CLFyJNR": {"tag": "img", "name": "imageFromLink", "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "ErtPjBaUdkDs", "parent": {"__ref": "II10h3OkhXQe"}, "locked": null, "vsettings": [{"__ref": "kPCNFgLr_THZ"}], "__type": "TplTag"}, "kPCNFgLr_THZ": {"variants": [{"__ref": "P5Xbsi8ElZbA"}], "args": [], "attrs": {"loading": {"__ref": "CaZUMWISgwNu"}, "src": {"__ref": "iX99Cuew0fqo"}}, "rs": {"__ref": "L6lb-3jLwlx9"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CaZUMWISgwNu": {"code": "\"lazy\"", "fallback": null, "__type": "CustomCode"}, "L6lb-3jLwlx9": {"values": {"position": "relative", "object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "iX99Cuew0fqo": {"code": "\"https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTXdBA9dnijNmKuHaYkEDKcy80J2Eexp4PEvMDCB0PcPg&s\"", "fallback": null, "__type": "CustomCode"}, "MGNaoFBn-Kgz": {"tag": "img", "name": "linkedImage", "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "0_P2fSixCl3g", "parent": {"__ref": "II10h3OkhXQe"}, "locked": null, "vsettings": [{"__ref": "kXBIkQlOy2Zo"}], "__type": "TplTag"}, "kXBIkQlOy2Zo": {"variants": [{"__ref": "P5Xbsi8ElZbA"}], "args": [], "attrs": {"loading": {"__ref": "94Zsi6-118us"}, "src": {"__ref": "YWDscbENkuzN"}}, "rs": {"__ref": "1fYZ7Dk2FY_W"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "94Zsi6-118us": {"code": "\"lazy\"", "fallback": null, "__type": "CustomCode"}, "1fYZ7Dk2FY_W": {"values": {"position": "relative", "object-fit": "cover", "max-width": "100%", "width": "100px"}, "mixins": [], "__type": "RuleSet"}, "7buSXg0RBISE": {"type": {"__ref": "Wz6qJRRNM4mZ"}, "variable": {"__ref": "DbQLiiMl0Ns4"}, "uuid": "22KofKZUhqje", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "dXcYEfvJPXCe"}, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "YWDscbENkuzN": {"variable": {"__ref": "DbQLiiMl0Ns4"}, "__type": "VarRef"}, "Wz6qJRRNM4mZ": {"name": "img", "__type": "Img"}, "DbQLiiMl0Ns4": {"name": "img", "uuid": "YPza4WrwL7aV", "__type": "Var"}, "YYp_-EfFTSzm": {"uuid": "kkmEh37L-6FP", "name": "image 3", "type": "picture", "dataUri": "https://site-assets.plasmic.app/688176ee64e5c71ea85803350598218a.png", "width": 1080, "height": 720, "aspectRatio": null, "__type": "ImageAsset"}, "dXcYEfvJPXCe": {"asset": {"__ref": "YYp_-EfFTSzm"}, "__type": "ImageAssetRef"}, "aECDHZ6f9Ds5": {"type": {"__ref": "Hn4u4sl_kyaG"}, "variable": {"__ref": "6ucTJTzH5cwo"}, "uuid": "oaXvqJY6ty2H", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Hn4u4sl_kyaG": {"name": "func", "params": [], "__type": "FunctionType"}, "6ucTJTzH5cwo": {"name": "prop0", "uuid": "AeqXJznJ7q1Y", "__type": "Var"}, "aSfFst2oaGwj": {"type": {"__ref": "WtI_MocHvCQU"}, "variable": {"__ref": "zs9nyKIHHjfP"}, "uuid": "5a9D_2G-n-Kr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "jKuhYUu4wOws"}, "previewExpr": {"__ref": "RQO4zG2Xtm71"}, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "WtI_MocHvCQU": {"name": "text", "__type": "Text"}, "zs9nyKIHHjfP": {"name": "prop1", "uuid": "9fWF8FXUfprf", "__type": "Var"}, "jKuhYUu4wOws": {"code": "\"something\"", "fallback": null, "__type": "CustomCode"}, "RQO4zG2Xtm71": {"code": "\"some\"", "fallback": null, "__type": "CustomCode"}, "5h_rWC3PSPLC": {"type": {"__ref": "jnIwvuzW0JU8"}, "variable": {"__ref": "vM_JZy1Gi_C9"}, "uuid": "noX3qMBxFB9C", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "_DTQ-uZ7SD7U"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "jnIwvuzW0JU8": {"name": "bool", "__type": "BoolType"}, "vM_JZy1Gi_C9": {"name": "prop2", "uuid": "tMZK7UUWHieI", "__type": "Var"}, "_DTQ-uZ7SD7U": {"code": "false", "fallback": null, "__type": "CustomCode"}, "tdny320f916e": {"type": {"__ref": "j1NLBpV6th6a"}, "variable": {"__ref": "8SSObO99HTx9"}, "uuid": "VTYUMnXILQhZ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "il43nEVoxbGX"}, "previewExpr": {"__ref": "M9N99GyAw_I8"}, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "j1NLBpV6th6a": {"name": "dateString", "__type": "DateString"}, "8SSObO99HTx9": {"name": "prop3", "uuid": "6RWnJuLIkWai", "__type": "Var"}, "il43nEVoxbGX": {"code": "\"2024-04-11T02:06:36.006Z\"", "fallback": null, "__type": "CustomCode"}, "M9N99GyAw_I8": {"code": "\"2024-04-25T02:06:38.974Z\"", "fallback": null, "__type": "CustomCode"}, "c2SIJjmxM5cE": {"type": {"__ref": "qGbKy5pZdoyI"}, "variable": {"__ref": "N5HXaepM0_DD"}, "uuid": "IqoxHevSGPJn", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "FNWLyWf52pRn"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "qGbKy5pZdoyI": {"name": "dateRangeStrings", "__type": "DateRangeStrings"}, "N5HXaepM0_DD": {"name": "prop4", "uuid": "7tv2nheO8W89", "__type": "Var"}, "FNWLyWf52pRn": {"code": "[\"2024-04-06T02:06:52.967Z\",\"2024-04-20T02:06:55.967Z\"]", "fallback": null, "__type": "CustomCode"}, "XwWH8nFeoxBA": {"type": {"__ref": "uQ_oIrrmIu8w"}, "variable": {"__ref": "8wVVzLrAlJ6q"}, "uuid": "gj7zVIt3e3ri", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "uQ_oIrrmIu8w": {"name": "text", "__type": "Text"}, "8wVVzLrAlJ6q": {"name": "prop5", "uuid": "Rc5DCUTuKOUn", "__type": "Var"}, "tOIfaAKH32I2": {"type": {"__ref": "Zla-p9zUn-Dn"}, "variable": {"__ref": "JYcGhJlZqvw_"}, "uuid": "4YzRR5iA2j6Z", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": {"__ref": "iGi07_oqCQuK"}, "previewExpr": null, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "Zla-p9zUn-Dn": {"name": "any", "__type": "AnyType"}, "JYcGhJlZqvw_": {"name": "prop6", "uuid": "usyFsPGUBEr2", "__type": "Var"}, "iGi07_oqCQuK": {"code": "{\"key\":\"value\"}", "fallback": null, "__type": "CustomCode"}, "iU_MSMf0qWxV": {"uuid": "hEYxyygY4TzY", "name": "ComponentWithTokens", "params": [], "states": [], "tplTree": {"__ref": "mDuj24D_5O_5"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "i0g6dHfJLduw"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "h8ijY8XRq4ZB": {"component": {"__ref": "iU_MSMf0qWxV"}, "matrix": {"__ref": "kB194K2NmNkT"}, "customMatrix": {"__ref": "CW4LWqki80RB"}, "__type": "ComponentArena"}, "mDuj24D_5O_5": {"tag": "div", "name": null, "children": [{"__ref": "IvLy9nRtfH8J"}, {"__ref": "BM_vubTmTK1v"}, {"__ref": "3EqdbDyB7Xep"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "J_pckbHUvUIx", "parent": null, "locked": null, "vsettings": [{"__ref": "FHXntAo58cnF"}], "__type": "TplTag"}, "i0g6dHfJLduw": {"uuid": "ZYO_OCKDpDtQ", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "kB194K2NmNkT": {"rows": [{"__ref": "kgOMK-Nm-j35"}], "__type": "ArenaFrameGrid"}, "CW4LWqki80RB": {"rows": [{"__ref": "_5Qhd4MeCtGE"}], "__type": "ArenaFrameGrid"}, "FHXntAo58cnF": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "RxJFCHOiaYUQ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "kgOMK-Nm-j35": {"cols": [{"__ref": "zMDekAol5GT9"}], "rowKey": null, "__type": "ArenaFrameRow"}, "_5Qhd4MeCtGE": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "RxJFCHOiaYUQ": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "wrap", "justify-content": "flex-start", "align-items": "center"}, "mixins": [], "__type": "RuleSet"}, "zMDekAol5GT9": {"frame": {"__ref": "dxsI6Ipdf7Jp"}, "cellKey": {"__ref": "i0g6dHfJLduw"}, "__type": "ArenaFrameCell"}, "dxsI6Ipdf7Jp": {"uuid": "zWj_C3Sv8Orm", "width": 1180, "height": 540, "container": {"__ref": "namF_pgw574r"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "i0g6dHfJLduw"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "namF_pgw574r": {"name": null, "component": {"__ref": "iU_MSMf0qWxV"}, "uuid": "3rnOs0VDjgdc", "parent": null, "locked": null, "vsettings": [{"__ref": "K3sSIg5qS4l3"}], "__type": "TplComponent"}, "K3sSIg5qS4l3": {"variants": [{"__ref": "PCo-Mei8FONz"}], "args": [], "attrs": {}, "rs": {"__ref": "awNoKwk_XJx6"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "awNoKwk_XJx6": {"values": {}, "mixins": [], "__type": "RuleSet"}, "IvLy9nRtfH8J": {"tag": "div", "name": null, "children": [{"__ref": "xPQF0QVU8bgZ"}, {"__ref": "6nxU9M2_x9wt"}, {"__ref": "Z4GaPzviKz7m"}, {"__ref": "06LQBM2I3jm4"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "DLCoYa9NkIIb", "parent": {"__ref": "mDuj24D_5O_5"}, "locked": null, "vsettings": [{"__ref": "S4pzBcioKz_h"}], "__type": "TplTag"}, "S4pzBcioKz_h": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "J4RrQxaeHK-v"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "J4RrQxaeHK-v": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "xPQF0QVU8bgZ": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "jfsZ607ttV7Z", "parent": {"__ref": "IvLy9nRtfH8J"}, "locked": null, "vsettings": [{"__ref": "oN8IIl3u5c3g"}], "__type": "TplTag"}, "oN8IIl3u5c3g": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "TsTU6nGLUYGE"}, "dataCond": null, "dataRep": null, "text": {"__ref": "sWK9L8_B8ep1"}, "columnsConfig": null, "__type": "VariantSetting"}, "TsTU6nGLUYGE": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-Sn488bmGNNUT)"}, "mixins": [], "__type": "RuleSet"}, "8ZU5DPIFgctD": {"name": "green", "type": "Color", "uuid": "Sn488bmGNNUT", "value": "#00FF7C", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "6nxU9M2_x9wt": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "PZI_jfbKiCa-", "parent": {"__ref": "IvLy9nRtfH8J"}, "locked": null, "vsettings": [{"__ref": "qnP77-gV9zIj"}], "__type": "TplTag"}, "qnP77-gV9zIj": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "J5CZTnACQpaK"}, "dataCond": null, "dataRep": null, "text": {"__ref": "fhpo1_bpgR1U"}, "columnsConfig": null, "__type": "VariantSetting"}, "J5CZTnACQpaK": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-DNn0fQhp72X-)"}, "mixins": [], "__type": "RuleSet"}, "fhpo1_bpgR1U": {"markers": [], "text": "Blue", "__type": "RawText"}, "gMkCatWR0Y-H": {"name": "blue", "type": "Color", "uuid": "DNn0fQhp72X-", "value": "#3251F1", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "sWK9L8_B8ep1": {"markers": [], "text": "Green", "__type": "RawText"}, "Z4GaPzviKz7m": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "Ot5h8oIuYUKV", "parent": {"__ref": "IvLy9nRtfH8J"}, "locked": null, "vsettings": [{"__ref": "524oUUhoFbFw"}], "__type": "TplTag"}, "524oUUhoFbFw": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "bGwgPAeaXGGp"}, "dataCond": null, "dataRep": null, "text": {"__ref": "6F9MP7J9U_3X"}, "columnsConfig": null, "__type": "VariantSetting"}, "bGwgPAeaXGGp": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-PksCh22YWpyj)"}, "mixins": [], "__type": "RuleSet"}, "6F9MP7J9U_3X": {"markers": [], "text": "Red", "__type": "RawText"}, "eM56m4EybwQU": {"name": "red", "type": "Color", "uuid": "PksCh22YWpyj", "value": "#F14B32", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "06LQBM2I3jm4": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "bQBkR2HDWn8i", "parent": {"__ref": "IvLy9nRtfH8J"}, "locked": null, "vsettings": [{"__ref": "DD4cMmveAEUM"}], "__type": "TplTag"}, "DD4cMmveAEUM": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "ngGTd2sWY12a"}, "dataCond": null, "dataRep": null, "text": {"__ref": "_T_xKQHkJjmy"}, "columnsConfig": null, "__type": "VariantSetting"}, "ngGTd2sWY12a": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token--AfSS4Yf5gTd)"}, "mixins": [], "__type": "RuleSet"}, "_T_xKQHkJjmy": {"markers": [], "text": "Purple", "__type": "RawText"}, "oa0gNEDz4Ft0": {"name": "purple", "type": "Color", "uuid": "-AfSS4Yf5gTd", "value": "#9B2AFF", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "BM_vubTmTK1v": {"tag": "div", "name": null, "children": [{"__ref": "WCb0OKiQCAiK"}, {"__ref": "d-qVIissr1SM"}, {"__ref": "npFkjjTk9BwK"}, {"__ref": "8qjVbcYBPya8"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "be-HtU_gK-kO", "parent": {"__ref": "mDuj24D_5O_5"}, "locked": null, "vsettings": [{"__ref": "MoVlY0Jf_Q50"}], "__type": "TplTag"}, "WCb0OKiQCAiK": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "xFflk-F7OBdy", "parent": {"__ref": "BM_vubTmTK1v"}, "locked": null, "vsettings": [{"__ref": "4fD8FTQD9wFZ"}], "__type": "TplTag"}, "d-qVIissr1SM": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "WUXGrO-Fsfkc", "parent": {"__ref": "BM_vubTmTK1v"}, "locked": null, "vsettings": [{"__ref": "usHi7v5O9nI4"}], "__type": "TplTag"}, "npFkjjTk9BwK": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "QOfwvfAd22dD", "parent": {"__ref": "BM_vubTmTK1v"}, "locked": null, "vsettings": [{"__ref": "priWwXtgWho4"}], "__type": "TplTag"}, "8qjVbcYBPya8": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "KTCZUOFBclhy", "parent": {"__ref": "BM_vubTmTK1v"}, "locked": null, "vsettings": [{"__ref": "aVYnlBjsSqVS"}], "__type": "TplTag"}, "MoVlY0Jf_Q50": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "2eDma-wlwzog"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "4fD8FTQD9wFZ": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "nDG-MthfjDOp"}, "dataCond": null, "dataRep": null, "text": {"__ref": "jeJR7HausbHF"}, "columnsConfig": null, "__type": "VariantSetting"}, "usHi7v5O9nI4": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "CSQsSGkgqc_1"}, "dataCond": null, "dataRep": null, "text": {"__ref": "ofJkV8C5hOCJ"}, "columnsConfig": null, "__type": "VariantSetting"}, "priWwXtgWho4": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "NtwjDfypkIlK"}, "dataCond": null, "dataRep": null, "text": {"__ref": "oYrMVq1WvzJC"}, "columnsConfig": null, "__type": "VariantSetting"}, "aVYnlBjsSqVS": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "7U6pcTk2i8eh"}, "dataCond": null, "dataRep": null, "text": {"__ref": "WUB-QXlFytLE"}, "columnsConfig": null, "__type": "VariantSetting"}, "2eDma-wlwzog": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "nDG-MthfjDOp": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-Sn488bmGNNUT)", "font-size": "var(--token-jtxJc0kX4IXp)"}, "mixins": [], "__type": "RuleSet"}, "jeJR7HausbHF": {"markers": [], "text": "Green", "__type": "RawText"}, "CSQsSGkgqc_1": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-DNn0fQhp72X-)", "font-size": "var(--token-eYu-jYKJw86E)"}, "mixins": [], "__type": "RuleSet"}, "ofJkV8C5hOCJ": {"markers": [], "text": "Blue", "__type": "RawText"}, "NtwjDfypkIlK": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-PksCh22YWpyj)", "font-size": "var(--token-zrY7AmRVMMNJ)"}, "mixins": [], "__type": "RuleSet"}, "oYrMVq1WvzJC": {"markers": [], "text": "Red", "__type": "RawText"}, "7U6pcTk2i8eh": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token--AfSS4Yf5gTd)", "font-size": "var(--token--LtteA9IEX_x)"}, "mixins": [], "__type": "RuleSet"}, "WUB-QXlFytLE": {"markers": [], "text": "Purple", "__type": "RawText"}, "RrOuG-Ij_ME1": {"name": "size-24", "type": "FontSize", "uuid": "jtxJc0kX4IXp", "value": "24px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "Fvhm44SSK7Zp": {"name": "size-32", "type": "FontSize", "uuid": "eYu-jYKJw86E", "value": "32px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "n0XSa8r5VqOD": {"name": "size-40", "type": "FontSize", "uuid": "zrY7AmRVMMNJ", "value": "40px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "_8Utb3m_wQKi": {"name": "size-48", "type": "FontSize", "uuid": "-LtteA9IEX_x", "value": "48px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "3EqdbDyB7Xep": {"tag": "div", "name": null, "children": [{"__ref": "E0G3RDL9g6FN"}, {"__ref": "ZuU-ke0sC8U1"}, {"__ref": "oAN8fnt7TBY3"}, {"__ref": "bxthkTQ1G6oY"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "FS6WSPcqcFwL", "parent": {"__ref": "mDuj24D_5O_5"}, "locked": null, "vsettings": [{"__ref": "M2TqI_Eoqn9r"}], "__type": "TplTag"}, "E0G3RDL9g6FN": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "2JP5L2aHUZMk", "parent": {"__ref": "3EqdbDyB7Xep"}, "locked": null, "vsettings": [{"__ref": "ivm1MgaB5b29"}], "__type": "TplTag"}, "ZuU-ke0sC8U1": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "SLDxkTlm2i04", "parent": {"__ref": "3EqdbDyB7Xep"}, "locked": null, "vsettings": [{"__ref": "bqfJtQaXv45i"}], "__type": "TplTag"}, "oAN8fnt7TBY3": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "H-2QqUwcrkXw", "parent": {"__ref": "3EqdbDyB7Xep"}, "locked": null, "vsettings": [{"__ref": "vxzj4bc2bqac"}], "__type": "TplTag"}, "bxthkTQ1G6oY": {"tag": "h3", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "C11gMJPpFjsK", "parent": {"__ref": "3EqdbDyB7Xep"}, "locked": null, "vsettings": [{"__ref": "WI1t6g6QXjFd"}], "__type": "TplTag"}, "M2TqI_Eoqn9r": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "cL6Hn_W9lq4I"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "ivm1MgaB5b29": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "gc54TaD3tlhA"}, "dataCond": null, "dataRep": null, "text": {"__ref": "P2ZKNdpqXSEO"}, "columnsConfig": null, "__type": "VariantSetting"}, "bqfJtQaXv45i": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "90L_8_5W0LiW"}, "dataCond": null, "dataRep": null, "text": {"__ref": "2W7eW8ZsCOlr"}, "columnsConfig": null, "__type": "VariantSetting"}, "vxzj4bc2bqac": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "bULZfDFyZA1T"}, "dataCond": null, "dataRep": null, "text": {"__ref": "k0Pp2ldke2Ea"}, "columnsConfig": null, "__type": "VariantSetting"}, "WI1t6g6QXjFd": {"variants": [{"__ref": "i0g6dHfJLduw"}], "args": [], "attrs": {}, "rs": {"__ref": "iAfFpahc8TCU"}, "dataCond": null, "dataRep": null, "text": {"__ref": "Z7ghA0z3EW0r"}, "columnsConfig": null, "__type": "VariantSetting"}, "cL6Hn_W9lq4I": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "var(--token-YW9EoKRccNtW)", "padding-right": "var(--token-YW9EoKRccNtW)", "padding-bottom": "var(--token-YW9EoKRccNtW)", "padding-top": "var(--token-YW9EoKRccNtW)"}, "mixins": [], "__type": "RuleSet"}, "gc54TaD3tlhA": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-Sn488bmGNNUT)", "font-size": "var(--token-jtxJc0kX4IXp)"}, "mixins": [], "__type": "RuleSet"}, "P2ZKNdpqXSEO": {"markers": [], "text": "Green", "__type": "RawText"}, "90L_8_5W0LiW": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-DNn0fQhp72X-)", "font-size": "var(--token-eYu-jYKJw86E)", "padding-top": "var(--token-1-NKXDJSfhw4)", "padding-right": "var(--token-1-NKXDJSfhw4)", "padding-bottom": "var(--token-1-NKXDJSfhw4)", "padding-left": "var(--token-1-NKXDJSfhw4)"}, "mixins": [], "__type": "RuleSet"}, "2W7eW8ZsCOlr": {"markers": [], "text": "Blue", "__type": "RawText"}, "bULZfDFyZA1T": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token-PksCh22YWpyj)", "font-size": "var(--token-zrY7AmRVMMNJ)", "padding-top": "var(--token-nkylqXjf7CSg)", "padding-right": "var(--token-nkylqXjf7CSg)", "padding-bottom": "var(--token-nkylqXjf7CSg)", "padding-left": "var(--token-nkylqXjf7CSg)"}, "mixins": [], "__type": "RuleSet"}, "k0Pp2ldke2Ea": {"markers": [], "text": "Red", "__type": "RawText"}, "iAfFpahc8TCU": {"values": {"position": "relative", "width": "stretch", "height": "wrap", "max-width": "100%", "color": "var(--token--AfSS4Yf5gTd)", "font-size": "var(--token--LtteA9IEX_x)", "padding-top": "var(--token-2bcuzAgCVvJ5)", "padding-right": "var(--token-2bcuzAgCVvJ5)", "padding-bottom": "var(--token-2bcuzAgCVvJ5)", "padding-left": "var(--token-2bcuzAgCVvJ5)"}, "mixins": [], "__type": "RuleSet"}, "Z7ghA0z3EW0r": {"markers": [], "text": "Purple", "__type": "RawText"}, "kPQwqlT6GH3q": {"name": "padding-8", "type": "Spacing", "uuid": "YW9EoKRccNtW", "value": "8px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "USv0n8rUkb0E": {"name": "padding-16", "type": "Spacing", "uuid": "1-NKXDJSfhw4", "value": "16px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "hE9zp5Qz8Vnx": {"name": "padding-24", "type": "Spacing", "uuid": "nkylqXjf7CSg", "value": "24px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "zH_yFs6IMX9z": {"name": "padding-32", "type": "Spacing", "uuid": "2bcuzAgCVvJ5", "value": "32px", "variantedValues": [], "isRegistered": false, "regKey": null, "__type": "StyleToken"}, "dkocFQfEW6S2": {"name": null, "component": {"__ref": "iU_MSMf0qWxV"}, "uuid": "3HUaMskwGU-J", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "WlfUGUv4blFK"}], "__type": "TplComponent"}, "WlfUGUv4blFK": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "dgpoDybnXXwt"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "dgpoDybnXXwt": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "TmO2m81SXqg_": {"name": null, "component": {"__ref": "pQqULoBb29eu"}, "uuid": "y1uuyNup8tVw", "parent": {"__ref": "3Hq942nvkmI5"}, "locked": null, "vsettings": [{"__ref": "An1HRZfIP8gW"}], "__type": "TplComponent"}, "An1HRZfIP8gW": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [{"__ref": "nF4QBflcMYRh"}], "attrs": {}, "rs": {"__ref": "gwRxDtli3qeZ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "gwRxDtli3qeZ": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "hQfmf6Gcq4Ku": {"uuid": "Tpn4HXfo3Ti2", "name": "image 4", "type": "picture", "dataUri": "https://site-assets.plasmic.app/f32b8e0a8f3ed9c65b71ae66dfae3fa1.png", "width": 670, "height": 400, "aspectRatio": null, "__type": "ImageAsset"}, "nF4QBflcMYRh": {"param": {"__ref": "7buSXg0RBISE"}, "expr": {"__ref": "pswOxHrJS7b-"}, "__type": "Arg"}, "pswOxHrJS7b-": {"asset": {"__ref": "hQfmf6Gcq4Ku"}, "__type": "ImageAssetRef"}, "3Hq942nvkmI5": {"tag": "div", "name": "option0", "children": [{"__ref": "J3avHBQl3VkF"}, {"__ref": "TdA4jKZocLh2"}, {"__ref": "LOJelv6ZCh0Z"}, {"__ref": "HB3jlWSBSysQ"}, {"__ref": "LtrUeTL_fWAv"}, {"__ref": "KgVE1orDcIG2"}, {"__ref": "dkocFQfEW6S2"}, {"__ref": "TmO2m81SXqg_"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "zjTRRXGmIHVI", "parent": {"__ref": "tzjk1QgKQs_m"}, "locked": null, "vsettings": [{"__ref": "Dop78cnoIV81"}], "__type": "TplTag"}, "Dop78cnoIV81": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "_XvT7XtwMjZ5"}, "dataCond": {"__ref": "yrV7FpbcfOin"}, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_XvT7XtwMjZ5": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px", "plasmic-display-none": "false"}, "mixins": [], "__type": "RuleSet"}, "yrV7FpbcfOin": {"code": "true", "fallback": null, "__type": "CustomCode"}, "8xCfK4xak63j": {"tag": "div", "name": "option1", "children": [{"__ref": "UCGYpeRth3Cq"}, {"__ref": "9dRUmZ6_-y3j"}, {"__ref": "cJgUporQCvhG"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "dsQ7J4XaqNFZ", "parent": {"__ref": "tzjk1QgKQs_m"}, "locked": null, "vsettings": [{"__ref": "8tJ2fhwyZMlU"}], "__type": "TplTag"}, "8tJ2fhwyZMlU": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "5BnATWg8S0J3"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "5BnATWg8S0J3": {"values": {"display": "flex", "position": "relative", "flex-direction": "column", "align-items": "center", "justify-content": "flex-start", "width": "stretch", "height": "wrap", "max-width": "100%", "padding-left": "8px", "padding-right": "8px", "padding-bottom": "8px", "padding-top": "8px"}, "mixins": [], "__type": "RuleSet"}, "UCGYpeRth3Cq": {"name": null, "component": {"__ref": "iU_MSMf0qWxV"}, "uuid": "4WEfu6V3HT7T", "parent": {"__ref": "8xCfK4xak63j"}, "locked": null, "vsettings": [{"__ref": "Y6868j0ab6Bf"}], "__type": "TplComponent"}, "Y6868j0ab6Bf": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "GZN7sVkh2zVI"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "GZN7sVkh2zVI": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "9dRUmZ6_-y3j": {"name": null, "component": {"__ref": "pQqULoBb29eu"}, "uuid": "8LRyS7ZtLmZb", "parent": {"__ref": "8xCfK4xak63j"}, "locked": null, "vsettings": [{"__ref": "sA36BHJwsW3V"}], "__type": "TplComponent"}, "sA36BHJwsW3V": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "D_lfvi5R_19L"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "D_lfvi5R_19L": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}, "cJgUporQCvhG": {"name": null, "component": {"__ref": "pQqULoBb29eu"}, "uuid": "oTqkBPCmmC11", "parent": {"__ref": "8xCfK4xak63j"}, "locked": null, "vsettings": [{"__ref": "oka1IC6IUerw"}], "__type": "TplComponent"}, "oka1IC6IUerw": {"variants": [{"__ref": "iLwMGkL58DTH"}], "args": [], "attrs": {}, "rs": {"__ref": "eJVaQBfsHqSp"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "eJVaQBfsHqSp": {"values": {"max-width": "100%", "position": "relative"}, "mixins": [], "__type": "RuleSet"}}, "deps": [], "version": "251-add-data-tokens"}], ["n7g49YuH8h3yxZY88Z61Vv", {"root": "UWyoYPeWTpf0", "map": {"nYShHSZBkFiY": {"values": {"font-family": "Roboto", "font-size": "16px", "font-weight": "400", "font-style": "normal", "color": "#535353", "text-align": "left", "text-transform": "none", "line-height": "1.5", "letter-spacing": "normal", "white-space": "pre-wrap", "user-select": "text", "text-decoration-line": "none", "text-overflow": "clip"}, "mixins": [], "__type": "RuleSet"}, "ANyCDUoMvayg": {"name": "Default Typography", "rs": {"__ref": "nYShHSZBkFiY"}, "preview": null, "uuid": "d7PQfRNyfKC0", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ukt7XBz5gD4d": {"values": {}, "mixins": [], "__type": "RuleSet"}, "kaEGEtQonh4o": {"rs": {"__ref": "ukt7XBz5gD4d"}, "__type": "ThemeLayoutSettings"}, "oRUyP4SmxUjv": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "72px", "font-weight": "900", "letter-spacing": "-4px", "line-height": "1"}, "mixins": [], "__type": "RuleSet"}, "OSkOncdOXZNr": {"name": "Default \"h1\"", "rs": {"__ref": "oRUyP4SmxUjv"}, "preview": null, "uuid": "_BIHTjDIiz3Q", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "Rm9CaUa29Pri": {"selector": "h1", "style": {"__ref": "OSkOncdOXZNr"}, "__type": "ThemeStyle"}, "HdJQWkwW3irZ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "48px", "font-weight": "700", "letter-spacing": "-1px", "line-height": "1.1"}, "mixins": [], "__type": "RuleSet"}, "Pu8LIfMkS0fe": {"name": "Default \"h2\"", "rs": {"__ref": "HdJQWkwW3irZ"}, "preview": null, "uuid": "EzIaOU4AkYwe", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "lHj0eDuwd44c": {"selector": "h2", "style": {"__ref": "Pu8LIfMkS0fe"}, "__type": "ThemeStyle"}, "BDbJji-Maatb": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "32px", "font-weight": "600", "letter-spacing": "-0.8px", "line-height": "1.2"}, "mixins": [], "__type": "RuleSet"}, "pecg4gM-eKSo": {"name": "Default \"h3\"", "rs": {"__ref": "BDbJji-Maatb"}, "preview": null, "uuid": "yRenVs1fHOtk", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "R8ZJwGP-Hv8W": {"selector": "h3", "style": {"__ref": "pecg4gM-eKSo"}, "__type": "ThemeStyle"}, "pVKddSmhjLRx": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "24px", "font-weight": "600", "letter-spacing": "-0.5px", "line-height": "1.3"}, "mixins": [], "__type": "RuleSet"}, "dDlKFOaMtLaY": {"name": "Default \"h4\"", "rs": {"__ref": "pVKddSmhjLRx"}, "preview": null, "uuid": "-K_k9yI9x5FN", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "wrUb5HQKDaQi": {"selector": "h4", "style": {"__ref": "dDlKFOaMtLaY"}, "__type": "ThemeStyle"}, "rtP-z8EIdNZz": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "20px", "font-weight": "600", "letter-spacing": "-0.3px", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "OPFrPFshBord": {"name": "Default \"h5\"", "rs": {"__ref": "rtP-z8EIdNZz"}, "preview": null, "uuid": "QTQ5F7dMgkVK", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "ujvmsGTGdfS2": {"selector": "h5", "style": {"__ref": "OPFrPFshBord"}, "__type": "ThemeStyle"}, "jsenrbcrIujZ": {"values": {"font-family": "Inter", "color": "#000000", "font-size": "16px", "font-weight": "600", "line-height": "1.5"}, "mixins": [], "__type": "RuleSet"}, "beM3utWYy-E-": {"name": "Default \"h6\"", "rs": {"__ref": "jsenrbcrIujZ"}, "preview": null, "uuid": "T06NUwKLTZnE", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "XhSPA_pNx8_Q": {"selector": "h6", "style": {"__ref": "beM3utWYy-E-"}, "__type": "ThemeStyle"}, "S5S0pTsIAuuA": {"values": {"color": "#0070f3"}, "mixins": [], "__type": "RuleSet"}, "5hAFeumFaUcK": {"name": "Default \"a\"", "rs": {"__ref": "S5S0pTsIAuuA"}, "preview": null, "uuid": "iW5NrUamtRvD", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "560KW7U8qiZc": {"selector": "a", "style": {"__ref": "5hAFeumFaUcK"}, "__type": "ThemeStyle"}, "kwQkDmZ6H7vb": {"values": {"color": "#3291ff"}, "mixins": [], "__type": "RuleSet"}, "XtH6VJivGEJu": {"name": "Default \"a:hover\"", "rs": {"__ref": "kwQkDmZ6H7vb"}, "preview": null, "uuid": "C9ClMpm05Wcl", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "yiego1VEyKr5": {"selector": "a:hover", "style": {"__ref": "XtH6VJivGEJu"}, "__type": "ThemeStyle"}, "180nHXSWMS09": {"values": {"border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "3px", "color": "#888888", "padding-left": "10px"}, "mixins": [], "__type": "RuleSet"}, "CWK2AIyco-xv": {"name": "Default \"blockquote\"", "rs": {"__ref": "180nHXSWMS09"}, "preview": null, "uuid": "uuA0YahynX3r", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "0mbuJ7Gawuh9": {"selector": "blockquote", "style": {"__ref": "CWK2AIyco-xv"}, "__type": "ThemeStyle"}, "YArEwnDIXlhe": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "1px", "padding-left": "4px", "padding-right": "4px", "padding-top": "1px"}, "mixins": [], "__type": "RuleSet"}, "8Wofx78h9uft": {"name": "Default \"code\"", "rs": {"__ref": "YArEwnDIXlhe"}, "preview": null, "uuid": "y1K_FBCqxK37", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "DaBC30L114yy": {"selector": "code", "style": {"__ref": "8Wofx78h9uft"}, "__type": "ThemeStyle"}, "Qm9GzgWOEotC": {"values": {"background": "linear-gradient(#f8f8f8, #f8f8f8)", "border-bottom-color": "#dddddd", "border-bottom-style": "solid", "border-bottom-width": "1px", "border-left-color": "#dddddd", "border-left-style": "solid", "border-left-width": "1px", "border-right-color": "#dddddd", "border-right-style": "solid", "border-right-width": "1px", "border-top-color": "#dddddd", "border-top-style": "solid", "border-top-width": "1px", "border-bottom-left-radius": "3px", "border-bottom-right-radius": "3px", "border-top-left-radius": "3px", "border-top-right-radius": "3px", "font-family": "Inconsolata", "padding-bottom": "3px", "padding-left": "6px", "padding-right": "6px", "padding-top": "3px"}, "mixins": [], "__type": "RuleSet"}, "SYSruyQV4oWR": {"name": "Default \"pre\"", "rs": {"__ref": "Qm9GzgWOEotC"}, "preview": null, "uuid": "9B5tJeL7cEbB", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "QllmTb-Yjw3a": {"selector": "pre", "style": {"__ref": "SYSruyQV4oWR"}, "__type": "ThemeStyle"}, "KMyP3LmWa5LW": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "decimal"}, "mixins": [], "__type": "RuleSet"}, "bAfaSnLBrBry": {"name": "Default \"ol\"", "rs": {"__ref": "KMyP3LmWa5LW"}, "preview": null, "uuid": "XG4p5OI4qbZL", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "GAPNM9Cq3zvb": {"selector": "ol", "style": {"__ref": "bAfaSnLBrBry"}, "__type": "ThemeStyle"}, "049y2G5ufII5": {"values": {"display": "flex", "flex-direction": "column", "align-items": "stretch", "justify-content": "flex-start", "list-style-position": "outside", "padding-left": "40px", "position": "relative", "list-style-type": "disc"}, "mixins": [], "__type": "RuleSet"}, "OgRKFMKcFMDN": {"name": "Default \"ul\"", "rs": {"__ref": "049y2G5ufII5"}, "preview": null, "uuid": "y6qQwKEOeXwX", "forTheme": true, "variantedRs": [], "__type": "Mixin"}, "FFt_dbgeEBZI": {"selector": "ul", "style": {"__ref": "OgRKFMKcFMDN"}, "__type": "ThemeStyle"}, "anXok8vgkqbT": {"defaultStyle": {"__ref": "ANyCDUoMvayg"}, "styles": [{"__ref": "Rm9CaUa29Pri"}, {"__ref": "lHj0eDuwd44c"}, {"__ref": "R8ZJwGP-Hv8W"}, {"__ref": "wrUb5HQKDaQi"}, {"__ref": "ujvmsGTGdfS2"}, {"__ref": "XhSPA_pNx8_Q"}, {"__ref": "560KW7U8qiZc"}, {"__ref": "yiego1VEyKr5"}, {"__ref": "0mbuJ7Gawuh9"}, {"__ref": "DaBC30L114yy"}, {"__ref": "QllmTb-Yjw3a"}, {"__ref": "GAPNM9Cq3zvb"}, {"__ref": "FFt_dbgeEBZI"}], "layout": {"__ref": "kaEGEtQonh4o"}, "addItemPrefs": {}, "active": true, "__type": "Theme"}, "uumASTFbvQT0": {"name": "text", "__type": "Text"}, "WeOAS5c8WrdS": {"name": "Screen", "uuid": "xWq1VDJhJyZd", "__type": "Var"}, "gJMYJXJ2ShFH": {"type": {"__ref": "uumASTFbvQT0"}, "variable": {"__ref": "WeOAS5c8WrdS"}, "uuid": "rvcODmhCLMt1", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "GlobalVariantGroupParam"}, "iokAIyYLfZFa": {"type": "global-screen", "param": {"__ref": "gJMYJXJ2ShFH"}, "uuid": "6GOcVbkEb0mZ", "variants": [], "multi": true, "__type": "GlobalVariantGroup"}, "VWKfrp8pJP4G": {"uuid": "5ML6OigcY12Y", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "UWyoYPeWTpf0": {"components": [{"__ref": "s1yoAK1vd9ph"}, {"__ref": "DTGLLa841Ac0"}, {"__ref": "TYUH6CV52DHs"}, {"__ref": "nrI2erZFK7If"}], "arenas": [{"__ref": "2YT8WK20mR68"}], "pageArenas": [{"__ref": "qB_pciXYC6gQ"}], "componentArenas": [{"__ref": "6AS4XiJGpNGs"}], "globalVariantGroups": [{"__ref": "iokAIyYLfZFa"}], "userManagedFonts": [], "globalVariant": {"__ref": "VWKfrp8pJP4G"}, "styleTokens": [], "mixins": [], "themes": [{"__ref": "anXok8vgkqbT"}], "activeTheme": {"__ref": "anXok8vgkqbT"}, "imageAssets": [], "projectDependencies": [], "activeScreenVariantGroup": {"__ref": "iokAIyYLfZFa"}, "flags": {"usePlasmicImg": true, "useLoadingState": true, "defaultInsertable": "plexus"}, "hostLessPackageInfo": null, "globalContexts": [], "splits": [], "defaultComponents": {}, "defaultPageRoleId": null, "pageWrapper": null, "customFunctions": [], "codeLibraries": [], "__type": "Site", "styleTokenOverrides": [], "dataTokens": []}, "2YT8WK20mR68": {"name": "Custom arena 1", "children": [], "__type": "Arena"}, "s1yoAK1vd9ph": {"uuid": "UgyoQZ_epx3c", "name": "hostless-plasmic-head", "params": [{"__ref": "tloZ17yAS6zA"}, {"__ref": "p8nF__XHCE0d"}, {"__ref": "G-TLSBFr27Ek"}, {"__ref": "su9ISTOENXn9"}], "states": [], "tplTree": {"__ref": "aCPus9NdqNRJ"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "W2rtMB3lmNxZ"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "cp6wUk3Xii_v"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "DTGLLa841Ac0": {"uuid": "U50dwmtj3iqU", "name": "plasmic-data-source-fetcher", "params": [{"__ref": "GnuQXLqzIgIb"}, {"__ref": "tDrvnISsQKbQ"}, {"__ref": "mCI2vo-5E9lq"}, {"__ref": "77gnOXBLEWIZ"}, {"__ref": "-V7I77P9SdGa"}], "states": [], "tplTree": {"__ref": "RjCHJZrECpZF"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "SDi-12WN3qq-"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": {"__ref": "a8gyFO5adPsv"}, "type": "code", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": true, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "tloZ17yAS6zA": {"type": {"__ref": "AUVjH-xBykMM"}, "variable": {"__ref": "kJCF2JOsaoOU"}, "uuid": "3Cdfv0nOWHGR", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Title", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "p8nF__XHCE0d": {"type": {"__ref": "LD6wMJnF-ltw"}, "variable": {"__ref": "xWylFFyyBK_C"}, "uuid": "MFWq000hUviu", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Description", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "G-TLSBFr27Ek": {"type": {"__ref": "-Jhr7FmEsuh4"}, "variable": {"__ref": "ZkXZlIIL2xv8"}, "uuid": "OMiSKtsmNs31", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Image", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "su9ISTOENXn9": {"type": {"__ref": "N3bAhlBUdh_J"}, "variable": {"__ref": "FKi566Ha5zn1"}, "uuid": "sy-oCPuDjqFC", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Canonical URL", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "aCPus9NdqNRJ": {"tag": "div", "name": null, "children": [], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ai41E8J1gAIu", "parent": null, "locked": null, "vsettings": [{"__ref": "0xax2LF7opCh"}], "__type": "TplTag"}, "W2rtMB3lmNxZ": {"uuid": "yXcXzGjSfL1S", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "cp6wUk3Xii_v": {"importPath": "@plasmicapp/react-web", "defaultExport": false, "displayName": "<PERSON><PERSON>", "importName": "PlasmicHead", "description": "Set page metadata (HTML <head />) to dynamic values.", "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": false, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "GnuQXLqzIgIb": {"type": {"__ref": "Gyn1rdezvdoT"}, "variable": {"__ref": "nJeXpAFy2nFN"}, "uuid": "y-OmTgIKw_Xl", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Data", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "tDrvnISsQKbQ": {"type": {"__ref": "Fg8bodIwD5Qc"}, "variable": {"__ref": "5JtyQhvSbe77"}, "uuid": "fNLeoRQchnZ-", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": "Variable name", "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "mCI2vo-5E9lq": {"type": {"__ref": "oN_Zm06a7-h4"}, "tplSlot": {"__ref": "ip95Y5njwVii"}, "variable": {"__ref": "Vc_wWmzh2jmb"}, "uuid": "PUcr5uCt3O9v", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": null, "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "SlotParam"}, "77gnOXBLEWIZ": {"type": {"__ref": "gOWpOgJbOW46"}, "variable": {"__ref": "9sxO__e9D6DU"}, "uuid": "CoUQx5pf1KhJ", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "Only fetch in batches of this size; for pagination", "displayName": "Page size", "about": "Only fetch in batches of this size; for pagination", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "-V7I77P9SdGa": {"type": {"__ref": "pMEVIj0sEtlE"}, "variable": {"__ref": "uzGnJRo4wn1-"}, "uuid": "hHExkDKRd2vr", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": null, "propEffect": null, "description": "0-based index of the paginated page to fetch", "displayName": "Page index", "about": "0-based index of the paginated page to fetch", "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "RjCHJZrECpZF": {"tag": "div", "name": null, "children": [{"__ref": "ip95Y5njwVii"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "ENmLghiAX_U5", "parent": null, "locked": null, "vsettings": [{"__ref": "cZhaUDkbvgDS"}], "__type": "TplTag"}, "SDi-12WN3qq-": {"uuid": "KQA46K4a68Gn", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "a8gyFO5adPsv": {"importPath": "@plasmicapp/react-web/lib/data-sources", "defaultExport": false, "displayName": "Data Fetcher", "importName": "<PERSON><PERSON><PERSON>", "description": null, "section": null, "thumbnailUrl": null, "classNameProp": null, "refProp": null, "defaultStyles": null, "defaultDisplay": null, "isHostLess": true, "isContext": false, "isAttachment": false, "providesData": false, "hasRef": false, "isRepeatable": true, "styleSections": false, "helpers": null, "defaultSlotContents": {}, "variants": {}, "__type": "CodeComponentMeta", "refActions": []}, "AUVjH-xBykMM": {"name": "text", "__type": "Text"}, "kJCF2JOsaoOU": {"name": "title", "uuid": "U4OANF6x15_Y", "__type": "Var"}, "LD6wMJnF-ltw": {"name": "text", "__type": "Text"}, "xWylFFyyBK_C": {"name": "description", "uuid": "bgUGow9UKwj5", "__type": "Var"}, "-Jhr7FmEsuh4": {"name": "img", "__type": "Img"}, "ZkXZlIIL2xv8": {"name": "image", "uuid": "fHHrSdeLcuX6", "__type": "Var"}, "N3bAhlBUdh_J": {"name": "text", "__type": "Text"}, "FKi566Ha5zn1": {"name": "canonical", "uuid": "jcavLEDb4dSg", "__type": "Var"}, "0xax2LF7opCh": {"variants": [{"__ref": "W2rtMB3lmNxZ"}], "args": [], "attrs": {}, "rs": {"__ref": "8ewnU3cVHOPr"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Gyn1rdezvdoT": {"name": "any", "__type": "AnyType"}, "nJeXpAFy2nFN": {"name": "dataOp", "uuid": "Xb-uC5tbKEF-", "__type": "Var"}, "Fg8bodIwD5Qc": {"name": "text", "__type": "Text"}, "5JtyQhvSbe77": {"name": "name", "uuid": "_LslHMU2t_CC", "__type": "Var"}, "oN_Zm06a7-h4": {"name": "renderFunc", "params": [{"__ref": "OGRQWuNr_zy9"}], "allowed": [], "allowRootWrapper": null, "__type": "RenderFuncType"}, "Vc_wWmzh2jmb": {"name": "children", "uuid": "KfrikU6PM9zk", "__type": "Var"}, "gOWpOgJbOW46": {"name": "num", "__type": "<PERSON><PERSON>"}, "9sxO__e9D6DU": {"name": "pageSize", "uuid": "MN2idfSi7v0k", "__type": "Var"}, "pMEVIj0sEtlE": {"name": "num", "__type": "<PERSON><PERSON>"}, "uzGnJRo4wn1-": {"name": "pageIndex", "uuid": "vx0yvcgWSEZf", "__type": "Var"}, "ip95Y5njwVii": {"param": {"__ref": "mCI2vo-5E9lq"}, "defaultContents": [], "uuid": "7C3RiODf4Je6", "parent": {"__ref": "RjCHJZrECpZF"}, "locked": null, "vsettings": [{"__ref": "StxOH1sqlt4a"}], "__type": "TplSlot"}, "cZhaUDkbvgDS": {"variants": [{"__ref": "SDi-12WN3qq-"}], "args": [], "attrs": {}, "rs": {"__ref": "VC8tRPuzTOqO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "8ewnU3cVHOPr": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "OGRQWuNr_zy9": {"name": "arg", "argName": "$queries", "displayName": null, "type": {"__ref": "MYmwLApBLpuV"}, "__type": "ArgType"}, "StxOH1sqlt4a": {"variants": [{"__ref": "SDi-12WN3qq-"}], "args": [], "attrs": {}, "rs": {"__ref": "DL8Ut9sHVp6n"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "VC8tRPuzTOqO": {"values": {"display": "flex", "flex-direction": "row"}, "mixins": [], "__type": "RuleSet"}, "MYmwLApBLpuV": {"name": "any", "__type": "AnyType"}, "DL8Ut9sHVp6n": {"values": {}, "mixins": [], "__type": "RuleSet"}, "TYUH6CV52DHs": {"uuid": "c9CH1GIBWs2V", "name": "ComponentWithImagesAndProps", "params": [{"__ref": "8JlU2el0LjsH"}], "states": [], "tplTree": {"__ref": "rydumTPZVr6_"}, "editableByContentEditor": false, "hiddenFromContentEditor": false, "variants": [{"__ref": "82GtkoOy4xpN"}], "variantGroups": [], "pageMeta": null, "codeComponentMeta": null, "type": "plain", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "6AS4XiJGpNGs": {"component": {"__ref": "TYUH6CV52DHs"}, "matrix": {"__ref": "4Qw1gA6icbRY"}, "customMatrix": {"__ref": "GQwt95HdOIFA"}, "__type": "ComponentArena"}, "rydumTPZVr6_": {"tag": "div", "name": null, "children": [{"__ref": "A4_dUe1IakuU"}, {"__ref": "CHz-wKmcD8MD"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "KwOpDcVouyBz", "parent": null, "locked": null, "vsettings": [{"__ref": "WLN5wJe0WrzT"}], "__type": "TplTag"}, "82GtkoOy4xpN": {"uuid": "1qPAJCXEDsg6", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "4Qw1gA6icbRY": {"rows": [{"__ref": "RcO_cIvWxmIw"}], "__type": "ArenaFrameGrid"}, "GQwt95HdOIFA": {"rows": [{"__ref": "rP0wcCS1PEF1"}], "__type": "ArenaFrameGrid"}, "WLN5wJe0WrzT": {"variants": [{"__ref": "82GtkoOy4xpN"}], "args": [], "attrs": {}, "rs": {"__ref": "EygyQ4g2eQEF"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "RcO_cIvWxmIw": {"cols": [{"__ref": "29nOCijOUwDS"}], "rowKey": null, "__type": "ArenaFrameRow"}, "rP0wcCS1PEF1": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "EygyQ4g2eQEF": {"values": {"display": "flex", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch"}, "mixins": [], "__type": "RuleSet"}, "29nOCijOUwDS": {"frame": {"__ref": "ywyJkR6PANhT"}, "cellKey": {"__ref": "82GtkoOy4xpN"}, "__type": "ArenaFrameCell"}, "ywyJkR6PANhT": {"uuid": "JKchkTzYve2h", "width": 1180, "height": 1113, "container": {"__ref": "JVg_JUxyh3hx"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "82GtkoOy4xpN"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "centered", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "JVg_JUxyh3hx": {"name": null, "component": {"__ref": "TYUH6CV52DHs"}, "uuid": "tjYeJSUkeqa2", "parent": null, "locked": null, "vsettings": [{"__ref": "RCDqDwws_LJU"}], "__type": "TplComponent"}, "RCDqDwws_LJU": {"variants": [{"__ref": "VWKfrp8pJP4G"}], "args": [], "attrs": {}, "rs": {"__ref": "_IFABeSWopUd"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "_IFABeSWopUd": {"values": {}, "mixins": [], "__type": "RuleSet"}, "A4_dUe1IakuU": {"tag": "img", "name": null, "children": [], "type": "image", "codeGenType": null, "columnsSetting": null, "uuid": "JZbIr-IoFsZU", "parent": {"__ref": "rydumTPZVr6_"}, "locked": null, "vsettings": [{"__ref": "Zedue402A2Ij"}], "__type": "TplTag"}, "Zedue402A2Ij": {"variants": [{"__ref": "82GtkoOy4xpN"}], "args": [], "attrs": {"loading": {"__ref": "XZlsoHeC-Ib3"}, "src": {"__ref": "HXIPFIuQkPuP"}}, "rs": {"__ref": "eNBwonLqVgGY"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "XZlsoHeC-Ib3": {"code": "\"lazy\"", "fallback": null, "__type": "CustomCode"}, "eNBwonLqVgGY": {"values": {"object-fit": "cover", "max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "HXIPFIuQkPuP": {"code": "\"https://i.natgeofe.com/n/135b255f-f37f-4b23-b0c3-2f0a4c1aa065/1106.jpg?w=2560&h=1922\"", "fallback": null, "__type": "CustomCode"}, "8JlU2el0LjsH": {"type": {"__ref": "Gt9ThnNPyfb6"}, "variable": {"__ref": "l3jbXByePOUq"}, "uuid": "IgNxzkSI7070", "enumValues": [], "origin": null, "exportType": "External", "defaultExpr": null, "previewExpr": {"__ref": "sKTzOeByh69c"}, "propEffect": null, "description": "metaProp", "displayName": null, "about": null, "isRepeated": null, "isMainContentSlot": false, "required": false, "mergeWithParent": false, "isLocalizable": false, "__type": "PropParam", "advanced": false}, "l3jbXByePOUq": {"name": "title", "uuid": "jmUTDnJZGtlc", "__type": "Var"}, "Gt9ThnNPyfb6": {"name": "text", "__type": "Text"}, "sKTzOeByh69c": {"code": "\"Check this title\"", "fallback": null, "__type": "CustomCode"}, "CHz-wKmcD8MD": {"tag": "h2", "name": null, "children": [], "type": "text", "codeGenType": null, "columnsSetting": null, "uuid": "DLNeTD4tQJ3E", "parent": {"__ref": "rydumTPZVr6_"}, "locked": null, "vsettings": [{"__ref": "Nh-zxTY4BAEn"}], "__type": "TplTag"}, "Nh-zxTY4BAEn": {"variants": [{"__ref": "82GtkoOy4xpN"}], "args": [], "attrs": {}, "rs": {"__ref": "IdLLm0nR4YKx"}, "dataCond": null, "dataRep": null, "text": {"__ref": "o-pjnWidsdQa"}, "columnsConfig": null, "__type": "VariantSetting"}, "IdLLm0nR4YKx": {"values": {"position": "relative", "color": "#C51E1E"}, "mixins": [], "__type": "RuleSet"}, "o-pjnWidsdQa": {"expr": {"__ref": "HdxglJeJUvPZ"}, "html": false, "__type": "ExprText"}, "HdxglJeJUvPZ": {"path": ["$props", "title"], "fallback": {"__ref": "kXymtJ6ct3G3"}, "__type": "ObjectPath"}, "kXymtJ6ct3G3": {"code": "\"\"", "fallback": null, "__type": "CustomCode"}, "nrI2erZFK7If": {"uuid": "DAWbg6PFHq-N", "name": "Homepage", "params": [], "states": [], "tplTree": {"__ref": "9JGagpwpCq6G"}, "editableByContentEditor": true, "hiddenFromContentEditor": false, "variants": [{"__ref": "wRrQmT4aJlHV"}], "variantGroups": [], "pageMeta": {"__ref": "NqqPwgrDJMSh"}, "codeComponentMeta": null, "type": "page", "subComps": [], "superComp": null, "plumeInfo": null, "templateInfo": null, "metadata": {}, "dataQueries": [], "serverQueries": [], "figmaMappings": [], "alwaysAutoName": false, "trapsFocus": false, "__type": "Component", "updatedAt": null}, "qB_pciXYC6gQ": {"component": {"__ref": "nrI2erZFK7If"}, "matrix": {"__ref": "AzrzGreDRzWs"}, "customMatrix": {"__ref": "0amSEEbCM_LS"}, "__type": "PageArena"}, "9JGagpwpCq6G": {"tag": "div", "name": null, "children": [{"__ref": "XVk0WK0MbJwV"}], "type": "other", "codeGenType": null, "columnsSetting": null, "uuid": "OZ191iGbkJWY", "parent": null, "locked": null, "vsettings": [{"__ref": "8cvZNRFnmaTZ"}], "__type": "TplTag"}, "wRrQmT4aJlHV": {"uuid": "czKEPB8EjQRD", "name": "base", "selectors": null, "codeComponentName": null, "codeComponentVariantKeys": null, "parent": null, "mediaQuery": null, "description": null, "forTpl": null, "__type": "<PERSON><PERSON><PERSON>"}, "NqqPwgrDJMSh": {"path": "/home-page", "params": {}, "query": {}, "title": null, "description": "", "canonical": null, "roleId": null, "openGraphImage": null, "__type": "PageMeta"}, "AzrzGreDRzWs": {"rows": [{"__ref": "Qh0v1anzYu4x"}], "__type": "ArenaFrameGrid"}, "0amSEEbCM_LS": {"rows": [{"__ref": "6InUJjQEKaup"}], "__type": "ArenaFrameGrid"}, "8cvZNRFnmaTZ": {"variants": [{"__ref": "wRrQmT4aJlHV"}], "args": [], "attrs": {}, "rs": {"__ref": "Rp99DBMSv-az"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "Qh0v1anzYu4x": {"cols": [{"__ref": "Ja6PSs-B3dAv"}, {"__ref": "gYQKLpJdenQj"}], "rowKey": {"__ref": "wRrQmT4aJlHV"}, "__type": "ArenaFrameRow"}, "6InUJjQEKaup": {"cols": [], "rowKey": null, "__type": "ArenaFrameRow"}, "Rp99DBMSv-az": {"values": {"display": "plasmic-content-layout", "flex-direction": "column", "position": "relative", "width": "stretch", "height": "stretch", "align-content": "flex-start", "justify-items": "center"}, "mixins": [], "__type": "RuleSet"}, "Ja6PSs-B3dAv": {"frame": {"__ref": "jsH-8rBImCoP"}, "cellKey": null, "__type": "ArenaFrameCell"}, "gYQKLpJdenQj": {"frame": {"__ref": "vn_oDVa0xPRu"}, "cellKey": null, "__type": "ArenaFrameCell"}, "jsH-8rBImCoP": {"uuid": "p2EX3S33i1s4", "width": 1366, "height": 768, "container": {"__ref": "6kc9y6VYhU6F"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "wRrQmT4aJlHV"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "vn_oDVa0xPRu": {"uuid": "7GDxX96MtNFH", "width": 414, "height": 736, "container": {"__ref": "Suzy5EuCstZq"}, "lang": "English", "pinnedVariants": {}, "targetVariants": [{"__ref": "wRrQmT4aJlHV"}], "pinnedGlobalVariants": {}, "targetGlobalVariants": [], "viewMode": "stretch", "bgColor": null, "name": "", "top": null, "left": null, "__type": "ArenaFrame"}, "6kc9y6VYhU6F": {"name": null, "component": {"__ref": "nrI2erZFK7If"}, "uuid": "nA7LKib31S1v", "parent": null, "locked": null, "vsettings": [{"__ref": "mICRkt7LIhMX"}], "__type": "TplComponent"}, "Suzy5EuCstZq": {"name": null, "component": {"__ref": "nrI2erZFK7If"}, "uuid": "-t76S7OX0VUI", "parent": null, "locked": null, "vsettings": [{"__ref": "CHt6ozMyvEdF"}], "__type": "TplComponent"}, "mICRkt7LIhMX": {"variants": [{"__ref": "VWKfrp8pJP4G"}], "args": [], "attrs": {}, "rs": {"__ref": "2U2tNV7z4KEs"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "CHt6ozMyvEdF": {"variants": [{"__ref": "VWKfrp8pJP4G"}], "args": [], "attrs": {}, "rs": {"__ref": "EhKte0mfUWcO"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "2U2tNV7z4KEs": {"values": {}, "mixins": [], "__type": "RuleSet"}, "EhKte0mfUWcO": {"values": {}, "mixins": [], "__type": "RuleSet"}, "XVk0WK0MbJwV": {"name": "option0", "component": {"__ref": "TYUH6CV52DHs"}, "uuid": "6IBRMXWo7Jpt", "parent": {"__ref": "9JGagpwpCq6G"}, "locked": null, "vsettings": [{"__ref": "Szpjuqn461Tn"}], "__type": "TplComponent"}, "Szpjuqn461Tn": {"variants": [{"__ref": "wRrQmT4aJlHV"}], "args": [{"__ref": "XfHb21t1HeIr"}], "attrs": {}, "rs": {"__ref": "NrP80jIj-SRJ"}, "dataCond": null, "dataRep": null, "text": null, "columnsConfig": null, "__type": "VariantSetting"}, "NrP80jIj-SRJ": {"values": {"max-width": "100%"}, "mixins": [], "__type": "RuleSet"}, "XfHb21t1HeIr": {"param": {"__ref": "8JlU2el0LjsH"}, "expr": {"__ref": "a<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "__type": "Arg"}, "aIMakjaaACji": {"text": ["This is the homepage prop"], "__type": "TemplatedString"}}, "deps": [], "version": "251-add-data-tokens"}]]