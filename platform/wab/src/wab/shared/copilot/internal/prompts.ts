import { assert, maybe, swallow } from "@/wab/shared/common";
import { fixJson } from "@/wab/shared/copilot/internal/fix-json";
import {
  CopilotCodeChainProps,
  CopilotSqlCodeChainProps,
  CreateChatCompletionRequest,
  humanJson,
  Issue,
  ModelInteraction,
  WholeChatCompletionResponse,
} from "@/wab/shared/copilot/prompt-utils";
import { DataSourceSchema } from "@plasmicapp/data-sources";

export function getResponseContent(response: WholeChatCompletionResponse) {
  return response.choices[0].message?.content;
}

export function extractJsonFromString(content: string) {
  for (const reg of [
    /\s*Fixed JSON:\s*```(?:json|)(?:\n|)([\s\S]*)```\s*/,
    // Sometimes model has trouble generating the ending ```
    /\s*Fixed JSON:\s*```(?:json|)(?:\n|)([\s\S]*)$/,
    /\s*```(?:json|)(?:\n|)([\s\S]*)```\s*/,
    /\s*```(?:json|)(?:\n|)([\s\S]*)$/,
  ]) {
    const matched = content.match(reg)?.[1];
    if (matched) {
      try {
        return JSON.parse(matched);
      } catch {
        try {
          // try it with fixJson
          return JSON.parse(fixJson(matched));
        } catch {
          // invalid json; try the next regexp
        }
      }
    }
  }
  // give up :-/
  return undefined;
}

export function getJsonResponse(response: WholeChatCompletionResponse) {
  const content = getResponseContent(response);
  if (!content) return undefined;
  return extractJsonFromString(content);
}

export function showResponse(response: WholeChatCompletionResponse) {
  const outputs: string[] = [];
  const softLog = (x: string, color?: string) => {
    if (color) {
      console.log(`%c${x}`, { color });
    } else {
      console.log(x);
    }
    outputs.push(x);
  };
  const answer = getJsonResponse(response);
  const rawMsg = getResponseContent(response) ?? "";
  softLog(
    "\n=================================raw output====================================\n",
    "red"
  );
  softLog(rawMsg);
  if (answer) {
    softLog(
      "\n================================extracted=====================================\n",
      "red"
    );
    softLog(humanJson(answer), "green");
  }
  softLog(JSON.stringify(response.usage));
  return outputs.join("\n");
}

export async function createAndRunCopilotCodeChain({
  currentCode,
  data,
  context,
  executeRequest,
  goal,
}: CopilotCodeChainProps) {
  assert(
    swallow(() => JSON.stringify(data)) != null,
    `Expected data to be stringify-able`
  );
  return executeRequest({
    model: "gpt-3.5-turbo",
    temperature: 0,
    messages: [
      {
        role: "user",
        content: `
You are helping a non-technical user write expressions in JavaScript.
The user will inform you their goal, along with the available Javascript variables, the current code (if any) and possibly a context where this code expression is being used.
You must output the required code to achieve that goal, which might just be a sub-expression of a bigger code.
Notice the user might refer to some states in \`$state\` as "variant"s.
The code will be evaluated and displayed to the user, so prefer simply writing javascript expressions rather than console logs even if the user asks you to display data.
Do not respond with anything besides the valid Javascript code to achieve the goal.

Let's work through a few examples:

---

Available variables:
\`\`\`
const currentItem = {"id":"123"};
const currentIndex = 1;
\`\`\`
Expression context: The text content to be displayed
Current code: None
Goal: "Display the current item id"
Output code: currentItem.id

---

Available variables:
\`\`\`
const $state = {};
const $queries = {"posts":{"data":[{"id":"1188","created_at":"2023-06-28T16:21:12.000Z"},{"id":"3722","created_at":"2023-06-28T16:21:12.000Z"},{"id":"1677","created_at":"2023-06-28T16:21:12.000Z"},{"id":"8117","created_at":"2023-06-28T16:21:12.000Z"}]},"isLoading":false}};
\`\`\`
Current code:
\`\`\`
$queries.posts.data.sort((a,b) => new Date(b.created_at) - new Date(a.created_at))[0].id
\`\`\`
Goal: "Make the code to get the most recently created post more efficiently"
Output code: $queries.posts.data.reduce((acc, curr) => new Date(curr.created_at) > new Date(acc.created_at) ? curr : acc).id

---

Available variables:
\`\`\`
const $props = {};
const $ctx = {"params":{},"query":{}};
const $state = {};
const $queries = {"users":{"data":[{"id":"111","created_at":"2023-06-15T19:45:01.592Z","first name":"Yang","email":"<EMAIL>","last name":"Zhang","date of birth":"1990-01-01T02:00:00.000Z"},{"id":"112","created_at":"2023-06-15T19:45:47.037Z","first name":"Chung","email":"<EMAIL>","last name":"Wu","date of birth":"1992-12-12T03:00:00.000Z"},{"id":"113","created_at":"2023-06-15T19:46:21.372Z","first name":"Victor","email":"<EMAIL>","last name":"Agnez Lima","date of birth":"1997-11-11T03:00:00.000Z"}],"schema":{"id":"public\\".\\"User","fields":[{"id":"id","type":"number","readOnly":false},{"id":"created_at","type":"date","readOnly":false},{"id":"first name","type":"string","readOnly":false},{"id":"email","type":"string","readOnly":false},{"id":"last name","type":"string","readOnly":false},{"id":"date of birth","type":"date","readOnly":false}]},"isLoading":false}};
const currentItem = {"id":"111","created_at":"2023-06-15T19:45:01.592Z","first name":"Yang","email":"<EMAIL>","last name":"Zhang","date of birth":"1990-01-01T02:00:00.000Z"};
const currentIndex = 0;
\`\`\`
Expression context: The text content to be displayed
Current code: None
Goal: "Display the full name and age of the current user"
Output code: (() => {
  const fullName = \`\${currentItem["first name"]} \${currentItem["last name"]}\`;
  const birthDate = new Date(currentItem["date of birth"]);
  const ageDiffMs = Date.now() - birthDate.getTime();
  const ageDate = new Date(ageDiffMs);
  const age = Math.abs(ageDate.getUTCFullYear() - 1970);
  return \`\${fullName}, \${age} years\`
})()

---

Available variables:
\`\`\`
const $props = {};
const $ctx = {"params":{"userId":"116"},"query":{}};
const $state = {};
const $queries = {"users":{"data":[{"id":"112","first name":"Chung","date of birth":"1992-12-12T03:00:00.000Z"},{"id":"116","first name":"Victor","date of birth":"1997-11-11T03:00:00.000Z"}, "... (long array)"],"isLoading":false}};
\`\`\`
Current code: None
Goal: "User date of birth for in the format dd/mm/yyyy"
Output code: $queries.users.data.find(user => user.id===$ctx.params.userId)["date of birth"].split('T')[0].split('-').reverse().join('/')

---

Available variables:
\`\`\`
const event = {};
const $props = {};
const $ctx = {"params":{},"query":{}};
const $state = {"counter":0};
const $queries = {};
\`\`\`
Current code: None
Goal: "Increment the counter every 2 seconds"
Output code:setInterval(() => {$state.counter++;}, 2000)

---

Available variables:
\`\`\`
const $props = {};
\`\`\`
Expression context: Condition to decide whether the selected element should be visible
Current code:
\`\`\`
const today = new Date().getDay();
const isMondayOrWednesday = today === 1 || today === 3;
isMondayOrWednesday
\`\`\`
Goal: "Fix it"
Output code: (() => {
  const today = new Date().getDay();
  const isMondayOrWednesday = today === 1 || today === 3;
  return isMondayOrWednesday;
})()

---

Available variables:
\`\`\`
const $props = {};
const $ctx = {};
\`\`\`
Current code: None
Goal: "Activate the dark variant"
Output code: $state.dark = true

---

Available variables:
\`\`\`
const $props = {};
const $ctx = {"params":{"slug":"value"},"query":{}};
\`\`\`
Current code: None
Goal: "Toggle the pretty variant"
Output code: $state.pretty = !$state.pretty

---

Available variables:
\`\`\`
const $props = {};
const $ctx = {};
const $state = {"counter":0};
\`\`\`
Current code: None
Goal: "if the counter reaches 10, change the color to the blue variant, otherwise make it green"
Output code: $state.color = ($state.counter === 10) ? "blue" : "green"

---

Now for the real prompt! Do not output anything besides the valid javascript code or you will DIE.
Available variables:
\`\`\`
${Object.keys(data)
  .map((key) => `const ${key} = ${JSON.stringify(data[key])};`)
  .join("\n")}
\`\`\`
${
  context
    ? `Expression context: ${context}
`
    : ""
}Current code: ${
          currentCode?.trim() && currentCode.trim() !== "undefined"
            ? `
\`\`\`
${currentCode}
\`\`\``
            : "None"
        }
Goal: ${JSON.stringify(goal)}
Output code:
          `.trim(),
      },
    ],
  });
}

export async function createAndRunCopilotSqlCodeChain({
  currentCode,
  data,
  executeRequest,
  goal,
  dataSourceSchema,
}: CopilotSqlCodeChainProps) {
  assert(
    swallow(() => JSON.stringify(data)) != null,
    `Expected data to be stringify-able`
  );

  function spawnPrompt(
    mkRequest: () => Promise<CreateChatCompletionRequest>,
    validatorRequest?: (
      previousReq: CreateChatCompletionRequest,
      res: WholeChatCompletionResponse
    ) => Promise<CreateChatCompletionRequest | undefined>
  ): ModelInteraction {
    const request = mkRequest();
    const response = request.then(executeRequest);
    const fixupRequest = (async () =>
      validatorRequest &&
      response.then((res) =>
        request.then((req) => validatorRequest(req, res))
      ))();
    const fixupResponse = fixupRequest.then((r) =>
      r ? executeRequest(r) : response
    );
    return {
      request: request,
      intermediateRequests: fixupRequest.then(async (r) =>
        r
          ? [
              {
                previousResponse: await response,
                followUpRequest: r,
              },
            ]
          : []
      ),
      response: fixupResponse,
    };
  }

  const tables = spawnPrompt(
    async () => prepSqlTablesPrompt(goal, dataSourceSchema),
    async (previousRequest, response) =>
      validateSqlTablesPrompt(dataSourceSchema, previousRequest, response)
  );

  const tablesResponse = await tables.response;
  const removeQuotes = (tableName: string) => tableName.split(`"."`).join(".");
  const relevantTablesNames = maybe(
    swallow(() =>
      maybe(getResponseContent(tablesResponse), (s) => JSON.parse(s))
    ),
    (tablesArr) =>
      Array.isArray(tablesArr) ? new Set<string>(tablesArr) : undefined
  );

  const filteredTables = dataSourceSchema.tables.filter((t) =>
    relevantTablesNames ? relevantTablesNames.has(removeQuotes(t.id)) : true
  );

  return {
    tables,
    composed: await executeRequest({
      model: "gpt-3.5-turbo",
      temperature: 0,
      messages: [
        {
          role: "user",
          content: `
You're helping a non-technical user to write a Postgres SQL statement in a low-code app builder.
If needed, The SQL code can be interpolated with JavaScript code.
You will be provided with the available Javascript variables, the existing code to build the SQL string (if any) and the available tables and columns.
You must output the SQL string to achieve the user goal, possibly interpolated with the double curly braces syntax (i.e., \`{{ expr }}\`) for using JavaScript expressions.
Do not respond with anything besides valid code in the requested syntax to achieve the goal.

Let's work through a few examples:

---

Available variables:
\`\`\`
const $props = {};
const $ctx = {"params":{"chartId":"123"},"query":{}};
\`\`\`
Database Tables:
\`\`\`
{"tables":[{"name":"\\"public\\".\\"ChartItem\\"","columns":["id","quantity","Chart ID","productId"]},{"name":"\\"public\\".\\"Product\\"","columns":["id","price","name"]}]}
\`\`\`
Current code: None
Goal: "The total chart price"
Output code: SELECT SUM(p.price * ci.quantity) AS total_price
FROM public."ChartItem" ci
JOIN public."Product" p ON ci."productId" = p.id
WHERE ci."Chart ID" = {{ $ctx.params.chartId }}

---

Available variables:
\`\`\`
const $props = {};
const $state = {"department":{"value":"Marketing"},"departmentsData":[{"name":"Sales","code":"SAL"},{"name":"Engineering","code":"ENG"},{"name":"Marketing","code":"MKT"},"... (long array"]};
\`\`\`
Database Tables:
\`\`\`
{"tables":[{"name":"\\"public\\".\\"Employees\\"","columns":["id","first_name","last_name","department_code"]}]}
\`\`\`
Current code: None
Goal: "all workers from the selected department"
Output code: SELECT *
FROM public."Employees"
WHERE "department_code" = {{ $state.departmentsData.find((department) => department.name === $state.department.value).code }}

---

Available variables:
\`\`\`
const $props = {};
\`\`\`
Database Tables:
\`\`\`
{"tables":[{"name":"\\"public\\".\\"Tasks\\"","columns":["id","name","archivedAt"]}]}
\`\`\`
Current code:
\`\`\`
SELECT COUNT(*) AS num_tasks FROM public.Tasks WHERE archivedAt IS NULL
\`\`\`
Goal: "Fix it"
Output code: SELECT COUNT(*) AS num_tasks FROM public."Tasks" WHERE "archivedAt" IS NULL

---

Now for the real prompt! Do not output anything besides the described code or you will DIE.

Available variables:
\`\`\`
${Object.keys(data)
  .map((key) => `const ${key} = ${JSON.stringify(data[key])};`)
  .join("\n")}
\`\`\`
Database Tables:
\`\`\`
${JSON.stringify({
  tables: filteredTables.map((table) => ({
    name: `"${table.id}"`,
    columns: table.fields.map((f) => f.id),
  })),
})}
\`\`\`
Current code: ${
            currentCode?.trim() && currentCode.trim() !== "undefined"
              ? `
\`\`\`
${currentCode}
\`\`\``
              : "None"
          }
Goal: ${JSON.stringify(goal)}
Output code:
`.trim(),
        },
      ],
    }),
  };
}

export function prepSqlTablesPrompt(
  goal: string,
  dataSourceSchema: DataSourceSchema
): CreateChatCompletionRequest {
  // GPT is really bad at escaping quotes inside JSON string literals.
  const removeQuotes = (tableName: string) => tableName.split(`"."`).join(".");
  return {
    model: "gpt-3.5-turbo",
    temperature: 0.1,
    max_tokens: 2000,
    messages: [
      {
        role: "system",
        content: `
The user will describe a goal they are trying to accomplish while building a SQL query in a low-code app builder.

They will also provide a JSON array with all the postgres table names available in the database.

Generate a JSON array filtering the tables that are likely relevant to accomplish the goal.
Include every table that could be possibly be relevant to achieving such goal.
Only discard tables completely unrelated to the goal.
Only include tables that exist.
Don't output a table that was not listed.
Don't modify the table names in any way (don't upper case, don't lower case, don't add or remove the schema or special characters, etc).

Let's work through a few examples:

---

Available tables:
\`\`\`
["public.Books", "public.Authors", "public.Publishers", "public.Genres", "public.Reviews"]
\`\`\`
Goal: "Books written by J.K. Rowling."
Relevant tables: ["public.Books", "public.Authors"]

---

Available tables:
\`\`\`
["public.Customer", "public.Order", "public.OrderItem", "public.Product", "public.Employee"]
\`\`\`
Goal: "Number of products bought by the user"
Relevant tables: ["public.Customer", "public.Order", "public.Item", "public.Product"]

---

Available tables:
\`\`\`
["company.Employee", "company.Department", "payroll.Salary_Details", "hr.Employee_Title"]
\`\`\`
Goal: "current salaries of employees in the Sales department"
Relevant tables: ["company.Employees", "company.Departments", "payroll.Salary_Details"]

---

Available tables:
\`\`\`
["pgsodium.key","vault.secrets","public.ChartItem","public.Product","public.Task","public.Test","public.User","storage.buckets","storage.migrations"]
\`\`\`
Goal: "Unarchived tasks from the user"
Relevant tables: ["public.Task","public.User"]

---

Now for the real prompt! Do not respond with anything besides the JSON.

Available tables:
\`\`\`
${JSON.stringify(dataSourceSchema.tables.map((t) => removeQuotes(t.id)))}
\`\`\`
Goal: ${JSON.stringify(goal)}
Relevant tables:
          `.trim(),
      },
    ],
  };
}

function validateSqlTablesPrompt(
  dataSourceSchema: DataSourceSchema,
  previousRequest: CreateChatCompletionRequest,
  response: WholeChatCompletionResponse
): CreateChatCompletionRequest | undefined {
  const removeQuotes = (tableName: string) => tableName.split(`"."`).join(".");
  const filteredTables = swallow(() =>
    maybe(getResponseContent(response), (s) => JSON.parse(s))
  );
  const issues: Issue[] = [];
  if (!filteredTables || !Array.isArray(filteredTables)) {
    issues.push({
      message: `Could not parse the JSON. Make sure to output a valid JSON Array of strings containing the table names (a subset of the available tables).`,
    });
  } else {
    filteredTables.forEach((table: string) => {
      if (!dataSourceSchema.tables.find((t) => removeQuotes(t.id) === table)) {
        issues.push({
          message: `There's no table named: ${table}`,
        });
      }
    });
  }
  if (issues.length === 0) {
    return undefined;
  }
  return {
    model: "gpt-3.5-turbo",
    temperature: 0.1,
    max_tokens: 100,
    messages: [
      ...previousRequest.messages,
      response.choices[0].message!,
      {
        role: "user",
        content: `
The previous response was invalid due to the following issues.
You must output a JSON array with the names of the relevant database tables to achieve the user goal.
Fix all the issues or you will DIE.
Issues:
            ${issues.map((issue) => `- ${issue.message}`).join("\n")}
        `.trim(),
      },
    ],
  };
}
