import { failable, FailableArgParams, IFailable } from "ts-failable";

export interface ChangeCtx {
  change<E = never>(
    f: (args: FailableArgParams<void, E>) => IFailable<void, E>
  ): Promise<IFailable<void, E>>;
}

export const NOOP_CHANGE_CTX: ChangeCtx = {
  async change<E = never>(
    f: (args: FailableArgParams<void, E>) => IFailable<void, E>
  ) {
    return failable<void, E>((args) => f(args));
  },
};
