// We want to keep this language simple for the LLM.
// It also needs to be self-contained.
// The LLM will be given everything between the BEGIN/END comments.

// But `export` and `Toy` will be removed.

// BEGIN TYPES

export type ToyType =
  | "string"
  | "number"
  | "boolean"
  | "object"
  | "datetime"
  /** For children */
  | "slot"
  | "any"
  | { type: "ref"; table: string }
  | { type: "array"; of: ToyType };

// Expressions

/** Strings can have "{{ JS expressions }}" which are reactive. */
export type ToyExpr = ToyElement | ToyElement[] | ToyAction[] | any;

// Elements (instances of components)

type ElementName = string;
type SlotName = string;
type PropName = string;

export type ToyElement =
  /** Shorthand for Text */
  | string
  | {
      component: string;
      name: ElementName;
      visibility?: ToyExpr;
      props: Record<PropName, ToyExpr>;
    }
  | { slot: SlotName };

// Data source schemas

type FieldName = string;
type TableName = string;
type DataSourceName = string;

// Data source operations

// BEGIN SNIP
// type Filter = {
//   field: FieldName;
//   op: string;
//   expr: ToyExpr;
// };
//
// interface SimpleOp {
//   opType: "select" | "insert" | "update" | "delete";
//   dataSource: DataSourceName;
//   table: TableName;
//   /** For select/update/delete */
//   filters?: Filter[];
//   /** For insert/update */
//   writes?: Record<string, ToyExpr>;
// }
//
// interface SqlOp {
//   opType: "sql";
//   dataSource: DataSourceName;
//   code: string;
// }
//
// export type ToyDataSourceOp = SimpleOp | SqlOp;
// END SNIP

interface ToySqlOp {
  opType: "sql";
  dataSource: DataSourceName;
  /** Can include {{ JS expressions }} */
  code: string;
}

export interface ToyHttpOp {
  opType: "http";
  dataSource: DataSourceName;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  /** Like "/api/users/{{ userId }}" */
  path: ToyExpr;
  /** Query params */
  params: Record<string, ToyExpr>;
  headers: Record<string, ToyExpr>;
  /** Not for GET */
  body?: ToyExpr;
}

export type ToyDataSourceOp = ToySqlOp | ToyHttpOp;

// Actions

interface NavTo {
  action: "NavTo";
  path: string;
}

interface SetState {
  action: "SetState";
  name: string;
  expr: ToyExpr;
}

/** Actions usually run insert/update/delete. */
interface RunDataSourceOp {
  action: "RunDataSourceOp";
  op: ToyDataSourceOp;
}

export type ToyAction = NavTo | SetState | RunDataSourceOp;

// Commands

/** Add elements to a slot prop. */
export type InsertElements = {
  cmd: "InsertElements";
  location: "before" | "after" | "inside";
  of: ElementName;
  /** Defaults to "children" slot. */
  prop?: PropName;
  /** Elements to insert. New ToyElements will have names that start with "*", like "*button" */
  elements: ToyElement[];
};

export type MoveElements = {
  cmd: "MoveElements";
  elementNames: ElementName[];
  location: "before" | "after" | "inside";
  of: ElementName;
  /** Defaults to "children" slot. */
  prop?: PropName;
};

type RemoveElement = {
  cmd: "RemoveElement";
  element: ElementName;
};

/** Use InsertElements or AddAction instead for props of type slot or actions. */
type SetProp = {
  cmd: "SetProp";
  element: ElementName;
  prop: string;
  expr: ToyExpr | undefined;
};

type AddQuery = {
  cmd: "AddQuery";
  name: string;
  /** Queries are always select operations */
  query: ToyDataSourceOp;
};

type AddState = {
  cmd: "AddState";
  name: string;
  defaultValue: any;
};

/** Add an action to a prop of type `actions`. */
type AddAction = {
  cmd: "AddAction";
  element: ElementName;
  prop: string;
  action: ToyAction;
};

export type AddPage = {
  cmd: "AddPage";
  path: string;
  $queries: Record<string, ToyDataSourceOp>;
  $state: string[];
  tree: ToyElement[];
};

export type Command =
  | InsertElements
  | MoveElements
  | RemoveElement
  | SetProp
  | AddQuery
  | AddState
  | AddAction
  | AddPage;

// END TYPES

export type CommandsStruct = { commands: Command[] };
