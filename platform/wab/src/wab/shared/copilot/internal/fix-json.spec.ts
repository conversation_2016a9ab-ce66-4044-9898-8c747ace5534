import { fixJson } from "@/wab/shared/copilot/internal/fix-json";

describe("fixJson", () => {
  it("returns the correct closing brackets for a partial JSON string", () => {
    const bad =
      '{"name": "<PERSON> \\"Doe\\"","age": 30, "hobbies": ["reading [books] {{"';
    const good = fixJson(bad);
    expect(good).toBe(bad + "]}");
  });
  it("returns the correct closing brackets for an overflow JSON string", () => {
    const bad = `{"commands":[{"cmd":"AddPage","path":"/orders","$queries":{"orders":{"opType":"sql","dataSource":"pg","code":"select * from orders"}},"$state":[],"tree":[{"component":"Table","name":"table1","props":{"data":"{{$queries.orders.data}}","bordered":true,"isSelectable":"single","rowKey":"id","selectedRowKeys":"{{$state.selectedRowKeys}}","onSelectedRowKeysChange":[{"action":"SetState","name":"selectedRowKeys","expr":"{{keys}}"}]}},{"component":"Form","name":"form1","props":{"onFinish":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"pg","code":"insert into orders (name, quantity) values ('{{$state.name}}', {{$state.quantity}})"}}],"children":[{"component":"FormItem","name":"name","props":{"label":"Name","children":[{"component":"Input","name":"input1","props":{"value":"{{$state.name}}","onChange":[{"action":"SetState","name":"name","expr":"{{event.target.value}}"}]}}]}},{"component":"FormItem","name":"quantity","props":{"label":"Quantity","children":[{"component":"Input","name":"input2","props":{"value":"{{$state.quantity}}","onChange":[{"action":"SetState","name":"quantity","expr":"{{event.target.value}}"}]}}]}}]}}]}]}]}\n`;
    const good = fixJson(bad);
    expect(good).toBe(
      `{"commands":[{"cmd":"AddPage","path":"/orders","$queries":{"orders":{"opType":"sql","dataSource":"pg","code":"select * from orders"}},"$state":[],"tree":[{"component":"Table","name":"table1","props":{"data":"{{$queries.orders.data}}","bordered":true,"isSelectable":"single","rowKey":"id","selectedRowKeys":"{{$state.selectedRowKeys}}","onSelectedRowKeysChange":[{"action":"SetState","name":"selectedRowKeys","expr":"{{keys}}"}]}},{"component":"Form","name":"form1","props":{"onFinish":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"pg","code":"insert into orders (name, quantity) values ('{{$state.name}}', {{$state.quantity}})"}}],"children":[{"component":"FormItem","name":"name","props":{"label":"Name","children":[{"component":"Input","name":"input1","props":{"value":"{{$state.name}}","onChange":[{"action":"SetState","name":"name","expr":"{{event.target.value}}"}]}}]}},{"component":"FormItem","name":"quantity","props":{"label":"Quantity","children":[{"component":"Input","name":"input2","props":{"value":"{{$state.quantity}}","onChange":[{"action":"SetState","name":"quantity","expr":"{{event.target.value}}"}]}}]}}]}}]}]}`
    );
  });
});
/*
TODO

{"commands":[{"cmd":"AddPage","path":"/admin","$queries":{"tables":{"opType":"sql","dataSource":"northwind","code":"show tables"}},"$state":[],"tree":[{"component":"div","name":"div1","props":{"children":[{"component":"h1","name":"heading1","props":{"children":"Admin Panel"}},{"component":"Select","name":"select1","props":{"options":"{{$queries.tables.data.map((t) => ({value: t, label: t}))}}"}},{"component":"Table","name":"table1","props":{"data":"{{$queries[$state.select1.value].data}}","canSelectRows":"none","children":[{"component":"Button","name":"button1","props":{"children":"Details","onClick":[{"action":"NavTo","path":"/admin/{{$state.select1.value}}/{{$row.id}}"}}]}]}}]}}]},{"cmd":"AddPage","path":"/admin/[table]/[id]","$queries":{"record":{"opType":"sql","dataSource":"northwind","code":"select * from {{$ctx.params.table}} where id = {{$ctx.params.id}}"}},"$state":[],"tree":[{"component":"div","name":"div1","props":{"children":[{"component":"h1","name":"heading1","props":{"children":"Record Details"}},{"component":"Form","name":"form1","props":{"initialValue":"{{$queries.record.data[0]}}","onFinish":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"northwind","code":"update {{$ctx.params.table}} set name = {{$state.form1.values.name}}, description = {{$state.form1.values.description}}, price = {{$state.form1.values.price}} where id = {{$ctx.params.id}}"}}],"children":[{"component":"FormItem","name":"formItem1","props":{"label":"Name","children":{"component":"Input","name":"input1","props":{"name":"name"}}}},{"component":"FormItem","name":"formItem2","props":{"label":"Description","children":{"component":"Input","name":"input2","props":{"name":"description"}}}},{"component":"FormItem","name":"formItem3","props":{"label":"Price","children":{"component":"Input","name":"input3","props":{"name":"price"}}}},{"component":"Button","name":"button1","props":{"type":"primary","htmlType":"submit","children":"Submit"}}]}},{"component":"Button","name":"button1","props":{"children":"Delete","onClick":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"northwind","code":"delete from {{$ctx.params.table}} where id = {{$ctx.params.id}}"}}]}}]}]}}]}]}

{"commands":[{"cmd":"InsertElements","location":"after","of":"table1","elements":[{"component":"Table","name":"table2","props":{"data":"{{$queries.tableData.data}}","canSelectRows":"single","onRowSelectionChanged":[{"action":"NavTo","path":"/admin/{{$ctx.params.table}}/{{$state.table2.selectedRow.id}}"}]}},{"component":"Button","name":"button1","props":{"children":"New","onClick":[{"action":"NavTo","path":"/admin/{{$ctx.params.table}}/new"}]}}]}]}]}

{"commands":[{"cmd":"AddPage","path":"/admin","$queries":{},"$state":[],"tree":[{"component":"div","name":"div1","props":{"children":[{"component":"h1","name":"heading1","props":{"children":"Admin Panel"}},{"component":"Select","name":"select1","props":{"options":[{"value":"orders","label":"Orders"},{"value":"customers","label":"Customers"},{"value":"products","label":"Products"}]}},{"component":"Table","name":"table1","props":{"data":"{{$queries.table.data}}","onRowSelectionChanged":[{"action":"NavTo","path":"/admin/{{$state.select1.value}}/{{$state.table1.selectedRow.id}}"},{"action":"SetState","name":"$state.table1.selectedRow","value":"{{$state.table1.selectedRow}}"}],"canSelectRows":"single","fields":"{{$queries.table.fields.map((f) => ({title: f, dataIndex: f}))}}"}},{"component":"div","name":"div2","props":{"children":[{"component":"Button","name":"button1","props":{"children":"Load More","onClick":[{"action":"SetState","name":"$state.table1.limit","value":"{{$state.table1.limit + 10}}"}]}}]}}]}}]},{"cmd":"AddPage","path":"/admin/:table/:id","$queries":{"record":{"opType":"sql","dataSource":"northwind","code":"select * from {{$ctx.params.table}} where id = {{$ctx.params.id}}"}},"$state":[],"tree":[{"component":"div","name":"div1","props":{"children":[{"component":"h1","name":"heading1","props":{"children":"Record Details"}},{"component":"Form","name":"form1","props":{"initialValue":"{{$queries.record.data[0]}}","onFinish":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"northwind","code":"update {{$ctx.params.table}} set {{$state.form1.values}} where id = {{$ctx.params.id}}"}}],"children":[{"component":"FormItem","name":"formItem1","props":{"label":"ID","name":"id","children":{"component":"Input","name":"input1","props":{"disabled":true}}}}},{"component":"FormItem","name":"formItem2","props":{"label":"Name","name":"name","children":{"component":"Input","name":"input2","props":{}}}},{"component":"FormItem","name":"formItem3","props":{"label":"Description","name":"description","children":{"component":"Input","name":"input3","props":{}}}},{"component":"FormItem","name":"formItem4","props":{"label":"Price","name":"price","children":{"component":"Input","name":"input4","props":{}}}},{"component":"Button","name":"button1","props":{"type":"primary","htmlType":"submit","children":"Submit"}}]}}]}]}}]}

{"commands":[{"cmd":"AddPage","path":"/admin","$queries":{},"$state":[],"tree":[{"component":"div","name":"div1","props":{"children":[{"component":"h1","name":"heading1","props":{"children":"Admin Panel"}},{"component":"Select","name":"select1","props":{"options":[{"value":"orders","label":"Orders"},{"value":"customers","label":"Customers"},{"value":"products","label":"Products"}]}},{"component":"Table","name":"table1","props":{"data":"{{$queries.table.data}}","onRowSelectionChanged":[{"action":"NavTo","path":"/admin/{{$state.select1.value}}/{{$state.table1.selectedRow.id}}"},{"action":"SetState","name":"$state.table1.selectedRow","value":"{{$event.row}}"}],"canSelectRows":"single","fields":"{{$queries.table.fields.map((f) => ({title: f, dataIndex: f}))}}"}},{"component":"Button","name":"button1","props":{"children":"Load","onClick":[{"action":"SetQuery","name":"table","query":{"opType":"sql","dataSource":"northwind","code":"select * from " + $state.select1.value}}}]}]}]},{"cmd":"AddPage","path":"/admin/:table/:id","$queries":{"record":{"opType":"sql","dataSource":"northwind","code":"select * from {{$ctx.params.table}} where id = {{$ctx.params.id}}"}},"$state":[],"tree":[{"component":"div","name":"div1","props":{"children":[{"component":"h1","name":"heading1","props":{"children":"Record Details"}},{"component":"Form","name":"form1","props":{"initialValue":"{{$queries.record.data[0]}}","onFinish":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"northwind","code":"update {{$ctx.params.table}} set name = {{$state.form1.values.name}}, description = {{$state.form1.values.description}}, price = {{$state.form1.values.price}} where id = {{$ctx.params.id}}"}}],"children":[{"component":"FormItem","name":"formItem1","props":{"label":"Name","name":"name","children":{"component":"Input","name":"input1","props":{"defaultValue":"{{$queries.record.data[0].name}}"}}}},{"component":"FormItem","name":"formItem2","props":{"label":"Description","name":"description","children":{"component":"Input","name":"input2","props":{"defaultValue":"{{$queries.record.data[0].description}}"}}}},{"component":"FormItem","name":"formItem3","props":{"label":"Price","name":"price","children":{"component":"Input","name":"input3","props":{"defaultValue":"{{$queries.record.data[0].price}}"}}}},{"component":"Button","name":"button1","props":{"type":"primary","htmlType":"submit","children":"Submit"}}]}},{"component":"Button","name":"button2","props":{"children":"Delete","onClick":[{"action":"RunDataSourceOp","op":{"opType":"sql","dataSource":"northwind","code":"delete from {{$ctx.params.table}} where id = {{$ctx.params.id}}"}}]}}]}]}}]}
 */
