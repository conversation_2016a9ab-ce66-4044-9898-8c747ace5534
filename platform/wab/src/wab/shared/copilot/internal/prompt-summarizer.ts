import { ApiDataSource } from "@/wab/shared/ApiSchema";
import { isSlot } from "@/wab/shared/SlotUtils";
import { TplMgr } from "@/wab/shared/TplMgr";
import { computedProjectFlags } from "@/wab/shared/cached-selectors";
import {
  getBuiltinComponentRegistrations,
  isBuiltinCodeComponent,
} from "@/wab/shared/code-components/builtin-code-components";
import { getExportedComponentName } from "@/wab/shared/codegen/react-p/serialize-utils";
import { toVarName } from "@/wab/shared/codegen/util";
import {
  arrayEqual,
  emptyToUndefined,
  filterObject,
  flatMapObject,
  mkNameMap,
} from "@/wab/shared/collections";
import {
  check,
  ensure,
  ensureInstance,
  maybe,
  switchType,
  todo,
  trySingularize,
  tuple,
  unexpected,
  uniqueName,
  withoutNils,
} from "@/wab/shared/common";
import {
  ToyAction,
  ToyDataSourceOp,
  ToyElement,
  ToyExpr,
  ToyHttpOp,
  ToyType,
} from "@/wab/shared/copilot/internal/toy-types";
import {
  getComponentDisplayName,
  isCodeComponent,
  isPageComponent,
} from "@/wab/shared/core/components";
import {
  ExprCtx,
  asCode,
  isDynamicExpr,
  tryExtractJson,
} from "@/wab/shared/core/exprs";
import { StateVariableType } from "@/wab/shared/core/states";
import { TplNamable, isTplTag } from "@/wab/shared/core/tpls";
import {
  DataSourceType,
  GenericDataSource,
} from "@/wab/shared/data-sources-meta/data-source-registry";
import { dataSourceTemplateToString } from "@/wab/shared/data-sources-meta/data-sources";
import { buildSqlStringForFilterTemplateArg } from "@/wab/shared/data-sources/to-sql";
import {
  Component,
  CustomCode,
  DataSourceOpExpr,
  DataSourceTemplate,
  EventHandler,
  Expr,
  Interaction,
  ObjectPath,
  PageHref,
  RenderExpr,
  RichText,
  Site,
  TemplatedString,
  TplComponent,
  TplNode,
  TplSlot,
  TplTag,
  Type,
  ensureKnownDataSourceOpExpr,
  isKnownExprText,
  isKnownNamedState,
} from "@/wab/shared/model/classes";
import {
  convertVariableTypeToWabType,
  isChoiceType,
  wabToTsType,
} from "@/wab/shared/model/model-util";
import { decapitalizeFirst } from "@/wab/shared/strs";
import { TableSchema } from "@plasmicapp/data-sources";
import { isString, pickBy } from "lodash";

function ensureTextLit(x: TemplatedString | string): string {
  if (typeof x === "string") {
    return x;
  }
  return x.text.length === 1 && typeof x.text[0] === "string"
    ? x.text[0]
    : unexpected();
}

export interface ComponentSummary {
  name: string;
  $props: string[];
  $queries: {
    [name: string]: ToyDataSourceOp;
  };
  $state: string[];
  tree: ToyElement[];
}

export interface PageSummary extends ComponentSummary {
  path: string;
}

export interface DataSourceSummary {
  name: string;
  type: string;
  tables?: string[];
  // tables: {
  //   [table: string]: {
  //     [field: string]: TypeSummary;
  //   };
  // };
}

type ExtendedType = ToyType | "event" | "slot" | any;

export interface AvailableComponentSummary {
  props: Record<string, ExtendedType>;
  states?: Record<string, ExtendedType>;
}

export type RootSummary = {
  availableComponents: Record<string, AvailableComponentSummary>;
  availableDataSources: DataSourceSummary[];
  allPages: string[];
  currentPage?: PageSummary;
  currentComponent?: ComponentSummary;
};

const ignoredAttrs = new Map(
  Object.entries({
    img: ["loading"],
  })
);

const ignoredComponents = new Set(["PlasmicHead"]);

function renameComponent(x: string) {
  return x.startsWith("plasmic-antd5-hostless-") ? x.slice(4) : x;
}

function computeNiceComponentName(name: string) {
  return name
    .replace(/[-_. ]+/g, " ")
    .replace(/( |^)[a-z]/g, (x) => x.toUpperCase())
    .replace(/ /g, "")
    .trim();
}

type NormalElement = Exclude<ToyElement, string | { slot: string }>;

function maybeCollapse(elt: NormalElement) {
  return elt.component === "div" &&
    !elt.visibility &&
    arrayEqual(Object.keys(elt.props), ["children"]) &&
    isString(elt.props.children)
    ? elt.props.children
    : elt;
}

interface ComponentExtraInfo {
  autoName: string;
  isSyntheticName: boolean;
}

interface TplExtraInfo {
  autoName: string;
  isSyntheticName: boolean;
}
export interface Bookkeeping {
  componentMap: Map<Component, ComponentExtraInfo>;
  tplMap: Map<TplTag | TplComponent, TplExtraInfo>;
}

export interface SummaryComputation {
  /** What's actually sent to the LLM */
  rootSummary: RootSummary;
  /** Tracking how we map model/Site objects to summaries */
  bookkeeping: Bookkeeping;
}

const supportedDataSources: DataSourceType[] = ["http", "postgres"];

export class Summarizer {
  constructor(
    public readonly site: Site,
    public readonly dataSources: ApiDataSource[],
    public readonly tablesByDataSourceName: Record<string, TableSchema[]>
  ) {}

  private getDataSourceById(dataSourceId: string) {
    return ensure(
      this.dataSources.find((ds) => ds.id === dataSourceId),
      ""
    );
  }

  getDataSourceByName(name: string) {
    return ensure(
      this.dataSources.find((ds) => ds.name === name),
      ""
    );
  }

  summarizeWithContext(
    ctxComponentName: string,
    relevantElementNames?: string[]
  ): SummaryComputation {
    const ctxComponent: Component = ensure(
      this.site.components.find((c) => c.name === ctxComponentName),
      ""
    );

    const projectFlags = computedProjectFlags(this.site);

    const tplMgr = new TplMgr({ site: this.site });

    const tplNames = tplMgr.getExistingTplAndParamNames(ctxComponent);

    // const tplExistingNameMap = new Map(
    //     flattenTpls(ensure(ctxComponent.tplTree, '')).flatMap(tpl => 'name' in tpl ? [[tpl.name,tpl]] : [])
    // )

    const books: Bookkeeping = {
      tplMap: new Map(),
      componentMap: new Map(),
    };

    const componentNames: string[] = [];

    const getNiceComponentName = (component: Component) => {
      const baseName = renameComponent(
        computeNiceComponentName(
          isCodeComponent(component)
            ? getComponentDisplayName(component)
            : getExportedComponentName(component)
        )
      );
      let extra = books.componentMap.get(component);
      if (!extra) {
        extra = {
          autoName:
            baseName ??
            uniqueName(componentNames, baseName, {
              separator: "",
            }),
          isSyntheticName: !baseName,
        };
        componentNames.push(extra.autoName);
        books.componentMap.set(component, extra);
      }
      return extra.autoName;
    };

    /**
     * Return the name of the tpl node, or else auto-name it something like div1 or formItem3.
     */
    const autoNameTpl = (tpl: TplTag | TplComponent): string => {
      let extra = books.tplMap.get(tpl);
      if (!extra) {
        const baseName = isTplTag(tpl)
          ? tpl.tag
          : decapitalizeFirst(getNiceComponentName(tpl.component));
        extra = {
          autoName:
            tpl.name ??
            uniqueName(tplNames, baseName + "1", {
              separator: "",
            }),
          isSyntheticName: !tpl.name,
        };
        tplNames.push(extra.autoName);
        books.tplMap.set(tpl, extra);
      }
      return extra.autoName;
    };

    const filtersToSql = (
      dataSource: ApiDataSource,
      filters: DataSourceTemplate,
      exprCtx: ExprCtx
    ): string => {
      return ensure(
        buildSqlStringForFilterTemplateArg(
          dataSource as GenericDataSource,
          dataSourceTemplateToString(filters, exprCtx)
        ),
        ""
      );
    };

    const summarizeDataSourceOp = (
      op: DataSourceOpExpr,
      exprCtx: ExprCtx
    ): ToyDataSourceOp => {
      const dataSource = this.getDataSourceById(op.sourceId);
      if (dataSource.source === "postgres" && op.opName === "getMany") {
        return {
          opType: "sql",
          dataSource: dataSource.name,
          code: `select *
                 from "${ensureTextLit(op.templates.resource.value)}"
                 ${
                   maybe(
                     op.templates.filters,
                     (filters) =>
                       `where ${filtersToSql(dataSource, filters, exprCtx)}`
                   ) ?? ""
                 }`,
        };
      } else if (
        (dataSource.source === "postgres" && op.opName === "customRead") ||
        op.opName === "customWrite"
      ) {
        return {
          opType: "sql",
          dataSource: dataSource.name,
          code: dataSourceTemplateToString(op.templates.query, exprCtx),
        };
      } else if (dataSource.source === "http") {
        return {
          opType: "http",
          dataSource: dataSource.name,
          method: op.opName.toUpperCase() as ToyHttpOp["method"],
          path:
            "/" +
            (maybe(op.templates.path, (path) =>
              dataSourceTemplateToString(path, exprCtx)
            ) ?? ""),
          params:
            // JSON.parse doesn't work since sub-expressions can have quotes
            maybe(
              op.templates.params,
              (params) => dataSourceTemplateToString(params, exprCtx) as any
            ) ?? {},
          headers:
            // JSON.parse doesn't work since sub-expressions can have quotes
            maybe(
              op.templates.headers,
              (headers) => dataSourceTemplateToString(headers, exprCtx) as any
            ) ?? {},
          body:
            maybe(op.templates.body, (body) =>
              dataSourceTemplateToString(body, exprCtx)
            ) ?? undefined,
        };
      } else {
        return todo();
      }
    };

    const summarizeAction = (
      action: Interaction,
      exprCtx: ExprCtx
    ): ToyAction => {
      const args = mkNameMap(action.args);
      const getArg = (name: string) =>
        ensure(args.get(name), `Missing arg ${name}`).expr;
      switch (action.actionName) {
        case "updateVariable":
          return {
            action: "SetState",
            name: asCode(getArg("name"), exprCtx).code,
            expr: summarizeExpr(getArg("value"), exprCtx),
          };
        case "dataSourceOp":
          return {
            action: "RunDataSourceOp",
            op: summarizeDataSourceOp(
              ensureKnownDataSourceOpExpr(getArg("op")),
              exprCtx
            ),
          };
        case "navigation":
          return {
            action: "NavTo",
            ...summarizeExpr(getArg("path"), exprCtx),
          };
        default:
          return unexpected();
      }
    };

    const summarizeExpr = (_expr: Expr, exprCtx: ExprCtx): ToyExpr => {
      return switchType(_expr)
        .when(CustomCode, (expr) => {
          return isDynamicExpr(expr)
            ? `{{${expr.code}}}`
            : tryExtractJson(expr);
        })
        .when(ObjectPath, (expr) => {
          return `{{${asCode(expr, exprCtx).code}}}`;
        })
        .when(TemplatedString, (expr) => {
          return expr.text
            .map((part) =>
              typeof part === "string" ? part : summarizeExpr(part, exprCtx)
            )
            .join("");
        })
        .when(PageHref, (expr) => {
          return {
            path: ensure(
              this.site.components.find(() => ctxComponent),
              ""
            ).pageMeta?.path,
            params: expr.params,
          };
        })
        .when(EventHandler, (expr) =>
          expr.interactions.map((interaction) =>
            summarizeAction(interaction, exprCtx)
          )
        )
        .when(RenderExpr, (expr) =>
          trySingularize(expr.tpl.map((child) => summarizeTpl(child, exprCtx)))
        )
        .elseUnsafe(() => undefined);
    };

    function summarizeRichText(text: RichText, exprCtx: ExprCtx) {
      return isKnownExprText(text)
        ? summarizeExpr(text.expr, exprCtx)
        : text.text;
    }

    const trackedElts = new Set<NormalElement>();

    function trackNode(x: ToyElement) {
      if (typeof x === "object" && "name" in x) {
        trackedElts.add(x);
      }
      return x;
    }

    // TODO handles only base variant
    function summarizeTpl(_tpl: TplNode, exprCtx: ExprCtx): ToyElement {
      return (
        switchType(_tpl)
          .when(TplTag, (tpl) =>
            trackNode(
              maybeCollapse({
                component: tpl.tag,
                name: autoNameTpl(tpl),
                visibility: maybe(tpl.vsettings[0].dataCond, (e) =>
                  summarizeExpr(e, exprCtx)
                ),
                props: {
                  ...flatMapObject(tpl.vsettings[0].attrs, ([k, v]) =>
                    (ignoredAttrs.get(tpl.tag) ?? []).includes(k)
                      ? []
                      : [tuple(k, summarizeExpr(v, exprCtx))]
                  ),
                  children:
                    emptyToUndefined(
                      tpl.children.map((child) => summarizeTpl(child, exprCtx))
                    ) ??
                    maybe(tpl.vsettings[0].text, (t) =>
                      summarizeRichText(t, exprCtx)
                    ),
                },
              })
            )
          )
          .when(TplComponent, (tpl) =>
            trackNode({
              component: getNiceComponentName(tpl.component),
              name: autoNameTpl(tpl),
              visibility: maybe(tpl.vsettings[0].dataCond, (e) =>
                summarizeExpr(e, exprCtx)
              ),
              props: Object.fromEntries(
                tpl.vsettings[0].args.map((arg) => [
                  toVarName(arg.param.variable.name),
                  summarizeExpr(arg.expr, exprCtx),
                ])
              ),
            })
          )
          // TODO We currently discard default slot contents
          .when(TplSlot, (tpl) => ({
            slot: toVarName(tpl.param.variable.name),
          }))
          .result()
      );
    }

    function summarizeType(type: Type): ToyType {
      return isChoiceType(type)
        ? type.options[0] !== "Dynamic options"
          ? {
              type: "choice",
              values: type.options.map((x) =>
                typeof x === "string" ? x : x.value
              ),
            }
          : "any"
        : (wabToTsType(type).replace("ReactNode", "slot") as any);
    }
    function summarizeVariableType(variableType: StateVariableType): ToyType {
      return summarizeType(convertVariableTypeToWabType(variableType));
    }

    // The Fetcher component is usually not needed, and having it as an
    // available component makes the model add unnecessary queries to fetch
    // data already in `currentComponent.queries`.
    const isFetcherComponent = (c: Component) =>
      isBuiltinCodeComponent(c) &&
      c.name === getBuiltinComponentRegistrations().PlasmicFetcher.meta.name;

    const exprCtx: ExprCtx = {
      projectFlags,
      component: ctxComponent,
      inStudio: true,
    };

    // Need to summarize the tree, regardless of whether we filter by
    // relevantElementNames, so we can name them and track them in
    // trackedElts.
    const summarizedRoot = summarizeTpl(ctxComponent.tplTree, exprCtx);

    const base: RootSummary = {
      availableComponents: Object.fromEntries(
        this.site.components
          .filter((c) => !isFetcherComponent(c) && !isPageComponent(c))
          .map((c) =>
            tuple(getNiceComponentName(c), {
              props: Object.fromEntries(
                c.params.flatMap((p) => [
                  tuple(toVarName(p.variable.name), summarizeType(p.type)),
                ])
              ),
              states: Object.fromEntries(
                c.states.flatMap((s) =>
                  isKnownNamedState(s)
                    ? [
                        tuple(
                          s.name,
                          summarizeVariableType(
                            s.variableType as StateVariableType
                          )
                        ),
                      ]
                    : []
                )
              ),
            })
          )
      ),
      availableDataSources: this.dataSources
        .filter((ds) => supportedDataSources.includes(ds.source))
        .map((ds) => ({
          name: ds.name,
          type: ds.source,
          ...(this.tablesByDataSourceName[ds.name]
            ? {
                tables: this.tablesByDataSourceName[ds.name].map(
                  (table) => table.label ?? table.id
                ),
              }
            : {}),
        })),
      allPages: withoutNils(this.site.components.map((c) => c.pageMeta?.path)),
    };
    const componentSummary: ComponentSummary = {
      name: getNiceComponentName(ctxComponent),
      $props: ctxComponent.params
        .filter(
          (p) =>
            !isSlot(p) &&
            !ctxComponent.states.some(
              (s) => s.accessType === "private" && s.param === p
            )
        )
        .map((p) => toVarName(p.variable.name)),
      $queries: Object.fromEntries(
        ctxComponent.dataQueries.flatMap((q) =>
          q.op ? [[q.name, summarizeDataSourceOp(q.op, exprCtx)]] : []
        )
      ),
      $state: ctxComponent.states.flatMap((s) =>
        s.implicitState ? [] : [s.param.variable.name]
      ),
      tree: relevantElementNames
        ? [...trackedElts.values()]
            .filter((elt) => relevantElementNames.includes(elt.name))
            .map((elt) => ({
              ...elt,
              props: pickBy(
                elt.props,
                (_, prop) =>
                  (base.availableComponents[elt.component] ?? {
                    props: { children: "slot" },
                  })[prop] !== "slot"
              ),
            }))
        : [summarizedRoot],
    };
    const rootSummary: RootSummary = ctxComponent.pageMeta
      ? {
          ...base,
          currentPage: {
            path: ctxComponent.pageMeta.path,
            ...componentSummary,
          },
        }
      : {
          ...base,
          currentComponent: componentSummary,
        };
    return { rootSummary, bookkeeping: books };
  }

  snipSummary(
    componentName: string,
    fullSummary: RootSummary,
    relevant: {
      dataSources: { name: string; type: string; tables: string[] }[];
      elements: string[];
      components: string[];
    }
  ): RootSummary {
    const { rootSummary: refreshedSummary } = this.summarizeWithContext(
      componentName,
      relevant.elements
    );

    return {
      ...fullSummary,
      currentPage: refreshedSummary.currentPage,
      currentComponent: refreshedSummary.currentComponent,
      availableComponents: filterObject(
        fullSummary.availableComponents,
        ([name, _]) => relevant.components.includes(name)
      ),
      availableDataSources: fullSummary.availableDataSources.flatMap((ds) => {
        const relevantDs = relevant.dataSources.find(
          (_ds) => _ds.name === ds.name && _ds.type === ds.type
        );
        if (!relevantDs) {
          return [];
        }
        return [
          {
            ...ds,
            // tables: filter(ds.tables, ([fields, table]) =>
            //     relevantDs.tables.includes(table)
            // ),
            tables: ds.tables?.filter((table) =>
              relevantDs.tables.includes(table)
            ),
          },
        ];
      }),
    };
  }
}

export class Desummarizer {
  constructor(
    private summary: RootSummary,
    private bookkeeping: Bookkeeping,
    private currentComponent: Component,
    private eltNameToTpl = new Map(
      [...bookkeeping.tplMap].map(([tpl, extra]) => [extra.autoName, tpl])
    ),
    private compNameToComp = new Map(
      [...bookkeeping.componentMap].map(
        ([component, extra]) => [extra.autoName, component] as const
      )
    )
  ) {}

  getTplByAutoName(elementName: string) {
    return elementName === "__root__"
      ? ensureInstance(this.currentComponent.tplTree, TplComponent, TplTag)
      : ensure(this.eltNameToTpl.get(elementName), "");
  }

  getComponentByAutoName(componentName: string) {
    return ensure(this.compNameToComp.get(componentName), "");
  }

  trackTpl(tpl: TplNamable, autoName: string) {
    if (tpl.name) {
      check(!this.eltNameToTpl.has(autoName));
      this.eltNameToTpl.set(autoName, tpl);
    }
  }
}

export function getCurrentComponent(summary: RootSummary) {
  return ensure(summary.currentPage ?? summary.currentComponent, "");
}
