BASE := src/wab/shared/copilot/internal

all: \
	types-content.json \
	validators/gen/toy-types-validator.ts
types-content.json: toy-types.ts
	yarn run-ts $(BASE)/gen-types-json.ts
validators/gen/toy-types-validator.ts: validators/src/toy-types-validator.ts
	rm -rf $(dir $@) > /dev/null
	yarn typia generate --input $(BASE)/$(dir $<) --output $(BASE)/$(dir $@) --project tsconfig.json
	{ grep ^import $< ; grep -v ^import $@ ; } > $@.tmp ; mv $@.tmp $@
