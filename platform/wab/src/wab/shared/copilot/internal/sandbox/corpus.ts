import { RootSummary } from "@/wab/shared/copilot/internal/prompt-summarizer";
import { baseComponents, sakiraDataSource } from "@/wab/shared/copilot/internal/sandbox/prompts";

export interface CorpusCase {
  summary: RootSummary;
  goal: string;
}

export const corpus: CorpusCase[] = [
  {
    summary: {
      availableComponents: baseComponents,
      availableDataSources: [sakiraDataSource],
      allPages: ["/dashboard", "/inventory/[id]"],
      currentPage: {
        name: "/dashboard",
        path: "/dashboard",
        $props: [],
        $queries: {
          categories: {
            opType: "sql",
            dataSource: "sakira",
            code: "select * from category",
          },
          inventory: {
            opType: "sql",
            dataSource: "sakira",
            code: `select * from inventory where category_id = {{$state.select1.value}}`,
          },
        },
        $state: [],
        tree: [
          {
            component: "PageLayout",
            name: "layout",
            props: {
              children: [
                {
                  component: "Select",
                  name: "select1",
                  props: {
                    label: "Category",
                    options: "{{$queries.categories.map((c) => c.name)}}",
                  },
                },
                {
                  component: "Table",
                  name: "table1",
                  props: {
                    data: "{{$queries.inventory}}",
                    fields: ["inventory_id", "title", "description"],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    goal: "make this table link to the details page for the selected row",
  },
  {
    summary: {
      availableComponents: baseComponents,
      availableDataSources: [sakiraDataSource],
      allPages: ["/"],
      currentPage: {
        name: "/",
        path: "/",
        $props: [],
        $queries: {},
        $state: [],
        tree: [],
      },
    },
    goal: "A dashboard that allows me to select a category, then shows me all of our inventory with the inventory ID, film name and film description, in that category",
  },
  {
    summary: {
      availableComponents: baseComponents,
      availableDataSources: [sakiraDataSource],
      allPages: ["/dashboard"],
      currentPage: {
        name: "/dashboard",
        path: "/dashboard",
        $props: [],
        $queries: {
          categories: {
            opType: "sql",
            dataSource: "sakira",
            code: "select * from category",
          },
          inventory: {
            opType: "sql",
            dataSource: "sakira",
            code: `select * from inventory where category_id = {{$state.select1.value}}`,
          },
        },
        $state: [],
        tree: [
          {
            component: "PageLayout",
            name: "layout",
            props: {
              children: [
                {
                  component: "Select",
                  name: "select1",
                  props: {
                    label: "Category",
                    options: "{{$queries.categories.map((c) => c.name)}}",
                  },
                },
                {
                  component: "Table",
                  name: "table1",
                  props: {
                    data: "{{$queries.inventory}}",
                    fields: ["inventory_id", "title", "description"],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    goal: "When I select an inventory, show me the customer name, email and rental date for all rentals of this film",
  },
  {
    summary: {
      availableComponents: baseComponents,
      availableDataSources: [sakiraDataSource],
      allPages: ["/dashboard"],
      currentPage: {
        name: "/dashboard",
        path: "/dashboard",
        $props: [],
        $queries: {
          categories: {
            opType: "sql",
            dataSource: "sakira",
            code: "select * from category",
          },
          inventory: {
            opType: "sql",
            dataSource: "sakira",
            code: `select * from inventory where category_id = {{$state.select1.value}}`,
          },
          rentals: {
            opType: "sql",
            dataSource: "sakira",
            code: `select * from rental where inventory_id = {{$state.table1.selectedRow.id}}`,
          },
        },
        $state: [],
        tree: [
          {
            component: "PageLayout",
            name: "layout",
            props: {
              children: [
                {
                  component: "Select",
                  name: "select1",
                  props: {
                    label: "Category",
                    options: "{{$queries.categories.map((c) => c.name)}}",
                  },
                },
                {
                  component: "Table",
                  name: "table1",
                  props: {
                    data: "{{$queries.inventory}}",
                    fields: ["inventory_id", "title", "description"],
                  },
                },
                {
                  component: "Table",
                  name: "table1",
                  props: {
                    data: "{{$queries.rentals}}",
                    fields: ["customer_id", "email", "rental_date"],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    goal: "Add a form that allows me to select a customer by email address then, when i hit submit, it create a new rental for the selected customer and the selected inventory.",
  },
  {
    summary: {
      availableComponents: baseComponents,
      availableDataSources: [sakiraDataSource],
      allPages: ["/dashboard"],
      currentPage: {
        name: "/dashboard",
        path: "/dashboard",
        $props: [],
        $queries: {
          categories: {
            opType: "sql",
            dataSource: "sakira",
            code: "select * from category",
          },
          inventory: {
            opType: "sql",
            dataSource: "sakira",
            code: `select * from inventory where category_id = {{$state.select1.value}}`,
          },
          rentals: {
            opType: "sql",
            dataSource: "sakira",
            code: `select * from rental where inventory_id = {{$state.table1.selectedRow.id}}`,
          },
        },
        $state: [],
        tree: [
          {
            component: "PageLayout",
            name: "layout",
            props: {
              children: [
                {
                  component: "Select",
                  name: "select1",
                  props: {
                    label: "Category",
                    options: "{{$queries.categories.map((c) => c.name)}}",
                  },
                },
                {
                  component: "Table",
                  name: "table1",
                  props: {
                    data: "{{$queries.inventory}}",
                    fields: ["inventory_id", "title", "description"],
                  },
                },
                {
                  component: "Form",
                  name: "form1",
                  props: {
                    onSubmit: [
                      {
                        action: "RunDataSourceOp",
                        op: {
                          opType: "insert",
                          dataSource: "sakira",
                          table: "rental",
                          writes: {
                            inventory_id: "{{$state.table1.selectedRow.id}}",
                            customer_id:
                              "{{$queries.customers.data[0].customer_id}}",
                            staff_id: 1,
                            rental_date: "{{new Date()}}",
                          },
                        },
                      },
                    ],
                    children: [
                      {
                        component: "Input",
                        name: "input1",
                        props: {
                          label: "Title",
                          value: "{{$state.table1.selectedRow.title}}",
                        },
                      },
                      {
                        component: "Button",
                        name: "button2",
                        props: {
                          isSubmit: true,
                          label: "Update",
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
    },
    goal: "update this form when selecting rows",
  },
  {
    summary: {
      availableComponents: {
        Button: {
          props: {
            children: "slot",
            isDisabled: "any",
            link: "string",
          },
          states: {},
        },
      },
      availableDataSources: [],
      allPages: ["/products"],
      currentPage: {
        path: "/products",
        name: "Products",
        $props: [],
        $queries: {},
        $state: [],
        tree: [
          {
            component: "div",
            name: "div1",
            props: {
              children: [
                {
                  component: "h2",
                  name: "h21",
                  props: { children: "Products list" },
                },
                { component: "Product", name: "product1", props: { id: "1" } },
                { component: "Product", name: "product2", props: { id: "2" } },
                { component: "Product", name: "product3", props: { id: "3" } },
              ],
            },
          },
        ],
      },
    },
    goal: "Add a button 'Go to chart' that redirects to a new '/chart' page",
  },
  {
    summary: {
      availableComponents: {},
      availableDataSources: [],
      allPages: ["/"],
      currentPage: {
        path: "/",
        name: "Homepage",
        $props: [],
        $queries: {},
        $state: [],
        tree: [{ component: "div", name: "div1", props: {} }],
      },
    },
    goal: "Add a text element saying 'Hello World!'",
  },
  {
    summary: {
      availableComponents: {
        Button: {
          props: {
            children: "slot",
            isDisabled: "any",
            link: "string",
          },
          states: {},
        },
      },
      availableDataSources: [],
      allPages: ["/"],
      currentPage: {
        path: "/",
        name: "Homepage",
        $props: [],
        $queries: {},
        $state: [],
        tree: [{ component: "div", name: "div1", props: {} }],
      },
    },
    goal: "Add a button saying 'Hello World!'",
  },
  {
    summary: {
      availableComponents: {
        Header: { props: {}, states: {} },
        Button: {
          props: {
            children: "slot",
            showStartIcon: "any",
            showEndIcon: "any",
            startIcon: "slot",
            endIcon: "slot",
            isDisabled: "any",
            link: "string",
          },
          states: {},
        },
      },
      availableDataSources: [],
      allPages: ["/homepage", "/profile"],
      currentPage: {
        path: "/homepage",
        name: "Homepage",
        $props: [],
        $queries: {},
        $state: [],
        tree: [
          { component: "Header", name: "header1", props: {} },
          {
            component: "Button",
            name: "profileBtn",
            props: {
              children: "Profile",
            },
          },
        ],
      },
    },
    goal: "Move the 'Profile' button to the page header, between the avatar and the menu",
  },
  {
    summary: {
      availableComponents: {},
      availableDataSources: [
        { name: "pg", type: "postgres", tables: ["User"] },
      ],
      allPages: ["/user/[id]"],
      currentPage: {
        path: "/user/[id]",
        name: "UserPage",
        $props: [],
        $queries: {},
        $state: [],
        tree: [{ component: "div", name: "div1", props: {} }],
      },
    },
    goal: "Add a new text element to the page that displays the user's name and email, and bind it to the user's data from the database",
  },
  {
    summary: {
      availableComponents: {
        Section: { props: { children: "slot" }, states: {} },
      },
      availableDataSources: [],
      allPages: ["/homepage"],
      currentPage: {
        path: "/homepage",
        name: "Homepage",
        $props: [],
        $queries: {},
        $state: [],
        tree: [
          {
            component: "Section",
            name: "pricing",
            props: {
              children: [
                {
                  component: "div",
                  name: "priceTier1",
                  props: { children: ["10 USD"] },
                },
                {
                  component: "div",
                  name: "priceTier2",
                  props: { children: ["20 USD"] },
                },
                {
                  component: "div",
                  name: "priceTier3",
                  props: { children: ["30 USD"] },
                },
              ],
            },
          },
        ],
      },
    },
    goal: "Extract the pricing section to a page '/pricing'",
  },
  {
    summary: {
      availableComponents: {
        TextInput: {
          props: {
            placeholder: "string",
            isDisabled: "any",
            value: "string",
            name: "string",
            onChange: "(event: any) => void",
            inputValue: "string",
          },
          states: { value: "string" },
        },
        Button: {
          props: {
            children: "slot",
            isDisabled: "any",
            link: "string",
          },
          states: {},
        },
      },
      availableDataSources: [{ name: "site-api", type: "http" }],
      allPages: ["/newsletter"],
      currentPage: {
        path: "/newsletter",
        name: "Newsletter",
        $props: [],
        $queries: {},
        $state: [],
        tree: [
          {
            component: "TextInput",
            name: "email",
            props: {},
          },
          {
            component: "Button",
            name: "button1",
            props: {
              children: "Subscribe",
            },
          },
        ],
      },
    },
    goal: "Make the subscribe button send a POST to 'site-api' (post to '/subscribe'). The body should be a JSON with 'email': the e-mail to subscribe (from the text input)",
  },
  {
    summary: {
      availableComponents: {
        Button: {
          props: {
            children: "slot",
            isDisabled: "any",
            link: "string",
          },
          states: {},
        },
      },
      availableDataSources: [],
      allPages: ["/game-setup"],
      currentPage: {
        path: "/game-setup",
        name: "Game Setup",
        $props: [],
        $queries: {},
        $state: [],
        tree: [
          {
            component: "div",
            name: "gameConfig",
            visibility: true,
            props: {
              children: [
                "Difficulty level: 0",
                {
                  component: "Button",
                  name: "button1",
                  props: {
                    children: "Start Game",
                  },
                },
              ],
            },
          },
        ],
      },
    },
    goal: "Add two buttons to configure (increase or decrease) the difficulty level of the game. Update the text to show the current difficulty level.",
  },
  {
    summary: {
      availableComponents: {
        GameResults: { props: { score: "number" }, states: {} },
      },
      availableDataSources: [],
      allPages: ["/play-game"],
      currentPage: {
        path: "/play-game",
        name: "PlayGame",
        $props: [],
        $queries: {},
        $state: ["gameEnded", "score"],
        tree: [
          {
            component: "h3",
            name: "h31",
            props: { children: "You won!!! 🥳" },
          },
          { component: "h3", name: "h32", props: { children: "You lost! 😢" } },
          { component: "GameResults", name: "gameResults", props: {} },
        ],
      },
    },
    goal: "Hide 'You won' and 'You lost' while the game hasn't ended ('gameEnded' variable is false). When it ends, show the corresponding message (user wins if the score is positive). Also provide the score to the GameResults component",
  },
];

/*

Task: make this table link to the details page for the selected row.
({
  selectedCategory: Select(),
  categories: Query(sakira, "select * from category"),
  inventory: Query(
    sakira,
    "select * From inventory where category_id = {{selectedCategory}}"
  ),
  children: [
    table1 = Table({
      data: "{{inventory}}",
      fields: ["inventory_id", "title", "description"],
    }),
  ]
}({
  select1: {
    type: "Component",
    component: "Select",
    props: {},
  },
  categories: {
    type: "Query",
    opType: "select",
    dataSource: "sakira",
    table: "category",
    filters: [],
  },
  inventory: {
    type: "Query",
    opType: "select",
    dataSource: "sakira",
    table: "inventory",
    filters: [
      { field: "category_id", op: "=", expr: "{{selectedCategory}}" },
    ],
  },
  table1: {
    type: "component",
    component: "Table",
    props: {
      data: "{{inventory}}",
      fields: ["inventory_id", "title", "description"],
    },
  },
}));
*/

// applyCommands(response);
