import {
  chatGptDefaultPrompt,
  createOpenAIClient,
} from "@/wab/server/copilot/llms";
import { ensure, spawn, strictZip } from "@/wab/shared/common";
import { RootSummary } from "@/wab/shared/copilot/internal/prompt-summarizer";
import { baseComponents } from "@/wab/shared/copilot/internal/sandbox/prompts";
import { humanJson } from "@/wab/shared/copilot/prompt-utils";
import { command, run, subcommands } from "cmd-ts";
import fs from "fs";
import { range } from "lodash";
import random from "random";
import { z } from "zod";

import {
  extractJsonFromString,
  getResponseContent,
  showResponse,
} from "@/wab/shared/copilot/internal/prompts";
import { ToyDataSourceOp } from "@/wab/shared/copilot/internal/toy-types";

/**
 * Generated with ChatGPT:
 *
 * We are training an AI model to come up with ideas for web apps.
 *
 * Generate 100 examples of specific ideas. Include products, business apps, and internal tools.
 */
const appIdeas = `
# Products
A meal planning app that generates weekly menus based on dietary restrictions and preferences.
A virtual wardrobe app that helps users create outfits from their existing clothes and suggests new items to purchase.
A language learning app that uses AI to personalize lessons based on the user's learning style and progress.
A mental health app that provides daily exercises and resources for managing anxiety and depression.
A travel planning app that recommends destinations and activities based on the user's budget and interests.
A fitness app that creates customized workout plans and tracks progress using wearable technology.
A home automation app that allows users to control their smart devices from a single interface.
A personal finance app that helps users track expenses, set budgets, and invest in stocks and cryptocurrencies.
A dating app that uses AI to match users based on personality traits and interests.
A virtual interior design app that allows users to visualize furniture and decor in their home before making purchases.
# Business Apps
A project management app that tracks tasks, deadlines, and team members' progress.
A customer relationship management (CRM) app that organizes customer data and interactions.
A human resources app that streamlines the hiring process and manages employee benefits and payroll.
A marketing automation app that creates and tracks email campaigns, social media posts, and ads.
A supply chain management app that tracks inventory, orders, and shipping.
A data analytics app that visualizes and analyzes business data to identify trends and opportunities.
A virtual meeting app that allows remote teams to collaborate and communicate in real-time.
A legal document management app that stores and organizes contracts, agreements, and other legal documents.
A cybersecurity app that monitors and protects against cyber threats and data breaches.
A customer service app that provides chatbots and live support to assist customers with inquiries and issues.
# Internal Tools
A time tracking app that helps employees log hours and manage their workload.
A knowledge management app that stores and organizes company information and resources.
A performance management app that tracks employee goals and provides feedback and coaching.
A training and development app that provides online courses and resources for employee learning.
A scheduling app that helps managers create and manage employee schedules.
A task management app that assigns and tracks tasks for individual employees or teams.
A communication app that allows employees to chat and collaborate in real-time.
A file sharing app that allows employees to share and collaborate on documents and files.
A feedback and survey app that collects feedback from employees and customers.
A benefits management app that allows employees to manage their benefits and enroll in new ones.
# Miscellaneous
A weather app that provides hyper-local weather forecasts and alerts.
A news app that curates articles based on the user's interests and reading history.
A recipe app that suggests meals based on ingredients and dietary restrictions.
A language translation app that translates text and speech in real-time.
A meditation app that provides guided meditations and mindfulness exercises.
A pet care app that tracks vet appointments, medication schedules, and pet behavior.
A gardening app that provides tips and resources for growing plants and vegetables.
A home cleaning app that creates cleaning schedules and provides cleaning tips and tricks.
A personal safety app that provides emergency alerts and resources.
A fashion app that provides style advice and recommendations based on body type and preferences.
# Education
A virtual classroom app that allows teachers to conduct online classes and assignments.
A student management app that tracks student progress and grades.
A learning management app that provides online courses and resources for students.
A language learning app that uses AI to personalize lessons based on the student's learning style and progress.
A test preparation app that provides practice tests and resources for standardized tests.
A college application app that helps students research and apply to colleges.
A scholarship search app that matches students with scholarships based on their qualifications.
A tutoring app that connects students with tutors for one-on-one sessions.
A study group app that allows students to collaborate and study together.
A note-taking app that allows students to take and organize notes for classes and assignments.
# Entertainment
A music streaming app that provides personalized playlists and recommendations.
A movie and TV streaming app that suggests movies and shows based on the user's viewing history.
A gaming app that provides multiplayer games and tournaments.
A book recommendation app that suggests books based on the user's reading history and preferences.
A social media app that connects users with similar interests and hobbies.
A podcast app that suggests podcasts based on the user's interests and listening history.
A virtual reality app that allows users to explore new places and experiences.
A sports app that provides live scores, news, and highlights for various sports.
A trivia app that provides daily quizzes and challenges.
A comedy app that provides stand-up comedy shows and sketches.
# Health and Wellness
A fitness app that creates customized workout plans and tracks progress using wearable technology.
A nutrition app that provides personalized meal plans and tracks calorie intake.
A meditation app that provides guided meditations and mindfulness exercises.
A sleep tracking app that monitors sleep patterns and provides tips for better sleep.
A mental health app that provides daily exercises and resources for managing anxiety and depression.
A women's health app that tracks menstrual cycles and provides health tips and resources.
A senior care app that tracks medication schedules and provides resources for senior health.
A first aid app that provides emergency first aid instructions and resources.
A telemedicine app that allows users to consult with doctors and healthcare professionals remotely.
A wellness app that provides resources for self-care and stress management.
# Travel and Transportation
A travel planning app that recommends destinations and activities based on the user's budget and interests.
A ride-sharing app that connects drivers with passengers for shared rides.
A parking app that helps users find and reserve parking spots in busy areas.
A flight booking app that compares prices and schedules for flights.
A car rental app that allows users to rent cars for short-term use.
A navigation app that provides real-time traffic updates and directions.
A hotel booking app that compares prices and amenities for hotels.
A travel insurance app that provides insurance coverage for travel-related incidents.
A language translation app that translates text and speech in real-time for travelers.
A travel journal app that allows users to document and share their travel experiences.
# Real Estate
A real estate listing app that provides listings for homes and apartments for sale or rent.
A mortgage calculator app that helps users calculate mortgage payments and affordability.
A home valuation app that provides estimates for home values based on location and features.
A property management app that helps landlords manage rental properties and tenants.
A home improvement app that provides resources and tips for home renovation and decoration.
A home buying app that provides resources and tips for first-time home buyers.
A real estate investment app that provides resources and tips for real estate investing.
A home inspection app that provides checklists and resources for home inspections.
A home insurance app that provides insurance coverage for homeowners.
A moving app that provides resources and tips for moving and relocation.
# Food and Beverage
A food delivery app that allows users to order food from local restaurants.
A recipe app that suggests meals based on ingredients and dietary restrictions.
A grocery delivery app that allows users to order groceries online and have them delivered.
A wine and spirits app that provides recommendations and reviews for wine and spirits.
A restaurant reservation app that allows users to make reservations at local restaurants.
A food waste reduction app that provides tips and resources for reducing food waste.
A meal kit delivery app that delivers pre-portioned ingredients and recipes for meals.
A coffee delivery app that delivers coffee and tea to users' homes or offices.
A food truck locator app that helps users find food trucks in their area.
A food safety app that provides resources and tips for food safety and hygiene.
`
  .replace(/\n#.*?\n/gm, "\n")
  .trim()
  .split("\n");

function extractCodeSnippets(text: string) {
  // Extract all code snippets in between ``` and ```
  return text.match(/(```[\s\S]*?```)/g) || [];
}

const zAppBlueprint = z.object({
  description: z.string(),
  schema: z.object({
    tables: z.record(z.record(z.string())),
  }),
});

type AppBlueprint = z.infer<typeof zAppBlueprint>;

export async function genDbSchemas() {
  const openai = createOpenAIClient();
  const appBlueprints: AppBlueprint[] = [];
  for (let i = 0; i < appIdeas.length; i += 10) {
    const chunk = appIdeas.slice(i, i + 10);
    const response = await openai.createChatCompletion({
      model: "gpt-3.5-turbo",
      temperature: 0,
      messages: [
        {
          role: "system",
          content: chatGptDefaultPrompt,
        },
        {
          role: "user",
          content: `
Here's a list of ideas for web apps. For each one, generate a plausible database schema. Use this JSON format:

${humanJson({
  tables: {
    order_details: {
      id: "string",
      order_id: "string",
      product_id: "string",
      quantity: "number",
      unit_price: "number",
      discount: "number",
      status: "string",
    },
    orders: {
      id: "string",
      customer_id: "string",
      status: "string",
      order_date: "string",
      required_date: "string",
      shipped_date: "string",
      ship_via: "string",
    },
  },
})}

Available types: string, number, boolean.

List of ideas:

${chunk.join("\n")}

Now generate database schemas for each.
`.trim(),
        },
      ],
    });
    showResponse(response);
    const schemas = extractCodeSnippets(
      ensure(getResponseContent(response), "")
    );
    const infos = strictZip(chunk, schemas).map(([description, schema]) =>
      zAppBlueprint.parse({
        description,
        schema: extractJsonFromString(schema),
      })
    );
    appBlueprints.push(...infos);
  }
  fs.writeFileSync(
    __dirname + "/app-blueprints.json",
    JSON.stringify(appBlueprints)
  );
}

export async function genExamples() {
  // Generate a list of examples.
  // Each example has: initial app state, a prompt describing a goal, and the commands to update the app to satisfy the goal.
  // Before starting, we first generate a small pool of app domains.
  // First, we randomly choose an app domain, and generate a random initial app state.
  // Then we generate a random set of changes.
  // Lastly, we generate the prompt--we do this in bulk using GPT.
  const blueprints: AppBlueprint[] = JSON.parse(
    fs.readFileSync(__dirname + "/app-blueprints.json", "utf8")
  ).map((x) => zAppBlueprint.parse(x));
  const numExamples = 1;
  const words = fs.readFileSync("/usr/share/dict/words", "utf8").split("\n");
  const rand = random.clone("");

  function sample<T>(xs: T[]) {
    return ensure(rand.choice(xs), "");
  }
  function randName() {
    return sample(words);
  }
  function randEntry<T>(xs: Record<string, T>): [string, T] {
    const k = sample(Object.keys(xs));
    return [k, xs[k]];
  }

  for (let i = 0; i < numExamples; i++) {
    const tables = sample(blueprints).schema.tables;
    const dataSourceName = randName();
    const rootSummary: RootSummary = {
      availableComponents: baseComponents,
      availableDataSources: [
        {
          name: dataSourceName,
          type: "postgres",
          tables: tables as any,
        },
      ],
      allPages: ["/"],
      currentPage: {
        name: "/",
        path: "/",
        $props: [],
        $state: [], // TODO
        $queries: Object.fromEntries(
          range(rand.int(3)).map((): [string, ToyDataSourceOp] => {
            const [table] = randEntry(tables);
            // TODO more interesting queries
            return [
              table,
              {
                opType: "sql",
                dataSource: dataSourceName,
                code: `SELECT * FROM ${table}`,
              },
            ];
          })
        ),
        tree: [
          {
            component: "PageLayout",
            name: "pageLayout",
            props: {
              children: [
                {
                  component: "div",
                  name: "content",
                  props: {},
                },
              ],
            },
          },
        ],
      },
    };
  }
}

export async function genExamplesMain() {
  await run(
    subcommands({
      name: "gen-examples",
      cmds: {
        "db-schemas": command({
          name: "db-schemas",
          args: {},
          handler: genDbSchemas,
        }),
        "gen-examples": command({
          name: "gen-examples",
          args: {},
          handler: genExamples,
        }),
      },
    }),
    process.argv.slice(2)
  );
}

spawn(genExamplesMain());
