[{"description": "A meal planning app that generates weekly menus based on dietary restrictions and preferences.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "dietary_restrictions": "string", "preferences": "string"}, "recipes": {"id": "string", "name": "string", "description": "string", "ingredients": "string", "instructions": "string", "image_url": "string"}, "meal_plans": {"id": "string", "user_id": "string", "start_date": "string", "end_date": "string"}, "meal_plan_details": {"id": "string", "meal_plan_id": "string", "recipe_id": "string", "day": "string", "meal_type": "string"}}}}, {"description": "A virtual wardrobe app that helps users create outfits from their existing clothes and suggests new items to purchase.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "clothing_items": {"id": "string", "user_id": "string", "name": "string", "description": "string", "image_url": "string", "category": "string", "color": "string", "size": "string"}, "outfits": {"id": "string", "user_id": "string", "name": "string", "description": "string", "image_url": "string"}, "outfit_details": {"id": "string", "outfit_id": "string", "clothing_item_id": "string"}}}}, {"description": "A language learning app that uses AI to personalize lessons based on the user's learning style and progress.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "learning_style": "string"}, "lessons": {"id": "string", "name": "string", "description": "string", "content": "string", "language": "string"}, "user_lessons": {"id": "string", "user_id": "string", "lesson_id": "string", "progress": "number"}}}}, {"description": "A mental health app that provides daily exercises and resources for managing anxiety and depression.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "exercises": {"id": "string", "name": "string", "description": "string", "content": "string"}, "user_exercises": {"id": "string", "user_id": "string", "exercise_id": "string", "completed": "boolean"}}}}, {"description": "A travel planning app that recommends destinations and activities based on the user's budget and interests.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "destinations": {"id": "string", "name": "string", "description": "string", "image_url": "string", "budget": "number"}, "activities": {"id": "string", "name": "string", "description": "string", "image_url": "string", "destination_id": "string"}, "user_destinations": {"id": "string", "user_id": "string", "destination_id": "string", "start_date": "string", "end_date": "string"}, "user_activities": {"id": "string", "user_id": "string", "activity_id": "string", "date": "string"}}}}, {"description": "A fitness app that creates customized workout plans and tracks progress using wearable technology.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "workouts": {"id": "string", "name": "string", "description": "string", "image_url": "string"}, "exercises": {"id": "string", "name": "string", "description": "string", "image_url": "string", "workout_id": "string"}, "user_workouts": {"id": "string", "user_id": "string", "workout_id": "string", "start_date": "string", "end_date": "string"}, "user_exercises": {"id": "string", "user_id": "string", "exercise_id": "string", "sets": "number", "reps": "number", "weight": "number", "date": "string"}}}}, {"description": "A home automation app that allows users to control their smart devices from a single interface.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "devices": {"id": "string", "name": "string", "description": "string", "type": "string", "status": "boolean"}, "user_devices": {"id": "string", "user_id": "string", "device_id": "string"}}}}, {"description": "A personal finance app that helps users track expenses, set budgets, and invest in stocks and cryptocurrencies.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "expenses": {"id": "string", "user_id": "string", "name": "string", "description": "string", "amount": "number", "date": "string"}, "budgets": {"id": "string", "user_id": "string", "name": "string", "description": "string", "amount": "number"}, "stocks": {"id": "string", "name": "string", "symbol": "string", "price": "number"}, "user_stocks": {"id": "string", "user_id": "string", "stock_id": "string", "quantity": "number"}, "cryptocurrencies": {"id": "string", "name": "string", "symbol": "string", "price": "number"}, "user_cryptocurrencies": {"id": "string", "user_id": "string", "cryptocurrency_id": "string", "quantity": "number"}}}}, {"description": "A dating app that uses AI to match users based on personality traits and interests.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "personality_traits": "string", "interests": "string"}, "matches": {"id": "string", "user_id": "string", "match_id": "string", "status": "string"}}}}, {"description": "A virtual interior design app that allows users to visualize furniture and decor in their home before making purchases.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "furniture": {"id": "string", "name": "string", "description": "string", "image_url": "string", "category": "string", "color": "string", "dimensions": "string"}, "user_rooms": {"id": "string", "user_id": "string", "name": "string", "description": "string", "image_url": "string"}, "room_details": {"id": "string", "room_id": "string", "furniture_id": "string", "x": "number", "y": "number"}}}}, {"description": "A project management app that tracks tasks, deadlines, and team members' progress.", "schema": {"tables": {"tasks": {"id": "string", "project_id": "string", "name": "string", "description": "string", "assigned_to": "string", "due_date": "string", "status": "string"}, "projects": {"id": "string", "name": "string", "description": "string", "start_date": "string", "end_date": "string", "status": "string"}, "team_members": {"id": "string", "name": "string", "email": "string", "role": "string"}}}}, {"description": "A customer relationship management (CRM) app that organizes customer data and interactions.", "schema": {"tables": {"customers": {"id": "string", "name": "string", "email": "string", "phone": "string", "address": "string", "company": "string"}, "interactions": {"id": "string", "customer_id": "string", "date": "string", "type": "string", "description": "string"}}}}, {"description": "A human resources app that streamlines the hiring process and manages employee benefits and payroll.", "schema": {"tables": {"employees": {"id": "string", "name": "string", "email": "string", "phone": "string", "address": "string", "position": "string", "salary": "number", "start_date": "string", "end_date": "string", "status": "string"}, "benefits": {"id": "string", "employee_id": "string", "type": "string", "description": "string"}, "payroll": {"id": "string", "employee_id": "string", "date": "string", "amount": "number"}}}}, {"description": "A marketing automation app that creates and tracks email campaigns, social media posts, and ads.", "schema": {"tables": {"campaigns": {"id": "string", "name": "string", "description": "string", "start_date": "string", "end_date": "string", "status": "string"}, "emails": {"id": "string", "campaign_id": "string", "subject": "string", "body": "string", "recipient": "string", "status": "string"}, "social_media_posts": {"id": "string", "campaign_id": "string", "platform": "string", "message": "string", "status": "string"}, "ads": {"id": "string", "campaign_id": "string", "platform": "string", "type": "string", "message": "string", "status": "string"}}}}, {"description": "A supply chain management app that tracks inventory, orders, and shipping.", "schema": {"tables": {"products": {"id": "string", "name": "string", "description": "string", "price": "number", "quantity": "number", "status": "string"}, "orders": {"id": "string", "product_id": "string", "quantity": "number", "order_date": "string", "required_date": "string", "shipped_date": "string", "status": "string"}, "suppliers": {"id": "string", "name": "string", "email": "string", "phone": "string", "address": "string"}}}}, {"description": "A data analytics app that visualizes and analyzes business data to identify trends and opportunities.", "schema": {"tables": {"data": {"id": "string", "date": "string", "metric_1": "number", "metric_2": "number", "metric_3": "number"}}}}, {"description": "A virtual meeting app that allows remote teams to collaborate and communicate in real-time.", "schema": {"tables": {"meetings": {"id": "string", "name": "string", "description": "string", "start_time": "string", "end_time": "string", "status": "string"}, "participants": {"id": "string", "meeting_id": "string", "name": "string", "email": "string", "role": "string"}, "messages": {"id": "string", "meeting_id": "string", "participant_id": "string", "message": "string", "timestamp": "string"}}}}, {"description": "A legal document management app that stores and organizes contracts, agreements, and other legal documents.", "schema": {"tables": {"documents": {"id": "string", "name": "string", "description": "string", "type": "string", "status": "string"}, "contracts": {"id": "string", "document_id": "string", "party_1": "string", "party_2": "string", "start_date": "string", "end_date": "string", "status": "string"}}}}, {"description": "A cybersecurity app that monitors and protects against cyber threats and data breaches.", "schema": {"tables": {"threats": {"id": "string", "name": "string", "description": "string", "severity": "string", "status": "string"}, "incidents": {"id": "string", "threat_id": "string", "date": "string", "description": "string", "status": "string"}, "users": {"id": "string", "name": "string", "email": "string", "role": "string"}}}}, {"description": "A customer service app that provides chatbots and live support to assist customers with inquiries and issues.", "schema": {"tables": {"tickets": {"id": "string", "customer_id": "string", "date": "string", "description": "string", "status": "string"}, "chatbots": {"id": "string", "ticket_id": "string", "message": "string", "timestamp": "string"}, "live_support": {"id": "string", "ticket_id": "string", "name": "string", "email": "string", "message": "string", "timestamp": "string"}}}}, {"description": "A time tracking app that helps employees log hours and manage their workload.", "schema": {"tables": {"time_entries": {"id": "string", "user_id": "string", "project_id": "string", "task_id": "string", "start_time": "string", "end_time": "string", "duration": "number", "description": "string"}, "projects": {"id": "string", "name": "string", "description": "string"}, "tasks": {"id": "string", "name": "string", "description": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A knowledge management app that stores and organizes company information and resources.", "schema": {"tables": {"documents": {"id": "string", "title": "string", "content": "string", "category_id": "string", "created_at": "string", "updated_at": "string"}, "categories": {"id": "string", "name": "string", "description": "string"}}}}, {"description": "A performance management app that tracks employee goals and provides feedback and coaching.", "schema": {"tables": {"goals": {"id": "string", "user_id": "string", "title": "string", "description": "string", "due_date": "string", "status": "string"}, "feedback": {"id": "string", "user_id": "string", "manager_id": "string", "title": "string", "description": "string", "created_at": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A training and development app that provides online courses and resources for employee learning.", "schema": {"tables": {"courses": {"id": "string", "title": "string", "description": "string", "category_id": "string", "created_at": "string", "updated_at": "string"}, "categories": {"id": "string", "name": "string", "description": "string"}, "enrollments": {"id": "string", "user_id": "string", "course_id": "string", "enrollment_date": "string", "status": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A scheduling app that helps managers create and manage employee schedules.", "schema": {"tables": {"shifts": {"id": "string", "user_id": "string", "start_time": "string", "end_time": "string", "status": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A task management app that assigns and tracks tasks for individual employees or teams.", "schema": {"tables": {"tasks": {"id": "string", "user_id": "string", "project_id": "string", "title": "string", "description": "string", "due_date": "string", "status": "string"}, "projects": {"id": "string", "name": "string", "description": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A communication app that allows employees to chat and collaborate in real-time.", "schema": {"tables": {"messages": {"id": "string", "sender_id": "string", "receiver_id": "string", "content": "string", "created_at": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A file sharing app that allows employees to share and collaborate on documents and files.", "schema": {"tables": {"files": {"id": "string", "name": "string", "description": "string", "user_id": "string", "created_at": "string", "updated_at": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A feedback and survey app that collects feedback from employees and customers.", "schema": {"tables": {"feedback": {"id": "string", "user_id": "string", "title": "string", "description": "string", "created_at": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A benefits management app that allows employees to manage their benefits and enroll in new ones.", "schema": {"tables": {"benefits": {"id": "string", "user_id": "string", "name": "string", "description": "string", "enrollment_date": "string", "status": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A weather app that provides hyper-local weather forecasts and alerts.", "schema": {"tables": {"weather_forecast": {"id": "string", "latitude": "number", "longitude": "number", "temperature": "number", "humidity": "number", "wind_speed": "number", "wind_direction": "string", "pressure": "number", "description": "string", "icon": "string", "alert": "boolean"}}}}, {"description": "A news app that curates articles based on the user's interests and reading history.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "articles": {"id": "string", "title": "string", "description": "string", "url": "string", "image_url": "string", "published_at": "string"}, "user_articles": {"id": "string", "user_id": "string", "article_id": "string", "read": "boolean", "favorite": "boolean"}, "interests": {"id": "string", "name": "string"}, "user_interests": {"id": "string", "user_id": "string", "interest_id": "string"}}}}, {"description": "A recipe app that suggests meals based on ingredients and dietary restrictions.", "schema": {"tables": {"recipes": {"id": "string", "name": "string", "description": "string", "image_url": "string", "prep_time": "number", "cook_time": "number", "servings": "number", "calories": "number", "dietary_restrictions": "string"}, "ingredients": {"id": "string", "name": "string"}, "recipe_ingredients": {"id": "string", "recipe_id": "string", "ingredient_id": "string", "quantity": "number", "unit": "string"}}}}, {"description": "A language translation app that translates text and speech in real-time.", "schema": {"tables": {"translations": {"id": "string", "source_language": "string", "target_language": "string", "source_text": "string", "target_text": "string"}}}}, {"description": "A meditation app that provides guided meditations and mindfulness exercises.", "schema": {"tables": {"meditations": {"id": "string", "name": "string", "description": "string", "audio_url": "string", "duration": "number"}, "user_meditations": {"id": "string", "user_id": "string", "meditation_id": "string", "completed": "boolean"}}}}, {"description": "A pet care app that tracks vet appointments, medication schedules, and pet behavior.", "schema": {"tables": {"pets": {"id": "string", "name": "string", "species": "string", "breed": "string", "age": "number", "weight": "number", "gender": "string"}, "appointments": {"id": "string", "pet_id": "string", "vet_id": "string", "date": "string", "reason": "string"}, "medications": {"id": "string", "name": "string", "dosage": "string", "frequency": "string"}, "pet_medications": {"id": "string", "pet_id": "string", "medication_id": "string", "start_date": "string", "end_date": "string"}, "behaviors": {"id": "string", "name": "string"}, "pet_behaviors": {"id": "string", "pet_id": "string", "behavior_id": "string", "description": "string"}}}}, {"description": "A gardening app that provides tips and resources for growing plants and vegetables.", "schema": {"tables": {"plants": {"id": "string", "name": "string", "description": "string", "image_url": "string", "light_requirement": "string", "water_requirement": "string", "soil_requirement": "string", "temperature_requirement": "string"}, "user_plants": {"id": "string", "user_id": "string", "plant_id": "string", "date_planted": "string", "date_harvested": "string"}, "resources": {"id": "string", "name": "string", "description": "string", "url": "string"}}}}, {"description": "A home cleaning app that creates cleaning schedules and provides cleaning tips and tricks.", "schema": {"tables": {"cleaning_tasks": {"id": "string", "name": "string", "description": "string", "frequency": "string"}, "user_tasks": {"id": "string", "user_id": "string", "task_id": "string", "last_completed": "string", "next_due": "string"}, "cleaning_tips": {"id": "string", "name": "string", "description": "string"}}}}, {"description": "A personal safety app that provides emergency alerts and resources.", "schema": {"tables": {"emergency_contacts": {"id": "string", "name": "string", "phone_number": "string", "relationship": "string"}, "emergency_alerts": {"id": "string", "user_id": "string", "emergency_contact_id": "string", "message": "string", "location": "string", "timestamp": "string"}, "resources": {"id": "string", "name": "string", "description": "string", "url": "string"}}}}, {"description": "A fashion app that provides style advice and recommendations based on body type and preferences.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "styles": {"id": "string", "name": "string", "description": "string"}, "user_styles": {"id": "string", "user_id": "string", "style_id": "string"}, "body_types": {"id": "string", "name": "string", "description": "string"}, "user_body_types": {"id": "string", "user_id": "string", "body_type_id": "string"}}}}, {"description": "A virtual classroom app that allows teachers to conduct online classes and assignments.", "schema": {"tables": {"classes": {"id": "string", "teacher_id": "string", "class_name": "string", "class_description": "string", "class_start_time": "string", "class_end_time": "string"}, "assignments": {"id": "string", "class_id": "string", "assignment_name": "string", "assignment_description": "string", "assignment_due_date": "string"}, "students": {"id": "string", "class_id": "string", "student_name": "string", "student_email": "string"}, "attendance": {"id": "string", "class_id": "string", "student_id": "string", "attendance_date": "string", "attendance_status": "string"}}}}, {"description": "A student management app that tracks student progress and grades.", "schema": {"tables": {"students": {"id": "string", "student_name": "string", "student_email": "string", "student_phone": "string", "student_address": "string"}, "courses": {"id": "string", "course_name": "string", "course_description": "string", "course_start_date": "string", "course_end_date": "string"}, "enrollments": {"id": "string", "student_id": "string", "course_id": "string", "enrollment_date": "string", "enrollment_status": "string", "grade": "number"}}}}, {"description": "A learning management app that provides online courses and resources for students.", "schema": {"tables": {"courses": {"id": "string", "course_name": "string", "course_description": "string", "course_start_date": "string", "course_end_date": "string"}, "lessons": {"id": "string", "course_id": "string", "lesson_name": "string", "lesson_description": "string", "lesson_content": "string"}, "enrollments": {"id": "string", "student_id": "string", "course_id": "string", "enrollment_date": "string", "enrollment_status": "string", "grade": "number"}}}}, {"description": "A language learning app that uses AI to personalize lessons based on the student's learning style and progress.", "schema": {"tables": {"students": {"id": "string", "student_name": "string", "student_email": "string", "student_phone": "string", "student_address": "string"}, "lessons": {"id": "string", "lesson_name": "string", "lesson_description": "string", "lesson_content": "string"}, "enrollments": {"id": "string", "student_id": "string", "lesson_id": "string", "enrollment_date": "string", "enrollment_status": "string", "grade": "number"}}}}, {"description": "A test preparation app that provides practice tests and resources for standardized tests.", "schema": {"tables": {"tests": {"id": "string", "test_name": "string", "test_description": "string", "test_date": "string"}, "questions": {"id": "string", "test_id": "string", "question_text": "string", "answer_choices": "string", "correct_answer": "string"}, "test_results": {"id": "string", "student_id": "string", "test_id": "string", "test_date": "string", "score": "number"}}}}, {"description": "A college application app that helps students research and apply to colleges.", "schema": {"tables": {"colleges": {"id": "string", "college_name": "string", "college_description": "string", "college_location": "string", "college_website": "string"}, "applications": {"id": "string", "student_id": "string", "college_id": "string", "application_date": "string", "application_status": "string"}}}}, {"description": "A scholarship search app that matches students with scholarships based on their qualifications.", "schema": {"tables": {"scholarships": {"id": "string", "scholarship_name": "string", "scholarship_description": "string", "scholarship_amount": "number", "scholarship_deadline": "string"}, "qualifications": {"id": "string", "scholarship_id": "string", "qualification_name": "string", "qualification_value": "string"}, "applications": {"id": "string", "student_id": "string", "scholarship_id": "string", "application_date": "string", "application_status": "string"}}}}, {"description": "A tutoring app that connects students with tutors for one-on-one sessions.", "schema": {"tables": {"tutors": {"id": "string", "tutor_name": "string", "tutor_email": "string", "tutor_phone": "string", "tutor_address": "string"}, "students": {"id": "string", "student_name": "string", "student_email": "string", "student_phone": "string", "student_address": "string"}, "sessions": {"id": "string", "tutor_id": "string", "student_id": "string", "session_date": "string", "session_duration": "number", "session_price": "number"}}}}, {"description": "A study group app that allows students to collaborate and study together.", "schema": {"tables": {"groups": {"id": "string", "group_name": "string", "group_description": "string"}, "students": {"id": "string", "student_name": "string", "student_email": "string", "student_phone": "string", "student_address": "string"}, "group_members": {"id": "string", "group_id": "string", "student_id": "string"}, "group_sessions": {"id": "string", "group_id": "string", "session_date": "string", "session_duration": "number", "session_location": "string"}}}}, {"description": "A note-taking app that allows students to take and organize notes for classes and assignments.", "schema": {"tables": {"notes": {"id": "string", "note_title": "string", "note_content": "string", "note_date": "string"}, "folders": {"id": "string", "folder_name": "string", "folder_description": "string"}, "notes_folders": {"id": "string", "note_id": "string", "folder_id": "string"}}}}, {"description": "A music streaming app that provides personalized playlists and recommendations.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "playlists": {"id": "string", "user_id": "string", "name": "string", "created_at": "string"}, "songs": {"id": "string", "name": "string", "artist": "string", "album": "string", "duration": "number", "genre": "string"}, "playlist_songs": {"id": "string", "playlist_id": "string", "song_id": "string", "created_at": "string"}, "song_plays": {"id": "string", "user_id": "string", "song_id": "string", "played_at": "string"}}}}, {"description": "A movie and TV streaming app that suggests movies and shows based on the user's viewing history.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "movies": {"id": "string", "title": "string", "director": "string", "genre": "string", "release_date": "string", "rating": "number"}, "shows": {"id": "string", "title": "string", "creator": "string", "genre": "string", "release_date": "string", "rating": "number"}, "user_movies": {"id": "string", "user_id": "string", "movie_id": "string", "watched_at": "string"}, "user_shows": {"id": "string", "user_id": "string", "show_id": "string", "watched_at": "string"}}}}, {"description": "A gaming app that provides multiplayer games and tournaments.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "games": {"id": "string", "name": "string", "genre": "string", "platform": "string", "release_date": "string", "rating": "number"}, "user_games": {"id": "string", "user_id": "string", "game_id": "string", "played_at": "string"}, "tournaments": {"id": "string", "game_id": "string", "start_date": "string", "end_date": "string", "prize": "number"}, "tournament_players": {"id": "string", "tournament_id": "string", "user_id": "string", "score": "number"}}}}, {"description": "A book recommendation app that suggests books based on the user's reading history and preferences.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "books": {"id": "string", "title": "string", "author": "string", "genre": "string", "published_date": "string", "rating": "number"}, "user_books": {"id": "string", "user_id": "string", "book_id": "string", "read_at": "string"}, "book_recommendations": {"id": "string", "user_id": "string", "book_id": "string", "recommended_at": "string"}}}}, {"description": "A social media app that connects users with similar interests and hobbies.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "interests": {"id": "string", "name": "string"}, "user_interests": {"id": "string", "user_id": "string", "interest_id": "string"}, "connections": {"id": "string", "user_id": "string", "connection_id": "string", "created_at": "string"}, "posts": {"id": "string", "user_id": "string", "content": "string", "created_at": "string"}, "post_likes": {"id": "string", "user_id": "string", "post_id": "string", "liked_at": "string"}}}}, {"description": "A podcast app that suggests podcasts based on the user's interests and listening history.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "podcasts": {"id": "string", "title": "string", "host": "string", "genre": "string", "release_date": "string", "rating": "number"}, "user_podcasts": {"id": "string", "user_id": "string", "podcast_id": "string", "listened_at": "string"}, "podcast_recommendations": {"id": "string", "user_id": "string", "podcast_id": "string", "recommended_at": "string"}}}}, {"description": "A virtual reality app that allows users to explore new places and experiences.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "experiences": {"id": "string", "name": "string", "description": "string", "location": "string", "rating": "number"}, "user_experiences": {"id": "string", "user_id": "string", "experience_id": "string", "visited_at": "string"}}}}, {"description": "A sports app that provides live scores, news, and highlights for various sports.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "sports": {"id": "string", "name": "string", "league": "string", "rating": "number"}, "games": {"id": "string", "sport_id": "string", "team1": "string", "team2": "string", "start_time": "string", "end_time": "string", "score1": "number", "score2": "number"}, "user_games": {"id": "string", "user_id": "string", "game_id": "string", "watched_at": "string"}}}}, {"description": "A trivia app that provides daily quizzes and challenges.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "questions": {"id": "string", "content": "string", "answer": "string", "category": "string", "difficulty": "number"}, "user_answers": {"id": "string", "user_id": "string", "question_id": "string", "answer": "string", "answered_at": "string"}}}}, {"description": "A comedy app that provides stand-up comedy shows and sketches.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "created_at": "string"}, "shows": {"id": "string", "title": "string", "creator": "string", "genre": "string", "release_date": "string", "rating": "number"}, "user_shows": {"id": "string", "user_id": "string", "show_id": "string", "watched_at": "string"}}}}, {"description": "A fitness app that creates customized workout plans and tracks progress using wearable technology.", "schema": {"tables": {"workout_plans": {"id": "string", "user_id": "string", "plan_name": "string", "plan_description": "string", "plan_duration": "number", "plan_difficulty": "string"}, "workout_sessions": {"id": "string", "user_id": "string", "workout_plan_id": "string", "session_date": "string", "session_duration": "number", "session_calories_burned": "number"}, "wearable_devices": {"id": "string", "user_id": "string", "device_name": "string", "device_type": "string", "device_serial_number": "string"}}}}, {"description": "A nutrition app that provides personalized meal plans and tracks calorie intake.", "schema": {"tables": {"meal_plans": {"id": "string", "user_id": "string", "plan_name": "string", "plan_description": "string", "plan_duration": "number", "plan_calorie_goal": "number"}, "meals": {"id": "string", "user_id": "string", "meal_plan_id": "string", "meal_name": "string", "meal_description": "string", "meal_calories": "number"}, "food_items": {"id": "string", "food_name": "string", "food_description": "string", "food_calories": "number"}}}}, {"description": "A meditation app that provides guided meditations and mindfulness exercises.", "schema": {"tables": {"meditation_sessions": {"id": "string", "user_id": "string", "session_date": "string", "session_duration": "number", "session_mood": "string"}, "meditation_exercises": {"id": "string", "exercise_name": "string", "exercise_description": "string", "exercise_duration": "number"}}}}, {"description": "A sleep tracking app that monitors sleep patterns and provides tips for better sleep.", "schema": {"tables": {"sleep_records": {"id": "string", "user_id": "string", "record_date": "string", "record_duration": "number", "record_quality": "string"}, "sleep_tips": {"id": "string", "tip_name": "string", "tip_description": "string"}}}}, {"description": "A mental health app that provides daily exercises and resources for managing anxiety and depression.", "schema": {"tables": {"daily_exercises": {"id": "string", "user_id": "string", "exercise_name": "string", "exercise_description": "string", "exercise_duration": "number"}, "resources": {"id": "string", "resource_name": "string", "resource_description": "string", "resource_url": "string"}}}}, {"description": "A women's health app that tracks menstrual cycles and provides health tips and resources.", "schema": {"tables": {"menstrual_cycles": {"id": "string", "user_id": "string", "cycle_start_date": "string", "cycle_end_date": "string", "cycle_length": "number"}, "health_tips": {"id": "string", "tip_name": "string", "tip_description": "string"}}}}, {"description": "A senior care app that tracks medication schedules and provides resources for senior health.", "schema": {"tables": {"medication_schedules": {"id": "string", "user_id": "string", "medication_name": "string", "medication_dosage": "string", "medication_frequency": "string", "medication_start_date": "string", "medication_end_date": "string"}, "health_resources": {"id": "string", "resource_name": "string", "resource_description": "string", "resource_url": "string"}}}}, {"description": "A first aid app that provides emergency first aid instructions and resources.", "schema": {"tables": {"emergency_instructions": {"id": "string", "instruction_name": "string", "instruction_description": "string"}, "emergency_resources": {"id": "string", "resource_name": "string", "resource_description": "string", "resource_url": "string"}}}}, {"description": "A telemedicine app that allows users to consult with doctors and healthcare professionals remotely.", "schema": {"tables": {"consultations": {"id": "string", "user_id": "string", "doctor_id": "string", "consultation_date": "string", "consultation_duration": "number", "consultation_notes": "string"}, "doctors": {"id": "string", "doctor_name": "string", "doctor_specialty": "string", "doctor_license_number": "string"}}}}, {"description": "A wellness app that provides resources for self-care and stress management.", "schema": {"tables": {"self_care_activities": {"id": "string", "user_id": "string", "activity_name": "string", "activity_description": "string", "activity_duration": "number"}, "stress_management_resources": {"id": "string", "resource_name": "string", "resource_description": "string", "resource_url": "string"}}}}, {"description": "A travel planning app that recommends destinations and activities based on the user's budget and interests.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "budget": "number", "interests": "string"}, "destinations": {"id": "string", "name": "string", "description": "string", "image_url": "string", "budget": "number", "interests": "string"}, "activities": {"id": "string", "name": "string", "description": "string", "image_url": "string", "budget": "number", "interests": "string"}, "user_destinations": {"id": "string", "user_id": "string", "destination_id": "string"}, "user_activities": {"id": "string", "user_id": "string", "activity_id": "string"}}}}, {"description": "A ride-sharing app that connects drivers with passengers for shared rides.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "phone_number": "string"}, "drivers": {"id": "string", "user_id": "string", "car_make": "string", "car_model": "string", "car_year": "number", "license_plate": "string"}, "passengers": {"id": "string", "user_id": "string"}, "rides": {"id": "string", "driver_id": "string", "passenger_id": "string", "start_location": "string", "end_location": "string", "start_time": "string", "end_time": "string", "price": "number"}}}}, {"description": "A parking app that helps users find and reserve parking spots in busy areas.", "schema": {"tables": {"parking_lots": {"id": "string", "name": "string", "address": "string", "latitude": "number", "longitude": "number", "capacity": "number"}, "parking_spots": {"id": "string", "parking_lot_id": "string", "spot_number": "string", "is_reserved": "boolean"}, "reservations": {"id": "string", "user_id": "string", "parking_spot_id": "string", "start_time": "string", "end_time": "string"}}}}, {"description": "A flight booking app that compares prices and schedules for flights.", "schema": {"tables": {"airlines": {"id": "string", "name": "string", "logo_url": "string"}, "flights": {"id": "string", "airline_id": "string", "departure_airport": "string", "arrival_airport": "string", "departure_time": "string", "arrival_time": "string", "price": "number"}, "bookings": {"id": "string", "user_id": "string", "flight_id": "string", "passenger_name": "string", "passenger_email": "string", "passenger_phone_number": "string"}}}}, {"description": "A car rental app that allows users to rent cars for short-term use.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string", "phone_number": "string"}, "cars": {"id": "string", "make": "string", "model": "string", "year": "number", "price_per_day": "number", "is_available": "boolean"}, "rentals": {"id": "string", "user_id": "string", "car_id": "string", "start_date": "string", "end_date": "string", "total_price": "number"}}}}, {"description": "A navigation app that provides real-time traffic updates and directions.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "locations": {"id": "string", "name": "string", "address": "string", "latitude": "number", "longitude": "number"}, "routes": {"id": "string", "user_id": "string", "start_location_id": "string", "end_location_id": "string", "distance": "number", "duration": "number"}}}}, {"description": "A hotel booking app that compares prices and amenities for hotels.", "schema": {"tables": {"hotels": {"id": "string", "name": "string", "address": "string", "latitude": "number", "longitude": "number", "price_per_night": "number", "amenities": "string"}, "bookings": {"id": "string", "user_id": "string", "hotel_id": "string", "check_in_date": "string", "check_out_date": "string", "total_price": "number"}}}}, {"description": "A travel insurance app that provides insurance coverage for travel-related incidents.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "policies": {"id": "string", "user_id": "string", "start_date": "string", "end_date": "string", "coverage_amount": "number"}, "claims": {"id": "string", "policy_id": "string", "claim_date": "string", "claim_amount": "number", "description": "string"}}}}, {"description": "A language translation app that translates text and speech in real-time for travelers.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "translations": {"id": "string", "user_id": "string", "source_language": "string", "target_language": "string", "source_text": "string", "target_text": "string"}}}}, {"description": "A travel journal app that allows users to document and share their travel experiences.", "schema": {"tables": {"users": {"id": "string", "name": "string", "email": "string", "password": "string"}, "trips": {"id": "string", "user_id": "string", "destination": "string", "start_date": "string", "end_date": "string"}, "entries": {"id": "string", "trip_id": "string", "title": "string", "content": "string", "image_url": "string"}}}}, {"description": "A real estate listing app that provides listings for homes and apartments for sale or rent.", "schema": {"tables": {"listings": {"id": "string", "title": "string", "description": "string", "price": "number", "bedrooms": "number", "bathrooms": "number", "sqft": "number", "address": "string", "city": "string", "state": "string", "zip": "string", "type": "string", "status": "string"}}}}, {"description": "A mortgage calculator app that helps users calculate mortgage payments and affordability.", "schema": {"tables": {"mortgage": {"id": "string", "home_price": "number", "down_payment": "number", "interest_rate": "number", "loan_term": "number", "property_tax": "number", "insurance": "number", "pmi": "number", "monthly_payment": "number", "total_payment": "number"}}}}, {"description": "A home valuation app that provides estimates for home values based on location and features.", "schema": {"tables": {"valuation": {"id": "string", "address": "string", "city": "string", "state": "string", "zip": "string", "bedrooms": "number", "bathrooms": "number", "sqft": "number", "estimated_value": "number"}}}}, {"description": "A property management app that helps landlords manage rental properties and tenants.", "schema": {"tables": {"properties": {"id": "string", "address": "string", "city": "string", "state": "string", "zip": "string", "bedrooms": "number", "bathrooms": "number", "sqft": "number", "rent": "number", "status": "string"}, "tenants": {"id": "string", "property_id": "string", "name": "string", "email": "string", "phone": "string", "lease_start": "string", "lease_end": "string", "rent": "number", "status": "string"}}}}, {"description": "A home improvement app that provides resources and tips for home renovation and decoration.", "schema": {"tables": {"projects": {"id": "string", "title": "string", "description": "string", "category": "string", "status": "string"}, "resources": {"id": "string", "project_id": "string", "title": "string", "description": "string", "url": "string"}}}}, {"description": "A home buying app that provides resources and tips for first-time home buyers.", "schema": {"tables": {"resources": {"id": "string", "title": "string", "description": "string", "category": "string", "status": "string"}}}}, {"description": "A real estate investment app that provides resources and tips for real estate investing.", "schema": {"tables": {"properties": {"id": "string", "address": "string", "city": "string", "state": "string", "zip": "string", "bedrooms": "number", "bathrooms": "number", "sqft": "number", "purchase_price": "number", "current_value": "number", "status": "string"}, "investments": {"id": "string", "property_id": "string", "investor_id": "string", "investment_amount": "number", "investment_date": "string", "status": "string"}, "investors": {"id": "string", "name": "string", "email": "string", "phone": "string", "status": "string"}}}}, {"description": "A home inspection app that provides checklists and resources for home inspections.", "schema": {"tables": {"checklists": {"id": "string", "title": "string", "description": "string", "category": "string", "status": "string"}}}}, {"description": "A home insurance app that provides insurance coverage for homeowners.", "schema": {"tables": {"policies": {"id": "string", "property_id": "string", "policy_number": "string", "coverage_amount": "number", "premium": "number", "start_date": "string", "end_date": "string", "status": "string"}}}}, {"description": "A moving app that provides resources and tips for moving and relocation.", "schema": {"tables": {"checklists": {"id": "string", "title": "string", "description": "string", "category": "string", "status": "string"}, "resources": {"id": "string", "title": "string", "description": "string", "url": "string"}}}}, {"description": "A food delivery app that allows users to order food from local restaurants.", "schema": {"tables": {"orders": {"id": "string", "customer_id": "string", "status": "string", "order_date": "string", "required_date": "string", "shipped_date": "string", "ship_via": "string"}, "order_details": {"id": "string", "order_id": "string", "product_id": "string", "quantity": "number", "unit_price": "number", "discount": "number", "status": "string"}, "restaurants": {"id": "string", "name": "string", "address": "string", "phone": "string", "email": "string"}, "menu_items": {"id": "string", "restaurant_id": "string", "name": "string", "description": "string", "price": "number"}}}}, {"description": "A recipe app that suggests meals based on ingredients and dietary restrictions.", "schema": {"tables": {"recipes": {"id": "string", "name": "string", "description": "string", "instructions": "string", "image_url": "string"}, "ingredients": {"id": "string", "name": "string", "description": "string", "image_url": "string"}, "recipe_ingredients": {"id": "string", "recipe_id": "string", "ingredient_id": "string", "quantity": "number", "unit": "string"}, "dietary_restrictions": {"id": "string", "name": "string", "description": "string"}, "recipe_restrictions": {"id": "string", "recipe_id": "string", "restriction_id": "string"}}}}, {"description": "A grocery delivery app that allows users to order groceries online and have them delivered.", "schema": {"tables": {"orders": {"id": "string", "customer_id": "string", "status": "string", "order_date": "string", "required_date": "string", "shipped_date": "string", "ship_via": "string"}, "order_details": {"id": "string", "order_id": "string", "product_id": "string", "quantity": "number", "unit_price": "number", "discount": "number", "status": "string"}, "products": {"id": "string", "name": "string", "description": "string", "image_url": "string", "price": "number"}, "categories": {"id": "string", "name": "string", "description": "string"}, "product_categories": {"id": "string", "product_id": "string", "category_id": "string"}}}}, {"description": "A wine and spirits app that provides recommendations and reviews for wine and spirits.", "schema": {"tables": {"products": {"id": "string", "name": "string", "description": "string", "image_url": "string", "price": "number"}, "reviews": {"id": "string", "product_id": "string", "user_id": "string", "rating": "number", "comment": "string"}, "users": {"id": "string", "name": "string", "email": "string", "password": "string"}}}}, {"description": "A restaurant reservation app that allows users to make reservations at local restaurants.", "schema": {"tables": {"reservations": {"id": "string", "restaurant_id": "string", "customer_id": "string", "date": "string", "time": "string", "party_size": "number"}, "restaurants": {"id": "string", "name": "string", "address": "string", "phone": "string", "email": "string"}, "customers": {"id": "string", "name": "string", "email": "string", "phone": "string"}}}}, {"description": "A food waste reduction app that provides tips and resources for reducing food waste.", "schema": {"tables": {"tips": {"id": "string", "title": "string", "description": "string", "image_url": "string"}, "resources": {"id": "string", "title": "string", "description": "string", "url": "string"}}}}, {"description": "A meal kit delivery app that delivers pre-portioned ingredients and recipes for meals.", "schema": {"tables": {"orders": {"id": "string", "customer_id": "string", "status": "string", "order_date": "string", "required_date": "string", "shipped_date": "string", "ship_via": "string"}, "order_details": {"id": "string", "order_id": "string", "product_id": "string", "quantity": "number", "unit_price": "number", "discount": "number", "status": "string"}, "recipes": {"id": "string", "name": "string", "description": "string", "instructions": "string", "image_url": "string"}, "ingredients": {"id": "string", "name": "string", "description": "string", "image_url": "string"}, "recipe_ingredients": {"id": "string", "recipe_id": "string", "ingredient_id": "string", "quantity": "number", "unit": "string"}}}}, {"description": "A coffee delivery app that delivers coffee and tea to users' homes or offices.", "schema": {"tables": {"orders": {"id": "string", "customer_id": "string", "status": "string", "order_date": "string", "required_date": "string", "shipped_date": "string", "ship_via": "string"}, "order_details": {"id": "string", "order_id": "string", "product_id": "string", "quantity": "number", "unit_price": "number", "discount": "number", "status": "string"}, "products": {"id": "string", "name": "string", "description": "string", "image_url": "string", "price": "number"}}}}, {"description": "A food truck locator app that helps users find food trucks in their area.", "schema": {"tables": {"food_trucks": {"id": "string", "name": "string", "description": "string", "image_url": "string", "phone": "string", "email": "string", "address": "string", "latitude": "number", "longitude": "number"}}}}, {"description": "A food safety app that provides resources and tips for food safety and hygiene.", "schema": {"tables": {"tips": {"id": "string", "title": "string", "description": "string", "image_url": "string"}, "resources": {"id": "string", "title": "string", "description": "string", "url": "string"}}}}]