import type { Falsy } from "@/wab/commons/types";
import { CopilotChatEntry } from "@/wab/shared/ApiSchema";
import { ensure, filterFalsy, unexpected } from "@/wab/shared/common";
import {
  Bookkeeping,
  getCurrentComponent,
  RootSummary,
  Summarizer,
} from "@/wab/shared/copilot/internal/prompt-summarizer";
import { getJsonResponse } from "@/wab/shared/copilot/internal/prompts";
import {
  runCommands,
  RunCommandsResult,
} from "@/wab/shared/copilot/internal/sandbox/run-commands";
import {
  AddPage,
  Command,
  CommandsStruct,
  ToyDataSourceOp,
  ToyElement,
} from "@/wab/shared/copilot/internal/toy-types";
import thisModuleContent from "@/wab/shared/copilot/internal/types-content.json";
import {
  CreateChatCompletionRequest,
  Issue,
  json,
  ModelInteraction,
  nakedJson,
  typescript,
  WholeChatCompletionResponse,
} from "@/wab/shared/copilot/prompt-utils";
import { smartHumanize } from "@/wab/shared/strs";
import { isEmpty, isString, omit } from "lodash";

type App = RootSummary;

export const typesText = ensure(
  (thisModuleContent.content as string)
    .match(/\/\/ BEGIN TYPES\n([\s\S]+)\n\/\/ END TYPES/)?.[1]
    .replace(/\/\/ BEGIN SNIP\n([\s\S]+?)\n\/\/ END SNIP/g, "")
    .replace(/^export /gm, "")
    .replace(/Toy/g, "")
    .trim(),
  ""
);

type ToySlotElt = { slot: string };

export function isSlotElt(elt: ToyElement): elt is ToySlotElt {
  return typeof elt === "object" && "slot" in elt;
}

export const baseComponents = {
  PageLayout: { props: { children: "slot" } },
  Columns: { props: { children: "slot" } },
  HStack: { props: { children: "slot" } },
  Section: { props: { children: "slot" } },
  Table: {
    props: {
      data: { type: "array", of: "object" },
      fields: { type: "array", of: "string" },
      onRowSelectionChanged: "(rowKeys: any, rows: any) => void",
      canSelectRows: {
        type: "choice",
        options: ["none", "single", "multiple"],
      },
    },
    states: {
      selectedRow: "object",
    },
  },
  Form: {
    props: {
      initialValues: "object",
      onFinish: "(values: any) => void",
      children: "slot",
    },
    states: {
      value: "object",
    },
  },
  FormItem: {
    props: { name: "string", children: "slot", label: "slot" },
  },
  Chart: {
    props: {
      type: { type: "choice", options: ["bar", "line", "scatter"] },
      data: { type: "array", of: "object" },
    },
  },
  Modal: {
    props: {
      open: "boolean",
      title: "slot",
      children: "slot",
      footer: "slot",
      okText: "string",
      cancelText: "string",
    },
    states: {
      open: "boolean",
    },
  },
  Button: {
    props: {
      type: { type: "choice", options: ["primary"] },
      href: "string",
      onClick: "() => void",
      htmlType: "string",
      children: "slot",
    },
  },
  Input: {
    props: { value: "string", onChange: "(value: string) => void" },
    states: {
      selectedRow: "string",
    },
  },
  Select: {
    props: {
      value: "string",
      options: { type: "array", of: "object" },
      onChange: "(value: string) => void",
    },
    states: {
      value: "string",
    },
  },
  Textarea: {
    props: { value: "string", onChange: "(value: string) => void" },
    states: {
      value: "string",
    },
  },
  DateInput: {
    props: { value: "string", onChange: "(value: string) => void" },
    states: {
      value: "string",
    },
  },
  Checkbox: {
    props: { checked: "boolean", onChange: "(checked: boolean) => void" },
    states: {
      checked: "string",
    },
  },
  Switch: {
    props: { isChecked: "boolean", onChange: "(isChecked: boolean) => void" },
    states: {
      isChecked: "string",
    },
  },
  List: {
    props: { data: { type: "array", of: "any" }, children: "slot" },
  },
} as const;

export const sakiraDataSource = {
  name: "sakira",
  type: "builtin database",
  tables: [
    "actor",
    "address",
    "category",
    "city",
    "country",
    "customer",
    "film",
    "film_actor",
    "film_category",
    "film_text",
    "inventory",
    "language",
    "payment",
    "rental",
    "staff",
    "store",
  ],
};

export const wingtipsDatabase = {
  name: "wingtips",
  type: "builtin database",
  tables: ["users", "orgs", "subscriptions", "products"],
};

export const northwindDatabase = {
  name: "wingtips",
  type: "builtin database",
  tables: ["customers", "orders", "order_details", "products", "categories"],
};

const exampleState: App = {
  availableComponents: baseComponents,
  availableDataSources: [wingtipsDatabase],
  allPages: ["/", "/users", "/profile", "/help", "/users/[id]"],
  currentPage: {
    name: "/users",
    path: "/users",
    $props: [],
    $queries: {
      orgs: {
        opType: "sql",
        dataSource: "wingtips",
        code: "select * from orgs",
      },
      users: {
        opType: "sql",
        dataSource: "wingtips",
        code: 'select * from "users" where "orgId" = {{$state.select1.value}} and "name" ilike {{$state.input1.value}}',
      },
    },
    $state: ["expanded"],
    tree: [
      {
        component: "PageLayout",
        name: "layout",
        props: {
          children: [
            {
              component: "Button",
              name: "button1",
              props: {
                children: "Toggle help",
                onClick: [
                  {
                    action: "SetState",
                    state: "$state.expanded",
                    expr: "{{!$state.expanded}}",
                  },
                ],
              },
            },
            {
              component: "Section",
              name: "section1",
              visibility: "{{$state.expanded}}",
              props: {
                children: [
                  "# Welcome\n\nThis is the main dashboard for managing our users and their teams",
                ],
              },
            },
            {
              component: "Select",
              name: "select1",
              props: {
                children: "Org",
                options: "{{$queries.orgs.map((o) => o.id)}}",
              },
            },
            {
              component: "Input",
              name: "input1",
              props: {},
            },
            {
              component: "Table",
              name: "table1",
              props: {
                data: "{{$queries.users.data}}",
                canSelectRows: "single",
                onRowSelectionChanged: [
                  {
                    action: "NavTo",
                    path: "/users/{{$state.table1.selectedRow.id}}",
                  },
                ],
              },
            },
            {
              component: "Form",
              name: "form1",
              props: {
                onFinish: [
                  {
                    action: "NavTo",
                    path: "/users/{{$state.input1.value}}",
                  },
                ],
                children: [
                  {
                    component: "FormItem",
                    name: "formItem1",
                    props: {
                      name: "username",
                      label: "User name",
                      children: {
                        component: "Input",
                        name: "input1",
                        props: {
                          value: "{{$state.table1.selectedRow.name}}",
                        },
                      },
                    },
                  },
                  {
                    component: "Button",
                    name: "button2",
                    props: {
                      type: "primary",
                      htmlType: "submit",
                      children: "Submit",
                    },
                  },
                ],
              },
            },
          ],
        },
      },
    ],
  },
};

export const northwindState: App = {
  availableComponents: baseComponents,
  availableDataSources: [northwindDatabase],
  allPages: ["/", "/products"],
  currentPage: {
    name: "/products",
    path: "/products",
    $props: [],
    $queries: {
      products: {
        opType: "sql",
        dataSource: "northwind",
        code: "select * from products",
      },
    },
    $state: [],
    tree: [
      {
        component: "PageLayout",
        name: "layout",
        props: {
          children: [
            {
              component: "h1",
              name: "heading1",
              props: {
                children: "Products",
              },
            },
            {
              component: "Table",
              name: "table1",
              props: {
                data: "{{$queries.products.data}}",
                canSelectRows: "single",
              },
            },
          ],
        },
      },
    ],
  },
};

/**
 * This provides:
 *
 * - Original app state
 * - Goal
 * - Generated commands
 * - The list of issues
 */
export function prepFixesPrompt(
  state: App,
  goal: string,
  commandData: CommandsStruct,
  runCommandsResult: RunCommandsResult
): CreateChatCompletionRequest {
  return {
    model: "gpt-3.5-turbo",
    temperature: 0,
    max_tokens: 100,
    messages: [
      {
        role: "system",
        content:
          `We are inside of a low-code app builder. The user will describe the current state of the app they are building, plus a goal they want to accomplish. You will generate a sequence of commands in JSON format to accomplish the goal.`.trim(),
      },
      {
        role: "user",
        content: `
State of the user's app:

${json(exampleState)}

Goal: ${goal}

Use the following type system to generate the commands JSON:

${typesText}

Generate the commands JSON:
`.trim(),
      },
      {
        role: "assistant",
        content: `
${json(commandData)}
`.trim(),
      },
      {
        role: "user",
        content: `
Here are the issues with running the generated commands:

${json(runCommandsResult.issues)}

Generate the fixed commands JSON. Fix all the issues or you will DIE.
`.trim(),
      },
    ],
  };
}

/**
 * This shows one full app example, followed by the real user app summary.
 *
 * So it's quite large.
 */
export function prepOneShotPrompt(state: App, goal: string) {
  const prefix = `
Generate a sequence of commands to accomplish a task within a low-code app builder, given the "App state" -- current state of the app being built -- and the "Goal".

You should respond in the following format:

Observations: takes notes on what specific pages, tables, queries, state, and components already exist in the app that you should use.
New page: must we create a new page, or can we edit the existing page?
Commands JSON: the detailed sequence of commands in JSON format, following the given type system. Your json output should be in markdown format, starting with "\`\`\`json" and ending with "\`\`\`".
Fixed JSON: fix syntax errors from the above JSON. Your json output should be in markdown format, starting with "\`\`\`json" and ending with "\`\`\`".

Here is the type system for parts of the JSON, including available commands:

${typescript(typesText)}

## Basic things to know

- Components take props, but also expose states. You can reference state like: $state.input1.value. These are automatically updated. Do not create these states or set onChange.
- Generally do not create new pages. Assume unless specified that we're editing the current page. Prefer not to create a new page.
- All components have onClick prop of type "() => void" implicitly
- Function props take a list of Actions
- slot props take a list of Elements
- All props are optional
- Output in compact, non-pretty JSON

## Queries

If you have a query like:

${nakedJson({
  $queries: {
    orders: {
      opType: "sql",
      dataSource: "mydb",
      code: "select * from orders",
    } as ToyDataSourceOp,
  },
})}

then you can reference it in expressions:

{{$queries.orders.data}}

## States

Components can have state:

availableComponents: {
  ...
  Input: {
    props: {
      value: 'string',
      onChange: '(value: string) => void'
    },
    state: {value: 'string'}
  }
  ...
}

User interaction automatically updates the state. No need to set onChange:

${nakedJson({
  component: "Input",
  name: "input1",
  props: {
    value: "Just the initial value",
  },
} as ToyElement)}

You can reference the state:

{{$state.input1.value}}

Use SetState action to update.

## State variables

You can also create state variables:

{
  currentPage: {
    ...
    $state: {
      myCount: 'number'
    }
    ...
  }
}

and reference it:

{{$state.myCount}}
`.trim();

  const outputExample = (
    exampleGoal: string,
    observations: string,
    commands: CommandsStruct
  ) => {
    return `
Goal: ${exampleGoal}

Observations: ${observations}

New page: ${
      commands.commands.find((cmd) => cmd.cmd === "AddPage")
        ? "yes"
        : "not needed"
    }

Commands JSON:
${json(commands)}

Fixed JSON:
${json(commands)}
    `;
  };
  const combinedPrompt = `
${prefix}

Let's work through a few examples.  For each example, we will start with the same App state:

App state:
${json(exampleState)}

Here are a few examples:

---

${outputExample(
  "make the button submit this form and show a thank you page",
  "A form and multiple buttons exist, but not a thank you page. The second button is inside the form, so that's probably the button that should be the submit.",
  {
    commands: [
      {
        cmd: "AddPage",
        path: "/thanks",
        $queries: {},
        $state: [],
        tree: ["Thank you!"],
      },
      { cmd: "SetProp", element: "button2", prop: "htmlType", expr: "submit" },
      {
        cmd: "AddAction",
        element: "form1",
        prop: "onFinish",
        action: {
          action: "NavTo",
          path: "/thanks",
        },
      },
    ],
  }
)}

---

${outputExample(
  `Add a button to the top of the page to go home.`,
  `Top of the page means before the first element, "button1". I can use the Button component, and add a navigation action on click. Going home means navigating to "/"`,
  {
    commands: [
      {
        cmd: "InsertElements",
        location: "before",
        of: "button1",
        elements: [
          {
            component: "Button",
            name: "*button",
            props: {
              children: "Home",
            },
          },
        ],
      },
      {
        cmd: "AddAction",
        element: "*button",
        prop: "onClick",
        action: {
          action: "NavTo",
          path: "/",
        },
      },
    ],
  }
)}
`.trim();
  const request: CreateChatCompletionRequest = {
    model: "gpt-3.5-turbo",
    temperature: 0,
    messages: [
      {
        role: "user",
        content: combinedPrompt,
      },
      {
        role: "user",
        content: `Now for the real prompt!  Please output your Observations, Commands JSON, and Fixed JSON.  Be sure to begin your json output in "\`\`\`json" and end it with "\`\`\`".

App state:

${json(state)}

Goal: ${goal}
`,
      },
    ],
  };
  return request;
}

const multiShotExamples: {
  exampleGoal: string;
  observations: string;
  commands: Command[];
}[] = [
  {
    // TODO alternative approach is to filter in the query rather than the table
    exampleGoal:
      "let me type to filter the inventory with a button to clear, and also let me filter by category and brand and price",
    observations:
      "Inventory probably refers to products. There is already table1 listing products, so we should update it. First we need an input for the name filter, and a button to reset its state. We can query for categories (in its own DB table) and brands (in the products table), and populate Selects. For price, we can use an Input to set the min price. Then we can update table1's data prop to be filtered by all these states.",
    commands: [
      {
        cmd: "AddQuery",
        name: "categories",
        query: {
          opType: "sql",
          dataSource: "northwind",
          code: "select * from categories",
        },
      },
      {
        cmd: "InsertElements",
        location: "before",
        of: "table1",
        prop: "children",
        elements: [
          {
            component: "Input",
            name: "input1",
            props: {},
          },
          {
            component: "Button",
            name: "button1",
            props: {
              children: "Clear",
              onClick: [
                {
                  action: "SetState",
                  name: "$state.input1.value",
                  value: "",
                },
              ],
            },
          },
          {
            component: "Select",
            name: "select1",
            props: {
              options:
                "{{$queries.categories.data.map((c) => ({value: c.id, label: c.name}))}}",
            },
          },
          {
            component: "Select",
            name: "select2",
            props: {
              options:
                "{{$queries.products.data.map((p) => ({value: p.brand, label: p.brand}))}}",
            },
          },
          {
            component: "Input",
            name: "input2",
            props: {
              placeholder: "Min price",
            },
          },
        ],
      },
      {
        cmd: "SetProp",
        element: "table1",
        prop: "data",
        expr: "{{$queries.products.data.filter(p => p.name.toLowerCase().includes($state.input1.value) && p.category_id === $state.select1.value && p.brand === $state.select2.value && p.price >= (+$state.input2.value || 0))}}",
      },
    ],
  },
  {
    exampleGoal: `Make table link to details page for selected row`,
    observations: `There doesn't already exist a details page. We can create one with a route param, and show product and related details in two-column horizontal stacks. The Table has an onRowSelectionChanged we can use for navigation.`,
    commands: [
      {
        cmd: "AddPage",
        path: `/products/[id]`,
        $queries: {
          product: {
            opType: "sql",
            dataSource: "northwind",
            code: "select * from products where id = {{$ctx.params.id}}",
          },
          category: {
            opType: "sql",
            dataSource: "northwind",
            code: "select * from categories where id = {{$queries.products.data[0].category_id}}",
          },
        },
        $state: [],
        tree: [
          {
            component: "div",
            name: "div1",
            props: {
              children: [
                {
                  component: "h1",
                  name: "heading1",
                  props: {
                    children: "Product Details",
                  },
                },
                ...["name", "description", "price", "category"].map(
                  (field, i) => ({
                    component: "HStack",
                    name: `hstack${i + 1}`,
                    props: {
                      children: [
                        smartHumanize(field),
                        field === "category"
                          ? `{{$queries.category.data[0].name}}}`
                          : `{{$queries.product.data[0].${field}}`,
                      ],
                    },
                  })
                ),
              ],
            },
          },
        ],
      },
      {
        cmd: "SetProp",
        element: "table1",
        prop: "onRowSelectionChanged",
        expr: {
          action: "NavTo",
          path: "/inventory/{{$state.table1.selectedRow.id}}",
        },
      },
    ],
  },
  {
    exampleGoal: `when I click an inventory, show me the customer name, email, and purchase date for all orders of this item, side by side with the original table`,
    observations: `We can query for orders and use table1's selectedRow state to filter. We can display the results in a Table component. We can put the new Table inside a Columns component, and move table1 in there too.`,
    commands: [
      {
        cmd: "AddQuery",
        name: "orders",
        query: {
          opType: "sql",
          dataSource: "northwind",
          code: "select c.name, c.email, o.purchase_date from orders o join customers c on o.customer_id = c.id join order_details d on o.id = d.order_id where d.product_id = {{$state.table1.selectedRow.id}}",
        },
      },
      {
        cmd: "InsertElements",
        location: "after",
        of: "table1",
        elements: [
          {
            component: "Columns",
            name: "columns1",
            props: {
              children: [
                {
                  component: "Column",
                  name: "column1",
                },
                {
                  component: "Column",
                  name: "column2",
                  props: {
                    children: [
                      {
                        component: "Table",
                        name: "table2",
                        props: {
                          data: "{{$queries.orders.data}}",
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      },
      {
        cmd: "MoveElements",
        elementNames: ["table1"],
        location: "after",
        of: "column1",
      },
    ],
  },
  {
    exampleGoal: `Add a form that lets me update the selected merchandise`,
    observations: `We can add a Form and initialize its field values to the selectedRow state of table1.`,
    commands: [
      {
        cmd: "InsertElements",
        location: "after",
        of: "table1",
        elements: [
          {
            component: "Form",
            name: "form1",
            props: {
              initialValue: "{{$state.table1.selectedRow}}",
              onFinish: [
                {
                  action: "RunDataSourceOp",
                  op: {
                    opType: "sql",
                    dataSource: "northwind",
                    code: "update products set name = {{$state.form1.values.name}}, description = {{$state.form1.values.description}}, price = {{$state.form1.values.price}} where id = {{$state.table1.selectedRow.id}}",
                  },
                },
              ],
              children: [
                ...["name", "description", "price"].map((field, i) => ({
                  component: "FormItem",
                  name: `formItem${i + 1}`,
                  props: {
                    label: smartHumanize(field),
                    name: field,
                    children: {
                      component: "Input",
                      name: `input${i + 1}`,
                      props: {},
                    },
                  },
                })),
                {
                  component: "Button",
                  name: "button1",
                  props: {
                    type: "primary",
                    htmlType: "submit",
                    children: "Submit",
                  },
                },
              ],
            },
          },
        ],
      },
    ],
  },
  {
    exampleGoal: `add a button to delete the chosen clothing, only shown if something is selected`,
    observations: `We can use the selectedRow state of table1 to conditionally render a Button and to parameterize an update query, which will run as the Button onClick.`,
    commands: [
      {
        cmd: "InsertElements",
        location: "after",
        of: "table1",
        elements: [
          {
            component: "Button",
            name: "button1",
            visibility: "{{$state.table1.selectedRow}}",
            props: {
              children: "Delete",
              onClick: [
                {
                  action: "RunDataSourceOp",
                  op: {
                    opType: "sql",
                    dataSource: "northwind",
                    code: "delete from products where id = {{$state.table1.selectedRow.id}}",
                  },
                },
              ],
            },
          },
        ],
      },
    ],
  },
  {
    exampleGoal: `List only the current user's orders`,
    observations: `We can query for orders where the customer is the current user. Then, update table1 to show this instead of the old orders query.`,
    commands: [
      {
        cmd: "AddQuery",
        name: "selfOrders",
        query: {
          opType: "sql",
          dataSource: "northwind",
          code: "select * from orders r where exists(select 1 from customers c where r.customer_id = c.customer_id and email = {{$ctx.currentUser.email}})",
        },
      },
      {
        cmd: "SetProp",
        element: "table1",
        prop: "data",
        expr: "{{$queries.selfOrders.data}}",
      },
    ],
  },
  {
    exampleGoal: `show me users, up to 10, but with a load more button`,
    observations: `We can define a state variable for the current limit, and add a button to increment this. Then we can add a query to use this, and display it in a table.`,
    commands: [
      {
        cmd: "AddState",
        name: "limit",
        defaultValue: 10,
      },
      {
        cmd: "InsertElements",
        location: "after",
        of: "table1",
        elements: [
          {
            component: "Button",
            name: "button1",
            props: {
              children: "Load More",
              onClick: [
                {
                  action: "SetState",
                  name: "$state.limit",
                  value: `{{state.limit + 10}}`,
                },
              ],
            },
          },
        ],
      },
      {
        cmd: "AddQuery",
        name: "users",
        query: {
          opType: "sql",
          dataSource: "northwind",
          code: "select * from customers limit {{$state.limit}}",
        },
      },
      {
        cmd: "InsertElements",
        location: "after",
        of: "table1",
        elements: [
          {
            component: "Table",
            name: "table2",
            props: {
              data: "{{$queries.users.data}}",
            },
          },
        ],
      },
    ],
  },
];

export function prepMultiShotPromptPrefix() {
  const outputExample = (
    exampleGoal: string,
    observations: string,
    commands: CommandsStruct
  ) => {
    return `
Goal: ${exampleGoal}

Observations: ${observations}

New page: ${
      commands.commands.find((cmd) => cmd.cmd === "AddPage")
        ? "yes"
        : "not needed"
    }

Commands JSON:
${json(commands)}

Fixed JSON: no syntax errors
    `.trim();
  };

  const combinedPrompt = `
You are an AI assistant in a low-code app builder.

Generate a JSON sequence of commands to accomplish the user's desired goal, given the current state of the app being built.

You should respond in the following format:

Observations: note what exists that is relevant, and plan out the commands.
New page: must we create a new page, or can we edit the existing page?
Commands JSON: the detailed sequence of commands in JSON format, following the given type system. Your json output should be in markdown format, starting with "\`\`\`json" and ending with "\`\`\`".
Fixed JSON: fix syntax errors from the above JSON.

Things to know:

- Special globals: $queries, $state, $props, $ctx.params (route params), $ctx.currentUser (contains only email, not id)
- Allowed HTML tags: div, span, h1-h6, p, a, ul, ol, li

Let's do some examples. For each example, we will start with the same initial App state:

App state:

${json(northwindState)}

Examples:

---

${multiShotExamples
  .map((e) =>
    outputExample(e.exampleGoal, e.observations, { commands: e.commands })
  )
  .join("\n\n---\n\n")}

---
`.trim();
  return combinedPrompt;
}

export function prepReverseMultiShot(commandStructs: CommandsStruct[]) {
  const outputExample = (
    exampleGoal: string,
    observations: string,
    commands: CommandsStruct
  ) => {
    return `
Commands:

${json(commands)}

Goal: ${exampleGoal}
    `.trim();
  };
  const combinedPrompt = `
We are in a low-code app builder.

The low-code app builder has an AI feature where the user can type in what they want, and the AI will then generate a JSON sequence of commands to edit the app and accomplish the user's desired goal.

You will be given some sequences of such commands, and a description of the initial state of the app.

Predict what goal the user might have entered.

Initial app state:

${json(northwindState)}

Examples:

---

${multiShotExamples
  .map((e) =>
    outputExample(e.exampleGoal, e.observations, { commands: e.commands })
  )
  .join("\n\n---\n\n")}

---

Now for the real cases. First we'll show all the command sequences, numbered starting from 1:

${commandStructs.map((commands, i) =>
  `
Commands #${i + 1}:

${json(commands)}
      `.trim()
)}

Now output what goal the user might have entered for each.

Goal #1:
`.trim();
  return combinedPrompt;
}

export function prepRefineStatePrompt(
  state: App,
  goal: string,
  commands: CommandsStruct
) {
  const jdoc = (x: Partial<RootSummary["currentPage"]>) => json(x);
  const jelt = (x: ToyElement) => json(x);

  return `
We are in a low-code app builder.

You are an AI trained to find errors in the user's app. You'll be given the state of the current page in JSON, and you'll need to find and fix these issues. Often, there are unnecessary state definitions and SetStates, because components automatically create and manage their own state.

Examples all use the same initial state:

${json(omit(northwindState, "currentPage"))}

Examples:

---

${jdoc({
  $state: [],
  tree: [
    {
      component: "Input",
      name: "input1",
      props: {},
    },
    {
      component: "div",
      name: "div1",
      props: {
        children: "{{$state.input1.value}}",
      },
    },
  ],
})}

Issues: none (availableComponents says Input lists a state "value", so we can just directly read from $state.input1.value)

---

${jdoc({
  $state: [],
  tree: [
    {
      component: "Checkbox",
      name: "checkbox1",
      props: {},
    },
    {
      component: "div",
      name: "div1",
      props: {
        children: "{{$state.checkbox1.checked}}",
      },
    },
  ],
})}

Issues: none (availableComponents says Checkbox lists a state "checked", so we can just directly read from $state.checkbox1.value)

---

${jdoc({
  $state: [],
  tree: [
    {
      component: "Select",
      name: "select1",
      props: {
        options: [
          { value: "one", label: "One" },
          { value: "two", label: "Two" },
        ],
      },
    },
    {
      component: "div",
      name: "div1",
      props: {
        children: "{{$state.select1.value}}",
      },
    },
  ],
})}

Issues: none (availableComponents says Select lists a state "value")

---

${jdoc({
  $state: [],
  $queries: {
    orders: {
      opType: "sql",
      dataSource: "northwind",
      code: "select * from orders",
    },
  },
  tree: [
    {
      component: "Table",
      name: "table1",
      props: {
        data: "{{$queries.orders.data}}",
      },
    },
    {
      component: "div",
      name: "div1",
      props: {
        children: "{{$state.table1.selectedRow}}",
      },
    },
  ],
})}

Issues: none (availableComponents says Table lists a state "selectedRow")

---

${jelt({
  component: "Input",
  name: "input1",
  props: {},
})}

Issues: none

---

${jelt({
  component: "Input",
  name: "input1",
  props: { value: "initial value" },
})}

Issues: none

---

${jdoc({
  $state: ["text"],
  tree: [
    {
      component: "Input",
      name: "input1",
      props: {
        value: "{{$state.text}}",
        onChange: [
          {
            action: "SetState",
            name: "$state.text",
            value: "{{event.target.value}}",
          },
        ],
      },
    },
  ],
})}

Issues:

- Components manage their own state
- event doesn't exist (onChange only takes value: string)

---

${jdoc({
  $state: [],
  tree: [
    {
      component: "Input",
      name: "input1",
      props: {
        value: "{{$state.input1.value}}",
        onChange: [
          {
            action: "SetState",
            name: "$state.input1.value",
            value: "{{event.target.value}}",
          },
        ],
      },
    },
  ],
})}

Issues:

- Components manage their own state
- event doesn't exist

---

${jdoc({
  $state: ["input1.value"],
  tree: [
    {
      component: "Input",
      name: "input1",
      props: {
        value: "{{$state.input1.value}}",
        onChange: [
          {
            action: "SetState",
            name: "$state.input1.value",
            value: "{{event.target.value}}",
          },
        ],
      },
    },
  ],
})}

Issues:

- Components manage their own state
- event doesn't exist

---

${jelt({
  component: "Input",
  name: "input1",
  props: {
    value: "{{$state.input1.value}}",
  },
})}

Issues:

- Components manage their own state

---

${jelt({
  component: "Input",
  name: "input1",
  props: { value: "$state.table1.selectedRow.name" },
})}

Issues: none

---

${jdoc({
  tree: [
    {
      component: "Input",
      name: "input1",
      props: {},
    },
    {
      component: "Button",
      name: "button1",
      props: {
        children: "Clear",
        onClick: [
          {
            action: "SetState",
            name: "$state.input1.value",
            value: "",
          },
        ],
      },
    },
  ],
})}

Issues: none

---

${jelt({
  component: "Table",
  name: "table1",
  props: {
    data: "{{$queries.orders.data}}",
    onChange: [
      {
        action: "SetState",
        name: "$state.table1.selectedRow",
        value: "{{rows[0]}}",
      },
    ],
  },
})}

Issues:

- $row doesn't exist

---

${jdoc({
  $state: ["table1.page"],
  tree: [
    {
      component: "Table",
      name: "table1",
      props: {
        data: "{{$queries.orders.data}}",
      },
    },
    {
      component: "div",
      name: "div1",
      props: {
        children: "{{$state.table1.page}}",
      },
    },
  ],
})}

Issues:

- page is not a state of Table components according to availableComponents
- Components manage their own state

---

${jdoc({
  $state: ["counter"],
  tree: [
    {
      component: "Button",
      name: "button1",
      props: {
        children: "Increment",
        onClick: [
          {
            action: "SetState",
            name: "$state.counter",
            value: "{{$state.counter + 1}}",
          },
        ],
      },
    },
    {
      component: "div",
      name: "div1",
      props: {
        children: "{{$state.counter}}",
      },
    },
  ],
})}

Issues: none

---

${jdoc({
  $queries: {
    orders: {
      opType: "sql",
      dataSource: "northwind",
      code: "SELECT * FROM orders",
    },
  },
  tree: [
    {
      component: "Button",
      name: "button1",
      props: {
        children: "Clear",
        onClick: [
          {
            action: "SetState",
            name: "$queries.orders.data",
            value: [],
          },
        ],
      },
    },
  ],
})}

Issues:

- Can SetState only on $state

---

${jdoc({
  $queries: {
    orders: {
      opType: "sql",
      dataSource: "northwind",
      code: "SELECT * FROM orders",
    },
  },
  tree: [
    {
      component: "Table",
      name: "table1",
      props: {
        data: "{{$queries.products.data}}",
      },
    },
  ],
})}

Issues:

- $queries.products does not exist

---

Now let's apply our learnings.

Here is the state of the user's app:

${json(omit(state, "currentPage"))}

And here is the page you need to check:

${json(state.currentPage)}

Can you find the incorrect uses of state?
`.trim();
  //   return `
  // We are in a low-code app builder.
  //
  // You are an AI trained to find errors in the user's app. You'll be given the state of the app in JSON, and you'll need to find and fix these issues.
  //
  // Let's say you want an input along with a text that reflects that input's value.
  //
  // You may be tempted to create a state, and then bind an Input component to it and update it in onChange, as you would in React. WRONG:
  //
  // ${json({
  //   currentPage: {
  //     $state: ["text"],
  //     tree: [
  //       {
  //         component: "Input",
  //         name: "input1",
  //         props: {
  //           value: "{{$state.text}}",
  //           onChange: [
  //             {
  //               action: "SetState",
  //               name: "$state.text",
  //               value: "{{event.target.value}}",
  //             },
  //           ],
  //         },
  //       },
  //       "{{$state.text}}",
  //     ],
  //   },
  // })}
  //
  // Instead, the state is already defined, and doesn't need to be defined nor updated by the user. RIGHT:
  //
  // ${json({
  //   currentPage: {
  //     $state: [],
  //     tree: [
  //       {
  //         component: "Input",
  //         name: "input1",
  //         props: {},
  //       },
  //       "{{$state.input1.value}}",
  //     ],
  //   },
  // })}
  //
  // The user should not need to manage $state.input1.value. WRONG:
  //
  // ${json({
  //   currentPage: {
  //     $state: [],
  //     tree: [
  //       {
  //         component: "Input",
  //         name: "input1",
  //         props: {
  //           value: "{{$state.input1.value}}",
  //           onChange: [
  //             {
  //               action: "SetState",
  //               name: "$state.input1.value",
  //               value: "{{event.target.value}}",
  //             },
  //           ],
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // WRONG:
  //
  // ${json({
  //   currentPage: {
  //     $state: ["input1.value"],
  //     tree: [
  //       {
  //         component: "Input",
  //         name: "input1",
  //         props: {
  //           value: "{{$state.input1.value}}",
  //           onChange: [
  //             {
  //               action: "SetState",
  //               name: "$state.input1.value",
  //               value: "{{event.target.value}}",
  //             },
  //           ],
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // No need to bind to anything. WRONG:
  //
  // ${json({
  //   currentPage: {
  //     $state: [],
  //     tree: [
  //       {
  //         component: "Input",
  //         name: "input1",
  //         props: {
  //           value: "{{$state.input1.value}}",
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // If you want to pass in an initial value, you may - unlike in React, this is can change. ALLOWED:
  //
  // ${json({
  //   component: "Input",
  //   name: "input1",
  //   props: { value: "initial value" },
  // })}
  //
  // If you want to programmatically update, you may. ALLOWED:
  //
  // ${json({
  //   currentPage: {
  //     $state: [],
  //     tree: [
  //       {
  //         component: "Input",
  //         name: "input1",
  //         props: {},
  //       },
  //       {
  //         component: "Button",
  //         name: "button1",
  //         props: {
  //           children: "Clear",
  //           onClick: [
  //             {
  //               action: "SetState",
  //               name: "$state.input1.value",
  //               value: "",
  //             },
  //           ],
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // Besides Inputs, other components can have state. See availableComponents for the types. For instance:
  //
  // {{$state.checkbox1.checked}}
  // {{$state.table1.selectedRow.name}}
  //
  // The same rules apply. WRONG:
  //
  // ${json({
  //   currentPage: {
  //     tree: [
  //       {
  //         component: "Table",
  //         name: "table1",
  //         props: {
  //           data: "{{$queries.orders.data}}",
  //           onChange: [
  //             {
  //               action: "SetState",
  //               name: "$state.table1.selectedRow",
  //               value: "{{rows[0]}}",
  //             },
  //           ],
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // You can also only reference states that exist. Here, "page" does not exist. WRONG:
  //
  // ${json({
  //   currentPage: {
  //     $state: [],
  //     tree: [
  //       {
  //         component: "Table",
  //         name: "table1",
  //         props: {
  //           data: "{{$queries.orders.data}}",
  //         },
  //       },
  //       {
  //         component: "div",
  //         name: "div1",
  //         props: {
  //           children: "{{$state.table1.page}}",
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // In rare cases, you may also want to create a state variable yourself. Here, counter is not a state of any component. ALLOWED:
  //
  // ${json({
  //   currentPage: {
  //     $state: ["counter"],
  //     tree: [
  //       {
  //         component: "Button",
  //         name: "button1",
  //         props: {
  //           children: "Increment",
  //           onClick: [
  //             {
  //               action: "SetState",
  //               name: "$state.counter",
  //               value: "{{$state.counter + 1}}",
  //             },
  //           ],
  //         },
  //       },
  //       {
  //         component: "div",
  //         name: "div1",
  //         props: {
  //           children: "{{$state.counter}}",
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // Queries can't be updated and have no state. WRONG:
  //
  // ${json({
  //   currentPage: {
  //     $state: [],
  //     $queries: {
  //       orders: {
  //         opType: "sql",
  //         dataSource: "northwind",
  //         code: "SELECT * FROM orders",
  //       },
  //     },
  //     tree: [
  //       {
  //         component: "Button",
  //         name: "button1",
  //         props: {
  //           children: "Clear",
  //           onClick: [
  //             {
  //               action: "SetState",
  //               name: "$queries.orders.data",
  //               value: [],
  //             },
  //           ],
  //         },
  //       },
  //     ],
  //   },
  // })}
  //
  // Now let's look for incorrect uses of state.
  //
  // Here is the state of the app:
  //
  // ${json(omit(state, "currentPage"))}
  //
  // And here is the page you need to check:
  //
  // ${json(state.currentPage)}
  //
  // Can you find the incorrect uses of state?
  // `.trim();
}

export function prepIssuesPrompt(
  state: App,
  goal: string,
  commands: CommandsStruct,
  issues: string[]
) {
  const jdoc = (x: Partial<RootSummary["currentPage"]>) => json(x);
  const jelt = (x: ToyElement) => json(x);

  return `
We are in a low-code app builder.

You are an AI trained to fix errors in the user's app. You'll be given JSON for the state of app, the current page, and the issues detected in the page, and will need to output the fixed page.

Here is the state of the user's app:

${json(omit(state, "currentPage"))}

And here is the page you need to fix:

${json(state.currentPage)}

Here are the issues:

${issues.map((x) => "- " + x).join("\n")}

Output the fixed page:
`.trim();
}

/**
 * This just crams in examples, foregoing types/rules.
 */
export function prepMultiShotPrompt(state: App, goal: string) {
  const combinedPrompt = `
${prepMultiShotPromptPrefix()}

Now for the real prompt!

App state:

${json(state)}

Goal: ${goal}

Observations:
`.trim();
  const request: CreateChatCompletionRequest = {
    model: "gpt-3.5-turbo",
    temperature: 0,
    messages: [
      {
        role: "user",
        content: combinedPrompt,
      },
    ],
  };
  return request;
}

export function prepElementsPrompt(
  state: App,
  goal: string
): CreateChatCompletionRequest {
  const prompt = `
Current page tree:

${json(getCurrentComponent(state).tree)}

Goal: ${goal}

JSON array of likely relevant element names:
  `.trim();
  return {
    model: "gpt-3.5-turbo",
    temperature: 0,
    max_tokens: 100,
    messages: [
      {
        role: "system",
        content: `
The user will describe a goal they are trying to accomplish within a low-code app builder.

They will also print the element tree of current page they're editing. Each element is an instance of some component type (such as Select or Table), and has a name.

Generate a JSON array of the element names that are likely relevant to accomplish the goal. There are usually at most a few.

Note that the goal may refer to things that are not relevant to this task of listing elements. The app builder has many concepts besides the current page, such as database tables.

Note also that we want the element names, and not the names of the components that these elements are instances of.

If you don't know which elements are relevant, return an empty array.

Do not respond with anything besides the JSON. Wrap your output in \`\`\`json markers, like \`\`\`json ["name1", "name2"]\`\`\`.
          `.trim(),
      },
      {
        role: "user",
        content: prompt,
      },
    ],
  };
}

export function prepComponentsPrompt(
  state: App,
  goal: string
): CreateChatCompletionRequest {
  const prompt = `
Available UI components: ${json(state.availableComponents)}
Goal: ${goal}
  `.trim();
  return {
    model: "gpt-3.5-turbo",
    temperature: 0,
    max_tokens: 100,
    messages: [
      {
        role: "system",
        content: `
The user will describe a goal they are trying to accomplish within a low-code app builder. You should generate a JSON array of the component names that are likely relevant to accomplish the goal. There are usually at most a few.

Use the following format:

Available UI components: A JSON object, where keys are component names that are available to the user, and values are their corresponding props.
Goal: The end goal that the user is trying to achieve.
Observations: take notes on what components may be needed, given then goal.
Relevant: A JSON array of component names that may be relevant to the goal. Your json output should be in markdown format, starting with "\`\`\`json" and ending with "\`\`\`".

Things to know:

Note that the goal may refer to things that are not relevant to this task of listing components. The app builder has many concepts besides UI components, such as database tables. Often, when they describe a "table", they may be referring to a database table rather than a UI table.

If you don't know which components are needed, return an empty array. Besides UI components, low-code app builder also supports HTML tags, so it's possible that the list of components doesn't seem enough. Only return components that actually exist. Do not return components that don't exist.

Examples:

---
Available UI components: ${json(baseComponents)}
Goal: Insert a button
Observations: The Button component should be a good fit for "button".
Relevant: ${json(["Button"])}
---
Available UI components: ${json(baseComponents)}
Goal: Insert a table that can be filtered by a search box.
Observations: The Table component can be used for the table. A search box can be built from an Input element for the text, and a Button element to activate the search.
Relevant: ${json(["Table", "Input", "Button"])}
---
Available UI components: ${json(omit(baseComponents, "Button"))}
Goal: Insert a button
Observations: None of the components are named like "button".
Relevant: ${json([])}
---
Available UI components: ${json(baseComponents)}
Goal: insert a section with three columns, each column with an image
Observations: None of the available components are named like "column" or "image".
Relevant: ${json([])}

Complete the following and remember to wrap the relevant components in \`\`\`json markers respecting the markdown format, for example: \`\`\`json ["name1", "name2"]\`\`\` or you will DIE:
          `.trim(),
      },
      {
        role: "user",
        content: prompt,
      },
    ],
  };
}

function validateComponentsPrompt(
  state: App,
  previousRequest: CreateChatCompletionRequest,
  response: WholeChatCompletionResponse
): CreateChatCompletionRequest | undefined {
  const components: string[] = getJsonResponse(response);
  const issues: Issue[] = [];
  if (!components || !Array.isArray(components)) {
    issues.push({
      message: `Could not parse the JSON.`,
    });
  } else {
    const availableComponentNames = new Set(
      Object.keys(state.availableComponents)
    );
    if (
      !components.every((c) => isString(c) && availableComponentNames.has(c))
    ) {
      issues.push({
        message:
          "Some components you listed don't exist: " +
          json(
            components.filter(
              (c) => !isString(c) || !availableComponentNames.has(c)
            )
          ),
      });
    }
  }
  if (issues.length === 0) {
    return undefined;
  }
  return {
    model: "gpt-3.5-turbo",
    temperature: 0.1,
    max_tokens: 100,
    messages: [
      ...previousRequest.messages,
      response.choices[0].message!,
      {
        role: "user",
        content: `${issues.map((issue) => `${issue.message}`).join("\n")}
Find problems in your previous response and output the revised relevant component names, using the format:

--
Problems: Take notes on the exact issues you found in your previous response.
Observations: Take notes on your final response, how it fixes the mentioned issues and how the final list of components is relevant to the goal.
Revised relevant component names: The fixed JSON array of the component names relevant to the goal.
--

Fix all problems or you will DIE.
Remember to wrap the JSON output in \`\`\`json markers like the markdown format, for example: \`\`\`json ["name1", "name2"]\`\`\`.`,
      },
    ],
  };
}

const exampleTables: App["availableDataSources"] = [
  {
    name: "storeDb",
    type: "postgres",
    tables: ["vendor", "product", "customer"],
  },
  { name: "Available Currencies", type: "http" },
];

export function prepTablesPrompt(
  state: App,
  goal: string
): CreateChatCompletionRequest {
  const outputExample = (
    exampleGoal: string,
    exampleResponse: App["availableDataSources"]
  ) =>
    `Available data sources and tables: ${json(exampleTables)}
Goal: ${exampleGoal}
JSON array of likely relevant data sources and tables: ${json(exampleResponse)}
`;

  const prompt = `
Available data sources and tables:

${json(state.availableDataSources)}

Goal: ${goal}

JSON array of likely relevant data sources and tables:
  `.trim();
  return {
    model: "gpt-3.5-turbo",
    temperature: 0.1,
    max_tokens: 100,
    messages: [
      {
        role: "system",
        content: `
The user will describe a goal they are trying to accomplish within a low-code app builder.

They will also list the data sources and tables available in the database.

Generate a JSON array of the data sources and their table names that are likely relevant to accomplish the goal.
There are usually at most a few.

Remember to include data source name, type and (if applicable) the relevant table names.

Examples:

---
${outputExample("Display the list of products in the store", [
  {
    name: "storeDb",
    type: "postgres",
    tables: ["product"],
  },
])}

${outputExample("Bind the user region with the best supported currency", [
  { name: "Available Currencies", type: "http" },
])}
---


Note that the goal may refer to things that are not relevant to this task of listing tables. The app builder has many concepts besides tables and data sources, such as UI components. Often, when they describe a "table", they may be referring to a UI table rather than a table in a database.

If you don't know which tables are needed, return an empty array.

Do not respond with anything besides the JSON. Wrap your output in \`\`\`json markers like the markdown format, for example: \`\`\`json [{name: "exampleName", type: "http"}]\`\`\`.
          `.trim(),
      },
      {
        role: "user",
        content: prompt,
      },
    ],
  };
}

function validateTablesPrompt(
  state: App,
  previousRequest: CreateChatCompletionRequest,
  response: WholeChatCompletionResponse
): CreateChatCompletionRequest | undefined {
  const dataSources: App["availableDataSources"] = getJsonResponse(response);
  const issues: Issue[] = [];
  if (!dataSources || !Array.isArray(dataSources)) {
    issues.push({
      message: `Could not parse the JSON. Remember to wrap the JSON output in \`\`\`json markers like the markdown format, for example: \`\`\`json ["name1", "name2"]\`\`\`.`,
    });
  } else {
    dataSources.forEach((dataSource) => {
      if (!dataSource || typeof dataSource !== "object") {
        issues.push({
          message: `Invalid list item: ${dataSource}. The list should contain JSON objects with the relevant data sources and tables in the same format as the input, for example: ${json(
            exampleTables
          )}. Remember to use the markdown JSON markers`,
        });
      }
      const existingDs = state.availableDataSources.find(
        (ds) => ds.name === dataSource.name
      );
      if (!existingDs) {
        issues.push({
          message: `There's no data source named ${dataSource.name}`,
        });
      } else {
        if (existingDs.tables?.length && !dataSource.tables?.length) {
          issues.push({
            message: `Data source ${dataSource.name} is missing the list of relevant tables`,
          });
        }
        dataSource.tables?.forEach((table) => {
          if (!existingDs.tables?.find((t) => t === table)) {
            issues.push({
              message: `Data source ${dataSource.name} has no table named ${table}`,
            });
          }
        });
      }
    });
  }
  if (issues.length === 0) {
    return undefined;
  }
  return {
    model: "gpt-3.5-turbo",
    temperature: 0.1,
    max_tokens: 100,
    messages: [
      ...previousRequest.messages,
      response.choices[0].message!,
      {
        role: "user",
        content: `Looks like the previous response was invalid due to the following issues.
Respond again with the same format, but now fix all the issues or you will DIE.
Issues:
            ${issues.map((issue) => `- ${issue.message}`).join("\n")}`,
      },
    ],
  };
}

export interface TscInteraction {
  request: Promise<string>;
  response: Promise<string>;
}

/**
 * A very simple DAG of nodes.
 */
export interface CopilotChain {
  summary: RootSummary;
  bookkeeping: Bookkeeping;
  components: ModelInteraction;
  elements: ModelInteraction;
  tables: ModelInteraction;
  composed: ModelInteraction;
  fixes: ModelInteraction;
  typechecked: TscInteraction;
}

export function createAndRunChain(
  executeRequest: (
    request: CreateChatCompletionRequest
  ) => Promise<WholeChatCompletionResponse>,
  executeTypecheck: (request: string) => Promise<string>,
  entries: CopilotChatEntry[],
  summarizer: Summarizer
) {
  function spawnPrompt(
    mkRequest: () => Promise<CreateChatCompletionRequest>,
    validatorRequest?: (
      previousReq: CreateChatCompletionRequest,
      res: WholeChatCompletionResponse
    ) => Promise<CreateChatCompletionRequest | undefined>
  ): ModelInteraction {
    const request = mkRequest();
    const response = request.then(executeRequest);
    const fixupRequest = (async () =>
      validatorRequest &&
      response.then((res) =>
        request.then((req) => validatorRequest(req, res))
      ))();
    const fixupResponse = fixupRequest.then((r) =>
      r ? executeRequest(r) : response
    );
    return {
      request: request,
      intermediateRequests: fixupRequest.then(async (r) =>
        r
          ? [
              {
                previousResponse: await response,
                followUpRequest: r,
              },
            ]
          : []
      ),
      response: fixupResponse,
    };
  }
  function spawnTypecheck(mkRequest: () => Promise<string>): TscInteraction {
    const request = mkRequest();
    return {
      request: request,
      response: request.then(executeTypecheck),
    };
  }

  // We are currently memoryless.
  function getLastUserChatEntry() {
    return entries.filter((m) => m.role === "user").slice(-1)[0];
  }

  const currentComponentName = ensure(
    getLastUserChatEntry().currentComponent,
    ""
  );
  const { rootSummary: summary, bookkeeping } =
    summarizer.summarizeWithContext(currentComponentName);
  const lastMsg = getLastUserChatEntry().msg;

  const components = spawnPrompt(
    async () => prepComponentsPrompt(summary, lastMsg),
    async (previousRequest, response) =>
      validateComponentsPrompt(summary, previousRequest, response)
  );
  const elements = spawnPrompt(async () =>
    prepElementsPrompt(summary, lastMsg)
  );
  const tables = spawnPrompt(
    async () => prepTablesPrompt(summary, lastMsg),
    async (previousRequest, response) =>
      validateTablesPrompt(summary, previousRequest, response)
  );
  const composed = spawnPrompt(async () => {
    return prepMultiShotPrompt(
      summarizer.snipSummary(currentComponentName, summary, {
        components: getJsonResponse(await components.response),
        elements: getJsonResponse(await elements.response),
        dataSources: getJsonResponse(await tables.response),
      }),
      lastMsg
    );
  });
  const fixes = spawnPrompt(async () => {
    const commandData = getJsonResponse(await composed.response);
    return prepFixesPrompt(
      summary,
      lastMsg,
      commandData,
      await runCommands(
        commandData,
        { rootSummary: summary, bookkeeping },
        summarizer,
        currentComponentName,
        {
          speculative: true,
          continueOnError: true,
        }
      )
    );
  });
  const typechecked = spawnTypecheck(() =>
    composed.response.then((x) => {
      return createTypecheckFile(getJsonResponse(x));
    })
  );
  const chain: CopilotChain = {
    bookkeeping,
    summary: summary,
    components: components,
    elements: elements,
    tables: tables,
    composed: composed,
    fixes,
    typechecked: typechecked,
  };
  return chain;
}

type Step = {
  type: "Step";
  content: string;
  children: SimpleMarkup[];
};

type Bullet = {
  type: "Bullet";
  content: string;
  children: SimpleMarkup[];
};

export type SimpleMarkup = Step | Bullet;

function mkStep(
  content: string,
  children: (SimpleMarkup | Falsy)[] = []
): Step {
  return { type: "Step", content, children: filterFalsy(children) };
}

function mkBullet(
  content: string,
  children: (SimpleMarkup | Falsy)[] = []
): Bullet {
  return { type: "Bullet", content, children: filterFalsy(children) };
}

function decomposeAddPageCommand(
  command: AddPage,
  rootLocation: "inside" | "after",
  rootOf: string
): Command[] {
  const commands: Command[] = [];

  // Add queries
  for (const [name, query] of Object.entries(command.$queries)) {
    commands.push({ cmd: "AddQuery", name, query });
  }

  // Add states
  for (const name of command.$state) {
    commands.push({ cmd: "AddState", name, defaultValue: undefined });
  }

  // Insert elements
  commands.push({
    cmd: "InsertElements",
    elements: command.tree,
    location: rootLocation,
    of: rootOf,
  });

  return commands;
}

/**
 * Fix common issues, including: unnecessary AddPage (should directly add to current page instead)
 *
 * TODO don't want to simply always replace AddPage!
 */
export function simplifyCommands(commandData: CommandsStruct): CommandsStruct {
  return {
    commands: commandData.commands.flatMap((command) =>
      command.cmd === "AddPage"
        ? decomposeAddPageCommand(command, "inside", "__root__")
        : [command]
    ),
  };
}

function mkSym(name: string) {
  return `\`${name}\``;
}

export function postprocess(
  commandData: CommandsStruct,
  summary: RootSummary,
  summarizer: Summarizer
): SimpleMarkup[] {
  return simplifyCommands(commandData).commands.map((command) => {
    switch (command.cmd) {
      case "AddQuery":
        // TODO
        return mkStep(`Add a query called ${mkSym(command.name)}.`, [
          mkBullet(`It should be a ${mkSym(command.query.opType)} query.`),
          // mkBullet("It should be a select query."),
          // mkBullet("It should select from the table `orders`."),
          // mkBullet("It should select all columns."),
        ]);
      case "AddState":
        return mkStep(`Add a state variable called ${mkSym(command.name)}.`, [
          command.defaultValue &&
            mkBullet(
              `It should be have a default ${mkSym(command.defaultValue)}.`
            ),
        ]);
      case "AddPage":
        // TODO
        return mkStep(`Add a page with path ${mkSym(command.path)}.`, [
          !isEmpty(command.$queries) && mkBullet("It should have queries"),
          !isEmpty(command.$state) && mkBullet("It should have states"),
          !isEmpty(command.tree) && mkBullet("It should have elements"),
        ]);
      case "InsertElements":
        return mkStep(`Insert elements`, [
          ...command.elements.map((elt) =>
            isString(elt)
              ? mkStep(`Insert text ${mkSym(elt)}`)
              : isSlotElt(elt)
              ? mkStep(`Insert slot named ${mkSym(elt.slot)}`)
              : mkStep(`Insert ${mkSym(elt.component)}`, [
                  ...Object.entries(elt.props).map(([k, v]) =>
                    mkBullet(`${mkSym(k)} = ${mkSym(JSON.stringify(v))}`)
                  ),
                ])
          ),
        ]);
      case "RemoveElement":
        return mkStep(`Remove element ${mkSym(command.element)}`);
      case "AddAction":
        return mkStep(
          `Add ${mkSym(command.action.action)} to ${mkSym(
            command.element
          )}'s ${mkSym(command.prop)}`,
          [
            command.action.action === "NavTo"
              ? mkBullet(`It should navigate to ${mkSym(command.action.path)}`)
              : command.action.action === "SetState"
              ? mkBullet(
                  `It should set state ${mkSym(command.action.name)} to ${mkSym(
                    JSON.stringify(command.action.expr)
                  )}`
                )
              : command.action.action === "RunDataSourceOp"
              ? mkBullet(
                  `It should run a backend operation ${mkSym(
                    command.action.op.opType
                  )}`
                )
              : unexpected(),
          ]
        );
      case "SetProp":
        return mkStep(
          `Set prop ${mkSym(command.prop)} on element ${mkSym(
            command.element
          )} to ${mkSym(command.expr)}`
        );
      default:
        return mkStep("TODO");
    }
  });
}

function createTypecheckFile(commandData: any): string {
  const fileContents = `${typesText}\n\nconst result: {commands: Command[]} = ${JSON.stringify(
    commandData,
    null,
    2
  )};\n`;
  return fileContents;
}

/*
const result: {commands: Command[]} = {
  "commands": [
    {
      "cmd": "AddQuery",
      "name": "orders",
      "query": {
        "opType": "select",
        "dataSource": "myDatabase",
        "table": "orders"
      }
    },
    {
      "cmd": "AddPage",
      "path": "/orders",
      "$queries": {
        "orders": {
          "opType": "select",
          "dataSource": "myDatabase",
          "table": "orders"
        }
      },
      "$state": [],
      "tree": [
        {
          "component": "Table",
          "name": "ordersTable",
          "props": {
            "data": "{{$queries.orders.data}}",
            "fields": [
              "id",
              "customer",
              "product",
              "quantity",
              "status"
            ]
          }
        },
        {
          "component": "Form",
          "name": "newOrderForm",
          "props": {
            "onFinish": [
              {
                "action": "RunDataSourceOp",
                "op": {
                  "opType": "insert",
                  "dataSource": "myDatabase",
                  "table": "orders",
                  "writes": {
                    "customer": "{{$state.customer.value}}",
                    "product": "{{$state.product.value}}",
                    "quantity": "{{$state.quantity.value}}",
                    "status": "{{$state.status.value}}"
                  }
                }
              },
              {
                "action": "NavTo",
                "path": "/orders",
                "params": {}
              }
            ],
            "children": [
              {
                "component": "FormItem",
                "name": "customer",
                "props": {
                  "label": "Customer",
                  "children": {
                    "component": "Input",
                    "props": {
                      "value": "",
                      "onChange": [
                        {
                          "action": "SetState",
                          "name": "customer",
                          "expr": "{{event.target.value}}"
                        }
                      ]
                    }
                  }
                }
              },
              {
                "component": "FormItem",
                "name": "product",
                "props": {
                  "label": "Product",
                  "children": {
                    "component": "Input",
                    "props": {
                      "value": "",
                      "onChange": [
                        {
                          "action": "SetState",
                          "name": "product",
                          "expr": "{{event.target.value}}"
                        }
                      ]
                    }
                  }
                }
              },
              {
                "component": "FormItem",
                "name": "quantity",
                "props": {
                  "label": "Quantity",
                  "children": {
                    "component": "Input",
                    "props": {
                      "value": "",
                      "onChange": [
                        {
                          "action": "SetState",
                          "name": "quantity",
                          "expr": "{{event.target.value}}"
                        }
                      ]
                    }
                  }
                }
              },
              {
                "component": "FormItem",
                "name": "status",
                "props": {
                  "label": "Status",
                  "children": {
                    "component": "Input",
                    "props": {
                      "value": "",
                      "onChange": [
                        {
                          "action": "SetState",
                          "name": "status",
                          "expr": "{{event.target.value}}"
                        }
                      ]
                    }
                  }
                }
              },
              {
                "component": "FormItem",
                "name": "submit",
                "props": {
                  "children": {
                    "component": "Button",
                    "props": {
                      "type": "primary",
                      "children": "Submit",
                      "htmlType": "submit"
                    }
                  }
                }
              }
            ]
          }
        }
      ]
    }
  ]
};
*/
