import { json<PERSON>lone, spawn } from "@/wab/shared/common";
import {
  createOpenAIClient,
  OpenAIWrapper,
} from "@/wab/server/copilot/llms";
import { fixJson } from "@/wab/shared/copilot/internal/fix-json";
import { RootSummary } from "@/wab/shared/copilot/internal/prompt-summarizer";
import { showResponse } from "@/wab/shared/copilot/internal/prompts";
import {
  northwindState,
  prepIssuesPrompt,
  prepMultiShotPromptPrefix,
  prepReverseMultiShot,
} from "@/wab/shared/copilot/internal/sandbox/prompts";

async function interact(openai: OpenAIWrapper, prompt: string) {
  const response = await openai.createChatCompletion({
    model: "gpt-3.5-turbo",
    temperature: 0,
    messages: [
      {
        role: "user",
        content: prompt,
      },
    ],
  });
  showResponse(response);
}

async function readStdin(): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    let input = "";
    process.stdin.setEncoding("utf-8");

    process.stdin.on("readable", () => {
      let chunk;
      while ((chunk = process.stdin.read()) !== null) {
        input += chunk;
      }
    });

    process.stdin.on("end", () => {
      resolve(input);
    });

    process.stdin.on("error", (error) => {
      reject(error);
    });
  });
}

export async function playPrompts() {
  process.stdin.setEncoding("utf8"); // set the input encoding to UTF-8
  const openai = createOpenAIClient();
  switch (process.argv[2]) {
    case "fix-json": {
      const stdin = await readStdin();
      console.log(fixJson(stdin));
      break;
    }
    case "multi-shot-prompt": {
      const prompt = `
${prepMultiShotPromptPrefix()}
`.trim();
      console.log(prompt);
      break;
    }
    case "refine-state": {
      const state: RootSummary = jsonClone(northwindState);
      // state.currentPage!.tree = [
      //   {
      //     component: "div",
      //     name: "div1",
      //     props: {
      //       children: [
      //         {
      //           component: "h1",
      //           name: "heading1",
      //           props: {
      //             children: "Admin Panel",
      //           },
      //         },
      //         {
      //           component: "Select",
      //           name: "select1",
      //           props: {
      //             options: [
      //               {
      //                 value: "orders",
      //                 label: "Orders",
      //               },
      //               {
      //                 value: "customers",
      //                 label: "Customers",
      //               },
      //               {
      //                 value: "products",
      //                 label: "Products",
      //               },
      //             ],
      //           },
      //         },
      //         {
      //           component: "Table",
      //           name: "table1",
      //           props: {
      //             data: "{{$queries.table.data}}",
      //             onRowSelectionChanged: [
      //               {
      //                 action: "NavTo",
      //                 path: "/admin/{{$state.select1.value}}/{{$state.table1.selectedRow.id}}",
      //               },
      //               {
      //                 action: "SetState",
      //                 name: "$state.table1.selectedRow",
      //                 value: "{{$state.table1.selectedRow}}",
      //               },
      //             ],
      //             canSelectRows: "single",
      //             fields:
      //               "{{$queries.table.fields.map((f) => ({title: f, dataIndex: f}))}}",
      //           },
      //         },
      //         {
      //           component: "div",
      //           name: "div2",
      //           props: {
      //             children: [
      //               {
      //                 component: "Button",
      //                 name: "button1",
      //                 props: {
      //                   children: "Load More",
      //                   onClick: [
      //                     {
      //                       action: "SetState",
      //                       name: "$state.table1.limit",
      //                       value: "{{$state.table1.limit + 10}}",
      //                     },
      //                   ],
      //                 },
      //               },
      //             ],
      //           },
      //         },
      //       ],
      //     },
      //   },
      // ];

      Object.assign(state.currentPage!, {
        path: "/admin/:table/:id",
        $queries: {
          record: {
            opType: "sql",
            dataSource: "northwind",
            code: "select * from {{$ctx.params.table}} where id = {{$ctx.params.id}}",
          },
        },
        $state: [],
        tree: [
          {
            component: "div",
            name: "div1",
            props: {
              children: [
                {
                  component: "h1",
                  name: "heading1",
                  props: {
                    children: "Record Details",
                  },
                },
                {
                  component: "Form",
                  name: "form1",
                  props: {
                    initialValue: "{{$queries.record.data[0]}}",
                    onFinish: [
                      {
                        action: "RunDataSourceOp",
                        op: {
                          opType: "sql",
                          dataSource: "northwind",
                          code: "update {{$ctx.params.table}} set {{$state.form1.values}} where id = {{$ctx.params.id}}",
                        },
                      },
                    ],
                    children: [
                      {
                        component: "FormItem",
                        name: "formItem1",
                        props: {
                          label: "ID",
                          name: "id",
                          children: {
                            component: "Input",
                            name: "input1",
                            props: {
                              disabled: true,
                            },
                          },
                        },
                      },
                      {
                        component: "FormItem",
                        name: "formItem2",
                        props: {
                          label: "Name",
                          name: "name",
                          children: {
                            component: "Input",
                            name: "input2",
                            props: {},
                          },
                        },
                      },
                      {
                        component: "FormItem",
                        name: "formItem3",
                        props: {
                          label: "Description",
                          name: "description",
                          children: {
                            component: "Input",
                            name: "input3",
                            props: {},
                          },
                        },
                      },
                      {
                        component: "FormItem",
                        name: "formItem4",
                        props: {
                          label: "Price",
                          name: "price",
                          children: {
                            component: "Input",
                            name: "input4",
                            props: {},
                          },
                        },
                      },
                      {
                        component: "Button",
                        name: "button1",
                        props: {
                          type: "primary",
                          htmlType: "submit",
                          children: "Submit",
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
        ],
      });

      const prompt = `
${prepIssuesPrompt(
  state,
  "Create me an admin panel for our database where we can select different database tables (orders, customers, products), explore those tables, and drill into individual records to inspect their details with the ability to update and delete them",
  { commands: [] },
  [
    "Invalid page path syntax - use [slug] instead of :slug",
    "{{$ctx.params.table}} SQL substitution: can only substitute SQL values, not identifiers or whole expressions",
  ]
  // [
  //   "$queries.table does not exist",
  //   "table1 has unnecessary SetState in onRowSelectionChanged props - components automatically manage their own state",
  //   "$state.table1.limit does not exist - Table components have only selectedRow",
  // ]
)}
`.trim();
      console.log(prompt);
      // await interact(openai, prompt);
      break;
    }
    case "multi-shot-example": {
      const goal = process.argv[3];
      const prompt = `
${prepMultiShotPromptPrefix()}

Goal: ${goal}
`.trim();
      await interact(openai, prompt);
      break;
    }
    case "reverse-multi-shot-example": {
      const prompt = prepReverseMultiShot([
        {
          commands: [
            {
              cmd: "AddQuery",
              name: "revByYear",
              query: {
                opType: "sql",
                dataSource: "northwind",
                code: "select extract(year from date) as year, sum(total) from orders group by 1 order by 1",
              },
            },
            {
              cmd: "InsertElements",
              location: "before",
              of: "table1",
              elements: [
                {
                  component: "Chart",
                  name: "chart1",
                  props: {
                    type: "bar",
                    data: "{{$queries.revByYear.data}}",
                  },
                },
              ],
            },
          ],
        },
        {
          commands: [
            {
              cmd: "AddQuery",
              name: "ordersForProduct",
              query: {
                opType: "sql",
                dataSource: "northwind",
                code: "select * from products p join order_details d on p.id = d.product_id join orders o on o.id = d.order_id where p.id = {{$state.table1.selectedRow.id}}",
              },
            },
            {
              cmd: "InsertElements",
              location: "after",
              of: "table1",
              elements: [
                {
                  component: "Modal",
                  name: "modal1",
                  props: {
                    title: "Orders",
                    children:
                      "There are a total of {{$queries.orders.data.length}} orders.",
                  },
                },
              ],
            },
            {
              cmd: "SetProp",
              element: "table1",
              prop: "onRowSelectionChange",
              expr: [
                {
                  action: "SetState",
                  name: "$state.modal1.open",
                  expr: true,
                },
              ],
            },
          ],
        },
      ]);
      await interact(openai, prompt);
      break;
    }
  }
}

spawn(playPrompts());
