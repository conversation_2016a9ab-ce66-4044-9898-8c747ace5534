import * as swc from "@swc/core";
import fs from "fs";
import { spawn } from "@/wab/shared/common";

async function go() {
  console.log(
    swc.printSync(
      swc.parseSync(
        fs.readFileSync(__dirname + "/restricted-react.tsx", "utf8"),
        {
          tsx: true,
          syntax: "typescript",
        }
      ),
      {
        jsc: {
          parser: {
            syntax: "typescript",
            tsx: true,
          },
          target: "es2022",
          loose: false,
          minify: {
            compress: false,
            mangle: false,
          },
        },
        module: {
          type: "es6",
        },
        minify: true,
        isModule: true,
      }
    ).code
  );
}

spawn(go());
