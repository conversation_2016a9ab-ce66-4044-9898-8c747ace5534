#!/usr/bin/env node

// Test script to verify rate limiting functionality
const http = require('http');
const https = require('https');

const config = {
  host: process.argv[2] || 'localhost',
  port: process.argv[3] || 3003,
  endpoint: process.argv[4] || '/api/v1/auth/csrf',
  requestsPerSecond: 5,
  duration: 30, // seconds
};

const isHttps = config.port === 443 || config.port === '443';
const httpModule = isHttps ? https : http;

console.log(`Testing rate limiting on ${config.host}:${config.port}${config.endpoint}`);
console.log(`Sending ${config.requestsPerSecond} requests per second for ${config.duration} seconds`);
console.log('Expected behavior: Should get rate limited after ~100 requests within a minute\n');

let requestCount = 0;
let successCount = 0;
let rateLimitedCount = 0;
let errorCount = 0;

function makeRequest() {
  requestCount++;
  const requestNum = requestCount;
  
  const options = {
    hostname: config.host,
    port: config.port,
    path: config.endpoint,
    method: 'GET',
    headers: {
      'x-plasmic-test-rate-limit': 'true', // Force rate limiting in development
    },
  };

  const startTime = Date.now();
  const req = httpModule.request(options, (res) => {
    const duration = Date.now() - startTime;
    
    if (res.statusCode === 429) {
      rateLimitedCount++;
      console.log(`Request #${requestNum}: RATE LIMITED (429) - ${duration}ms`);
    } else if (res.statusCode >= 200 && res.statusCode < 300) {
      successCount++;
      console.log(`Request #${requestNum}: Success (${res.statusCode}) - ${duration}ms`);
    } else {
      errorCount++;
      console.log(`Request #${requestNum}: Other status (${res.statusCode}) - ${duration}ms`);
    }
    
    // Read response body to ensure request completes
    res.on('data', () => {});
    res.on('end', () => {});
  });

  req.on('error', (error) => {
    errorCount++;
    console.error(`Request #${requestNum}: ERROR - ${error.message}`);
  });

  req.end();
}

// Send requests at specified rate
const interval = setInterval(() => {
  for (let i = 0; i < config.requestsPerSecond; i++) {
    makeRequest();
  }
}, 1000);

// Stop after duration
setTimeout(() => {
  clearInterval(interval);
  
  console.log('\n--- Test Results ---');
  console.log(`Total requests: ${requestCount}`);
  console.log(`Successful: ${successCount}`);
  console.log(`Rate limited (429): ${rateLimitedCount}`);
  console.log(`Errors: ${errorCount}`);
  
  if (rateLimitedCount > 0) {
    console.log('\n✅ Rate limiting is working!');
  } else {
    console.log('\n❌ No rate limiting detected. This might be expected if:');
    console.log('- Total requests were under the limit (100/min)');
    console.log('- The server is in development mode without x-plasmic-test-rate-limit header');
  }
  
  process.exit(0);
}, config.duration * 1000);

console.log('Starting test...\n');