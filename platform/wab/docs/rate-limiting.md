# Global Rate Limiting

## Overview

A global rate limiter has been implemented to prevent flooding of all API endpoints. This helps protect the server from abuse and ensures fair usage for all users.

## Configuration

The rate limiter can be configured using environment variables:

- `GLOBAL_RATE_LIMIT_WINDOW_MS`: Time window in milliseconds (default: 60000 = 1 minute)
- `GLOBAL_RATE_LIMIT_MAX`: Maximum number of requests per window (default: 100)

## Features

1. **Per-IP Rate Limiting**: Each IP address is limited independently
2. **Configurable Limits**: Adjust window size and request count via environment variables
3. **Development Mode**: Rate limiting is disabled in development unless explicitly enabled
4. **Health Check Exemption**: Health check and metrics endpoints are exempt from rate limiting

## Exempt Endpoints

The following endpoints are exempt from global rate limiting:
- `/metrics`
- `/health`
- `/api/v1/health`

## Testing

### Development Mode
In development mode, rate limiting is disabled by default. To test rate limiting in development:

1. Add the header `x-plasmic-test-rate-limit: true` to your requests
2. Or set the server to production mode

### Test Script
A test script is provided to verify rate limiting functionality:

```bash
node test-rate-limit.js [host] [port] [endpoint]

# Example: Test localhost
node test-rate-limit.js localhost 3003 /api/v1/auth/csrf

# Example: Test production
node test-rate-limit.js production.example.com 443 /api/v1/auth/csrf
```

## Response

When rate limited, the server responds with:
- Status Code: `429 Too Many Requests`
- Headers:
  - `X-RateLimit-Limit`: Request limit per window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit window resets
- Body: `"Too many requests from this IP, please try again later."`

## Implementation Details

The rate limiter uses:
- `express-rate-limit` package
- In-memory storage (can be upgraded to Redis for distributed systems)
- IP-based tracking using Express's trust proxy settings

## Future Improvements

1. **Redis Storage**: For distributed deployments, implement Redis-based storage
2. **User-based Limits**: Add authenticated user-based rate limiting
3. **Endpoint-specific Limits**: Different limits for different endpoint categories
4. **Dynamic Configuration**: Allow runtime configuration changes without restart