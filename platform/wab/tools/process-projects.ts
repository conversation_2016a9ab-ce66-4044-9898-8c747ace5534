import { createConnection, getConnectionOptions } from "typeorm";
import { ProjectRevision } from "../src/wab/server/entities/Entities";
import { DbMgr, SUPER_USER } from "../src/wab/server/db/DbMgr";
import fs from "fs";
import { Bundle } from "../src/wab/shared/bundles";

const writeStream = fs.createWriteStream("data.txt", { flags: 'a' });

async function main() {
  const con = await createConnection({
    ...((await getConnectionOptions()) as any),
    name: "jason-test",
    type: "postgres",
    url: "postgresql://wabreadonly:password@localhost:5433/wab",
    logging: true,
  })
  const em = con.createEntityManager();
  const dbMgr = new DbMgr(em, SUPER_USER);

  const realProjects: {
    id: string,
    name: string,
    createdAt: string,
    workspaceId: string,
    workspaceName: string,
    teamId: string,
    teamName: string,
    billingEmail: string,
    stripeCustomerId: string,
    seats: number,
  }[] = await em.query(`
select p.id, p.name, p."createdAt", w.id as "workspaceId", w.name as "workspaceName", t.id as "teamId", t.name as "teamName", t."billingEmail", t."stripeCustomerId", t.seats
from (
  select distinct pr."projectId"
  from project_revision pr
  where pr.revision > 1000 and pr."createdAt" > '2024-01-01'
) as big
left join project p on p.id = big."projectId"
left join workspace w on w.id = p."workspaceId"
left join team t on t.id = w."teamId"
order by p."createdAt" asc
`);

  console.log(realProjects);

  for (const project of realProjects) {
    try {
      const projectRev: ProjectRevision = await dbMgr.getLatestProjectRev(project.id);
      const bundle: Bundle = JSON.parse(projectRev.data);

      const depComponentsUsed = {};
      for (const [id, inst] of Object.entries(bundle.map)) {
        if (inst.__type === "TplComponent" && inst["component"]?.["__xref"]?.["uuid"]) {
          const depId: string = inst["component"]["__xref"]["uuid"];
          depComponentsUsed[depId] = (depComponentsUsed[depId] ?? 0) + 1;
        }
      }

      const depRows: { id: string, name: string, depId: string }[] = await em.query(`
select p.id, p.name, pkgv.id as "depId"
from pkg_version pkgv
left join pkg pkg on pkg.id = pkgv."pkgId"
left join project p on p.id = pkg."projectId"
where pkgv.id = ANY($1)
`, [bundle.deps]);
      console.log(bundle.deps);
      console.log(depRows);
      const deps = bundle.deps.map(
        depId => {
          const depRow = depRows.find(row => row.depId === depId);
          return {
            depId,
            depProjectId: depRow.id,
            depProjectName: depRow.name,
            componentsUsed: depComponentsUsed[depId] ?? 0,
          }
        }
      )

      writeStream.write(JSON.stringify(
        {
          ...project,
          revision: projectRev.revision,
          revisionCreatedAt: projectRev.createdAt,
          deps,
        }));
    } catch (err) {
      console.error(err);
      writeStream.write(JSON.stringify(
        {
          ...project,
          error: JSON.stringify(err),
        }
      ))
    } finally {
      writeStream.write("\n");
    }
  }
}

main()
  .catch((err) => {
    console.error(err)
  })
  .finally(() => {
    writeStream.end();
  });
