/** @format */

/**
 *
 * Refresh your cookies file by logging into Substack, opening up Devtools, and grabbing the cookie header from the curl
 * copy of the network request when loading any page on plasmic.substack.com.
 */

import { parse } from "csv-parse/sync";
import { stringify } from "csv/sync";
import execa from "execa";
import fs, { readFileSync } from "fs";
import os from "os";
import sh from "shellsync";
import { xDifference } from "../../src/wab/shared/common";

const timestampPath = `${os.homedir()}/.plasmic-misc/substack-import-timestamp`;

const alwaysIgnore = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "",
  "<EMAIL>",
  "<EMAIL>",
  "blixt",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
];

const alwaysIgnorePat = ["yopmail.com", ".whitelabeled"];

async function main() {
  try {
    // Everything after 4/6 should be squarely within post-Intercom era
    const sinceDate = readFileSync(timestampPath, "utf8");
    const real = !!process.argv[2];

    // Get new emails
    const query = `select email
                   from "user"
                   where email is not null
                     and "createdAt" > '${sinceDate}';`;
    const psqlCmd = sh.quote`psql -h localhost -p 5432 -U wabreadonly wab -Atc ${query}`;
    const { stdout: newEmailsRaw } = await execa.command(psqlCmd, {
      shell: "bash",
    });
    const newEmails = newEmailsRaw.split("\n");

    // Filter out denylisted & ignored emails.
    const { stdout: devFlagOverridesRaw } = await execa.command(
      sh.quote`psql -h localhost -p 5432 -U wabreadonly wab -Atc ${'select data from dev_flag_overrides order by "createdAt" desc limit 1;'}`,
      { shell: "bash" }
    );
    const devflags = JSON.parse(devFlagOverridesRaw);
    const reDenylist = new RegExp(devflags.hideHelpForUsers, "i");
    const withoutDenied = (emails: string[]) =>
      emails.filter(
        (email) =>
          !email.match(reDenylist) &&
          !alwaysIgnore.includes(email.toLowerCase()) &&
          !alwaysIgnorePat.some((pat) => email.toLowerCase().includes(pat))
      );
    const allowedEmails = withoutDenied(newEmails);

    // De-dupe with Substack
    const substackCsv = fs.existsSync("./substack-export.csv")
      ? parse(
          await fs.readFileSync("./substack-export.csv", {
            encoding: "utf8",
          })
        )
      : [["Email"]];
    const emailCol = substackCsv[0].indexOf("Email");
    const substackEmails = substackCsv.slice(1).map((row) => row[emailCol]);
    const toAdd = [...xDifference(allowedEmails, substackEmails)];

    const toAddCsv = [["Email"], ...toAdd.map((email) => [email])];
    await fs.writeFileSync(
      "./to-import-into-substack.csv",
      stringify(toAddCsv)
    );

    // Warn about Substack users to remove
    const toRemove = withoutDenied(substackEmails);
    if (toRemove.length > 0) {
      console.log("REMOVE THE FOLLOWING!");
      console.log([...xDifference(substackEmails, toRemove)].join(","));
    }

    if (!real) {
      return;
    }

    fs.writeFileSync(timestampPath, new Date().toISOString());

    // Upload to Substack
    const cookie = (
      await fs.readFileSync(`${os.homedir()}/.plasmic-misc/substack-cookie`, {
        encoding: "utf8",
      })
    ).trim();
    const payload = JSON.stringify({
      email: allowedEmails.join(","),
      subscription: false,
    });
    console.log(
      sh.quote`bash ./tools/internal/import-to-substack.bash ${cookie} ${payload}`
    );
    const { stdout } = await execa.command(
      sh.quote`bash ./tools/internal/import-to-substack.bash ${cookie} ${payload}`,
      { shell: "bash", stderr: "inherit" }
    );
    if (stdout !== "{}") {
      console.error(`Received response: ${stdout}`);
      throw new Error();
    }
  } catch (err) {
    console.log(err, err.stack);
    process.exit(1);
  }
}

main();
