/**
 * Test with --send-self and/or --save-state (for testing state/re-runs).
 *
 * If you run again right after a --save-state, you should not see any new emails to send.
 *
 * Run with --send-real-and-save-state to actually send and update state file.
 *
 * @format
 */

import { parse } from "csv/sync";
import execa from "execa";
import * as fs from "fs";
import os from "os";
import pg from "pg";
import sh from "shellsync";
import "temporal-polyfill/global";
import { z } from "zod";
import { createMailer } from "../../../src/wab/server/emails/Mailer";

const client = new pg.Client();
const escapeLiteral = (text: string) => client.escapeLiteral(text);

const ignoreSet = new Set(["<EMAIL>"]);
const isIgnore = (email: string) =>
  ignoreSet.has(email) ||
  email.match(
    /.*@(envato.com|linearb.io|kilo.health|scale.com|acuitymd.com|circles.asia|guidewire.com)$/
  );

process.on("unhandledRejection", (error) => {
  throw error;
});

const SentMailLogEntry = z.object({
  timestamp: z.string(),
  email: z.string(),
  type: z.union([z.literal("signup"), z.literal("churn")]),
});
const SentMailLog = z.array(SentMailLogEntry);

const dbhost = "proddb.c856obael8lq.us-west-2.rds.amazonaws.com";

/**
 * If sshProfile is given, then `ssh profile sudo -u postgres psql`, else `psql -h host -U user dbname`.
 */
async function doQuery(
  sshProfile: string | undefined,
  dbuser: string,
  dbname: string,
  query: string
) {
  const csvQuery = `copy (${query}) to stdout with csv delimiter ',' header`;
  const psqlCmd = sshProfile
    ? sh.quote`sudo -u postgres psql ${dbname} -c ${csvQuery}`
    : sh.quote`psql -h ${dbhost} -U ${dbuser} ${dbname} -c ${csvQuery}`;
  const finalCmd = sshProfile
    ? sh.quote`ssh ${sshProfile} ${psqlCmd}`
    : psqlCmd;
  const { stdout: signupsRaw } = await execa.command(finalCmd, {
    shell: "bash",
  });
  const results = parse(signupsRaw, { columns: true });
  return results;
}

async function queryNames(churnersToEmail) {
  if (churnersToEmail.length === 0) {
    return [];
  }
  const churnersToEmailWithNames = await doQuery(
    undefined,
    "wab",
    "wab",
    `
select lower(email) as email, "firstName"
from "user"
where "deletedAt" is null and lower(email) in (${churnersToEmail
      .map((email) => escapeLiteral(email))
      .join(",")})
`
  );
  return churnersToEmailWithNames;
}

function name(text: string) {
  return text && text.match(/^[A-Z][a-z]/) ? " " + text : "";
}

async function main() {
  const saveState = process.argv.includes("--save-state");
  const sendSelf = process.argv.includes("--send-self");
  const sendReal = process.argv.includes("--send-real-and-save-state");
  const send = sendSelf || sendReal;

  // Enable ssh-ing into gerrit.

  await execa.command(
    "aws ec2-instance-connect send-ssh-public-key --instance-id i-05fe50699238dd81d --instance-os-user ubuntu --ssh-public-key file://$HOME/.ssh/id_rsa.pub",
    { shell: "bash" }
  );

  const { stdout: gerritDescrip } = await execa.command(
    "source tools/util.bash; describe-instance-by-name gerrit-server",
    { shell: "bash" }
  );
  const [gerritId, gerritIp] = gerritDescrip.trim().split(/\s+/g);

  // Prepare email denylist.

  const [{ data: devFlagOverridesRaw }] = await doQuery(
    undefined,
    "wab",
    "wab",
    `select data from dev_flag_overrides order by "createdAt" desc limit 1`
  );
  const devflags = JSON.parse(devFlagOverridesRaw);
  const reDenylist = new RegExp(devflags.hideHelpForUsers, "i");
  const withoutDenied = (emails: string[]) =>
    emails.filter((email) => !email.match(reDenylist));

  // Get signups - users who have visited in the past 2 weeks.

  const signupRows = await doQuery(
    undefined,
    `wab`,
    `wab`,
    `
select lower(email) as email
from "user"
where "createdAt" > now() - '2 weeks'::interval
  and "deletedAt" is null
  and lower(email) !~ '@(163|googlemail|gmail|yahoo|outlook|hotmail|live|yandex|mail|protonmail|icloud|naver|guidewire|qq|comcast|proton)\\.[a-z]{2,3}$|\\.edu$|whitelabeled$'
order by 1
`
  );
  const signupEmails = withoutDenied(signupRows.map(({ email }) => email));

  // Get churners - users who visited in the past quarter but not in the past 2 weeks.
  // We push down usage of emails here since the same user can be mapped to multiple Segment user IDs (not digging into this).

  const churnerRows = await doQuery(
    "ubuntu@" + gerritIp,
    "segment",
    "segment",
    `
select distinct lower(u.email) as email
from app.tracks t join app.users u on t.user_id = u.id
where t.received_at > now() - '3 months'::interval
except
select distinct lower(u.email) as email
from app.tracks t join app.users u on t.user_id = u.id
where t.received_at > now() - '2 weeks'::interval
`
  );
  const churnerEmails = withoutDenied(churnerRows.map(({ email }) => email));

  // Load history of sent emails.
  const sentMailLogPath = `${os.homedir()}/.plasmic-misc/sent-mail-log.json`;
  const sentMailLog = SentMailLog.parse(
    fs.existsSync(sentMailLogPath)
      ? JSON.parse(fs.readFileSync(sentMailLogPath, "utf8"))
      : []
  );

  if (sentMailLog.length === 0) {
    throw new Error(
      "Sanity check that we are not emailing everyone! (The sent mail log should not be empty, or else we are emailing everyone.)"
    );
  }

  // Don't re-spam users we've already emailed churn messages to in the past 999 months,
  // or users we've already emailed greetings to in the past 2 weeks.
  const now = Temporal.Now.plainDateTimeISO();
  const alreadyEmailed = new Set(
    sentMailLog
      .filter(
        (row) =>
          (row.type === "churn" &&
            Temporal.PlainDateTime.from(row.timestamp)
              .since(now.subtract(Temporal.Duration.from({ months: 999 })))
              .total({ unit: "seconds" }) > 0) ||
          (row.type === "signup" &&
            Temporal.PlainDateTime.from(row.timestamp)
              .since(now.subtract(Temporal.Duration.from({ days: 15 })))
              .total({ unit: "seconds" }) > 0)
      )
      .map((row) => row.email)
  );
  const allChurnerEmails = new Set(
    sentMailLog.filter((row) => row.type === "churn").map((row) => row.email)
  );
  const allSignupEmails = new Set(
    sentMailLog.filter((row) => row.type === "signup").map((row) => row.email)
  );

  // Don't send churn emails to users who are paying subscribers on a team.
  const subscriberRows = await doQuery(
    undefined,
    "wab",
    "wab",
    `
select lower(u.email) as email
from "user" u join permission p on u.id = p."userId"
join team t on p."teamId" = t.id
where t."stripeSubscriptionId" is not null
`
  );
  const subscribers = new Set(subscriberRows.map((row) => row.email));

  // Exclude past churner emails for good measure
  const churnersToEmail = churnerEmails.filter(
    (email) =>
      !isIgnore(email) &&
      !alreadyEmailed.has(email) &&
      !subscribers.has(email) &&
      !allChurnerEmails.has(email)
  );
  console.log("CHURNERS TO EMAIL", churnersToEmail.join("\n"));

  // Exclude past signup emails for good measure
  const signupsToEmail = signupEmails.filter(
    (email) =>
      !isIgnore(email) &&
      !alreadyEmailed.has(email) &&
      !churnersToEmail.includes(email) &&
      !allSignupEmails.has(email)
  );
  console.log("SIGNUPS TO EMAIL", signupsToEmail.join("\n"));

  // Actually send emails.
  // Log sent emails as we send, so that partial failures don't lose our progress.

  const mailer = createMailer();

  const timestamp = Temporal.Now.plainDateTimeISO().toString();
  function logSentMail(email: string, type: "churn" | "signup") {
    if (saveState || sendReal) {
      sentMailLog.push({
        email,
        type: type,
        timestamp,
      });
      fs.writeFileSync(sentMailLogPath + ".tmp", JSON.stringify(sentMailLog));
      fs.renameSync(sentMailLogPath + ".tmp", sentMailLogPath);
    }
  }

  const churnersToEmailWithNames = await queryNames(churnersToEmail);
  for (const { email, firstName } of churnersToEmailWithNames) {
    console.log("emailing churner", firstName, email);
    if (send) {
      await mailer.sendMail({
        from: "Yang Zhang <<EMAIL>>",
        to: sendSelf ? "<EMAIL>" : sendReal ? email : undefined,
        subject: `Feedback`,
        text: `
Hi${name(firstName)} -- I'm Yang @ Plasmic.

I'm reaching out because it's been a little while since you logged in, so just wanted to check in.

If you're simply busy, no problem at all! But if it's more than that, is there anything I can do to make your Plasmic experience better?
`.trim(),
      });
    }
    logSentMail(email, "churn");
  }

  const signupsToEmailWithNames = await queryNames(signupsToEmail);
  for (const { email, firstName } of signupsToEmailWithNames) {
    console.log("emailing signup", firstName, email);
    if (send) {
      await mailer.sendMail({
        from: "Yang Zhang <<EMAIL>>",
        to: sendSelf ? "<EMAIL>" : sendReal ? email : undefined,
        subject: `👋${name(firstName) || "Hello from Plasmic"}`,
        text: `
Hi${name(firstName)} -- I work on Plasmic. Thank you for signing up!

Is there any specific project you're looking to use Plasmic for?

Either way, I just wanted to make myself available as a resource, both technical and otherwise, to answer any questions (e.g. is X a good use case for Plasmic?) or help you get started with Plasmic.

For now, anything I can help with?
`.trim(),
      });
    }
    logSentMail(email, "signup");
  }
}

main();
