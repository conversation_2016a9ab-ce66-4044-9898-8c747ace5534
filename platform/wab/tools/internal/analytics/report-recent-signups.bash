#!/usr/bin/env bash

set -o errexit -o nounset -o pipefail

timestamp_path=~/.plasmic-misc/last-report-timestamp
if [[ ! -f $timestamp_path ]]; then
  query_ts="select now() - '28 days'::interval"
  psql -Atc "${query_ts}" -h db.aws.plasmic.app -U wab wab >"$timestamp_path"
fi

last_timestamp="$(cat "$timestamp_path")"

query1="$(
  cat <<EOF
select (substring(email from '(?<=@).*')) as domain, email, source, role, "surveyResponse"->>'projectOption' as project
from "user"
where email !~ '@(163|googlemail|gmail|yahoo|outlook|hotmail|live|yandex|mail|protonmail|icloud|naver|guidewire|qq|comcast|proton)\.[a-z]{2,3}$|\.edu$|whitelabeled$'
  and "createdAt" > '$last_timestamp'
order by 1;
EOF
)"

query2="$(
  cat <<EOF
select
  (substring(email from '(?<=@).*')) as domain,
  source,
  "createdAt",
  role,
  email,
  "surveyResponse"->>'projectOption' as project,
  "firstName",
  "lastName",
  'https://www.linkedin.com/search/results/companies/?keywords=' || (substring(email from '(?<=@)[^.]+')) as linkedin
from "user"
where email !~ '@(163|googlemail|gmail|yahoo|outlook|hotmail|live|yandex|mail|protonmail|icloud|naver|guidewire|qq|comcast|proton)\.[a-z]{2,3}$|\.edu$|whitelabeled$'
  and "createdAt" > '$last_timestamp'
order by 3 desc;
EOF
)"

query_ts="select now()"
now="$(psql -Atc "${query_ts}" -h db.aws.plasmic.app -U wab wab)"

echo 'Recent signups from non-consumer non-whitelabeled domains'
echo "$last_timestamp to"
echo "$now"
echo

psql -c "${query1}" -h db.aws.plasmic.app -U wab wab
psql -xc "${query2}" -h db.aws.plasmic.app -U wab wab

if [[ ! ${pretend:-} ]]
then echo "$now" >"$timestamp_path"
fi