import * as fs from "fs";
import { crunch } from "../../src/wab/shared/common";

async function main() {
  const content = fs.readFileSync("/Users/<USER>/Desktop/hiring.txt", "utf8");

  const query = crunch(
    [...content.matchAll(/[A-Z]+\n\n(.*?)\n\n/gs)]
      .map((match) => match[1].trim().split(/\n/g).join(" OR "))
      .join(" OR ")
  );

  console.log(query);
  console.log();
  let acc: string[] = [];

  function showAndReset() {
    console.log(
      `https://www.upwork.com/ab/profiles/search/?q=React%20AND%20(${encodeURIComponent(
        acc.join(" OR ")
      )})&rate=10-30&english=2&category_uid=531770282580668418&occupation_uid=1110580755107926016&loc=central-america,south-america&user_pref=1`
    );
    console.log();
    acc = [];
  }

  for (const chunk of query.split(/ OR /g)) {
    acc.push(chunk);
    if (acc.join(" OR ").length > 100) {
      showAndReset();
    }
  }
  showAndReset();
}

main();
