/**
 * # Generic Builder CMS importer script for the Plasmic CMS
 *
 * Please configure things under INPUT PARAMS!!
 *
 * First we generate the schema in two passes. Second pass fills the schema, first pass just creates the types so that they can be referenced in the schema. We use table upsertion API call.
 *
 * Second we populate the entries in two passes.
 *
 * The first pass we populate all the entries as best we can (1) without references (since we don't have the IDs of every model type yet) and (2) without images (since they are slow to upload). We also clear any existing entries.
 *
 * The second pass we populate all the entries, but with the missing data this time. We also publish anything that should be published.
 *
 * We include draft data but skip deleted/archived data (since in source systems these may not even have the right schemas).
 *
 * For file uploads we rely on the private /cmse API for now.
 *
 * All operations are cached in ~/.plasmic-misc/importer-cache.json.
 *
 * ## For re-migrations
 *
 * All missing entries are inserted, but all entries (including existing) are overwritten to sync with the source.
 *
 * Optionally set doSchema to false.
 *
 * Also optionally run only for specific models. But need to make sure all ref dependencies are there - we checked that there are no broken refs (unless they point to archived entries). This works based on the migratedId recorded.
 *
 * TODO should fix the type: "model" fields
 */

import fs from "fs";
import { open } from "lmdb";
import { isEqual, mapValues, omit, omitBy, pick } from "lodash";
import camelCase from "lodash/camelCase";
import difference from "lodash/difference";
import stringify from "safe-stable-stringify";
import { scaleSchema } from "../../../sandbox/import-cms/scale-schema.ts";
import jsonFetch from "../../src/wab/commons/json-fetch";
import { sha256 } from "../../src/wab/server/util/hash";
import {
  ApiCmsDatabase,
  ApiCmsRow,
  ApiCmsTable,
  ApiCmsWriteRow,
  CmsFieldMeta,
  CmsTableId,
  CmsTypeName,
} from "../../src/wab/shared/ApiSchema";
import {
  assert,
  ensure,
  jsonClone,
  maybe,
  spawn,
  strictZip,
} from "../../src/wab/shared/common";
import { smartHumanize } from "../../src/wab/shared/strs";

const tqdm = require("tqdm");

const concurrency = 16;

const imageTypes = ["jpeg", "png", "svg", "webp", "gif"];

const promisePool = async <T>(
  count: number,
  promiseFuncs: (() => Promise<T>)[]
) => {
  const iterator = tqdm(promiseFuncs.entries(), { total: promiseFuncs.length });
  const results: T[] = Array(promiseFuncs.length);
  const workers = Array(count)
    .fill(null)
    .map(async () => {
      // @ts-ignore
      for (const [index, func] of iterator) {
        results[index] = await func();
      }
    });
  await Promise.all(workers);
  return results;
};
const cache = open({
  path: process.env.HOME + "/.plasmic-misc/importer-cache.json",
  // any options go here, we can turn on compression like this:
  compression: true,
});

async function cacheLookup(
  key: string,
  refresh: boolean,
  fetcher: () => Promise<any>
) {
  const cached = await cache.get(key);
  if (cached && !refresh) {
    return JSON.parse(cached);
  }
  const result = await fetcher();
  await cache.put(key, JSON.stringify(result));
  return result;
}

type SimpleTable = Omit<
  ApiCmsTable,
  | "createdAt"
  | "createdById"
  | "updatedAt"
  | "updatedById"
  | "deletedAt"
  | "deletedById"
  | "id"
>;
const builderTypes = [
  "TinyTextEditor",
  "boolean",
  "date",
  "file",
  "html",
  "list",
  "longText",
  "map",
  "model",
  "number",
  "object",
  "reference",
  "text",
  "timestamp",
  "uiBlocks",
  "url",
] as const;
type BuilderTypes = (typeof builderTypes)[number];
// Maybe can replace with `satisfies` keyword when we upgrade TS
const typeMapChecked: {
  [key in BuilderTypes]: CmsTypeName | "unhandled:model" | "unhandled:uiBlocks";
} = {
  TinyTextEditor: "rich-text",
  boolean: "boolean",
  date: "date-time",
  file: "file",
  html: "rich-text",
  list: "list",
  longText: "long-text",
  map: "long-text",
  model: "unhandled:model",
  number: "number",
  object: "object",
  reference: "ref",
  text: "text",
  timestamp: "date-time",
  uiBlocks: "unhandled:uiBlocks",
  url: "text",
};
const typeMap = {
  TinyTextEditor: "rich-text",
  boolean: "boolean",
  date: "date-time",
  file: "file",
  html: "rich-text",
  list: "list",
  longText: "long-text",
  map: "long-text",
  model: "unhandled:model",
  number: "number",
  object: "object",
  reference: "ref",
  text: "text",
  timestamp: "date-time",
  uiBlocks: "unhandled:uiBlocks",
  url: "text",
} as const;
type BuilderModel = (typeof scaleSchema.data.models)[number];
type BuilderField = BuilderModel["fields"][number];

async function mn() {
  const prelimTables: SimpleTable[] = scaleSchema.data.models.map((model) => ({
    name: smartHumanize(model.name),
    identifier: camelCase(model.name),
    description: model.helperText,
    schema: {
      fields: [],
    },
  }));

  const { user, token, host } = JSON.parse(
    fs.readFileSync(
      process.argv[2] || process.env.HOME + "/.plasmic.auth",
      "utf8"
    )
  );
  const baseUrl = host;

  const headers = {
    "x-plasmic-api-user": user,
    "x-plasmic-api-token": token,
    "content-type": "application/json",
  };

  // INPUT PARAMS

  // Main
  const cmsId = `eavgJ5cRW89hU6yjD6c6Jc`;
  // Temp Pending
  // const cmsId = `27AqFUoWS3n152wUxaceNs`;

  const doSchema = false;

  const doRefresh = false;

  const doRefreshAndPopulateOnlyTables: string[] | undefined = undefined;
  // const doRefreshAndPopulateOnlyTables = ["blogModel"];

  const pretend = false;

  const keepExisting = true;

  const firstPageOnly = false;

  const doPlasmicPlasmic = false;

  const echoOnPretend = false;

  async function maybeJsonFetch<JSON = any>(
    input: RequestInfo,
    init?: RequestInit
  ): Promise<JSON> {
    return !pretend
      ? jsonFetch(input, init)
      : echoOnPretend
      ? (console.log(input, init) as any)
      : undefined;
  }

  function rec(field): CmsFieldMeta {
    const type = typeMap[field.type];
    const base = {
      name: "", // unused
      identifier: camelCase(field.name),
      label: smartHumanize(field.name),
      helperText: field.helperText,
      required: field.required,
      hidden: field.hidden,
      localized: false,
      defaultValueByLocale: {},
    };

    return field.type === "list"
      ? {
          type: typeMap[field.type],
          ...base,
          fields: field.subFields.map(rec),
        }
      : field.type === "object"
      ? {
          type: typeMap[field.type],
          ...base,
          fields: field.subFields.map(rec),
        }
      : field.type === "reference"
      ? {
          type: typeMap[field.type],
          ...base,
          tableId: ensure(modelToTableId.get(field.modelId)),
        }
      : field.type === "file"
      ? {
          type:
            difference(field.allowedFileTypes ?? [], imageTypes).length === 0
              ? "image"
              : "file",
          ...base,
        }
      : { type, ...base };
  }

  if (doSchema) {
    const oldDb_: ApiCmsDatabase = await maybeJsonFetch(
      `${baseUrl}/api/v1/cms/databases/${cmsId}/tables`,
      {
        method: "PUT",
        body: JSON.stringify({
          deleteUnspecified: false,
          tables: prelimTables,
        }),
        headers,
      }
    );
  }

  const oldDb: ApiCmsDatabase = await jsonFetch(
    `${baseUrl}/api/v1/cmse/databases/${cmsId}`,
    {
      method: "GET",
      headers: {
        ...headers,
      },
    }
  );

  const modelToTableId = new Map<string, CmsTableId>(
    scaleSchema.data.models.map((model) => [
      model.id,
      ensure(
        oldDb.tables.find((table) => table.identifier === camelCase(model.name))
      ).id,
    ])
  );

  const srcPlasmicCms: ApiCmsDatabase = await jsonFetch(
    `${baseUrl}/api/v1/cmse/databases/eavgJ5cRW89hU6yjD6c6Jc`,
    {
      method: "GET",
      headers,
    }
  );

  const newTablesInPlasmic = srcPlasmicCms.tables
    .map((t) => pick(t, ["name", "identifier", "description", "schema"]))
    .filter((table) => table.identifier.startsWith("summit"));

  const tables: SimpleTable[] = [
    ...scaleSchema.data.models.map((model) => ({
      name: model.name,
      identifier: camelCase(model.name),
      description: model.helperText,
      schema: {
        fields: [
          ...model.fields.map((field) => {
            return rec(field);
          }),
          {
            type: "text",
            name: "",
            identifier: "migratedId",
            label: "Migrated ID",
            helperText: "",
            required: false,
            hidden: false,
            localized: false,
            defaultValueByLocale: {},
          },
        ],
      },
    })),
    ...newTablesInPlasmic.map((table) => ({
      ...table,
      schema: {
        fields: [
          ...table.schema.fields,
          {
            type: "text",
            name: "",
            identifier: "migratedId",
            label: "Migrated ID",
            helperText: "",
            required: false,
            hidden: false,
            localized: false,
            defaultValueByLocale: {},
          },
        ],
      },
    })),
  ];

  console.log(tables);

  if (doSchema) {
    const newDb: ApiCmsDatabase = await maybeJsonFetch(
      `${baseUrl}/api/v1/cms/databases/${cmsId}/tables`,
      {
        method: "PUT",
        body: JSON.stringify({
          deleteUnspecified: false,
          tables: tables,
        }),
        headers,
      }
    );
    console.log("Final database:", newDb);
  }

  async function getModel(model: BuilderModel) {
    const modelSymName = camelCase(model.name);
    const allResults: {
      id: string;
      name: string;
      published: string;
      data: any;
    }[] = [];
    for (let page = 0; true; page++) {
      const query = `
query Q($offset: Int) {
  ${modelSymName}(limit:100, options:{includeUnpublished:true}, offset:$offset) {
    id
    name
    published
    data {
      ${model.fields.map((field) => field.name).join("\n")}
    }
  }
}
`;
      const body = JSON.stringify({
        query,
        variables: { offset: 100 * page },
      });
      const queryRes = await cacheLookup(
        sha256(body),
        doRefresh && shouldRefreshAndPopulateTable(modelSymName),
        () =>
          jsonFetch(
            "https://cdn.builder.io/api/v3/graphql/e0438815ba51486bbb6a202747122d4b/view?",
            {
              headers: {
                accept: "application/json",
                "accept-language": "en-US,en;q=0.9",
                "cache-control": "no-cache",
                "content-type": "application/json",
                pragma: "no-cache",
                "sec-ch-ua":
                  '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"macOS"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                Referer: "https://cdn.builder.io/",
                "Referrer-Policy": "origin",
              },
              body: body,
              method: "POST",
            }
          )
      );
      const newRows = Object.values(queryRes.data)[0];
      allResults.push(...newRows);
      if (newRows.length < 100 || firstPageOnly) {
        console.log("done", allResults.length);
        break;
      }
    }
    return [modelSymName, allResults] as const;
  }

  // Perform two-phase insertion. First time, we have the inserts.

  const srcRowToDstRowId = new Map<string, string>();

  async function downloadAndUploadFile(downloadUrl: string): Promise<any> {
    const uploadUrl = `${baseUrl}/api/v1/cmse/file-upload`;
    return cacheLookup(
      JSON.stringify({ v: 3, downloadUrl, uploadUrl }),
      false,
      async () => {
        // Step 1: Download the image from the download URL
        const download = await fetch(downloadUrl);
        if (!download.ok) {
          throw new Error("Failed to download the image");
        }
        const imageBlob = await download.blob();

        // Step 2: Upload the image to the upload URL
        const formData = new FormData();
        formData.append("image", imageBlob, "image.jpg");

        const uploadRes = await jsonFetch(uploadUrl, {
          method: "POST",
          body: formData,
          headers: {
            "x-plasmic-api-cms-tokens":
              "eavgJ5cRW89hU6yjD6c6Jc:FzrN0L7aboDRPzxGJLg8d5io6ZiN8FBUNwCS9oDWUcC5ZIfOyVlouGzuqDcM6cSNbTSx0eO0qTvOp36hiw",
          },
        });
        return uploadRes.files[0];
      }
    );
  }

  let allSrcRowsCum = [];

  function shouldRefreshAndPopulateTable(tableName: string) {
    return (
      !doRefreshAndPopulateOnlyTables ||
      doRefreshAndPopulateOnlyTables.includes(tableName)
    );
  }

  // First migrate Plasmic -> Plasmic
  if (doPlasmicPlasmic) {
    for (const newTable of newTablesInPlasmic) {
      const rows = (
        await jsonFetch<{ rows: ApiCmsRow[] }>(
          `${baseUrl}/api/v1/cms/databases/eavgJ5cRW89hU6yjD6c6Jc/tables/${
            newTable.identifier
          }/query?draft=1&q=${encodeURIComponent(
            JSON.stringify({ limit: 999 })
          )}`,
          { headers }
        )
      ).rows;

      console.log(newTable.identifier, rows);
      await clearPlasmicTable(newTable.identifier);

      const insertRes: {
        rows: ApiCmsWriteRow[];
      } = await maybeJsonFetch(
        `${baseUrl}/api/v1/cms/databases/${cmsId}/tables/${newTable.identifier}/rows`,
        {
          method: "POST",
          body: JSON.stringify({
            rows: rows.map((row) => {
              return {
                ...row,
                data: {
                  ...row.data,
                  migratedId: row.id,
                },
              };
            }),
          }),
          headers,
        }
      );
    }
  }
  // TODO break this up into separate code, instead of a loop. They differ more than they share.
  const existingDstRowsByType = new Map<string, ApiCmsRow[]>();

  async function clearPlasmicTable(tableName: string) {
    const { rows } = await getPlasmicRows(tableName);
    console.log(`Truncating ${rows.length} rows`);
    await promisePool(
      concurrency,
      rows.map(
        (row) => () =>
          maybeJsonFetch(`${baseUrl}/api/v1/cms/rows/${row.id}`, {
            method: "DELETE",
            headers,
          })
      )
    );
  }

  async function getPlasmicRows(tableName: string) {
    return await jsonFetch<{ rows: ApiCmsRow[] }>(
      `${baseUrl}/api/v1/cms/databases/${cmsId}/tables/${tableName}/query?draft=1&q=${encodeURIComponent(
        JSON.stringify({ limit: 999 })
      )}&nonce=${Math.random()}`,
      { headers }
    );
  }

  for (const pass of ["insert", "update"]) {
    if (pass === "update") {
      console.log("!!", srcRowToDstRowId);
    }
    for (const model of scaleSchema.data.models) {
      console.log(`PASS ${pass} PROCESSING MODEL`, model.name);
      if (model.fields.length === 0) {
        continue;
      }

      const [modelSymName, queryRes] = await getModel(model);

      // Should only run in the insert pass. We don't want to include the newly inserted rows.
      if (pass === "insert") {
        existingDstRowsByType.set(
          modelSymName,
          (await getPlasmicRows(modelSymName)).rows
        );
      }
      const existingDstRows = ensure(existingDstRowsByType.get(modelSymName));

      // First, delete any existing data
      if (
        pass === "insert" &&
        shouldRefreshAndPopulateTable(modelSymName) &&
        !keepExisting
      ) {
        await clearPlasmicTable(modelSymName);
        existingDstRows.splice(0, existingDstRows.length);
      }

      const table = tables.find((t) => t.identifier === modelSymName);

      const allSrcRows = queryRes;
      if (pass === "insert") {
        allSrcRowsCum = allSrcRowsCum.concat(allSrcRows);
      }
      const srcRows = allSrcRows.filter((e) => e.published !== "archived");

      const mungeSrcRow = async (field: CmsFieldMeta, obj: any) => {
        !obj
          ? null
          : field.type === "list"
          ? obj[field.identifier]?.map((x) =>
              field.fields.map((f) => mungeSrcRow(f, x))
            )
          : field.type === "object"
          ? field.fields.map((f) => mungeSrcRow(f, obj[field.identifier]))
          : field.type === "ref"
          ? (obj[field.identifier] =
              pass === "update" && obj[field.identifier]?.id
                ? // Either it exists, or it better be an archived entry.
                  (() => {
                    return (
                      srcRowToDstRowId.get(obj[field.identifier].id) ??
                      (() => {
                        const foundRow = ensure(
                          allSrcRowsCum.find(
                            (r) => r.id === obj[field.identifier].id
                          ),
                          `Found a ref in field ${field.identifier} to object ${
                            obj[field.identifier].id
                          } but it's not found anywhere including among archived`
                        );
                        assert(
                          foundRow.published === "archived",
                          `Found a ref in field ${field.identifier} to object ${
                            obj[field.identifier].id
                          } but it's not in the synced set and it's not archived, which shouldn't be possible since all non-archived should be synced`
                        );
                        return undefined;
                      })()
                    );
                  })()
                : obj[field.identifier]?.id)
          : field.type === "file" || field.type === "image"
          ? // Save expensive step for later
            pass === "update"
            ? (obj[field.identifier] = await maybe(
                obj[field.identifier],
                downloadAndUploadFile
              ))
            : null
          : field.type === "date-time"
          ? (obj[field.identifier] = obj[field.identifier]
              ? new Date(obj[field.identifier]).toISOString()
              : null)
          : null;
      };

      // Get the existing rows from the CMS
      // Match by srcRow.name / dstRow.identifier

      const srcRowsToInsert: typeof srcRows = [];

      const existingDstRowByMigratedId = new Map(
        existingDstRows.map((row) => [row.data.migratedId, row])
      );
      if (modelSymName === "blogModel")
        console.log("!!", existingDstRows.length);

      for (const srcRow of srcRows) {
        const dstRow = existingDstRowByMigratedId.get(srcRow.id);
        if (dstRow) {
          srcRowToDstRowId.set(srcRow.id, dstRow.id);
        } else {
          srcRowsToInsert.push(srcRow);
        }
      }

      if (shouldRefreshAndPopulateTable(modelSymName)) {
        console.log("prepping rows");
        const desiredDstRows = await promisePool(
          concurrency,
          srcRows.map((srcRow) => async () => {
            const data = jsonClone(srcRow.data);

            for (const field of table.schema.fields) {
              await mungeSrcRow(field, data);
            }
            data.migratedId = srcRow.id;

            return {
              identifier: srcRow.name,
              data: data,
            };
          })
        );
        const dstRowsToInsert = desiredDstRows.filter((dstRow) =>
          srcRowsToInsert.some((srcRow) => srcRow.id === dstRow.data.migratedId)
        );

        if (pass === "insert") {
          console.log("inserting rows", dstRowsToInsert.length);
          const insertRes: {
            rows: ApiCmsWriteRow[];
          } = (await maybeJsonFetch(
            `${baseUrl}/api/v1/cms/databases/${cmsId}/tables/${table.identifier}/rows`,
            {
              method: "POST",
              body: JSON.stringify({
                rows: dstRowsToInsert,
              }),
              headers,
            }
          )) ?? {
            rows: dstRowsToInsert.map((row) => ({ id: "FAKEID" } as any)),
          };
          for (const [srcRow, created] of strictZip(
            srcRowsToInsert,
            insertRes.rows
          )) {
            srcRowToDstRowId.set(srcRow.id, created.id);
          }
        }

        const omitFalsyRec = (x: any) =>
          mapValues(
            omitBy(x, (v) => !v),
            (v) => (typeof v === "object" ? omitFalsyRec(v) : v)
          );

        function compare(x: any, y: any) {
          x = omitFalsyRec(omit(x, "blocks"));
          y = omitFalsyRec(omit(y, "blocks"));
          const eq = isEqual(x, y);
          if (!eq) {
            fs.writeFileSync("/tmp/a", stringify(x, null, 2));
            fs.writeFileSync("/tmp/b", stringify(y, null, 2));
          }
          return eq;
        }
        if (pass === "update") {
          const dstRowsToUpdate = strictZip(srcRows, desiredDstRows).flatMap(
            ([srcRow, desiredDstRow]) =>
              // Either it's new / was just inserted, or it was existing and different
              srcRowsToInsert.some((r) => r.id === srcRow.id) ||
              !compare(
                existingDstRowByMigratedId.get(desiredDstRow.data.migratedId)
                  .data,
                desiredDstRow.data
              )
                ? [desiredDstRow]
                : []
          );
          console.log(
            "!! TOTAL",
            srcRows.length,
            "MUST INSERT",
            srcRowsToInsert.length,
            "AND UPDATE",
            dstRowsToUpdate.length
          );
          // May need to update more than just dstRowsToInsert, since there could be references that we need to now fill in (in existing rows).
          await promisePool(
            concurrency,
            dstRowsToUpdate.map((dstRow) => async () => {
              const srcRow = ensure(
                srcRows.find((srcRow) => srcRow.id === dstRow.data.migratedId),
                ""
              );
              const dstRowId = srcRowToDstRowId.get(srcRow.id);
              await maybeJsonFetch(`${baseUrl}/api/v1/cms/rows/${dstRowId}`, {
                method: "PUT",
                headers,
                body: JSON.stringify(dstRow),
              });
              if (srcRow.published === "published") {
                await maybeJsonFetch(
                  `${baseUrl}/api/v1/cms/rows/${dstRowId}/publish`,
                  {
                    method: "POST",
                    headers,
                  }
                );
              }
            })
          );
        }
      }
    }
  }
  console.log("all done!");
}

spawn(mn());
