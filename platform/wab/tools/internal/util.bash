# aws-configure-mfa gives you access to the full permissions on your IAM user.
#
# By default, all human users have a "DenyIfNoMFA" policy, which greatly
# restricts the permissions a user has, unless <PERSON><PERSON> is present.
#
# This command will configure an "mfa" profile for your AWS CLI, calling
# `aws sts get-session-token` with your MFA code to get temporary session
# credentials with <PERSON><PERSON> present.
#
# Prerequisites:
# - Create an MFA.
# - Create a CLI access key on console (has limited access due to DenyIfNoMFA).
# - Run `aws configure`, copy/paste the access key ID and secret.
# - Test with `aws sts get-caller-identity`.
aws-configure-mfa() {
  # Local file to store MFA ARN.
  MFA_ARN_FILE="$HOME/.aws_mfa_arn"

  # Read file if it exists.
  if [ -f "$MFA_ARN_FILE" ]; then
    MFA_ARN=$(cat $MFA_ARN_FILE)
  fi

  # Else prompt for MFA ARN.
  if [ -z "$MFA_ARN" ]; then
    echo "Enter your MFA ARN (find here https://console.aws.amazon.com/iam/home?#security_credential):"
    read MFA_ARN

    # Save to file for future use
    echo $MFA_ARN > $MFA_ARN_FILE
  fi

  # Prompt for MFA code.
  echo "Enter your MFA code:"
  read MFA_CODE

  # Get a session token from STS.
  # Use "default" profile, since "mfa" profile may still be exported.
  DURATION_SECONDS=86400 # 24 hours
  SESSION_JSON=$(aws sts get-session-token \
    --profile default \
    --serial-number $MFA_ARN \
    --token-code $MFA_CODE \
    --duration-seconds $DURATION_SECONDS \
  )
  if [ $? -ne 0 ]; then
    return 1
  fi

  # Configure AWS CLI with the temporary credentials
  aws configure set --profile mfa region us-west-2
  aws configure set --profile mfa aws_access_key_id $(echo $SESSION_JSON | jq -r '.Credentials.AccessKeyId')
  aws configure set --profile mfa aws_secret_access_key $(echo $SESSION_JSON | jq -r '.Credentials.SecretAccessKey')
  aws configure set --profile mfa aws_session_token $(echo $SESSION_JSON | jq -r '.Credentials.SessionToken')

  # Confirm the login
  echo 'Credentials written to "mfa" profile.'
  echo 'Running `export AWS_PROFILE=mfa` to default to this profile.'
  export AWS_PROFILE=mfa
  echo "Session expires in $((DURATION_SECONDS/3600)) hours."
  echo "You are now signed in with MFA as:"
  aws sts get-caller-identity
}

describe-instance-by-name() {
  local name="$1"
  aws ec2 describe-instances --filters "Name=tag:Name,Values=$name" --output text --query 'Reservations[*].Instances[*].{a:InstanceId,b:PrivateIpAddress}'
}

mssh-by-name() {
  local name="$1" ip id
  local user="${2:-ubuntu}"
  read id ip <<<"$(describe-instance-by-name "$name")"
  mssh "$user@$ip" -t "$id"
}

mssh-discourse() {
  mssh-by-name discourse-server $1
}

mssh-discourse-test() {
  mssh-by-name discourse-test-server $1
}

mssh-gerrit() {
  mssh-by-name gerrit-server
}

# Not yet behind VPN.
mssh-prod() {
  mssh ubuntu@i-0623a5e1bb61dd97b
}

prep-ssh-by-name() {
  local name="$1" ip id
  read id ip <<<"$(describe-instance-by-name "$name")"
  aws ec2-instance-connect send-ssh-public-key \
    --instance-id "$id" \
    --instance-os-user ubuntu \
    --ssh-public-key file://$HOME/.ssh/id_rsa.pub
}

"$@"
