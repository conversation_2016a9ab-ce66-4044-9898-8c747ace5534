#!/usr/bin/env bash

echo "#####################################"
echo "# To launch nodeenv:"
echo ". ~/.node/14.7.0/bin/activate"
echo ""
echo "# NOTE: if this is the first time running this container,"
echo "# additional steps (such as installing dependencies) are needed."
echo "# please check dev-setup.txt"
echo ""
echo "# To setup/reset the database"
echo "bash /code/wab/tools/docker-dev/db-setup.bash"
echo "node /code/wab/tools/docker-dev/db-reset.js"
echo ""
echo "# To start the server"
echo "bash /code/wab/tools/start.bash"
echo "screen -dR wab-screens"
echo "  Ctrl-A :source /code/wab/tools/layout.screen"
echo ""
echo "# Login (3 users)"
echo "<EMAIL> <EMAIL> <EMAIL>"
echo "!53kr3tz!"
echo "#####################################"
