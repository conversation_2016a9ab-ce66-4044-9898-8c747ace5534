# Fix for NullOrUndefinedValueError during project cloning

## Problem
The error "NullOrUndefinedValueError: Value must not be undefined or null- All tpls should be mapped" occurs during project cloning when the `fixExprRefs` function encounters expressions (CustomCode, ObjectPath, EventHandler) that are missing their required `tpl` field.

## Root Cause
Some older projects have expressions without the `tpl` field set. When cloning these projects, the `fixExprRefs` function tries to map the tpl reference but fails because the field is missing entirely.

## Solution Applied

### 1. Immediate Fix in cloneComponent function
Modified `/workspace/platform/wab/src/wab/shared/core/components.ts`:

- Added logic to check if expressions that should have tpl references are missing them
- If missing, set the tpl reference to the current tpl before trying to fix references
- Updated `fixExprRefs` to handle expressions with tpl fields

### 2. Long-term Fix: Migration
Created migration `/workspace/platform/wab/src/wab/server/bundle-migrations/251-fix-missing-expr-tpl.ts`:

- Scans all components, arenas, and page arenas
- Finds expressions (CustomCode, ObjectPath, EventHandler) missing tpl references
- Sets the appropriate tpl reference for each expression
- Registered in migrations-list.txt

## Changes Made

1. **core/components.ts** (lines 865-880): Added check and fix for missing tpl references
2. **core/components.ts** (lines 830-839): Added handling for expressions with tpl in fixExprRefs
3. **bundle-migrations/251-fix-missing-expr-tpl.ts**: New migration to fix data permanently
4. **migrations-list.txt**: Added new migration to the list

## Testing the Fix

The fix should:
1. Allow projects with missing tpl references to be cloned successfully
2. Set the correct tpl reference for expressions during cloning
3. Eventually fix the data permanently when the migration runs

## Investigation Scripts Created

- `investigate-tpl-error.js`: Basic investigation of project structure
- `investigate-tpl-error-deep.js`: Deep analysis that found 10,880 expressions missing tpl references
- `fix-expr-tpl-during-clone.ts`: Helper functions to fix expressions before cloning
- `patch-fix-expr-refs.ts`: Documentation of the fix approach