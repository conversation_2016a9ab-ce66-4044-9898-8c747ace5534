#!/usr/bin/env node

/**
 * CLI script to analyze mutation logs
 * 
 * Usage:
 *   yarn analyze-mutations
 *   yarn analyze-mutations --file custom-log.jsonl
 */

import { analyzeMutationLogs } from "./src/wab/server/db/mutation-detector";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";

const argv = yargs(hideBin(process.argv))
  .option("file", {
    alias: "f",
    type: "string",
    description: "Path to mutation log file",
    default: "mutation-audit.jsonl",
  })
  .help()
  .parseSync();

console.log(`Analyzing mutation logs from: ${argv.file}\n`);

analyzeMutationLogs(argv.file)
  .then(() => {
    console.log("\nAnalysis complete!");
  })
  .catch((error) => {
    console.error("Error analyzing logs:", error);
    process.exit(1);
  });