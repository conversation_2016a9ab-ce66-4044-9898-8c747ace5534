#!/bin/bash -eux

function print_usage() {
  echo "Usage: $0 <codegen|integrations|socket|app>"
}

if [ $# != 1 ]; then
  print_usage
  exit 1
fi

if [ "$1" = "codegen" ]; then
  script="codegen-backend.ts"
elif [ "$1" = "integrations" ]; then
  script="integrations-backend.ts"
elif [ "$1" = "socket" ]; then
  script="app-socket-backend-real.ts"
elif [ "$1" = "app" ]; then
  script="main.ts"
else
  print_usage
  exit 1
fi

export SERVICE_NAME="$1-service"
config="src/wab/server/internal/config.$1.prod.json"

npm run run-ts -- src/wab/server/$script
