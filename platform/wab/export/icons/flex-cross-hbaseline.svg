<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="24px" height="20px" viewBox="0 0 24 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>icons/flex-cross-hbaseline</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="16" height="5"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="16" height="5" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-3" x="1" y="7" width="12" height="5"></rect>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="12" height="5" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
    </defs>
    <g id="Flexbox" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Container" transform="translate(-222.000000, -260.000000)">
            <g id="icons/flex-cross-hbaseline" transform="translate(221.000000, 260.000000)">
                <path d="M24.5,0.5 L24.5,19.5" id="Line-Copy" stroke-opacity="0.52173913" stroke="#979797" fill-opacity="0.5" fill="#979797"></path>
                <path d="M1.5,0.5 L1.5,19.5" id="Line-Copy-10" stroke-opacity="0.52173913" stroke="#979797" fill-opacity="0.5" fill="#979797"></path>
                <path d="M8.5,2.5 L8.5,17.5" id="Line-Copy-11" stroke="#888C90"></path>
                <g id="Group" transform="translate(3.000000, 4.000000)">
                    <use id="Rectangle" stroke="#888C90" mask="url(#mask-2)" stroke-width="2" xlink:href="#path-1"></use>
                    <rect id="Rectangle-Copy-10" fill="#888C90" x="5.00097656" y="1" width="10.9990234" height="3"></rect>
                    <use id="Rectangle-Copy" stroke="#888C90" mask="url(#mask-4)" stroke-width="2" xlink:href="#path-3"></use>
                    <rect id="Rectangle-Copy-11" fill="#888C90" x="5.00097656" y="8" width="7.99902344" height="3"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>