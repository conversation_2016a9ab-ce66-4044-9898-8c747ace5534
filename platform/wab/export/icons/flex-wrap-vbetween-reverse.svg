<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="20px" height="22px" viewBox="0 0 20 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>icons/flex-wrap-vbetween-reverse</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Flexbox" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Container" transform="translate(-227.000000, -387.000000)">
            <g id="icons/flex-wrap-vbetween-reverse" transform="translate(227.000000, 386.000000)">
                <g id="flex-top-bottom" stroke="#979797" stroke-opacity="0.52173913" fill="#979797" fill-opacity="0.5">
                    <g id="Group-2">
                        <path d="M0.5,1.5 L19.5,1.5" id="Line"></path>
                        <path d="M0.5,22.5 L19.5,22.5" id="Line-Copy"></path>
                    </g>
                </g>
                <g id="Group-2" transform="translate(2.000000, 3.000000)" fill="#888C90">
                    <rect id="Rectangle" x="0" y="0" width="4" height="3"></rect>
                    <rect id="Rectangle-Copy" x="12" y="15" width="4" height="3"></rect>
                    <rect id="Rectangle" x="6" y="0" width="4" height="3"></rect>
                    <rect id="Rectangle-Copy" x="6" y="15" width="4" height="3"></rect>
                    <rect id="Rectangle" x="12" y="0" width="4" height="3"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>