<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="21px" height="22px" viewBox="0 0 21 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>icons/push-left</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Position-Control" transform="translate(-26.000000, -108.000000)">
            <g id="icons/push-left" transform="translate(26.000000, 109.000000)">
                <path d="M-7.75210084,10 L11.2741968,10" id="Line" stroke="#979797" stroke-width="2" stroke-linecap="square" transform="translate(1.747899, 10.000000) rotate(270.000000) translate(-1.747899, -10.000000) "></path>
                <text id="arrow-down---FontAwesome-Copy-3" transform="translate(12.000000, 9.500000) scale(-1, 1) rotate(-90.000000) translate(-12.000000, -9.500000) " font-family="FontAwesome" font-size="20" font-weight="normal" fill="#979797">
                    <tspan x="2.71428571" y="16.5"></tspan>
                </text>
                <text id="arrow-down---FontAwesome-Copy-5" transform="translate(12.000000, 9.500000) scale(-1, 1) rotate(-90.000000) translate(-12.000000, -9.500000) " font-family="FontAwesome" font-size="20" font-weight="normal" fill="#979797">
                    <tspan x="2.71428571" y="16.5"></tspan>
                </text>
            </g>
        </g>
    </g>
</svg>