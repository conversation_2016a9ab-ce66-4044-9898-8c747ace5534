<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="24px" height="20px" viewBox="0 0 24 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>icons/flex-center</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="Flexbox" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Container" transform="translate(-81.000000, -228.000000)">
            <g id="icons/flex-center" transform="translate(93.000000, 238.000000) rotate(-90.000000) translate(-93.000000, -238.000000) translate(83.000000, 225.000000)">
                <path d="M0.5,1.5 L19.5,1.5" id="Line-Copy-3" stroke-opacity="0.52173913" stroke="#979797" fill-opacity="0.5" fill="#979797"></path>
                <path d="M0.5,24.5 L19.5,24.5" id="Line-Copy-2" stroke-opacity="0.52173913" stroke="#979797" fill-opacity="0.5" fill="#979797"></path>
                <g id="Group-6" transform="translate(6.000000, 9.000000)" fill="#888C90">
                    <rect id="Rectangle-Copy-3" x="0" y="0" width="8" height="3"></rect>
                    <rect id="Rectangle-Copy-2" x="0" y="5" width="8" height="3"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>