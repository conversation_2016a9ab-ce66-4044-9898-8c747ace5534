<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="35px" height="28px" viewBox="0 0 35 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>background-video</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="-2.27373675e-13" width="34.5" height="27.3785086" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="34.5" height="27.3785086" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-44.000000, -1717.000000)">
            <g id="background-video" transform="translate(44.000000, 1717.000000)">
                <use id="Rectangle-67" stroke="#979797" mask="url(#mask-2)" stroke-width="2" xlink:href="#path-1"></use>
                <text id="video-camera---FontAwesome" font-family="FontAwesome" font-size="20" font-weight="normal" fill-opacity="0.5" fill="#FFFFFF">
                    <tspan x="8" y="21"></tspan>
                </text>
            </g>
        </g>
    </g>
</svg>