<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="22px" height="27px" viewBox="0 0 22 27" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>carousel</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-51.000000, -1173.000000)">
            <g id="carousel" transform="translate(51.000000, 1172.000000)">
                <text id="picture-o---FontAwesome" font-family="FontAwesome" font-size="20" font-weight="normal" fill-opacity="0.526211504" fill="#FFFFFF">
                    <tspan x="0" y="17"></tspan>
                </text>
                <ellipse id="Oval-8" stroke="#979797" fill="#979797" cx="4" cy="25" rx="2" ry="2"></ellipse>
                <ellipse id="Oval-8" stroke="#979797" cx="11" cy="25" rx="2" ry="2"></ellipse>
                <ellipse id="Oval-8" stroke="#979797" cx="18" cy="25" rx="2" ry="2"></ellipse>
            </g>
        </g>
    </g>
</svg>