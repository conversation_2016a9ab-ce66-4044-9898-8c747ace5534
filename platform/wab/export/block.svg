<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="51px" height="33px" viewBox="0 0 51 33" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>block</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="35" y="304" width="51" height="33"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="51" height="33" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-35.000000, -304.000000)" stroke="#979797" stroke-width="2">
            <use id="block" mask="url(#mask-2)" xlink:href="#path-1"></use>
        </g>
    </g>
</svg>