<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="32px" height="24px" viewBox="0 0 32 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>bg-radial-gradient</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="50%" id="radialGradient-1">
            <stop stop-color="#C8C8C8" offset="0%"></stop>
            <stop stop-color="#5E5E5E" offset="100%"></stop>
        </radialGradient>
        <rect id="path-2" x="225.5" y="1929" width="31" height="24"></rect>
        <mask id="mask-3" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="31" height="24" fill="white">
            <use xlink:href="#path-2"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Styles-Sidebar" transform="translate(-225.000000, -1929.000000)" stroke="#1E1E1E" stroke-width="2" fill="url(#radialGradient-1)">
            <use id="bg-radial-gradient" mask="url(#mask-3)" xlink:href="#path-2"></use>
        </g>
    </g>
</svg>