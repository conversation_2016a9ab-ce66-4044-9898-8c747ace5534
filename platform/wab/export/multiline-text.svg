<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="51px" height="33px" viewBox="0 0 51 33" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>multiline-text</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="51" height="33"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="51" height="33" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-35.000000, -753.000000)">
            <g id="multiline-text" transform="translate(35.000000, 753.000000)">
                <use id="Rectangle-67" stroke="#979797" mask="url(#mask-2)" stroke-width="2" xlink:href="#path-1"></use>
                <text id="i-cursor---FontAwesome" font-family="FontAwesome" font-size="12" font-weight="normal" fill-opacity="0.507246377" fill="#FFFFFF">
                    <tspan x="5" y="16"></tspan>
                </text>
            </g>
        </g>
    </g>
</svg>