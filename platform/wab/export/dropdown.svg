<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="45px" height="20px" viewBox="0 0 45 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>dropdown</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="45" height="19.5"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="45" height="19.5" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-38.000000, -876.000000)">
            <g id="dropdown" transform="translate(38.000000, 876.000000)">
                <use id="Rectangle-67" stroke="#979797" mask="url(#mask-2)" stroke-width="2" xlink:href="#path-1"></use>
                <polygon id="Path-4" fill="#6F6F6F" transform="translate(37.906250, 9.253470) rotate(-270.000000) translate(-37.906250, -9.253470) " points="35.6527805 5.34721955 35.6527805 13.1597195 40.1597195 9.09028045"></polygon>
            </g>
        </g>
    </g>
</svg>