<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="17px" height="17px" viewBox="0 0 17 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>radio-button</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" font-weight="400" font-family="Ionicons" font-size="20" fill-opacity="0.514492754">
        <g id="Create-Sidebar" transform="translate(-51.000000, -914.000000)" fill="#FFFFFF">
            <g id="radio-button" transform="translate(51.000000, 912.000000)">
                <text id="ion-android-radio-button-on---Ionicons">
                    <tspan x="0" y="18"></tspan>
                </text>
            </g>
        </g>
    </g>
</svg>