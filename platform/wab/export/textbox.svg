<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="41px" height="21px" viewBox="0 0 41 21" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>textbox</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="4" width="41" height="12.5"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="41" height="12.5" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-40.000000, -718.000000)">
            <g id="textbox" transform="translate(40.000000, 719.000000)">
                <use id="Rectangle-67" stroke="#979797" mask="url(#mask-2)" stroke-width="2" xlink:href="#path-1"></use>
                <text id="i-cursor---FontAwesome" font-family="FontAwesome" font-size="20" font-weight="normal" fill-opacity="0.507246377" fill="#FFFFFF">
                    <tspan x="30" y="17"></tspan>
                </text>
            </g>
        </g>
    </g>
</svg>