<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="40px" height="27px" viewBox="0 0 40 27" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch -->
    <title>display-flex</title>
    <desc>Created with Sketch.</desc>
    <defs></defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Styles-Sidebar" transform="translate(-32.000000, -3168.000000)" fill="#979797">
            <g id="display-flex" transform="translate(32.000000, 3168.000000)">
                <g id="display-inline-block-copy" transform="translate(0.000000, 10.000000)">
                    <rect id="Rectangle-4-Copy-3" x="0" y="0" width="6" height="7"></rect>
                    <rect id="Rectangle-4-Copy-4" x="8" y="0" width="6" height="7"></rect>
                    <rect id="Rectangle-4-Copy-5" x="16" y="0" width="11" height="7"></rect>
                </g>
                <g id="display-inline-block-copy-3" transform="translate(36.500000, 13.500000) rotate(90.000000) translate(-36.500000, -13.500000) translate(23.000000, 10.000000)">
                    <rect id="Rectangle-4-Copy-3" x="0" y="0" width="6" height="7"></rect>
                    <rect id="Rectangle-4-Copy-4" x="8" y="0" width="6" height="7"></rect>
                    <rect id="Rectangle-4-Copy-5" x="16" y="0" width="11" height="7"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>