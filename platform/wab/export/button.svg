<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="40px" height="20px" viewBox="0 0 40 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>button</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="2.27373675e-13" width="40" height="19.5"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="40" height="19.5" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-41.000000, -804.000000)">
            <g id="button" transform="translate(41.000000, 804.000000)">
                <text id="OK" font-family="Arial-BoldMT, Arial" font-size="10" font-weight="bold" fill="#979797">
                    <tspan x="12" y="13">OK</tspan>
                </text>
                <use id="Rectangle-67" stroke="#979797" mask="url(#mask-2)" stroke-width="2" xlink:href="#path-1"></use>
            </g>
        </g>
    </g>
</svg>