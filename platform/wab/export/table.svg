<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="40px" height="21px" viewBox="0 0 40 21" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 41.2 (35397) - http://www.bohemiancoding.com/sketch -->
    <title>table</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="26" y="15" width="14" height="6"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-3" x="13" y="15" width="14" height="6"></rect>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-3"></use>
        </mask>
        <rect id="path-5" x="0" y="15" width="14" height="6"></rect>
        <mask id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-5"></use>
        </mask>
        <rect id="path-7" x="26" y="10" width="14" height="6"></rect>
        <mask id="mask-8" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-7"></use>
        </mask>
        <rect id="path-9" x="13" y="10" width="14" height="6"></rect>
        <mask id="mask-10" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-9"></use>
        </mask>
        <rect id="path-11" x="0" y="10" width="14" height="6"></rect>
        <mask id="mask-12" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-11"></use>
        </mask>
        <rect id="path-13" x="26" y="5" width="14" height="6"></rect>
        <mask id="mask-14" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-13"></use>
        </mask>
        <rect id="path-15" x="13" y="5" width="14" height="6"></rect>
        <mask id="mask-16" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-15"></use>
        </mask>
        <rect id="path-17" x="0" y="5" width="14" height="6"></rect>
        <mask id="mask-18" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-17"></use>
        </mask>
        <rect id="path-19" x="26" y="0" width="14" height="6"></rect>
        <mask id="mask-20" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-19"></use>
        </mask>
        <rect id="path-21" x="13" y="0" width="14" height="6"></rect>
        <mask id="mask-22" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-21"></use>
        </mask>
        <rect id="path-23" x="0" y="0" width="14" height="6"></rect>
        <mask id="mask-24" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="14" height="6" fill="white">
            <use xlink:href="#path-23"></use>
        </mask>
    </defs>
    <g id="All-Screens" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Create-Sidebar" transform="translate(-42.000000, -540.000000)" stroke-width="2" stroke="#979797">
            <g id="table" transform="translate(42.000000, 540.000000)">
                <g id="Group-2">
                    <use id="Rectangle-68-Copy-78" mask="url(#mask-2)" xlink:href="#path-1"></use>
                    <use id="Rectangle-68-Copy-77" mask="url(#mask-4)" xlink:href="#path-3"></use>
                    <use id="Rectangle-68-Copy-76" mask="url(#mask-6)" xlink:href="#path-5"></use>
                    <use id="Rectangle-68-Copy-75" mask="url(#mask-8)" xlink:href="#path-7"></use>
                    <use id="Rectangle-68-Copy-74" mask="url(#mask-10)" xlink:href="#path-9"></use>
                    <use id="Rectangle-68-Copy-73" mask="url(#mask-12)" xlink:href="#path-11"></use>
                    <use id="Rectangle-68-Copy-72" mask="url(#mask-14)" xlink:href="#path-13"></use>
                    <use id="Rectangle-68-Copy-71" mask="url(#mask-16)" xlink:href="#path-15"></use>
                    <use id="Rectangle-68-Copy-70" mask="url(#mask-18)" xlink:href="#path-17"></use>
                    <use id="Rectangle-68-Copy-69" mask="url(#mask-20)" xlink:href="#path-19"></use>
                    <use id="Rectangle-68-Copy" mask="url(#mask-22)" xlink:href="#path-21"></use>
                    <use id="Rectangle-68" mask="url(#mask-24)" xlink:href="#path-23"></use>
                </g>
            </g>
        </g>
    </g>
</svg>