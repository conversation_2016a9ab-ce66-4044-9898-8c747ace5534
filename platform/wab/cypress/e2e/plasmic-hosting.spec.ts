import { pressPublishButton } from "../support/util";

describe.skip("plasmic-hosting-domains", function () {
  it("lets domain management work", function () {
    cy.setupNewProject({ name: "left-panel" }).withinStudioIframe(() => {
      cy.switchToTreeTab();

      cy.createNewFrame().focusCreatedFrameRoot().insertFromAddDrawer("Text");

      pressPublishButton();
    });

    // Add Plasmic Hosting
    cy.get("#publish-flow-dialog-add-website-btn").click();

    // Configure
    cy.contains("Configure").click();

    // Input custom domain without www
    cy.get('input[placeholder="my-domain.com"]').type(
      `www.x${Math.round(Math.random() * 1e9)}.co.uk{enter}`
    );

    // Ensure there are two domain cards
    cy.contains("***********").should("exist");
    cy.contains("cname.plasmicdev.com").should("exist");

    // Input custom domain with www
    cy.get('[data-test-id="domain-card"]').contains("Remove").click();
    cy.get('input[placeholder="my-domain.com"]').type(
      `hostingtest${Math.round(Math.random() * 99)}.plasmiq.app{enter}`
    );

    // Success - should already be configured
    cy.contains("Correctly configured").should("exist");

    cy.removeCurrentProject();
  });
});
