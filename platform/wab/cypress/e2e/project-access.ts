describe("project-access", function () {
  afterEach(() => {
    cy.removeCurrentProject("<EMAIL>");
  });
  it("does not allow other users to view project by default", function () {
    cy.setupNewProject({
      name: "project-access",
      email: "<EMAIL>",
    }).then((projectId: string) => {
      // Logged out
      cy.wait(1000);
      cy.clearCookies();
      cy.openProject({ projectId });
      cy.location("href").should(
        "contains",
        `/login?continueTo=%2Fprojects%2F${projectId}`
      );

      // Log in as another user
      cy.login("<EMAIL>");
      cy.openProject({ projectId });
      cy.contains("Could not open project").should("be.visible");
    });
  });
  it("allows other users to view project if inviteOnly: false", function () {
    cy.setupNewProject({
      name: "project-access",
      email: "<EMAIL>",
      inviteOnly: false,
    }).then((projectId: string) => {
      // Logged out
      cy.wait(1000);
      cy.clearCookies();
      cy.openProject({ projectId });
      cy.location("href").should(
        "contains",
        `/login?continueTo=%2Fprojects%2F${projectId}`
      );

      // Log in as another user
      cy.login("<EMAIL>");
      cy.openProject({ projectId });
      cy.withinStudioIframe(() => {
        cy.contains("You only have read permission to this project").should(
          "be.visible"
        );
      });
    });
  });
});
