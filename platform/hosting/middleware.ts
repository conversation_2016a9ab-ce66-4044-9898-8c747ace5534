import { NextRequest, NextResponse } from "next/server";

// Serve all paths except /_next/*, /_sites/*, /api/*, favicon.ico, /*.js, /*.php, wp-admin, wp-content
// The patterns /*.js, /*.php, wp-admin, wp-content, wp-includes are added to avoid handling erroneous requests
// that are being sent by some custom domains.
export const config = {
  matcher: [
    "/:path((?!_next/|_sites/|_seo/|api/|favicon\\.ico|.*\\.js|.*\\.php|wp-admin|wp-content|wp-includes).*)",
  ],
};

export default function middleware(req: NextRequest) {
  const url = req.nextUrl;

  // Get hostname of request (e.g. demo.vercel.pub, demo.localhost:3000)
  const hostname = req.headers.get("host") || "demo.vercel.pub";

  if (url.pathname === "/robots.txt") {
    url.pathname = `/api/_seo/${encodeURIComponent(hostname)}/robots`;
    return NextResponse.rewrite(url);
  }

  // rewrite everything else to `/_sites/[site] dynamic route
  url.pathname = `/_sites/${encodeURIComponent(hostname)}${url.pathname}`;
  return NextResponse.rewrite(url);
}
