To test this out locally against production projects/domains:

1. Hardcode the `hostname` in middleware.

1. Run the following command:

   ```bash
   yarn dev
   ```

To test this out locally against a localhost Studio project:

1. Hardcode the `projectId, token, wantToShowBadge` in the catchall.

1. Run the following command:

   ```bash
   NEXT_PUBLIC_PLASMIC_HOST=http://localhost:3003 yarn dev
   ```

## Deploying

We use the vercel CLI to deploy this application to production. To install the vercel CLI, run the following command:

```bash
npm i -g vercel
```

After installing the CLI, you can deploy by running the following command while inside the `platform/hosting` repository:

```bash
vercel deploy --prod
```

You will need to authenticate to vercel if this is your first time. Choose the github authentication method and log in using github admin Plasmic account. After that, link the repository to the `plasmic/hosting` project inside the `Plasmic` organization.
