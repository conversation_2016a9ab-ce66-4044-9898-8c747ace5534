{"compilerOptions": {"target": "esnext", "module": "commonjs", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/components/*": ["components/*"], "@/data/*": ["data/*"], "@/layouts/*": ["layouts/*"], "@/lib/*": ["lib/*"], "@/styles/*": ["styles/*"], "@/types": ["types/index"], "@/types/*": ["types/*"]}, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "incremental": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}