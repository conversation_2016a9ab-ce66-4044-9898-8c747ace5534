import { PLASMIC_API_HOST } from "./constants";

export async function getMetadataForSite(site: string) {
  const resp = await fetch(
    `${PLASMIC_API_HOST}/api/v1/project-token-for-domain?domain=` +
      encodeURIComponent(site as string),
    {
      headers: {
        Accept: "application/json; charset=UTF-8",
      },
    }
  );
  return (await resp.json()) as {
    projectId?: string;
    token?: string;
    showBadge: boolean;
    favicon:
      | {
          url: string;
          mimeType?: string;
        }
      | undefined;
    hasAppAuth: boolean;
    allowRobots: boolean;
  };
}
