{"name": "hosting", "private": true, "scripts": {"build": "next build", "dev": "next dev", "format:write": "prettier --write \"**/*.{css,js,json,jsx,ts,tsx}\"", "format": "prettier \"**/*.{css,js,json,jsx,ts,tsx}\"", "lint": "next lint", "start": "next start", "type-check": "tsc"}, "engines": {"node": "^18.12.0"}, "dependencies": {"@plasmicapp/loader-nextjs": "^1.0.431", "@sentry/nextjs": "^7.26.0", "next": "^12.2.5", "next-auth": "^4.18.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.2.0", "sitemap": "^7.1.0", "swr": "^2.0.0"}, "devDependencies": {"@types/node": "^18.12.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "8.11.0", "eslint-config-next": "^12.1.7-canary.48", "prettier": "^2.5.1", "typescript": "^4.6.2"}}