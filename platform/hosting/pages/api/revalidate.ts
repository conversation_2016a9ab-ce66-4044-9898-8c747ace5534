import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { urlPaths }: { urlPaths: string[] } = req.body;

  try {
    const results = await Promise.allSettled(
      urlPaths.map((path) => res.revalidate(path))
    );

    const mungedResults = results.map((r) => ({
      status: r.status,
      details: r.status === "rejected" ? r.reason.toString() : r.value,
    }));

    console.log(
      "revalidate",
      req.headers.host,
      req.url,
      urlPaths,
      mungedResults
    );

    res.status(200).json({
      results: mungedResults,
    });
  } catch (error) {
    console.error(
      "Failed to revalidate",
      req.headers.host,
      req.url,
      urlPaths,
      error
    );

    res.status(500).json({
      message: `Failed to revalidate`,
    });
  }
}
