import { getMetadataForSite } from "api";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { site } = req.query;
  const metadata = await getMetadataForSite(site as string);
  if (metadata.allowRobots) {
    res.setHeader("Content-Type", "text/plain");
    res.status(200).send(`User-agent: *\nAllow: /`);
  } else {
    res.setHeader("Content-Type", "text/plain");
    res.status(200).send(`User-agent: *\nDisallow: /`);
  }
}
