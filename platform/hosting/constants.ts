const DEFAULT_PLASMIC_HOST = "https://studio.plasmic.app";
// NEXT_PUBLIC to allow variable to be exposed in browser code
export const PLASMIC_HOST =
  process.env.NEXT_PUBLIC_PLASMIC_HOST ?? DEFAULT_PLASMIC_HOST;

export const PLASMIC_AUTH_KEY = "plasmic_user";

export const AUTH_TOKEN_ENDPOINT = `${PLASMIC_HOST}/api/v1/app-auth/token`;

export const DEFAULT_REVALIDATE_PERIOD = 600; // 10 minutes
export const DEFAUlT_NOT_FOUND_REVALIDATE_PERIOD = 300; // 5 minutes

export const DEFAULT_PLASMIC_API_HOST = "https://studio.plasmic.app";
export const DEFAULT_PLASMIC_CODEGEN_HOST = "https://codegen.plasmic.app";
// NEXT_PUBLIC to allow variable to be exposed in browser code
export const PLASMIC_API_HOST =
  process.env.NEXT_PUBLIC_PLASMIC_HOST ?? DEFAULT_PLASMIC_API_HOST;
export const PLASMIC_CODEGEN_HOST =
  process.env.NEXT_PUBLIC_PLASMIC_HOST ?? DEFAULT_PLASMIC_CODEGEN_HOST;
export const BADGE_PROJECT =
  process.env.BADGE_PROJECT || "bR9hw24Z3CaJmESyNv8MvK";
export const BADGE_TOKEN =
  process.env.BADGE_TOKEN ||
  "a0QDcyE5h0ojFYGdj5BV8rgo76rp4be8jlNZNLbGZvdL4y04jSwBh9PpaAWlVAGr7mscUdccfF7z6IKA2Q";
export const FORCE_BADGE = process.env.FORCE_BADGE === "true";
// Normally, the badge can only be shown if we are using the production host,
// which is where the actual badge component project exists.
export const HAS_BADGE_PROJECT = PLASMIC_API_HOST === DEFAULT_PLASMIC_API_HOST;
