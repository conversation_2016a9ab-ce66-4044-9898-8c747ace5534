const fs = require("fs");
const path = require("path");

const packageJsonPath = path.resolve(process.cwd(), "package.json");
const yarnLockPath = path.resolve(process.cwd(), "yarn.lock");

const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
const yarnLock = fs.readFileSync(yarnLockPath, "utf8");

const getExactVersion = (pkgName, requestedVersion) => {
  const escapedVersion = requestedVersion.replace(/([\^~.])/g, "\\$1");
  const regex = new RegExp(
    `(^|"| )${pkgName}@${escapedVersion}"?.*\\n\\s*version\\s*"(.*?)"`,
    "gm"
  );
  const match = regex.exec(yarnLock);

  if (match) {
    return match[2];
  } else {
    console.warn(`No exact version found for ${pkgName}@${requestedVersion}`);
    return null;
  }
};

const syncDeps = (deps) => {
  if (!deps) {
    return {};
  }
  return Object.fromEntries(
    Object.entries(deps).map(([name, version]) => {
      const exactVersion = getExactVersion(name, version);
      return [name, exactVersion || version];
    })
  );
};

const updatedPackageJson = {
  ...packageJson,
  dependencies: syncDeps(packageJson.dependencies),
  devDependencies: syncDeps(packageJson.devDependencies),
};

fs.writeFileSync(
  packageJsonPath.replace("package.json", "package.exact.json"),
  JSON.stringify(updatedPackageJson, null, 2)
);

console.log("Synced dependencies written to package.exact.json");
