!**/.gitkeep
**/Dockerfile
# Auto-generated
**/.git/

# wab/create-react-app:
create-react-app/
create-react-app-new/

**/.delivery/

# Static assets from <PERSON>:
static-*.tgz

# Generated
.git/
src/wab/shared/copilot/internal/types-content.json
src/wab/shared/copilot/internal/validators/gen/
deps/chrome/
src/wab/gen/
public/static/img/*.svg
public/static/img/icons/*.svg
build/
src/wab/shared/model/classes.ts
src/wab/shared/model/classes-metas.ts
server/.cachedev-build/
**/dev-build
src/wab/styles/_tokens.sass
src/wab/styles/_tokens.ts
src/wab/styles/css-variables.scss
src/wab/styles/css-variables.ts
**/.intercom-token
**/yarn-error.log
**/cypress/videos
**/.cache
**/package-lock.json
**/localhost-key.pem
**/localhost.pem
**/import-graph.json
**/import-chains.png
**/import-chains.dot
**/.env

# Code coverage
**/coverage/
**/cc-test-reporter

**/to-import-into-substack.csv


**/**/*.swp
**/**/.idea/libraries/Generated_files.xml
**/**/.idea/sqldialects.xml
**/**/.idea/workspace.xml
# Local .terraform directories
**/**/.terraform/*
**/**/.vscode/
**/**/api/temp
**/**/api/tsc
**/*.pyc
# .tfstate files
**/*.tfstate
**/*.tfstate.*
**/.DS_Store
**/.cache
**/.cache-loader/
**/.direnv
**/.env
**/.eslintcache
**/.idea
**/.ipynb_checkpoints/
**/.venv/
.direnv/
# Typedoc output
platform/sub/typedoc-*/
platform/sub/typedoc.json
platform/sub/typedoc.json.gz
platform/sub/typedoc/
**/bower_components/
**/node_modules/
**/storybook-static/
**/.nx/
