{"version": "1.0.64", "license": "MIT", "types": "./dist/index.d.ts", "main": "./dist/index.js", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "engines": {"node": ">=10"}, "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.ts", "yalcp": "yalc publish --push", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "coverage": "TEST_CWD=`pwd` yarn --cwd=../.. test --coverage --passWithNoTests", "lint": "eslint", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh", "size": "size-limit", "analyze": "size-limit --why"}, "name": "@plasmicapp/loader-splits", "author": "<PERSON>", "size-limit": [{"path": "./dist/index.js", "limit": "10 KB"}], "devDependencies": {"@plasmicapp/loader-fetcher": "1.0.55", "@types/json-logic-js": "^1.2.1"}, "dependencies": {"json-logic-js": "^2.0.2"}, "publishConfig": {"access": "public"}, "gitHead": "fa53f7d79f0e26d8b061102fda0c06788da6f8a7"}