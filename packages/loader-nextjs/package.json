{"version": "1.0.435", "license": "MIT", "types": "./dist/index.d.ts", "main": "./dist/index.js", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}, "./react-server": {"types": "./dist/react-server.d.ts", "import": "./dist/react-server.esm.js", "require": "./dist/react-server.js"}, "./react-server-conditional": {"react-server": {"types": "./dist/react-server.d.ts", "import": "./dist/react-server.esm.js", "require": "./dist/react-server.js"}, "default": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "./edge": {"types": "./dist/edge.d.ts", "default": "./dist/edge.js"}}, "files": ["dist", "index.d.ts", "react-server.d.ts", "react-server-conditional.d.ts", "edge.d.ts"], "engines": {"node": ">=10"}, "scripts": {"build": "yarn build:types && yarn build:index && yarn build:edge && yarn build:react-server", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.tsx --use-client", "build:edge": "node ../../build.mjs ./src/edge.ts --no-esm", "build:react-server": "node ../../build.mjs ./src/react-server.tsx", "yalcp": "yalc publish --push", "test": "jest packages/loader-nextjs --config=../../jest.config.js --passWithNoTests", "coverage": "yarn test --coverage", "lint": "eslint", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"next": ">=10.1.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}, "name": "@plasmicapp/loader-nextjs", "author": "<PERSON>", "size-limit": [{"path": "dist/index.js", "limit": "15 KB"}, {"path": "dist/react-server.js", "limit": "15 KB"}], "devDependencies": {"@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "next": "^13.2.0"}, "dependencies": {"@plasmicapp/loader-core": "1.0.137", "@plasmicapp/loader-edge": "1.0.70", "@plasmicapp/loader-react": "1.0.395", "@plasmicapp/nextjs-app-router": "1.0.17", "@plasmicapp/watcher": "1.0.84", "server-only": "0.0.1"}, "gitHead": "fa53f7d79f0e26d8b061102fda0c06788da6f8a7"}