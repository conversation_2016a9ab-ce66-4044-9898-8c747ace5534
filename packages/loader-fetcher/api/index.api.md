## API Report File for "@plasmicapp/loader-fetcher"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

// @public (undocumented)
export class Api {
    constructor(opts: {
        projects: {
            id: string;
            token: string;
        }[];
        host?: string;
        nativeFetch?: boolean;
        manualRedirect?: boolean;
    });
    // (undocumented)
    fetchHtmlData(opts: {
        projectId: string;
        component: string;
        hydrate?: boolean;
        embedHydrate?: boolean;
    }): Promise<LoaderHtmlOutput>;
    // (undocumented)
    fetchLoaderData(projectIds: string[], opts: {
        platform?: "react" | "nextjs" | "gatsby";
        platformOptions?: {
            nextjs?: {
                appDir: boolean;
            };
        };
        preview?: boolean;
        browserOnly?: boolean;
        i18nKeyScheme?: "content" | "hash" | "path";
        i18nTagPrefix?: string;
        skipHead?: boolean;
    }): Promise<LoaderBundleOutput>;
    // (undocumented)
    getChunksUrl(bundle: LoaderBundleOutput, modules: CodeModule[]): string;
}

// @public (undocumented)
export interface AssetModule {
    // (undocumented)
    fileName: string;
    // (undocumented)
    source: string;
    // (undocumented)
    type: "asset";
}

// @public (undocumented)
export interface CodeModule {
    // (undocumented)
    code: string;
    // (undocumented)
    fileName: string;
    // (undocumented)
    imports: string[];
    // (undocumented)
    type: "code";
}

// @public (undocumented)
export interface ComponentMeta {
    // (undocumented)
    cssFile: string;
    // (undocumented)
    displayName: string;
    // (undocumented)
    entry: string;
    // (undocumented)
    id: string;
    // (undocumented)
    isCode: boolean;
    // (undocumented)
    isGlobalContextProvider: boolean;
    // (undocumented)
    isPage: boolean;
    // (undocumented)
    metadata?: Record<string, string>;
    // (undocumented)
    name: string;
    // (undocumented)
    pageMetadata?: PageMetadata;
    // (undocumented)
    path: string | undefined;
    // (undocumented)
    plumeType?: string;
    // (undocumented)
    projectId: string;
    // (undocumented)
    serverQueriesExecFuncFileName?: string;
    // (undocumented)
    usedComponents: string[];
}

// @public (undocumented)
export interface ExperimentSlice extends Slice {
    // (undocumented)
    prob: number;
}

// @public (undocumented)
export interface FetcherOptions {
    // (undocumented)
    cache?: LoaderBundleCache;
    // (undocumented)
    host?: string;
    // (undocumented)
    i18n?: {
        keyScheme: "content" | "hash" | "path";
        tagPrefix?: string;
    };
    // @deprecated (undocumented)
    i18nKeyScheme?: "content" | "hash" | "path";
    // (undocumented)
    manualRedirect?: boolean;
    // (undocumented)
    nativeFetch?: boolean;
    // (undocumented)
    platform?: "react" | "nextjs" | "gatsby";
    // (undocumented)
    platformOptions?: {
        nextjs?: {
            appDir: boolean;
        };
    };
    // (undocumented)
    preview?: boolean;
    // (undocumented)
    projects: {
        id: string;
        version?: string;
        token: string;
    }[];
    // (undocumented)
    skipHead?: boolean;
}

// @public (undocumented)
export interface FontMeta {
    // (undocumented)
    url: string;
}

// @public (undocumented)
export interface GlobalGroupMeta {
    // (undocumented)
    contextFile: string;
    // (undocumented)
    id: string;
    // (undocumented)
    name: string;
    // (undocumented)
    projectId: string;
    // (undocumented)
    type: string;
    // (undocumented)
    useName: string;
}

// @public (undocumented)
export function internal_getCachedBundleInNodeServer(opts: FetcherOptions): LoaderBundleOutput | undefined;

// @public (undocumented)
export interface LoaderBundleCache {
    // (undocumented)
    get: () => Promise<LoaderBundleOutput>;
    // (undocumented)
    set: (data: LoaderBundleOutput) => Promise<void>;
}

// @public (undocumented)
export interface LoaderBundleOutput extends ApiLoaderBundleOutput {
    // (undocumented)
    filteredIds: Record<string, string[]>;
}

// @public (undocumented)
export interface LoaderHtmlOutput {
    // (undocumented)
    html: string;
}

// @public (undocumented)
export interface PageMeta extends ComponentMeta {
    // (undocumented)
    isPage: true;
    // (undocumented)
    pageMetadata: PageMetadata;
    // (undocumented)
    path: string;
    // (undocumented)
    plumeType: never;
}

// @public (undocumented)
export interface PageMetadata {
    // (undocumented)
    canonical?: string | null;
    // (undocumented)
    description?: string | null;
    // (undocumented)
    openGraphImageUrl?: string | null;
    // (undocumented)
    path: string;
    // (undocumented)
    title?: string | null;
}

// @public (undocumented)
export class PlasmicModulesFetcher {
    constructor(opts: FetcherOptions);
    // (undocumented)
    fetchAllData(): Promise<LoaderBundleOutput>;
    // (undocumented)
    getChunksUrl(bundle: LoaderBundleOutput, modules: CodeModule[]): string;
}

// @public (undocumented)
export interface ProjectMeta {
    // (undocumented)
    globalContextsProviderFileName: string;
    // (undocumented)
    id: string;
    // (undocumented)
    indirect?: boolean;
    // (undocumented)
    name: string;
    // (undocumented)
    remoteFonts: FontMeta[];
    // (undocumented)
    teamId?: string;
    // (undocumented)
    version: string;
}

// @public (undocumented)
export interface SegmentSlice extends Slice {
    // (undocumented)
    cond: any;
}

// @public (undocumented)
export type Split = ExperimentSplit | SegmentSplit;

// (No @packageDocumentation comment for this package)

```
