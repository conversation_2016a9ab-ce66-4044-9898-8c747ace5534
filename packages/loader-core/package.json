{"version": "1.0.137", "license": "MIT", "types": "./dist/index.d.ts", "main": "./dist/index.js", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "engines": {"node": ">=10"}, "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.ts", "yalcp": "yalc publish --push", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test", "lint": "eslint", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh", "size": "size-limit", "analyze": "size-limit --why"}, "name": "@plasmicapp/loader-core", "author": "<PERSON>", "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}], "devDependencies": {"@types/node": "^20.8.9"}, "dependencies": {"@plasmicapp/isomorphic-unfetch": "1.0.3", "@plasmicapp/loader-fetcher": "1.0.55"}, "gitHead": "fa53f7d79f0e26d8b061102fda0c06788da6f8a7"}