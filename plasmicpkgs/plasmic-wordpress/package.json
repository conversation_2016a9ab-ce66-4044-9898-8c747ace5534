{"name": "@plasmicpkgs/plasmic-wordpress", "version": "0.0.146", "description": "Plasmic Wordpress components.", "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.tsx", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": ">=0.1.0", "react": ">=16"}, "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/index.esm.js", "limit": "10 KB"}], "devDependencies": {"@plasmicapp/host": "1.0.224", "@plasmicapp/query": "0.1.80", "@size-limit/preset-small-lib": "^7.0.8", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "husky": "^7.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^7.0.8"}, "dependencies": {"@types/dlv": "^1.1.2", "dlv": "^1.1.3"}}