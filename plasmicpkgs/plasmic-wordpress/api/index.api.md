## API Report File for "@plasmicpkgs/plasmic-wordpress"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import { ComponentMeta } from '@plasmicapp/host';
import { GlobalContextMeta } from '@plasmicapp/host';
import { default as React_2 } from 'react';
import { ReactNode } from 'react';
import registerComponent from '@plasmicapp/host/registerComponent';
import registerGlobalContext from '@plasmicapp/host/registerGlobalContext';

// @public (undocumented)
export function queryWordpress(wordpressUrl: string, type: "pages" | "posts", queryOperator?: QueryOperator, filterValue?: string, limit?: number): Promise<any>;

// @public (undocumented)
export function registerAll(loader?: {
    registerComponent: typeof registerComponent;
    registerGlobalContext: typeof registerGlobalContext;
}): void;

// @public (undocumented)
export function registerAllCustomFunctions(loader?: {
    registerFunction: any;
}): void;

// @public (undocumented)
export function WordpressFetcher({ queryOperator, filterValue, noAutoRepeat, limit, queryType, children, className, noLayout, }: WordpressFetcherProps): React_2.JSX.Element;

// @public (undocumented)
export const WordpressFetcherMeta: ComponentMeta<WordpressFetcherProps>;

// @public (undocumented)
export function WordpressField({ className, field }: WordpressFieldProps): React_2.JSX.Element;

// @public (undocumented)
export const WordpressFieldMeta: ComponentMeta<WordpressFieldProps>;

// @public (undocumented)
export function WordpressProvider({ wordpressUrl, children, }: React_2.PropsWithChildren<WordpressProviderProps>): React_2.JSX.Element;

// @public (undocumented)
export const WordpressProviderMeta: GlobalContextMeta<WordpressProviderProps>;

// (No @packageDocumentation comment for this package)

```
