{"name": "@plasmicpkgs/rive", "version": "0.0.11", "description": "Plasmic registration call for rive animation", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/rive.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/rive.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/rive.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"@rive-app/react-canvas": "^4.18.8"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0"}, "publishConfig": {"access": "public"}}