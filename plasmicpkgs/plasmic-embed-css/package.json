{"name": "@plasmicpkgs/plasmic-embed-css", "version": "0.1.209", "description": "Plasmic embed css code components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/plasmic-embed-css.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/plasmic-embed-css.cjs.production.min.js", "limit": "20 KB"}, {"path": "dist/plasmic-embed-css.esm.js", "limit": "20 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@size-limit/preset-small-lib": "^7.0.8", "@types/node": "^17.0.14", "@types/react": "^18.2.33", "size-limit": "^7.0.8", "tsdx": "^0.14.1", "tslib": "^2.3.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}