{"name": "@plasmicpkgs/react-scroll-parallax", "version": "0.0.232", "description": "Plasmic registration call for the HTML5 video element", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-scroll-parallax.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/react-scroll-parallax.cjs.production.min.js", "limit": "40 KB"}, {"path": "dist/react-scroll-parallax.esm.js", "limit": "40 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"react-scroll-parallax": "3.0.0-alpha.14", "resize-observer-polyfill": "^1.5.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}