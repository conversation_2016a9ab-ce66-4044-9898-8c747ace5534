{"name": "@plasmicpkgs/fetch", "version": "0.0.14", "description": "Plasmic registration call for fetch function", "repository": {"type": "git", "url": "git+https://github.com/plasmicapp/plasmic.git", "directory": "plasmicpkgs/fetch"}, "publishConfig": {"access": "public"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.ts", "prepublishOnly": "npm run build", "postpublish": "bash ../../scripts/publish-api-doc-model.sh"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "typescript": "^5.7.3"}, "peerDependencies": {"@plasmicapp/host": "^1.0.211"}}