import { registerButton } from "./registerButton";
import { registerCheckbox } from "./registerCheckbox";
import { registerCheckboxGroup } from "./registerCheckboxGroup";
import { registerComboBox } from "./registerComboBox";
import { registerDescription } from "./registerDescription";
import { registerDialog } from "./registerDialog";
import { registerDialogTrigger } from "./registerDialogTrigger";
// import { registerForm } from "./registerForm";
import { registerHeading } from "./registerHeading";
import { registerInput } from "./registerInput";
import { registerLabel } from "./registerLabel";
import { registerListBox } from "./registerListBox";
import { registerModal } from "./registerModal";
import { registerOverlayArrow } from "./registerOverlayArrow";
import { registerPopover } from "./registerPopover";
import { registerRadioGroup } from "./registerRadioGroup";
import { registerSelect } from "./registerSelect";
import { registerSlider } from "./registerSlider";
import { registerSwitch } from "./registerSwitch";
import { registerText } from "./registerText";
import { registerTextArea } from "./registerTextArea";
import { registerTextField } from "./registerTextField";
import { registerTooltip } from "./registerTooltip";
import { Registerable } from "./utils";

export function registerAll(loader?: Registerable) {
  registerText(loader);
  registerHeading(loader);
  registerDescription(loader);
  registerDialog(loader);
  registerOverlayArrow(loader);
  registerSelect(loader);
  registerComboBox(loader);
  registerButton(loader);
  registerLabel(loader);
  registerListBox(loader);
  registerPopover(loader);
  registerTextArea(loader);
  registerInput(loader);
  registerSwitch(loader);
  // registerForm(loader);
  registerCheckbox(loader);
  registerCheckboxGroup(loader);
  registerRadioGroup(loader);
  registerTextField(loader);
  registerModal(loader);
  registerTooltip(loader);
  registerDialogTrigger(loader);
  registerSlider(loader);
}
