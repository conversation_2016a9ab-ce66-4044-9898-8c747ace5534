{"name": "@plasmicpkgs/react-aria", "version": "0.0.150", "description": "Plasmic registration calls for react-aria based components", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/react-aria.esm.js", "nx": {"targets": {"build": {"inputs": ["{projectRoot}/**/*", "!{projectRoot}/**/dist/**/*", "!{projectRoot}/skinny/**/*"], "outputs": ["{projectRoot}/**/dist/**/*", "{projectRoot}/skinny/**/*"]}}}, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/react-aria.esm.js"}, "./skinny/*": {"types": "./skinny/*.d.ts", "require": "./skinny/*.cjs.js", "import": "./skinny/*.esm.js"}}, "files": ["dist", "skinny"], "scripts": {"build": "rollup -c rollup.config.mjs && yarn tsc --emitDeclarationOnly --declaration src/index.tsx --incremental --tsBuildInfoFile ./dist/.tsbuildinfo  --skipLibCheck --lib esnext,dom,dom.iterable --jsx react --esModuleInterop --strict --outDir ./dist/ && cp ./dist/*.d.ts skinny/ && rm skinny/index.d.ts", "prepare": "if-env PREPARE_NO_BUILD=true || yarn build", "clean": "rm -rf dist/ skinny/*.ts skinny/*.map skinny/*.js", "storybook": "storybook dev -p 6006 --no-open", "build-storybook": "storybook build", "test-storybook": "test-storybook", "upgrade-aria": "yarn upgrade --latest --scope @react-stately && yarn upgrade --latest --scope @react-aria && yarn upgrade --latest --scope react-aria-components"}, "dependencies": {"@react-aria/i18n": "^3.12.9", "@react-aria/utils": "^3.29.0", "react-aria": "^3.40.0", "react-aria-components": "^1.9.0", "react-keyed-flatten-children": "^3.0.0", "react-stately": "^3.38.0"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@rollup/plugin-commonjs": "^11.0.0", "@rollup/plugin-json": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "glob": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.10.1", "rollup-plugin-esbuild": "^5.0.0", "typescript": "^5"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0"}, "publishConfig": {"access": "public"}}