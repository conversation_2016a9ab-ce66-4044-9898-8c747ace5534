## API Report File for "@plasmicpkgs/graphql"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import registerFunction from '@plasmicapp/host/registerFunction';

// @public (undocumented)
export function fetchGraphQL(url: string, method: "GET" | "POST" | "PUT" | "DELETE", headers: Record<string, string>, request: {
    query: string;
    variables?: object;
}, varOverrides?: Record<string, any>): Promise<{
    statusCode: number;
    headers: {
        [k: string]: string;
    };
    body: any;
}>;

// @public (undocumented)
export function registerGraphQL(loader?: Registerable): void;

// (No @packageDocumentation comment for this package)

```
