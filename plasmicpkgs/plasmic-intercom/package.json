{"name": "@plasmicpkgs/plasmic-intercom", "version": "0.0.2", "description": "Plasmic intercom components.", "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/plasmic-intercom.esm.js", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"start": "tsdx watch", "build": "tsdx build", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16"}, "size-limit": [{"path": "dist/plasmic-intercom.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/plasmic-intercom.esm.js", "limit": "10 KB"}], "devDependencies": {"@plasmicapp/host": "^1.0.184", "@size-limit/preset-small-lib": "^7.0.8", "@types/react": "^17.0.43", "@types/react-dom": "^17.0.14", "husky": "^7.0.4", "react": "^18.0.0", "react-dom": "^18.0.0", "size-limit": "^7.0.8", "tsdx": "^0.14.1", "tslib": "^2.3.1"}, "dependencies": {"react-use-intercom": "^2.0.0"}}