{"name": "@plasmicpkgs/plasmic-strapi", "version": "0.1.175", "description": "Plasmic Strapi components.", "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.tsx --use-client", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "peerDependencies": {"react": ">=16.8.0"}, "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/index.esm.js", "limit": "10 KB"}], "dependencies": {"@types/dlv": "^1.1.2", "change-case": "^4.1.2", "dlv": "^1.1.3", "qs": "^6.11.0"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@plasmicapp/query": "0.1.80", "@size-limit/preset-small-lib": "^7.0.8", "@types/dlv": "^1.1.2", "@types/qs": "^6.9.7", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^7.0.8", "typescript": "^5.2.2"}}