## API Report File for "@plasmicpkgs/plasmic-strapi"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import { ComponentMeta } from '@plasmicapp/host';
import { CustomFunctionMeta } from '@plasmicapp/host/registerFunction';
import { GlobalContextMeta } from '@plasmicapp/host';
import { default as React_2 } from 'react';
import { ReactNode } from 'react';
import registerComponent from '@plasmicapp/host/registerComponent';
import registerGlobalContext from '@plasmicapp/host/registerGlobalContext';

// @public (undocumented)
export function queryStrapi(host: string, token: string | undefined, collection: string | undefined, filterField?: string, filterValue?: string, filterParameter?: string): Promise<any>;

// @public (undocumented)
export const queryStrapiMeta: CustomFunctionMeta<typeof queryStrapi>;

// @public (undocumented)
export function registerAll(loader?: {
    registerComponent: typeof registerComponent;
    registerGlobalContext: typeof registerGlobalContext;
}): void;

// @public (undocumented)
export function registerAllCustomFunctions(loader?: {
    registerFunction: any;
}): void;

// @public (undocumented)
export function StrapiCollection({ name, filterParameter, filterValue, filterField, limit, children, className, noLayout, noAutoRepeat, setControlContextData, }: StrapiCollectionProps): React_2.JSX.Element;

// @public (undocumented)
export const strapiCollectionMeta: ComponentMeta<StrapiCollectionProps>;

// @public (undocumented)
export function StrapiCredentialsProvider({ host, token, children, }: React_2.PropsWithChildren<StrapiCredentials>): React_2.JSX.Element;

// @public (undocumented)
export const strapiCredentialsProviderMeta: GlobalContextMeta<StrapiCredentials>;

// @public (undocumented)
export function StrapiField({ className, path, setControlContextData, }: StrapiFieldProps): React_2.JSX.Element;

// @public (undocumented)
export const strapiFieldMeta: ComponentMeta<StrapiFieldProps>;

// (No @packageDocumentation comment for this package)

```
