{"name": "@plasmicpkgs/lottie-react", "version": "0.0.216", "description": "Plasmic registration call for the HTML5 video element", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/lottie-react.esm.js", "files": ["dist"], "size-limit": [{"path": "dist/lottie-react.cjs.production.min.js", "limit": "10 KB"}, {"path": "dist/lottie-react.esm.js", "limit": "10 KB"}], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0"}, "dependencies": {"lottie-react": "^2.4.0"}, "resolutions": {"lottie-web": ">=5.13.0"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}