{"name": "@plasmicpkgs/commerce-commercetools", "version": "0.0.172", "description": "Plasmic registration calls for commercetools commerce provider", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/commerce-commercetools.esm.js", "files": ["dist"], "scripts": {"build": "tsdx build", "start": "tsdx watch", "test": "TEST_CWD=`pwd` yarn --cwd=../../.. test --passWithNoTests", "lint": "tsdx lint", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@plasmicapp/query": "0.1.80", "@size-limit/preset-small-lib": "^4.11.0", "@types/debounce": "^1.2.4", "@types/js-cookie": "^3.0.1", "@types/node": "^14.0.26", "react": "^18.2.0", "react-dom": "^18.2.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": ">=0.1.0", "react": ">=16.8.0"}, "dependencies": {"@commercetools/platform-sdk": "^2.8.0", "@commercetools/sdk-client-v2": "^2.2.2", "@plasmicpkgs/commerce": "0.0.222", "@types/react": "^18.0.27", "debounce": "^2.0.0", "js-cookie": "^3.0.5", "qs": "^6.11.0"}, "publishConfig": {"access": "public"}}