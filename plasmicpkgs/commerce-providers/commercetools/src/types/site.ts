/*
  Forked from https://github.com/vercel/commerce/tree/main/packages/shopify/src
  Changes: None
*/
import { SiteTypes } from "@plasmicpkgs/commerce"

export type Category = SiteTypes.Category;

export type Brand = SiteTypes.Brand;

export type SiteTypes = SiteTypes.SiteTypes;

export type GetSiteInfoOperation = SiteTypes.GetSiteInfoOperation;

export type GetCategoriesHook = SiteTypes.GetCategoriesHook;

export type GetBrandsHook = SiteTypes.GetBrandsHook;