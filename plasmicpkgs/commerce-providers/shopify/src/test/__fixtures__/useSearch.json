[{"scope": "https://next-js-store.myshopify.com:443", "method": "POST", "path": "/api/2025-01/graphql.json", "body": {"query": "\n    query getAllProducts($first: Int = 250, $query: String = \"\", $sortKey: ProductSortKeys = RELEVANCE, $reverse: Boolean = false) {\n  products(first: $first, sortKey: $sortKey, reverse: $reverse, query: $query) {\n    pageInfo {\n      hasNextPage\n      hasPreviousPage\n    }\n    edges {\n      node {\n        ...product\n      }\n    }\n  }\n}\n    fragment image on Image {\n  url\n  altText\n  width\n  height\n}\nfragment productVariant on ProductVariant {\n  id\n  sku\n  title\n  availableForSale\n  requiresShipping\n  selectedOptions {\n    name\n    value\n  }\n  image {\n    ...image\n  }\n  price {\n    amount\n    currencyCode\n  }\n  compareAtPrice {\n    amount\n    currencyCode\n  }\n}\nfragment product on Product {\n  id\n  handle\n  availableForSale\n  title\n  productType\n  description\n  descriptionHtml\n  options {\n    id\n    name\n    values\n  }\n  priceRange {\n    maxVariantPrice {\n      amount\n      currencyCode\n    }\n    minVariantPrice {\n      amount\n      currencyCode\n    }\n  }\n  variants(first: 250) {\n    edges {\n      node {\n        ...productVariant\n      }\n    }\n  }\n  featuredImage {\n    ...image\n  }\n  images(first: 20) {\n    edges {\n      node {\n        ...image\n      }\n    }\n  }\n  seo {\n    ...seo\n  }\n  tags\n  updatedAt\n  vendor\n}\nfragment seo on SEO {\n  description\n  title\n}", "variables": {"query": ""}}, "status": 200, "response": ["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"], "rawHeaders": {"access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "90c2e4aacc88f7d7-LAX", "connection": "keep-alive", "content-encoding": "gzip", "content-language": "en", "content-security-policy": "block-all-mixed-content; frame-ancestors 'none'; upgrade-insecure-requests;", "content-type": "application/json; charset=utf-8", "date": "Mon, 03 Feb 2025 13:51:00 GMT", "nel": "{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}", "powered-by": "Shopify", "report-to": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=PUNXVg0BUQ%2B5vwOzgcM2ARTMyIx3aiQNNORewSEX7uZ0X27cZcaAX8xeisMkelFFLpBnAap49Giz7TVd%2B0TnfR62GLa9IjumN9Gwvnkj1NC%2F683kO8SgLBuXDFU4aOg6Dvc1PhjE7vugwBoVtQ%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}", "server": "cloudflare", "server-timing": "processing;dur=132, db;dur=63, fetch;dur=94, asn;desc=\"7922\", edge;desc=\"LAX\", country;desc=\"US\", servedBy;desc=\"slw4\", graphql;desc=\"storefront/query/getAllProducts\", gqlSelectionNames;desc=\"sfr/products\", requestID;desc=\"986e2017-c30f-4097-b7b9-90498616881d-1738590660\", cfRequestDuration;dur=184.999943", "transfer-encoding": "chunked", "vary": "Accept-Encoding,Accept", "x-content-type-options": "nosniff", "x-dc": "gcp-us-west1,gcp-us-west1,gcp-us-west1", "x-download-options": "noopen", "x-frame-options": "DENY", "x-permitted-cross-domain-policies": "none", "x-request-id": "986e2017-c30f-4097-b7b9-90498616881d-1738590660", "x-shardid": "163", "x-shopid": "43402854564", "x-shopify-api-deprecated-reason": "https://shopify.dev/api/usage/versioning#deprecation-practices", "x-shopify-api-version": "2025-01", "x-sorting-hat-podid": "163", "x-sorting-hat-shopid": "43402854564", "x-storefront-renderer-rendered": "1", "x-xss-protection": "1; mode=block"}, "responseIsBinary": false}]