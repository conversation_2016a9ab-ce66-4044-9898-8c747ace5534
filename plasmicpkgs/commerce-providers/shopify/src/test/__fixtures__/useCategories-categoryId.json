[{"scope": "https://next-js-store.myshopify.com:443", "method": "POST", "path": "/api/2025-01/graphql.json", "body": {"query": "\n    query getSiteCollection($id: ID, $handle: String, $first: Int = 1) {\n  collection(id: $id, handle: $handle) {\n    ...collection\n    products(first: $first) {\n      edges {\n        node {\n          id\n        }\n      }\n    }\n  }\n}\n    fragment collection on Collection {\n  id\n  title\n  handle\n  image {\n    ...image\n  }\n}\nfragment image on Image {\n  url\n  altText\n  width\n  height\n}", "variables": {"id": "gid://shopify/Collection/271211167908"}}, "status": 200, "response": ["1f8b0800000000000013758db10ac2301445ffe5ce85985013cd2a38771787d0f7da066252da6490927f972ae8e4780ff7703690cb0e76439f42e03efb14f7e50916a3272bc43aa5d90f4f71f91e8432524929b5391f4e68907d0e0c8b2bbb5c162634985ca4371b7ecc3fdcc8b0b184d0605e12953eaf7b8c69e415f6b62126e23ff9ee2308ad953446e9b63d4a8d5aefb5d6fa0231dbf8b0c7000000"], "rawHeaders": {"access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "90c2e4aa2ac9f7d7-LAX", "connection": "keep-alive", "content-encoding": "gzip", "content-language": "en", "content-security-policy": "block-all-mixed-content; frame-ancestors 'none'; upgrade-insecure-requests;", "content-type": "application/json; charset=utf-8", "date": "Mon, 03 Feb 2025 13:51:00 GMT", "nel": "{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}", "powered-by": "Shopify", "report-to": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=%2B1PMWKXxkSDzx9NMNZwmcaSfIK6QOs%2BgSGiXqy%2FliSUEEeinK1jzROkiJtjeOciAbSKgFUGzYez5bpymjb%2FAQ%2F1ZhUkIEWg%2FaJMZ%2B3bzcjGE8yHUa70Tj0C6tyBR0Sz17Ugr919uCC3Cmsmkug%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}", "server": "cloudflare", "server-timing": "processing;dur=7, db;dur=2, asn;desc=\"7922\", edge;desc=\"LAX\", country;desc=\"US\", servedBy;desc=\"4zks\", graphql;desc=\"storefront/query/getSiteCollection\", gqlSelectionNames;desc=\"sfr/collection\", requestID;desc=\"863eb871-314a-4b5d-87a2-d52f1d3a0adb-1738590660\", cfRequestDuration;dur=62.000036", "transfer-encoding": "chunked", "vary": "Accept-Encoding,Accept", "x-cache": "hit, server", "x-content-type-options": "nosniff", "x-dc": "gcp-us-west1,gcp-us-west1,gcp-us-west1", "x-download-options": "noopen", "x-frame-options": "DENY", "x-permitted-cross-domain-policies": "none", "x-request-id": "863eb871-314a-4b5d-87a2-d52f1d3a0adb-1738590660", "x-shardid": "163", "x-shopid": "43402854564", "x-shopify-api-version": "2025-01", "x-sorting-hat-podid": "163", "x-sorting-hat-shopid": "43402854564", "x-storefront-renderer-rendered": "1", "x-xss-protection": "1; mode=block"}, "responseIsBinary": false}]