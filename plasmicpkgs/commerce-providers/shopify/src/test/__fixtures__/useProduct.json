[{"scope": "https://next-js-store.myshopify.com:443", "method": "POST", "path": "/api/2025-01/graphql.json", "body": {"query": "\n    query getProductById($id: ID!) {\n  product(id: $id) {\n    ...product\n  }\n}\n    fragment image on Image {\n  url\n  altText\n  width\n  height\n}\nfragment productVariant on ProductVariant {\n  id\n  sku\n  title\n  availableForSale\n  requiresShipping\n  selectedOptions {\n    name\n    value\n  }\n  image {\n    ...image\n  }\n  price {\n    amount\n    currencyCode\n  }\n  compareAtPrice {\n    amount\n    currencyCode\n  }\n}\nfragment product on Product {\n  id\n  handle\n  availableForSale\n  title\n  productType\n  description\n  descriptionHtml\n  options {\n    id\n    name\n    values\n  }\n  priceRange {\n    maxVariantPrice {\n      amount\n      currencyCode\n    }\n    minVariantPrice {\n      amount\n      currencyCode\n    }\n  }\n  variants(first: 250) {\n    edges {\n      node {\n        ...productVariant\n      }\n    }\n  }\n  featuredImage {\n    ...image\n  }\n  images(first: 20) {\n    edges {\n      node {\n        ...image\n      }\n    }\n  }\n  seo {\n    ...seo\n  }\n  tags\n  updatedAt\n  vendor\n}\nfragment seo on SEO {\n  description\n  title\n}", "variables": {"id": "gid://shopify/Product/5447324270756"}}, "status": 200, "response": ["1f8b0800000000000013ed98db6ee33610865f85986b45a2651d6c61bb4536db23822668d2c268e10b5a9cc8dcf0a0e5c14dbaf0bb17949d64b37082a668d2a0f08d2c51c399e17cfc47963e01679e41f3097a6b78687d3c151c1ae8046fb2cc2d4d2f2eaeb3d3cdddac2c8a7a9c17794debb28204964c7389d0c0c2a805da830facbd440f09b01513922d247e6bec198b26de064cc00b3fd8bf1becc98f37f6dbf0e7d77dbc7beb86a36bade8bd301a1a3836161511bd0b8a70238d254e78c214fa84b4463b6c3dfa6009e3a217ae15ba2328854f88434eb82128825386138faa379608dd0a2e78d09e044f245b188b04fdc63512c53acd0893e263602939b50c1d6a4f50a242ed832217ac155238e18844435628c98515ba13523282ce93202553adb13d5a821dfa941c7a22548f968b182608c2da3628c7f4ed42888eb36e5d1365ec420c0907f7d4245272647417e24a82158ed8a5d16d7084a146a6a36d4abee9d079e6860a6da29060358b65e897cca2b7ecee4431e76e8e417a2b5a812ebd8fe97baf2434f0a67fbba7f51a69bdc9fab79080196039687e7f4cef278355564d0b5ad2311d95e564020968a6a24acfc49f0809ac980c183dc13124301b0eb36398af93bfe17a5295655d4debaaa0b4b8737d14a1def3fd4eb2f6121278cfece589142bfcce226a98afe7b17988167f66bac3d8be14bbfa9559c1b43f8de371882913b48706aa71994ea790401bac45dd5e1f191ee3fd72f61ed60928a1ffd9d4754c7598e8e224e41d6e6aab07a3c70ab10d98c5025479319ed2b2a614127097011a80bb9e794c3272538687faabc58f415874674bd1f7427737e30e65d41b3ff98cfb0e8c310a4470bb38c4b63d848f35178a6dca1d6cd4fbd2fbde3559d6729d6e1798b646652ebb10125d36ca68312e329a4fcaac28ab22db367c97dd7b721cd0b4d7ddd7abaf46559e4f695ed7755cacf4e778e5a189724be00fc1fd129a11a53481258a6ee93757ebed56780af2d6a89e593cdc41bccc294de943c4872a3d9dee7852549387e87eb1b9ff33cc5fe4f18cbc47ff77def5281f57bb78cf5e46ceb3bd9e9f916ffcd93cb876f37d41413f0e7aafe87f8df8b8aaeb7c27f19792f45ed3cf4bb89a96c5ce7f60b31717f55ed54f663e5f277081cc078bfc87d7a98001d2036f0aaf29d3cff5f39af6d3c0d8a18979ddfb44b499b8956bbc5827e05937bc416e1280e4eeeb9289d2817902a1e7cc233f8cbb2ba7f9e880560779793e1a37e5b8a1d3dfa2e4507363a1819ff0caa71f5cdc697f01c1da7ea13e130000"], "rawHeaders": {"access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "90c2e4aceb36f7d7-LAX", "connection": "keep-alive", "content-encoding": "gzip", "content-language": "en", "content-security-policy": "block-all-mixed-content; frame-ancestors 'none'; upgrade-insecure-requests;", "content-type": "application/json; charset=utf-8", "date": "Mon, 03 Feb 2025 13:51:00 GMT", "nel": "{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}", "powered-by": "Shopify", "report-to": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=tDSZwuzXx7xXG4oYKaTSnKkjCouNWsR39ig2fQOnbjCNRo0xCc%2BURIC3uQP2mw5N0ad4NEYIU5fGs4cmLYovrILukTKGMSUMwhtiVTqovgQ5Oy3%2F3v692U0a%2Fuaj%2FuKnSqx3QM8ftBpmsVlClw%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}", "server": "cloudflare", "server-timing": "processing;dur=10;desc=\"gc:1\", db;dur=2, asn;desc=\"7922\", edge;desc=\"LAX\", country;desc=\"US\", servedBy;desc=\"w4sr\", graphql;desc=\"storefront/query/getProductById\", gqlSelectionNames;desc=\"sfr/product\", requestID;desc=\"05ccbd3f-d3e7-4927-8e14-faf2f276d78c-1738590660\", cfRequestDuration;dur=61.000109", "transfer-encoding": "chunked", "vary": "Accept-Encoding,Accept", "x-cache": "hit, server", "x-content-type-options": "nosniff", "x-dc": "gcp-us-west1,gcp-us-west1,gcp-us-west1", "x-download-options": "noopen", "x-frame-options": "DENY", "x-permitted-cross-domain-policies": "none", "x-request-id": "05ccbd3f-d3e7-4927-8e14-faf2f276d78c-1738590660", "x-shardid": "163", "x-shopid": "43402854564", "x-shopify-api-version": "2025-01", "x-sorting-hat-podid": "163", "x-sorting-hat-shopid": "43402854564", "x-storefront-renderer-rendered": "1", "x-xss-protection": "1; mode=block"}, "responseIsBinary": false}, {"scope": "https://next-js-store.myshopify.com:443", "method": "POST", "path": "/api/2025-01/graphql.json", "body": {"query": "\n    query getProductBySlug($slug: String!) {\n  productByHandle(handle: $slug) {\n    ...product\n  }\n}\n    fragment image on Image {\n  url\n  altText\n  width\n  height\n}\nfragment productVariant on ProductVariant {\n  id\n  sku\n  title\n  availableForSale\n  requiresShipping\n  selectedOptions {\n    name\n    value\n  }\n  image {\n    ...image\n  }\n  price {\n    amount\n    currencyCode\n  }\n  compareAtPrice {\n    amount\n    currencyCode\n  }\n}\nfragment product on Product {\n  id\n  handle\n  availableForSale\n  title\n  productType\n  description\n  descriptionHtml\n  options {\n    id\n    name\n    values\n  }\n  priceRange {\n    maxVariantPrice {\n      amount\n      currencyCode\n    }\n    minVariantPrice {\n      amount\n      currencyCode\n    }\n  }\n  variants(first: 250) {\n    edges {\n      node {\n        ...productVariant\n      }\n    }\n  }\n  featuredImage {\n    ...image\n  }\n  images(first: 20) {\n    edges {\n      node {\n        ...image\n      }\n    }\n  }\n  seo {\n    ...seo\n  }\n  tags\n  updatedAt\n  vendor\n}\nfragment seo on SEO {\n  description\n  title\n}", "variables": {"slug": "bomber-jacket"}}, "status": 200, "response": ["1f8b0800000000000013ed985b4fe43614c7bf8a759e43e2c97526da6e056cdb6d850a2ab442ad78f0c467325e7cc9fac24257f3dd2b67065856032a55a1a8e2259338e7e6f3f3df99e43370e619b49f61b08687ceef5dbd679a4b8c4382430bbde06d96b9a519c4e22a3b5a5b65555936455ee60d6daa1a12586e9c606ed41cedce07d69da38704d8051392cd257e6fec318b26de064cc00b3fdaef8df6e4a76bfb4d19275743bc7b1386a3ebac18bc301a5a3830161511830b8a70238d254e78c214fa8474463bec3cfa6009e36210ae13ba2728854f88434eb82128825386138f6a309608dd092e78d09e044f249b1b8b04fd3a3412c57acd0893e263602939b20c1d6a4f50a242ed83220bd609299c7044a2211728c9c20add0b291941e7499092a9ced8012dc11e7d4a763d116a40cb454c1304615d179463fa66224447af9bd044193b1763c1c13db68894ec1bdd873893608523766974171c61a891e9689b92ef7a749eb9b143eb2c2458cd621b8625b3e82dbb3d51ccb9eb6390de8a4ea04bef627aef958416de0c6f5f69bd445a6fb2e12d246046580eda3f1ed2fbe16895d5b39256b4a093aa9a4e2101cd5454e9b1f81321810b2603c6487000099c8e87d303385b257f23f4b4aeaaa69e35754969791b7a3f42bd137b4fb2ee1c1278c7ecf9a11417f88345d470b63a8b9b87e8f017a6fb71fb52ecf2376605d3fe288ec721a64cd01e5aa88b2a9dcd20812e588bbabbda373ce6fbf5f81dac125042ff33d7552c757474d109798febdeead1e8a1466c1266b101755e16335a35944202ee3c400b70bb671e908c5cb7e1befdd5e2c7202cbae3a51806a1fbeb718732ea8d1f7ec17d0bc6980522b86d1ce2b63da68f3d178aaddb1d6cd4fbd2fbc1b559d6719d6e26987646652e5b08892e9b64b42cca8ce6d32a2babbacc361bbecbee3c3976683ae8fedb8b6f26759ecf68de344d9cacf42778e9a18d724be093e07e09ed84529ac01245bff4ebabd566293c067967d4c02cee6e215ee594a6f43ee263971e4fb79896f5f43eba5f2deeff0cf357753c21efc9ff9d7733c98b7a1befd3e791f3e9ab9e9f906ffc593fb8b6f37d46413f0cfa55d1ff1af1a26e9a7c2bf1e792f4aba69f96703dabcaadffc04e9f5dd4afaa7e34f3b355020b643e58e43fbe4c058c90ee79537849957ea99f97b49e46c60e4dacebce27a2b5e346aef162958067fdf806b92e0092dbaf4b264a07ce120803671ef96e5c5d39cd273bb4dec9ab9349d156454b67bf47c9a1e6c6420b3fe3a54f3fb8b8d2fe02aa26dabb46130000"], "rawHeaders": {"access-control-allow-origin": "*", "alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "90c2e4ad7d2df7d7-LAX", "connection": "keep-alive", "content-encoding": "gzip", "content-language": "en", "content-security-policy": "block-all-mixed-content; frame-ancestors 'none'; upgrade-insecure-requests;", "content-type": "application/json; charset=utf-8", "date": "Mon, 03 Feb 2025 13:51:00 GMT", "nel": "{\"success_fraction\":0.01,\"report_to\":\"cf-nel\",\"max_age\":604800}", "powered-by": "Shopify", "report-to": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v4?s=htpxqq9MWBKJm1x5ZxGNKtuIFJFFIIuwb5zrYa77xwqPMk0t4EhGV%2FzuJ8PCyczPuoF0gbcM7qKJzJLaUUqRnM5qW%2BQNK68igUZMDTA4B498TBdMK8ZiitEwV%2B2OghTqOXmquZinjaXYcpa5oQ%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}", "server": "cloudflare", "server-timing": "processing;dur=14;desc=\"gc:1\", db;dur=3, asn;desc=\"7922\", edge;desc=\"LAX\", country;desc=\"US\", servedBy;desc=\"6b4q\", graphql;desc=\"storefront/query/getProductBySlug\", gqlSelectionNames;desc=\"sfr/productByHandle\", requestID;desc=\"963c1835-90f6-408b-bf55-d08d17a0850a-1738590660\", cfRequestDuration;dur=62.000036", "transfer-encoding": "chunked", "vary": "Accept-Encoding,Accept", "x-cache": "hit, server", "x-content-type-options": "nosniff", "x-dc": "gcp-us-west1,gcp-us-west1,gcp-us-west1", "x-download-options": "noopen", "x-frame-options": "DENY", "x-permitted-cross-domain-policies": "none", "x-request-id": "963c1835-90f6-408b-bf55-d08d17a0850a-1738590660", "x-shardid": "163", "x-shopid": "43402854564", "x-shopify-api-version": "2025-01", "x-sorting-hat-podid": "163", "x-sorting-hat-shopid": "43402854564", "x-storefront-renderer-rendered": "1", "x-xss-protection": "1; mode=block"}, "responseIsBinary": false}]