import registerComponent, {
  ComponentMeta,
} from "@plasmicapp/host/registerComponent";
import registerGlobalContext from "@plasmicapp/host/registerGlobalContext";
import {
    SanityCredentialsProvider,
    sanityCredentialsProviderMeta,
    <PERSON>ityF<PERSON>cher,
    sanityFetcherMeta,
    SanityField,
    sanityFieldMeta,
} from "./sanity";


export function registerAll(loader?: {
  registerComponent: typeof registerComponent;
  registerGlobalContext: typeof registerGlobalContext;
}) {
  const _registerComponent = <T extends React.ComponentType<any>>(
    Component: T,
    defaultMeta: ComponentMeta<React.ComponentProps<T>>
  ) => {
    if (loader) {
      loader.registerComponent(Component, defaultMeta);
    } else {
      registerComponent(Component, defaultMeta);
    }
  };

  if (loader) {
    loader.registerGlobalContext(SanityCredentialsProvider, sanityCredentialsProviderMeta);
  } else {
    registerGlobalContext(SanityCredentialsProvider, sanityCredentialsProviderMeta);
  }

  _registerComponent(<PERSON>ityF<PERSON>cher, sanityFetcherMeta);
  _registerComponent(<PERSON><PERSON><PERSON>ield, sanityFieldMeta);
}

export * from "./sanity";