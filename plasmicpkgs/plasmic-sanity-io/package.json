{"name": "@plasmicpkgs/plasmic-sanity-io", "version": "1.0.203", "description": "Plasmic Sanity.io components.", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/index.mjs", "files": ["dist"], "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/index.mjs", "limit": "10 KB"}], "scripts": {"build": "tsup-node src/index.tsx --dts --format esm,cjs --target es2019", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@plasmicapp/query": "0.1.80", "@size-limit/preset-small-lib": "^7.0.8", "@types/dlv": "^1.1.2", "@types/node": "^17.0.14", "@types/react": "^18.2.33", "size-limit": "^7.0.8", "tslib": "^2.3.1", "tsup": "^7.2.0", "typescript": "^5.2.2"}, "dependencies": {"@sanity/client": "^6.2.0", "@sanity/image-url": "^1.0.2", "change-case": "^4.1.2", "dlv": "^1.1.3"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}