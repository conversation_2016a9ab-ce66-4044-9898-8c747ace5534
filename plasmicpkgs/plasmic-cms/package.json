{"name": "@plasmicpkgs/plasmic-cms", "version": "0.0.289", "description": "Plasmic CMS components", "main": "./dist/index.js", "types": "./dist/index.d.ts", "module": "./dist/index.esm.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "size-limit": [{"path": "dist/index.js", "limit": "20 KB"}, {"path": "dist/index.esm.js", "limit": "20 KB"}], "scripts": {"build": "yarn build:types && yarn build:index", "build:types": "yarn tsc", "build:index": "node ../../build.mjs ./src/index.tsx", "test": "TEST_CWD=`pwd` yarn --cwd=../.. test --passWithNoTests", "prepublishOnly": "npm run build", "size": "size-limit", "analyze": "size-limit --why"}, "devDependencies": {"@plasmicapp/host": "1.0.224", "@plasmicapp/query": "0.1.80", "@size-limit/preset-small-lib": "^7.0.8", "@types/node": "^17.0.14", "@types/react": "^18.2.33", "size-limit": "^7.0.8"}, "dependencies": {"dayjs": "^1.10.7"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "@plasmicapp/query": ">=0.1.0", "react": ">=16.8.0", "react-dom": ">=16.8.0"}}