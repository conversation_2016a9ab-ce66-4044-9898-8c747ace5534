## API Report File for "@plasmicpkgs/plasmic-cms"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import { ApiCmsRow as ApiCmsRow_2 } from './schema';
import { ApiCmsTable as ApiCmsTable_2 } from './schema';
import { CanvasComponentProps } from '@plasmicapp/host/registerComponent';
import { ComponentMeta } from '@plasmicapp/host/registerComponent';
import { GlobalContextMeta } from '@plasmicapp/host/registerGlobalContext';
import { default as React_2 } from 'react';
import registerComponent from '@plasmicapp/host/registerComponent';
import registerGlobalContext from '@plasmicapp/host/registerGlobalContext';

// @public (undocumented)
export class API {
    constructor(config: DatabaseConfig);
    // (undocumented)
    count(table: string, params?: Pick<QueryParams, "where" | "useDraft">): Promise<number>;
    // (undocumented)
    fetchTables(): Promise<ApiCmsTable[]>;
    // (undocumented)
    get(endpoint: string, params?: any): Promise<any>;
    // (undocumented)
    query(table: string, params?: QueryParams): Promise<ApiCmsRow[]>;
}

// @public (undocumented)
export function CmsCount({ className, table, setControlContextData, ...rest }: CmsCountProps): React_2.JSX.Element | null;

// @public (undocumented)
export const cmsCountFieldMeta: ComponentMeta<CmsCountProps>;

// @public (undocumented)
export function CmsCredentialsProvider({ children, databaseId, databaseToken, host, locale, useDraft, }: CmsCredentialsProviderProps): React_2.JSX.Element;

// @public (undocumented)
export const cmsCredentialsProviderMeta: GlobalContextMeta<CmsCredentialsProviderProps>;

// @public (undocumented)
export function CmsQueryRepeater({ table, children, setControlContextData, mode, where, useDraft, orderBy, desc, limit, offset, emptyMessage, forceEmptyState, loadingMessage, forceLoadingState, noLayout, noAutoRepeat, className, filterField, filterValue, fields, }: CmsQueryRepeaterProps): React_2.JSX.Element;

// @public (undocumented)
export const cmsQueryRepeaterMeta: ComponentMeta<CmsQueryRepeaterProps>;

// @public (undocumented)
export function CmsRowField({ className, table, field, dateFormat, setControlContextData, usePlasmicTheme, themeResetClassName, ...rest }: CmsRowFieldProps): React_2.JSX.Element | null;

// @public (undocumented)
export const cmsRowFieldMeta: ComponentMeta<CmsRowFieldProps>;

// @public (undocumented)
export function CmsRowFieldValue({ table, field, valueProp, children, setControlContextData, ...rest }: CmsRowFieldValueProps): React_2.ReactElement | null;

// @public (undocumented)
export const cmsRowFieldValueMeta: ComponentMeta<CmsRowFieldValueProps>;

// @public (undocumented)
export function CmsRowImage({ table, field, srcProp, children, setControlContextData, }: CmsRowImageProps): React_2.ReactElement | null;

// @public (undocumented)
export const cmsRowImageMeta: ComponentMeta<CmsRowImageProps>;

// @public (undocumented)
export function CmsRowLink({ table, field, hrefProp, children, setControlContextData, prefix, suffix, }: CmsRowLinkProps): React_2.ReactElement | null;

// @public (undocumented)
export const cmsRowLinkMeta: ComponentMeta<CmsRowLinkProps>;

// @public (undocumented)
export interface DatabaseConfig {
    // (undocumented)
    databaseId: string;
    // (undocumented)
    databaseToken: string;
    // (undocumented)
    host: string;
    // (undocumented)
    locale?: string;
    // (undocumented)
    useDraft?: boolean | string[];
}

// @public (undocumented)
export function fetchContent(cmsId: string, cmsPublicToken: string, tableId: string, params: QueryParams, useDraft: boolean, locale: string): Promise<ApiCmsRow_2[]>;

// @public (undocumented)
export function fetchCount(cmsId: string, cmsPublicToken: string, tableId: string, params: QueryParams, useDraft: boolean): Promise<number>;

// @public (undocumented)
export function fetchTables(cmsId: string, cmsPublicToken: string): Promise<ApiCmsTable_2[]>;

// @public (undocumented)
export class HttpError extends Error {
    constructor(status: number, message: string);
    // (undocumented)
    status: number;
}

// @public (undocumented)
export function mkApi(config: DatabaseConfig | undefined): API;

// @public (undocumented)
export interface QueryParams {
    // (undocumented)
    desc?: boolean;
    // (undocumented)
    fields?: string[];
    // (undocumented)
    limit?: number;
    // (undocumented)
    offset?: number;
    // (undocumented)
    orderBy?: string;
    // (undocumented)
    useDraft?: boolean;
    // (undocumented)
    where?: any;
}

// @public (undocumented)
export function registerAll(loader?: {
    registerComponent: typeof registerComponent;
    registerGlobalContext: typeof registerGlobalContext;
}): void;

// @public (undocumented)
export function registerAllCustomFunctions(loader?: {
    registerFunction: any;
}): void;

// (No @packageDocumentation comment for this package)

```
