{"name": "@plasmicpkgs/tiptap", "version": "0.0.3", "description": "Tiptap for React", "main": "dist/index.js", "types": "dist/index.d.ts", "module": "dist/tiptap.esm.js", "nx": {"targets": {"build": {"inputs": ["{projectRoot}/**/*", "!{projectRoot}/**/dist/**/*", "!{projectRoot}/skinny/**/*"], "outputs": ["{projectRoot}/**/dist/**/*", "{projectRoot}/skinny/**/*"]}}}, "exports": {".": {"require": "./dist/index.js", "import": "./dist/tiptap.esm.js", "types": "./dist/index.d.ts"}, "./skinny/*": {"require": "./skinny/*.cjs.js", "import": "./skinny/*.esm.js", "types": "./skinny/*.d.ts"}}, "files": ["dist", "skinny"], "size-limit": [{"path": "dist/index.js", "limit": "10 KB"}, {"path": "dist/tiptap.esm.js", "limit": "10 KB"}], "scripts": {"build": "rollup -c rollup.config.mjs && yarn tsc --emitDeclarationOnly --declaration src/index.ts --incremental --tsBuildInfoFile ./dist/.tsbuildinfo  --skipLibCheck --jsx react --lib dom,esnext --esModuleInterop --strict --outDir ./dist/ && cp ./dist/*.d.ts skinny/ && rm skinny/index.d.ts", "prepublishOnly": "npm run build", "clean": "rm -rf dist/ skinny/*.ts skinny/*.map skinny/*.js", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test-storybook": "test-storybook"}, "devDependencies": {"@plasmicapp/data-sources": "0.1.141", "@plasmicapp/host": "1.0.183", "@rollup/plugin-commonjs": "^11.0.0", "@rollup/plugin-json": "^4.0.0", "@rollup/plugin-node-resolve": "^9.0.0", "@size-limit/preset-small-lib": "^4.11.0", "@types/node": "^14.0.26", "@types/react": "^18.2.33", "@types/react-dom": "^18.0.10", "glob": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^3.10.1", "rollup-plugin-esbuild": "^5.0.0", "size-limit": "^4.11.0", "tsdx": "^0.14.1", "tslib": "^2.2.0", "typescript": "^5.2.2"}, "peerDependencies": {"@plasmicapp/host": ">=1.0.0", "react": ">=16.8.0"}, "publishConfig": {"access": "public"}, "dependencies": {"@tiptap/core": "^2.1.12", "@tiptap/extension-bold": "^2.1.12", "@tiptap/extension-code": "^2.1.12", "@tiptap/extension-document": "^2.1.12", "@tiptap/extension-italic": "^2.1.12", "@tiptap/extension-link": "^2.1.12", "@tiptap/extension-mention": "^2.1.12", "@tiptap/extension-paragraph": "^2.1.12", "@tiptap/extension-strike": "^2.1.12", "@tiptap/extension-text": "^2.1.12", "@tiptap/extension-underline": "^2.1.12", "@tiptap/pm": "^2.1.12", "@tiptap/react": "^2.1.12", "@tiptap/suggestion": "^2.1.12", "antd": "^5.11.5", "tippy.js": "^6.3.7"}}